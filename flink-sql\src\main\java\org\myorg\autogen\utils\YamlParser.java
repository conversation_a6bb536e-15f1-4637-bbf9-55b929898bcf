package org.myorg.autogen.utils;

import org.myorg.autogen.configs.JobConfig;
import org.yaml.snakeyaml.Yaml;
import org.yaml.snakeyaml.constructor.Constructor;
import org.yaml.snakeyaml.LoaderOptions;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Utility class for parsing YAML job configuration files
 */
public class YamlParser {

    /**
     * Parse a YAML file into JobConfig object
     * 
     * @param filePath Path to the YAML file
     * @return JobConfig object
     * @throws IOException if file cannot be read
     */
    public static JobConfig parseJobConfig(String filePath) throws IOException {
        try (InputStream inputStream = new FileInputStream(filePath)) {
            return parseJobConfig(inputStream);
        }
    }

    /**
     * Parse a YAML input stream into JobConfig object
     * 
     * @param inputStream InputStream containing YAML content
     * @return JobConfig object
     */
    public static JobConfig parseJobConfig(InputStream inputStream) {
        Constructor constructor = new Constructor(JobConfig.class, new LoaderOptions());
        Yaml yaml = new Yaml(constructor);
        return yaml.load(inputStream);
    }

    /**
     * Validate that the job configuration is valid
     * 
     * @param jobConfig JobConfig to validate
     * @throws IllegalArgumentException if configuration is invalid
     */
    public static void validateJobConfig(JobConfig jobConfig) {
        if (jobConfig == null) {
            throw new IllegalArgumentException("Job configuration cannot be null");
        }

        if (jobConfig.getJobs() == null || jobConfig.getJobs().isEmpty()) {
            throw new IllegalArgumentException("At least one job must be defined");
        }

        for (JobConfig.Job job : jobConfig.getJobs()) {
            validateJob(job);
        }
    }

    private static void validateJob(JobConfig.Job job) {
        if (job.getName() == null || job.getName().trim().isEmpty()) {
            throw new IllegalArgumentException("Job name cannot be null or empty");
        }

        if (job.getReaders() == null) {
            throw new IllegalArgumentException("Job must have a reader configuration");
        }

        if (job.getWriters() == null) {
            throw new IllegalArgumentException("Job must have a writer configuration");
        }

        // Validate reader
        if (job.getReaders().getType() == null || job.getReaders().getType().trim().isEmpty()) {
            throw new IllegalArgumentException("Reader type cannot be null or empty");
        }

        // Validate writer
        if (job.getWriters().getType() == null || job.getWriters().getType().trim().isEmpty()) {
            throw new IllegalArgumentException("Writer type cannot be null or empty");
        }
    }
}
