package org.myorg.autogen.configs;

import java.util.List;
import java.util.Map;

/**
 * Configuration for data transformers
 */
public class TransformerConfig {
    private String type;
    private List<ColumnConfig> columns;
    private Map<String, Object> properties;
    private SchemaConfig outputSchema;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<ColumnConfig> getColumns() {
        return columns;
    }

    public void setColumns(List<ColumnConfig> columns) {
        this.columns = columns;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    public SchemaConfig getOutputSchema() {
        return outputSchema;
    }

    public void setOutputSchema(SchemaConfig outputSchema) {
        this.outputSchema = outputSchema;
    }

    /**
     * Column configuration for transformers
     */
    public static class ColumnConfig {
        private String colname;
        private String expr;

        public String getColname() {
            return colname;
        }

        public void setColname(String colname) {
            this.colname = colname;
        }

        public String getExpr() {
            return expr;
        }

        public void setExpr(String expr) {
            this.expr = expr;
        }
    }
}
