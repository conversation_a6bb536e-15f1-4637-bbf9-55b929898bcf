/* [create-plugin] version: 5.25.8 */
define(["react-router","rxjs","module","@grafana/ui","moment","lodash","react","@emotion/css","@grafana/data","react-dom","@grafana/runtime","@emotion/react"],(e,r,t,a,n,o,i,s,c,d,l,p)=>(()=>{"use strict";var u,f,m,g,v={1159:r=>{r.exports=e},1269:e=>{e.exports=r},1308:e=>{e.exports=t},1829:(e,r,t)=>{t.d(r,{$U:()=>d,$V:()=>U,$d:()=>T,Ao:()=>H,BS:()=>O,CE:()=>b,D5:()=>s,EY:()=>g,Ld:()=>R,MV:()=>p,PL:()=>u,PU:()=>N,Sr:()=>l,V2:()=>E,Vl:()=>w,W5:()=>P,X0:()=>L,ZM:()=>F,ZV:()=>M,a5:()=>f,bD:()=>k,bw:()=>i,cd:()=>c,gP:()=>x,gR:()=>v,jx:()=>D,nr:()=>Z,pf:()=>A,s9:()=>q,sv:()=>V,u0:()=>$,uK:()=>z,ui:()=>S,vR:()=>_,x5:()=>m,xT:()=>C,xc:()=>I,y2:()=>K,z:()=>y,zM:()=>h,zd:()=>j});var a=t(7781),n=t(2533);function o(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var i=function(e){return e.Explore="explore",e.Home="home",e}({});const s=`${`/a/${n.id}`}/explore`,c="grafana.drilldown.traces.datasource",d="grafana.drilldown.traces.homepage.filters",l="grafana.drilldown.traces.bookmarks",p="repeat(auto-fit, minmax(400px, 1fr))",u="No data for selected query",f="Please try removing some filters or changing your query.",m=" && ",g="ds",v="${ds}",b="primarySignal",H="filters",S="${primarySignal} && ${filters}",h="homeFilter",y="groupBy",x="spanListColumns",N="metric",A="latencyThreshold",C="${latencyThreshold}",I="partialLatencyThreshold",R="${partialLatencyThreshold}",w={uid:v},E="actionView",P="primarySignal",U="selection",K="All",k="Resource",O="Span",T="resource.",j="span.",L="event.",M="event:",$=["resource.service.name","resource.service.namespace","resource.service.version","resource.cluster","resource.environment","resource.namespace","resource.deployment.environment","resource.k8s.namespace.name","resource.k8s.pod.name","resource.k8s.container.name","resource.k8s.node.name"],D=["name","kind","rootName","rootServiceName","status","statusMessage","span.http.status_code"],z=["duration","event:name","nestedSetLeft","nestedSetParent","nestedSetRight","span:duration","span:id","trace:duration","trace:id","traceDuration"],F=["status","span:status","rootName","rootService","rootServiceName","trace:rootName","trace:rootService","trace:rootServiceName"],Z=1e3;class V extends a.BusEventWithPayload{}o(V,"type","timeseries-data-received");class _ extends a.BusEventWithPayload{}o(_,"type","trace-opened");const q=[{id:"filterByRefId",options:{exclude:"streaming-progress"}}]},2007:e=>{e.exports=a},2468:e=>{e.exports=n},2533:e=>{e.exports=JSON.parse('{"id":"grafana-exploretraces-app"}')},3241:e=>{e.exports=o},5959:e=>{e.exports=i},6089:e=>{e.exports=s},7781:e=>{e.exports=c},8398:e=>{e.exports=d},8531:e=>{e.exports=l},9089:e=>{e.exports=p}},b={};function H(e){var r=b[e];if(void 0!==r)return r.exports;var t=b[e]={id:e,loaded:!1,exports:{}};return v[e].call(t.exports,t,t.exports,H),t.loaded=!0,t.exports}H.m=v,H.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return H.d(r,{a:r}),r},f=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,H.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var t=Object.create(null);H.r(t);var a={};u=u||[null,f({}),f([]),f(f)];for(var n=2&r&&e;("object"==typeof n||"function"==typeof n)&&!~u.indexOf(n);n=f(n))Object.getOwnPropertyNames(n).forEach(r=>a[r]=()=>e[r]);return a.default=()=>e,H.d(t,a),t},H.d=(e,r)=>{for(var t in r)H.o(r,t)&&!H.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},H.f={},H.e=e=>Promise.all(Object.keys(H.f).reduce((r,t)=>(H.f[t](e,r),r),[])),H.u=e=>e+".js?_cache="+{2:"e8dfc983374527051249",43:"95a609b750f84f19dfc4",64:"10e976106e72ff766ba1",70:"42189331f58e1324fca7",74:"d2f789fe90b11fba1e97",150:"a99ca9bc52a47e729056",190:"e94cf276a1cd31bbbc4d",202:"71520d621a6b7c0f04bb",206:"e569ac060d9762698b17",211:"521089bb432d9ae7374f",220:"06dc1f2a0010c383dc1e",327:"9823d5c8efdaeaf61bf4",341:"9833176b4a90b4233a97",353:"4585cf7a0d5ac43a4d0e",535:"e2a01fd0b11de0d12956",549:"44846817cfe6ea228d0f",582:"e5187a54f2ecabc4a3b3",644:"fac49ed120538f0524ea",660:"e00eec86d5ee7174f79c",697:"4aba4d7effd97b7d96a7",722:"be0474f2c387037968b8",766:"29ca892e7a1a5f927464",767:"2192d8a8c4e273062769",812:"4ec166e238793ea3adc8",876:"96bbdbeb0467b2f321e5",980:"76b6c0390aa4e6d9cb6f"}[e],H.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),H.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),m={},g="grafana-exploretraces-app:",H.l=(e,r,t,a)=>{if(m[e])m[e].push(r);else{var n,o;if(void 0!==t)for(var i=document.getElementsByTagName("script"),s=0;s<i.length;s++){var c=i[s];if(c.getAttribute("src")==e||c.getAttribute("data-webpack")==g+t){n=c;break}}n||(o=!0,(n=document.createElement("script")).charset="utf-8",n.timeout=120,H.nc&&n.setAttribute("nonce",H.nc),n.setAttribute("data-webpack",g+t),n.src=e,0!==n.src.indexOf(window.location.origin+"/")&&(n.crossOrigin="anonymous"),n.integrity=H.sriHashes[a],n.crossOrigin="anonymous"),m[e]=[r];var d=(r,t)=>{n.onerror=n.onload=null,clearTimeout(l);var a=m[e];if(delete m[e],n.parentNode&&n.parentNode.removeChild(n),a&&a.forEach(e=>e(t)),r)return r(t)},l=setTimeout(d.bind(null,void 0,{type:"timeout",target:n}),12e4);n.onerror=d.bind(null,n.onerror),n.onload=d.bind(null,n.onload),o&&document.head.appendChild(n)}},H.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},H.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),H.p="public/plugins/grafana-exploretraces-app/",H.sriHashes={2:"sha256-ozvWwgQmuK9+TwMmQLF1z4hIQ5+EbYVmU5clv1nZdxo=",43:"sha256-WTOkwEnWF3xG0CsKUp/eXyI2lMHNl9SnHcSt/PYoNm0=",64:"sha256-bb/9IO/khyMAfqDcLi9xHcJuDzn4P7s1FW8MTZy2+Tk=",70:"sha256-lhDC2IJY5GmoLaG9t8qVkUbiRZggym4oze4GzJ/AAuE=",74:"sha256-5MICDaC04IJMrw40d3xlKW0W/Heh5j6uAV93+qNw+R0=",150:"sha256-VA1+wiiwDnhT6sPuo8UIZvo8utVrjrZpMWVEppAOl4A=",190:"sha256-I3K9kMp3ilTAUEthLZWhMvH0j9mj3Fp/la8W5q69gn0=",202:"sha256-RW5rEBXqhWNLYAUfYskrOcZM2S43HBHLR2lMHachEC8=",206:"sha256-Vc991mwVTOVAvWOIeXx8azR6TEnVzQVw8dsrSL1nX/U=",211:"sha256-5f+62qrFLjppEh55XQYIkLQHV6zy1eJ+fdRjH/B84zI=",220:"sha256-RPeNih50Pioa+tXBDGgpnGTlz/aIzXKwl8iw6QTo2EE=",327:"sha256-L9Qto+LIClKANH7m+8vnBM8R7LYcLJMGnsdyJTWQCLg=",341:"sha256-eGWpTJhzhIB4nzEDSuxpH9fPWbVaQ38+rBvzH7Tj9Rw=",353:"sha256-VR5w6VrSSCB+nW9g8UPakBpr1dyqYhQnUYCt0XLQa+g=",535:"sha256-V9c1HAaYXjRq9fuGiWjARAXA7DwnThPW0/vx8mYalvk=",549:"sha256-ki96QeIHFNYKKodLu8ME2UJAMq7u4KP+7X3ReayoKlk=",582:"sha256-NaDyVQAokvxWnpA0O7X3K/ZIJ5tv4AUprLw/6cn+Ztc=",644:"sha256-hCXVrrggiU2If9T88bYNTsD/lWhpK+tN6Rx7Q1OXLSc=",660:"sha256-aY7bE0jxhJeXiYF7CqoxnQZHdWa/x9sHTjjRM1max4Q=",697:"sha256-D+YQNw4SH6ipkv2UfBT/gmZKcZFZ2PH3XkAPMcaISx4=",722:"sha256-+xztgjugG5uVSOWEUkNqPQOFEL1EFS2qYJ6qLsWEYgA=",766:"sha256-5PnwAYiZf0GakDlfQ8jtaqkHMnpEg/Q8/3GDNk5Bpfk=",767:"sha256-viGJfugGdg+RJjb+c5DWoX4rHLcs3vXrPGeP/A6e1+A=",812:"sha256-7czl9kFTxAEedJjm+ib34lZWIufuSzS+hdNa6Dz659U=",876:"sha256-xt34WZuqq7lULGC+O+3VVVxhk504Cmv0WRzGptOTy2E=",980:"sha256-bguHNHiLG5tfVgqAz9NjOnnFkNCjIGeHAnn7nLm50DQ="},(()=>{var e={231:0};H.f.j=(r,t)=>{var a=H.o(e,r)?e[r]:void 0;if(0!==a)if(a)t.push(a[2]);else{var n=new Promise((t,n)=>a=e[r]=[t,n]);t.push(a[2]=n);var o=H.p+H.u(r),i=new Error;H.l(o,t=>{if(H.o(e,r)&&(0!==(a=e[r])&&(e[r]=void 0),a)){var n=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;i.message="Loading chunk "+r+" failed.\n("+n+": "+o+")",i.name="ChunkLoadError",i.type=n,i.request=o,a[1](i)}},"chunk-"+r,r)}};var r=(r,t)=>{var a,n,[o,i,s]=t,c=0;if(o.some(r=>0!==e[r])){for(a in i)H.o(i,a)&&(H.m[a]=i[a]);if(s)s(H)}for(r&&r(t);c<o.length;c++)n=o[c],H.o(e,n)&&e[n]&&e[n][0](),e[n]=0},t=self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})(),H.nc=void 0;var S={};H.r(S),H.d(S,{plugin:()=>T});var h=H(1308),y=H.n(h);H.p=y()&&y().uri?y().uri.slice(0,y().uri.lastIndexOf("/")+1):"public/plugins/grafana-exploretraces-app/";var x=H(5959),N=H.n(x),A=H(7781),C=H(2007);const I=(0,x.lazy)(()=>H.e(812).then(H.bind(H,5812))),R=(0,x.lazy)(()=>Promise.all([H.e(43),H.e(211),H.e(722)]).then(H.bind(H,8722)));var w=H(1829);const E=[{targets:A.PluginExtensionPoints.DashboardPanelMenu,title:"Open in Traces Drilldown",description:"Open current query in the Traces Drilldown app",path:U(),configure:e=>P(e)},{targets:A.PluginExtensionPoints.ExploreToolbarAction,title:"Open in Grafana Traces Drilldown",description:"Try our new queryless experience for traces",path:U(),configure:e=>P(e)}];function P(e){var r,t,a;if(!e)return;const n=e.targets.find(e=>{var r;return"tempo"===(null===(r=e.datasource)||void 0===r?void 0:r.type)});if(!n||!(null===(r=n.datasource)||void 0===r?void 0:r.uid))return;const o=null===(t=n.filters)||void 0===t?void 0:t.filter(e=>e.scope&&e.tag&&e.operator&&e.value&&e.value.length);if(!o||0===o.length)return;const i=new URLSearchParams;i.append(`var-${w.EY}`,(null===(a=n.datasource)||void 0===a?void 0:a.uid)||"");const s=(0,A.toURLRange)(e.timeRange);i.append("from",String(s.from)),i.append("to",String(s.to));const c=o.find(e=>"status"===e.tag);c&&i.append(`var-${w.PU}`,"error"===c.value?"errors":"rate"),i.append("var-primarySignal","true");(e=>e.filter(e=>"status"!==e.tag).map(e=>`${e.scope}${function(e){return function(e){return K.some(r=>r.tag===e.tag&&r.scope===e.scope)}(e)?":":"."}(e)}${e.tag}|${e.operator}|${e.value}`))(o).forEach(e=>i.append(`var-${w.Ao}`,e));return{path:`${U(i)}`}}function U(e){return`${w.D5}${e?`?${e.toString()}`:""}`}const K=["event:name","event:timeSinceStart","instrumentation:name","instrumentation:version","link:spanID","link:traceID","span:duration","span:id","span:kind","span:name","span:status","span:statusMessage","trace:duration","trace:id","trace:rootName","trace:rootService"].map(e=>{const[r,t]=e.split(":");return{scope:r,tag:t}});const k=(0,x.lazy)(()=>H.e(535).then(H.bind(H,3535))),O=(0,x.lazy)(()=>H.e(202).then(H.bind(H,202))),T=(new A.AppPlugin).setRootPage(k).addConfigPage({title:"Configuration",icon:"cog",body:O,id:"configuration"}).exposeComponent({id:"grafana-exploretraces-app/open-in-explore-traces-button/v1",title:"Open in Traces Drilldown button",description:"A button that opens a traces view in the Traces drilldown app.",component:function(e){return N().createElement(x.Suspense,{fallback:N().createElement(C.LinkButton,{variant:"secondary",disabled:!0},"Open in Traces Drilldown")},N().createElement(I,e))}}).exposeComponent({id:"grafana-exploretraces-app/embedded-trace-exploration/v1",title:"Embedded Trace Exploration",description:"A component that renders a trace exploration view that can be embedded in other parts of Grafana.",component:function(e){return N().createElement(x.Suspense,{fallback:N().createElement("div",null,"Loading...")},N().createElement(R,e))}});for(const e of E)T.addLink(e);return S})());
//# sourceMappingURL=module.js.map