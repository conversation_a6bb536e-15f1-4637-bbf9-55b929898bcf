package org.myorg.autogen.readers;

import org.apache.flink.table.api.TableEnvironment;
import org.myorg.autogen.configs.ReaderConfig;
import org.myorg.autogen.configs.SchemaConfig;

/**
 * Kafka reader implementation for creating Kafka source tables
 */
public class KafkaReader implements Reader {

    @Override
    public void createSourceTable(TableEnvironment tableEnv, ReaderConfig config, String tableName) {
        StringBuilder ddl = new StringBuilder();

        ddl.append("CREATE TABLE ").append(tableName).append(" (\n");

        // Use topic-specific schema or default schema
        String topic = config.getTopic() != null ? config.getTopic() : config.getTable();
        generateSchemaForTopic(ddl, topic);

        ddl.append(") WITH (\n");
        ddl.append("  'connector' = 'kafka',\n");
        
        // Topic configuration
        ddl.append("  'topic' = '").append(topic).append("',\n");
        
        // Kafka properties
        ddl.append("  'properties.bootstrap.servers' = 'redpanda:9092',\n");
        ddl.append("  'properties.group.id' = 'flink-consumer-group',\n");
        ddl.append("  'scan.startup.mode' = 'latest-offset',\n");
        
        // Format configuration
        if (config.getFormat() != null && "avro".equalsIgnoreCase(config.getFormat().getType())) {
            ddl.append("  'format' = 'avro-confluent',\n");
            ddl.append("  'avro-confluent.url' = 'http://localhost:8081'\n");
        } else {
            // Default to JSON format
            ddl.append("  'format' = 'json',\n");
            ddl.append("  'json.fail-on-missing-field' = 'false',\n");
            ddl.append("  'json.ignore-parse-errors' = 'true'\n");
        }
        
        ddl.append(")");
        
        System.out.println("Creating Kafka source table with DDL:");
        System.out.println(ddl.toString());
        
        tableEnv.executeSql(ddl.toString());
    }

    @Override
    public String getReaderType() {
        return "KafkaReader";
    }

    private void generateSchemaFromConfig(StringBuilder ddl, SchemaConfig schema) {
        boolean first = true;

        // Add regular columns
        for (SchemaConfig.ColumnDefinition column : schema.getColumns()) {
            if (!first) {
                ddl.append(",\n");
            }
            ddl.append("  `").append(column.getName()).append("` ").append(column.getType());

            // Add metadata if specified
            if (column.getMetadata() != null && !column.getMetadata().isEmpty()) {
                ddl.append(" METADATA FROM '").append(column.getMetadata()).append("'");
            }

            first = false;
        }

        // Add primary key if specified
        if (schema.getPrimaryKey() != null && !schema.getPrimaryKey().isEmpty()) {
            ddl.append(",\n  PRIMARY KEY (");
            boolean firstKey = true;
            for (String keyColumn : schema.getPrimaryKey()) {
                if (!firstKey) {
                    ddl.append(", ");
                }
                ddl.append("`").append(keyColumn).append("`");
                firstKey = false;
            }
            ddl.append(") NOT ENFORCED");
        }

        // Add watermark if specified
        if (schema.getWatermark() != null) {
            SchemaConfig.WatermarkConfig watermark = schema.getWatermark();
            ddl.append(",\n  WATERMARK FOR `").append(watermark.getColumn()).append("` AS ");
            ddl.append("`").append(watermark.getColumn()).append("` - INTERVAL '");
            ddl.append(watermark.getDelay() != null ? watermark.getDelay() : "5").append("' SECOND");
        }

        ddl.append("\n");
    }

    private void generateSchemaForTopic(StringBuilder ddl, String topic) {
        System.out.println("Generating schema for topic: " + topic);

        if (topic != null && topic.contains("station")) {
            // Station schema - only id and status (matches actual data structure)
            System.out.println("Using station schema");
            ddl.append("  `id` STRING,\n");
            ddl.append("  `status` STRING,\n");
            ddl.append("  `order_time` TIMESTAMP(3) METADATA FROM 'timestamp'\n");
        } else {
            // Order schema - full order structure
            System.out.println("Using order schema");
            ddl.append("  `id` STRING,\n");
            ddl.append("  `station_id` STRING,\n");
            ddl.append("  `customer_id` STRING,\n");
            ddl.append("  `amount` DOUBLE,\n");
            ddl.append("  `currency` STRING,\n");
            ddl.append("  `status` STRING,\n");
            ddl.append("  `order_time` TIMESTAMP(3) METADATA FROM 'timestamp'\n");
        }
    }
}
