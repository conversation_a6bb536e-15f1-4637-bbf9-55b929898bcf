"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[919],{6919:(e,a,t)=>{t.r(a),t.d(a,{default:()=>i});var n=t(5959),o=t.n(n),r=t(1159),s=t(8531),l=t(6865),p=t(2152),c=t(6991);const u=()=>new l.Oh({pages:[(0,c.c)(),(0,c.uL)(),(0,c.Oo)()],urlSyncOptions:{createBrowserHistorySteps:!0,updateUrlOnInit:!0}});const i=function(){const[e,a]=o().useState(!1);(0,p.rX)();const t=(0,l.TG)(u);(0,n.useEffect)(()=>{e||a(!0)},[t,e]);const c=s.config.bootData.user.permissions;return(null==c?void 0:c["grafana-lokiexplore-app:read"])||(null==c?void 0:c["datasources:explore"])?e?o().createElement(t.Component,{model:t}):null:o().createElement(r.Navigate,{to:"/",replace:!0})}}}]);
//# sourceMappingURL=919.js.map?_cache=728718e594379dd03c81