package org.myorg.autogen.configs;

import java.util.Map;

/**
 * Configuration for data readers (sources)
 */
public class ReaderConfig {
    private String type;
    private String endpoint;
    private String table;
    private String strategy;
    private String deltaColumn;
    private String topic;
    private FormatConfig format;
    private SchemaConfig schema;
    private Map<String, Object> properties;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getStrategy() {
        return strategy;
    }

    public void setStrategy(String strategy) {
        this.strategy = strategy;
    }

    public String getDeltaColumn() {
        return deltaColumn;
    }

    public void setDeltaColumn(String deltaColumn) {
        this.deltaColumn = deltaColumn;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public FormatConfig getFormat() {
        return format;
    }

    public void setFormat(FormatConfig format) {
        this.format = format;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    public SchemaConfig getSchema() {
        return schema;
    }

    public void setSchema(SchemaConfig schema) {
        this.schema = schema;
    }
}
