package org.myorg.autogen.writers;

import org.apache.flink.table.api.TableEnvironment;
import org.myorg.autogen.configs.WriterConfig;
import org.myorg.autogen.configs.SchemaConfig;

/**
 * Base interface for all data writers
 */
public interface Writer {
    
    /**
     * Create a sink table in the Flink Table Environment
     *
     * @param tableEnv Flink Table Environment
     * @param config Writer configuration
     * @param outputSchema Schema definition for the sink table
     * @param tableName Name for the sink table
     */
    void createSinkTable(TableEnvironment tableEnv, WriterConfig config, SchemaConfig outputSchema, String tableName);
    
    /**
     * Get the writer type that this implementation handles
     * 
     * @return Writer type string
     */
    String getWriterType();
}
