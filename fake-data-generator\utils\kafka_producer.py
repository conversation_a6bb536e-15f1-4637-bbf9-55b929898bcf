"""
Kafka producer utilities
"""

import os
from kafka import KafkaProducer
from typing import Optional
import logging
import json

logger = logging.getLogger(__name__)


class KafkaProducerManager:
    """
    Kafka producer manager for publishing messages
    """
    
    def __init__(self):
        """Initialize Kafka producer with environment variables"""
        self.kafka_host = os.getenv("KAFKA_HOST", "localhost")
        self.kafka_port = os.getenv("KAFKA_PORT", "19092")
        self.bootstrap_servers = f"{self.kafka_host}:{self.kafka_port}"
        self.producer = None
        
    def create_producer(self) -> KafkaProducer:
        """
        Create and configure Kafka producer
        
        Returns:
            KafkaProducer instance
        """
        try:
            self.producer = KafkaProducer(
                bootstrap_servers=[self.bootstrap_servers],
                key_serializer=lambda x: x.encode('utf-8') if x else None,
                value_serializer=lambda x: x.encode('utf-8') if isinstance(x, str) else json.dumps(x).encode('utf-8'),
                acks='all',
                retries=3,
                retry_backoff_ms=100
            )
            logger.info(f"Kafka producer created for {self.bootstrap_servers}")
            return self.producer
        except Exception as e:
            logger.error(f"Failed to create Kafka producer: {e}")
            raise
    
    def get_producer(self) -> KafkaProducer:
        """
        Get existing producer or create new one
        
        Returns:
            KafkaProducer instance
        """
        if self.producer is None:
            return self.create_producer()
        return self.producer
    
    def send_message(self, topic: str, message: str, key: Optional[str] = None) -> bool:
        """
        Send message to Kafka topic
        
        Args:
            topic: Kafka topic name
            message: Message content (JSON string)
            key: Optional message key
            
        Returns:
            True if message sent successfully, False otherwise
        """
        try:
            producer = self.get_producer()
            future = producer.send(topic, value=message, key=key)
            
            # Wait for the message to be sent
            record_metadata = future.get(timeout=10)
            logger.debug(f"Message sent to {topic}: partition={record_metadata.partition}, offset={record_metadata.offset}")
            return True
        except Exception as e:
            logger.error(f"Failed to send message to {topic}: {e}")
            return False
    
    def send_station_status(self, station_status_json: str, topic: Optional[str] = None) -> bool:
        """
        Send station status message
        
        Args:
            station_status_json: Station status as JSON string
            topic: Optional topic override
            
        Returns:
            True if message sent successfully, False otherwise
        """
        topic = topic or os.getenv("KAFKA_TOPIC", "cf.iot.station.status")
        return self.send_message(topic, station_status_json)
    
    def send_order(self, order_json: str, topic: Optional[str] = None) -> bool:
        """
        Send order message
        
        Args:
            order_json: Order as JSON string
            topic: Optional topic override
            
        Returns:
            True if message sent successfully, False otherwise
        """
        topic = topic or os.getenv("ORDER_KAFKA_TOPIC", "cf.iot.order.events")
        return self.send_message(topic, order_json)
    
    def flush(self):
        """Flush producer to ensure all messages are sent"""
        if self.producer:
            self.producer.flush()
            logger.info("Kafka producer flushed")
    
    def close(self):
        """Close Kafka producer"""
        if self.producer:
            self.producer.close()
            logger.info("Kafka producer closed")
    
    def __enter__(self):
        """Context manager entry"""
        self.create_producer()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.flush()
        self.close()
