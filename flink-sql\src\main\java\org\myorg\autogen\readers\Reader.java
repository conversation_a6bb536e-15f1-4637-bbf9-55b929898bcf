package org.myorg.autogen.readers;

import org.apache.flink.table.api.TableEnvironment;
import org.myorg.autogen.configs.ReaderConfig;

/**
 * Base interface for all data readers
 */
public interface Reader {
    
    /**
     * Create a source table in the Flink Table Environment
     *
     * @param tableEnv Flink Table Environment
     * @param config Reader configuration
     * @param sourceSchema Schema definition for the source table
     * @param tableName Name for the source table
     */
    void createSourceTable(TableEnvironment tableEnv, ReaderConfig config, String tableName);
    
    /**
     * Get the reader type that this implementation handles
     * 
     * @return Reader type string
     */
    String getReaderType();
}
