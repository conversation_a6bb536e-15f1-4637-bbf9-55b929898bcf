"""
Order data model
"""

import uuid
from dataclasses import dataclass
from typing import Optional
import json


@dataclass
class Order:
    """
    Data model for order events
    """
    id: str
    station_id: str
    customer_id: str
    amount: float
    currency: str
    status: str

    def __init__(self,
                 id: Optional[str] = None,
                 station_id: Optional[str] = None,
                 customer_id: Optional[str] = None,
                 amount: float = 0.0,
                 currency: str = "USD",
                 status: str = "pending"):
        """
        Initialize Order

        Args:
            id: Order UUID string. If None, generates a random UUID
            station_id: Station UUID string
            customer_id: Customer UUID string. If None, generates a random UUID
            amount: Order amount
            currency: Currency code (default: USD)
            status: Order status (pending, completed, failed, cancelled)
        """
        self.id = id if id is not None else str(uuid.uuid4())
        self.station_id = station_id if station_id is not None else str(
            uuid.uuid4())
        self.customer_id = customer_id if customer_id is not None else str(
            uuid.uuid4())
        self.amount = amount
        self.currency = currency
        self.status = status

    def to_json(self) -> str:
        """Convert to JSON string for Kafka publishing"""
        return json.dumps({
            "id": self.id,
            "station_id": self.station_id,
            "customer_id": self.customer_id,
            "amount": self.amount,
            "currency": self.currency,
            "status": self.status
        })

    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "station_id": self.station_id,
            "customer_id": self.customer_id,
            "amount": self.amount,
            "currency": self.currency,
            "status": self.status
        }

    @classmethod
    def from_dict(cls, data: dict) -> 'Order':
        """Create Order from dictionary"""
        return cls(
            id=data.get("id"),
            station_id=data.get("station_id"),
            customer_id=data.get("customer_id"),
            amount=data.get("amount", 0.0),
            currency=data.get("currency", "USD"),
            status=data.get("status", "pending")
        )
