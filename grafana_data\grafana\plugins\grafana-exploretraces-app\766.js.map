{"version": 3, "file": "766.js?_cache=29ca892e7a1a5f927464", "mappings": "4PAQA,MAOA,EAP6B,KAC3B,MAAMA,EAAYC,aAAaC,QAAQC,EAAAA,KAAsB,IACtDC,IAAeC,EAAAA,EAAAA,WAASC,EAAAA,EAAAA,IAAqBN,IAEpD,OAAO,kBAACO,EAAAA,CAAqBH,YAAaA,KAKrC,SAASG,GAAqB,YAAEH,IACrC,MAAOI,EAAeC,GAAoBC,IAAAA,UAAe,GAUzD,OARAC,EAAAA,EAAAA,WAAU,KACHH,IACHC,GAAiB,IAEjBG,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,OAAQC,EAAAA,GAAoBD,OAAOE,mBAE3E,CAACZ,EAAaI,IAEZA,EAKH,kBAACS,EAAAA,GAAsBA,CAACC,MAAOd,EAAae,iBAAiB,EAAMC,2BAA2B,GAC5F,kBAAChB,EAAYiB,UAAS,CAACC,MAAOlB,KALzB,IAQX,C", "sources": ["webpack://grafana-exploretraces-app/./pages/Explore/TraceExplorationPage.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\n\nimport { newTracesExploration } from '../../utils/utils';\nimport { TraceExploration } from './TraceExploration';\nimport { DATASOURCE_LS_KEY } from '../../utils/shared';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../utils/analytics';\nimport { UrlSyncContextProvider } from '@grafana/scenes';\n\nconst TraceExplorationPage = () => {\n  const initialDs = localStorage.getItem(DATASOURCE_LS_KEY) || '';\n  const [exploration] = useState(newTracesExploration(initialDs));\n\n  return <TraceExplorationView exploration={exploration} />;\n};\n\nexport default TraceExplorationPage;\n\nexport function TraceExplorationView({ exploration }: { exploration: TraceExploration }) {\n  const [isInitialized, setIsInitialized] = React.useState(false);\n\n  useEffect(() => {\n    if (!isInitialized) {\n      setIsInitialized(true);\n\n      reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.app_initialized);\n    }\n  }, [exploration, isInitialized]);\n\n  if (!isInitialized) {\n    return null;\n  }\n\n  return (\n    <UrlSyncContextProvider scene={exploration} updateUrlOnInit={true} createBrowserHistorySteps={true}>\n      <exploration.Component model={exploration} />\n    </UrlSyncContextProvider>\n  );\n}\n"], "names": ["initialDs", "localStorage", "getItem", "DATASOURCE_LS_KEY", "exploration", "useState", "newTracesExploration", "TraceExplorationView", "isInitialized", "setIsInitialized", "React", "useEffect", "reportAppInteraction", "USER_EVENTS_PAGES", "common", "USER_EVENTS_ACTIONS", "app_initialized", "UrlSyncContextProvider", "scene", "updateUrlOnInit", "createBrowserHistorySteps", "Component", "model"], "sourceRoot": ""}