{"version": 3, "file": "module.js", "mappings": ";2LAAIA,EACAC,E,YCDJC,EAAOC,QAAUC,C,WCAjBF,EAAOC,QAAUE,C,WCAjBH,EAAOC,QAAUG,C,WCAjBJ,EAAOC,QAAUI,C,WCAjBL,EAAOC,QAAUK,C,WCAjBN,EAAOC,QAAUM,C,WCAjBP,EAAOC,QAAUO,C,WCAjBR,EAAOC,QAAUQ,C,WCAjBT,EAAOC,QAAUS,C,WCAjBV,EAAOC,QAAUU,C,WCAjBX,EAAOC,QAAUW,C,GCCbC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAaf,QAGrB,IAAID,EAASa,EAAyBE,GAAY,CACjDG,GAAIH,EACJI,QAAQ,EACRlB,QAAS,CAAC,GAUX,OANAmB,EAAoBL,GAAUM,KAAKrB,EAAOC,QAASD,EAAQA,EAAOC,QAASa,GAG3Ed,EAAOmB,QAAS,EAGTnB,EAAOC,OACf,CAGAa,EAAoBQ,EAAIF,EC5BxBN,EAAoBS,KAAO,CAAC,ECC5BT,EAAoBU,EAAKxB,IACxB,IAAIyB,EAASzB,GAAUA,EAAO0B,WAC7B,IAAO1B,EAAiB,QACxB,IAAM,EAEP,OADAc,EAAoBa,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CAAM,ECLdX,EAAoBa,EAAI,CAAC1B,EAAS4B,KACjC,IAAI,IAAIC,KAAOD,EACXf,EAAoBiB,EAAEF,EAAYC,KAAShB,EAAoBiB,EAAE9B,EAAS6B,IAC5EE,OAAOC,eAAehC,EAAS6B,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDhB,EAAoBsB,EAAI,CAAC,EAGzBtB,EAAoBuB,EAAKC,GACjBC,QAAQC,IAAIR,OAAOS,KAAK3B,EAAoBsB,GAAGM,QAAO,CAACC,EAAUb,KACvEhB,EAAoBsB,EAAEN,GAAKQ,EAASK,GAC7BA,IACL,KCNJ7B,EAAoB8B,EAAKN,GAEZA,EAAU,cAAgB,CAAC,IAAM,uBAAuB,IAAM,wBAAwBA,GCHnGxB,EAAoB+B,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAOC,MAAQ,IAAIC,SAAS,cAAb,EAChB,CAAE,MAAOX,GACR,GAAsB,iBAAXY,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBnC,EAAoBiB,EAAI,CAACmB,EAAKC,IAAUnB,OAAOoB,UAAUC,eAAehC,KAAK6B,EAAKC,GnBA9ErD,EAAa,CAAC,EACdC,EAAoB,yBAExBe,EAAoBwC,EAAI,CAACC,EAAKC,EAAM1B,EAAKQ,KACxC,GAAGxC,EAAWyD,GAAQzD,EAAWyD,GAAKE,KAAKD,OAA3C,CACA,IAAIE,EAAQC,EACZ,QAAW1C,IAARa,EAEF,IADA,IAAI8B,EAAUC,SAASC,qBAAqB,UACpCC,EAAI,EAAGA,EAAIH,EAAQI,OAAQD,IAAK,CACvC,IAAIE,EAAIL,EAAQG,GAChB,GAAGE,EAAEC,aAAa,QAAUX,GAAOU,EAAEC,aAAa,iBAAmBnE,EAAoB+B,EAAK,CAAE4B,EAASO,EAAG,KAAO,CACpH,CAEGP,IACHC,GAAa,GACbD,EAASG,SAASM,cAAc,WAEzBC,QAAU,QACjBV,EAAOW,QAAU,IACbvD,EAAoBwD,IACvBZ,EAAOa,aAAa,QAASzD,EAAoBwD,IAElDZ,EAAOa,aAAa,eAAgBxE,EAAoB+B,GAExD4B,EAAOc,IAAMjB,EAC4C,IAArDG,EAAOc,IAAIC,QAAQxB,OAAOyB,SAASC,OAAS,OAC/CjB,EAAOkB,YAAc,aAEtBlB,EAAOmB,UAAY/D,EAAoBgE,UAAUxC,GACjDoB,EAAOkB,YAAc,aAEtB9E,EAAWyD,GAAO,CAACC,GACnB,IAAIuB,EAAmB,CAACC,EAAMC,KAE7BvB,EAAOwB,QAAUxB,EAAOyB,OAAS,KACjCC,aAAaf,GACb,IAAIgB,EAAUvF,EAAWyD,GAIzB,UAHOzD,EAAWyD,GAClBG,EAAO4B,YAAc5B,EAAO4B,WAAWC,YAAY7B,GACnD2B,GAAWA,EAAQG,SAASC,GAAQA,EAAGR,KACpCD,EAAM,OAAOA,EAAKC,EAAM,EAExBZ,EAAUqB,WAAWX,EAAiBY,KAAK,UAAM1E,EAAW,CAAE2E,KAAM,UAAWC,OAAQnC,IAAW,MACtGA,EAAOwB,QAAUH,EAAiBY,KAAK,KAAMjC,EAAOwB,SACpDxB,EAAOyB,OAASJ,EAAiBY,KAAK,KAAMjC,EAAOyB,QACnDxB,GAAcE,SAASiC,KAAKC,YAAYrC,EAzCkB,CAyCX,EoB5ChD5C,EAAoBkF,EAAK/F,IACH,oBAAXgG,QAA0BA,OAAOC,aAC1ClE,OAAOC,eAAehC,EAASgG,OAAOC,YAAa,CAAEC,MAAO,WAE7DnE,OAAOC,eAAehC,EAAS,aAAc,CAAEkG,OAAO,GAAO,ECL9DrF,EAAoBsF,IAAOpG,IAC1BA,EAAOqG,MAAQ,GACVrG,EAAOsG,WAAUtG,EAAOsG,SAAW,IACjCtG,GCHRc,EAAoByF,EAAI,wCCCxBzF,EAAoBgE,UAAY,CAAC,IAAM,sDAAsD,IAAM,uD,MCInG,IAAI0B,EAAkB,CACrB,IAAK,GAGN1F,EAAoBsB,EAAEqE,EAAI,CAACnE,EAASK,KAElC,IAAI+D,EAAqB5F,EAAoBiB,EAAEyE,EAAiBlE,GAAWkE,EAAgBlE,QAAWrB,EACtG,GAA0B,IAAvByF,EAGF,GAAGA,EACF/D,EAASc,KAAKiD,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIpE,SAAQ,CAACqE,EAASC,IAAYH,EAAqBF,EAAgBlE,GAAW,CAACsE,EAASC,KAC1GlE,EAASc,KAAKiD,EAAmB,GAAKC,GAGtC,IAAIpD,EAAMzC,EAAoByF,EAAIzF,EAAoB8B,EAAEN,GAEpDwE,EAAQ,IAAIC,MAgBhBjG,EAAoBwC,EAAEC,GAfF0B,IACnB,GAAGnE,EAAoBiB,EAAEyE,EAAiBlE,KAEf,KAD1BoE,EAAqBF,EAAgBlE,MACRkE,EAAgBlE,QAAWrB,GACrDyF,GAAoB,CACtB,IAAIM,EAAY/B,IAAyB,SAAfA,EAAMW,KAAkB,UAAYX,EAAMW,MAChEqB,EAAUhC,GAASA,EAAMY,QAAUZ,EAAMY,OAAOrB,IACpDsC,EAAMI,QAAU,iBAAmB5E,EAAU,cAAgB0E,EAAY,KAAOC,EAAU,IAC1FH,EAAMK,KAAO,iBACbL,EAAMlB,KAAOoB,EACbF,EAAMM,QAAUH,EAChBP,EAAmB,GAAGI,EACvB,CACD,GAEwC,SAAWxE,EAASA,EAE/D,CACD,EAcF,IAAI+E,EAAuB,CAACC,EAA4BC,KACvD,IAGIxG,EAAUuB,GAHTkF,EAAUC,EAAaC,GAAWH,EAGhBxD,EAAI,EAC3B,GAAGyD,EAASG,MAAMzG,GAAgC,IAAxBsF,EAAgBtF,KAAa,CACtD,IAAIH,KAAY0G,EACZ3G,EAAoBiB,EAAE0F,EAAa1G,KACrCD,EAAoBQ,EAAEP,GAAY0G,EAAY1G,IAGhD,GAAG2G,EAAsBA,EAAQ5G,EAClC,CAEA,IADGwG,GAA4BA,EAA2BC,GACrDxD,EAAIyD,EAASxD,OAAQD,IACzBzB,EAAUkF,EAASzD,GAChBjD,EAAoBiB,EAAEyE,EAAiBlE,IAAYkE,EAAgBlE,IACrEkE,EAAgBlE,GAAS,KAE1BkE,EAAgBlE,GAAW,CAC5B,EAIGsF,EAAqBC,KAAwC,kCAAIA,KAAwC,mCAAK,GAClHD,EAAmBpC,QAAQ6B,EAAqB1B,KAAK,KAAM,IAC3DiC,EAAmBnE,KAAO4D,EAAqB1B,KAAK,KAAMiC,EAAmBnE,KAAKkC,KAAKiC,G,kEClFvF,IACE,KAAiB,QACb,QAAkBE,MAAM,EAAG,QAAkBC,YAAY,KAAO,GAChE,wC,iCCJN,MAAMC,EAAUC,IAAAA,MAAW,IAAM,sDAAgBC,MAAMlI,IAAY,CAAEmI,QAASnI,EAAOoI,UCarF,SAASC,EAASC,G,IAOEA,EAAAA,EAUwBC,EAhB1C,MAAM,UAAEC,EAAS,eAAED,GAAmBD,EAEtC,IAAIG,EAAiB,GACjBC,EAAoB,GACpBC,EAAkB,MAElBC,EAAgD,QAAlCN,EAAAA,EAAMC,eAAeM,qBAArBP,IAAAA,GAA0C,QAA1CA,EAAAA,EAAoCQ,MAAM,iCAA1CR,IAAAA,OAAAA,EAAAA,EAAsE,GAEpFM,IACFD,EAAkB,UAGhBL,EAAMK,kBACRA,EAAkBL,EAAMK,iBAG1B,MAAMI,EAAkB,kBAA2C,QAAzBR,EAAAA,EAAeS,kBAAfT,IAAAA,OAAAA,EAAAA,EAA2BU,MAC/DC,EAAmBN,EAAc,oBAAoBA,IAAgB,GACrEO,EAAmB,wBAAwBZ,EAAea,gBAC1DC,EAAuB,oBAAoBV,IAC7CH,IACFC,EAAiB,SAASD,EAAUc,WAAWd,EAAUe,MAEvDhB,EAAeiB,eACjBd,EAAoB,qBAAqBH,EAAeiB,gBAO1D,MAAO,oCAHQ,IAAIC,gBACjB,GAAGV,IAAkBG,IAAmBC,IAAmBV,IAAiBY,IAAuBX,KACnGgB,YAEJ,CAEO,MAAMC,EAAwF,CACnGC,QAAS,CAACC,EAAAA,sBAAsBC,sBAChCC,MAAO,qCACPC,KAAM,OACNC,YAAa,gDACbC,KAAM,mCACNC,SAAAA,CAAUC,GACR,IAAKA,IAAYA,EAAQR,UAAYQ,EAAQ5B,WAAa4B,EAAQR,QAAQ5F,OAAS,EACjF,OAGF,MAAMqG,EAAaD,EAAQR,QAAQ,GAEnC,OAAIS,EAAWrB,YAA6C,iCAA/BqB,EAAWrB,WAAWpD,KAC1C,CACLsE,KAAM7B,EAAS,CACbE,eAAgB8B,EAChB7B,UAAW4B,EAAQ5B,kBAJzB,CASF,GAGW8B,EAAgE,CAC3EV,QAAS,CAAC,6BACVG,MAAO,qCACPE,YAAa,gDACbC,KAAM,mCACNK,QAAS,CAACC,GAAKJ,cACb,KAAKA,GAAYA,EAAQxB,aAAgBwB,EAAQZ,cAAiBY,EAAQhB,eAAkBgB,EAAQ5B,WAClG,OAGF,MAAMI,EAAcwB,EAAQxB,YACtBY,EAAeY,EAAQZ,aACvBJ,EAAgBgB,EAAQhB,cACxBZ,EAAY4B,EAAQ5B,UAEpBD,EAA4C,CAChDkC,MAAO,2CACP5B,cAAe,iBAAiBD,KAChCQ,gBACAI,eACAR,WAAYoB,EAAQpB,WACpB0B,QAAS,CAAC,iBAGZ,GAAInC,EAAeS,WAAY,CAC7B,MAAMkB,EAAO7B,EAAS,CACpBE,eAAgBA,EAChBC,YACAG,gBAAiB,gBAEnB1F,OAAO0H,KAAKT,EAAM,SAAU,sBAC9B,CACgB,GCrGPU,GAAS,IAAIC,EAAAA,WACvBC,QAAuCnB,GACvCmB,QAAuCR,GACvCS,aFLI,WACL,OACE,kBAACC,EAAAA,SAAQA,KACP,kBAAChD,EAAAA,MAGP,I", "sources": ["webpack://grafana-pyroscope-app/webpack/runtime/load script", "webpack://grafana-pyroscope-app/external amd \"@emotion/css\"", "webpack://grafana-pyroscope-app/external amd \"@grafana/data\"", "webpack://grafana-pyroscope-app/external amd \"@grafana/runtime\"", "webpack://grafana-pyroscope-app/external amd \"@grafana/ui\"", "webpack://grafana-pyroscope-app/external amd \"d3\"", "webpack://grafana-pyroscope-app/external amd \"lodash\"", "webpack://grafana-pyroscope-app/external amd \"module\"", "webpack://grafana-pyroscope-app/external amd \"react\"", "webpack://grafana-pyroscope-app/external amd \"react-dom\"", "webpack://grafana-pyroscope-app/external amd \"react-router\"", "webpack://grafana-pyroscope-app/external amd \"rxjs\"", "webpack://grafana-pyroscope-app/webpack/bootstrap", "webpack://grafana-pyroscope-app/webpack/runtime/amd options", "webpack://grafana-pyroscope-app/webpack/runtime/compat get default export", "webpack://grafana-pyroscope-app/webpack/runtime/define property getters", "webpack://grafana-pyroscope-app/webpack/runtime/ensure chunk", "webpack://grafana-pyroscope-app/webpack/runtime/get javascript chunk filename", "webpack://grafana-pyroscope-app/webpack/runtime/global", "webpack://grafana-pyroscope-app/webpack/runtime/hasOwnProperty shorthand", "webpack://grafana-pyroscope-app/webpack/runtime/make namespace object", "webpack://grafana-pyroscope-app/webpack/runtime/node module decorator", "webpack://grafana-pyroscope-app/webpack/runtime/publicPath", "webpack://grafana-pyroscope-app/webpack/runtime/compat", "webpack://grafana-pyroscope-app/webpack/runtime/jsonp chunk loading", "webpack://grafana-pyroscope-app/./node_modules/grafana-public-path.js", "webpack://grafana-pyroscope-app/./app/Root.tsx", "webpack://grafana-pyroscope-app/./links.ts", "webpack://grafana-pyroscope-app/./module.ts"], "sourcesContent": ["var inProgress = {};\nvar dataWebpackPrefix = \"grafana-pyroscope-app:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t\tif (script.src.indexOf(window.location.origin + '/') !== 0) {\n\t\t\tscript.crossOrigin = \"anonymous\";\n\t\t}\n\t\tscript.integrity = __webpack_require__.sriHashes[chunkId];\n\t\tscript.crossOrigin = \"anonymous\";\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "module.exports = __WEBPACK_EXTERNAL_MODULE__6089__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7781__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8531__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__2007__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__4201__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3241__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1308__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__5959__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8398__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1159__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1269__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "__webpack_require__.amdO = {};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".js?_cache=\" + {\"133\":\"299f6178a056a1505796\",\"516\":\"9710b856bdea6ea0d64f\"}[chunkId] + \"\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.p = \"public/plugins/grafana-pyroscope-app/\";", "\n__webpack_require__.sriHashes = {\"133\":\"*-*-*-CHUNK-SRI-HASH-QVqAmNUiwkq4jRstLrS7zrn7DOVh4=\",\"516\":\"*-*-*-CHUNK-SRI-HASH-K7JNBVC5C02UJ8f7JmYwDE249DhLA=\"};", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkgrafana_pyroscope_app\"] = self[\"webpackChunkgrafana_pyroscope_app\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "\nimport amdMetaModule from 'amd-module';\n\n__webpack_public_path__ =\n  amdMetaModule && amdMetaModule.uri\n    ? amdMetaModule.uri.slice(0, amdMetaModule.uri.lastIndexOf('/') + 1)\n    : 'public/plugins/grafana-pyroscope-app/';\n", "import React, { Suspense } from 'react';\n\nconst LazyApp = React.lazy(() => import('./App').then((module) => ({ default: module.App })));\n\nexport function Root() {\n  return (\n    <Suspense>\n      <LazyApp />\n    </Suspense>\n  );\n}\n", "import { PluginExtensionAddedLinkConfig, PluginExtensionPoints, RawTimeRange } from '@grafana/data';\nimport { DataQuery } from '@grafana/schema';\nimport { GrafanaPyroscopeDataQuery } from '@grafana/schema/dist/esm/raw/composable/grafanapyroscope/dataquery/x/GrafanaPyroscopeDataQuery_types.gen';\n\nexport type PluginExtensionExploreContext = {\n  targets: DataQuery[];\n  timeRange: RawTimeRange;\n};\n\ntype URLParamsBuilderProps = {\n  pyroscopeQuery: GrafanaPyroscopeDataQuery;\n  timeRange?: RawTimeRange;\n  explorationType?: string;\n};\n\nfunction buildURL(props: URLParamsBuilderProps) {\n  const { timeRange, pyroscopeQuery } = props;\n\n  let timeRangeParam = '';\n  let spanSelectorParam = '';\n  let explorationType = 'all';\n\n  let serviceName = props.pyroscopeQuery.labelSelector?.match(/service_name=\"([^\"]+)\"/)?.[1];\n\n  if (serviceName) {\n    explorationType = 'labels';\n  }\n\n  if (props.explorationType) {\n    explorationType = props.explorationType;\n  }\n\n  const datasourceParam = `var-dataSource=${pyroscopeQuery.datasource?.uid}`;\n  const serviceNameParam = serviceName ? `&var-serviceName=${serviceName}` : '';\n  const profileTypeParam = `&var-profileMetricId=${pyroscopeQuery.profileTypeId}`;\n  const explorationTypeParam = `&explorationType=${explorationType}`;\n  if (timeRange) {\n    timeRangeParam = `&from=${timeRange.from}&to=${timeRange.to}`;\n  }\n  if (pyroscopeQuery.spanSelector) {\n    spanSelectorParam = `&var-spanSelector=${pyroscopeQuery.spanSelector}`;\n  }\n\n  const base = '/a/grafana-pyroscope-app/explore?';\n  const params = new URLSearchParams(\n    `${datasourceParam}${serviceNameParam}${profileTypeParam}${timeRangeParam}${explorationTypeParam}${spanSelectorParam}`\n  ).toString();\n  return `${base}${params}`;\n}\n\nexport const EXPLORE_TOOLBAR_ACTION: PluginExtensionAddedLinkConfig<PluginExtensionExploreContext> = {\n  targets: [PluginExtensionPoints.ExploreToolbarAction],\n  title: 'Open in Grafana Profiles Drilldown',\n  icon: 'fire',\n  description: 'Try our new queryless experience for profiles',\n  path: '/a/grafana-pyroscope-app/explore',\n  configure(context: PluginExtensionExploreContext | undefined) {\n    if (!context || !context.targets || !context.timeRange || context.targets.length > 1) {\n      return undefined;\n    }\n\n    const firstQuery = context.targets[0];\n\n    if (firstQuery.datasource && firstQuery.datasource.type === 'grafana-pyroscope-datasource') {\n      return {\n        path: buildURL({\n          pyroscopeQuery: firstQuery as GrafanaPyroscopeDataQuery,\n          timeRange: context.timeRange,\n        }),\n      };\n    }\n    return undefined;\n  },\n};\n\nexport const TRACEVIEW_DETAILS_ACTION: PluginExtensionAddedLinkConfig<any> = {\n  targets: ['grafana/traceview/details'],\n  title: 'Open in Grafana Profiles Drilldown',\n  description: 'Try our new queryless experience for profiles',\n  path: '/a/grafana-pyroscope-app/explore',\n  onClick: (_, { context }) => {\n    if (!context || !context.serviceName || !context.spanSelector || !context.profileTypeId || !context.timeRange) {\n      return;\n    }\n\n    const serviceName = context.serviceName;\n    const spanSelector = context.spanSelector;\n    const profileTypeId = context.profileTypeId;\n    const timeRange = context.timeRange;\n\n    const pyroscopeQuery: GrafanaPyroscopeDataQuery = {\n      refId: 'span-flamegraph-profiles-drilldown-refId',\n      labelSelector: `service_name=\"${serviceName}\"`,\n      profileTypeId,\n      spanSelector,\n      datasource: context.datasource,\n      groupBy: ['service_name'],\n    };\n\n    if (pyroscopeQuery.datasource) {\n      const path = buildURL({\n        pyroscopeQuery: pyroscopeQuery,\n        timeRange,\n        explorationType: 'flame-graph',\n      });\n      window.open(path, '_blank', 'noopener,noreferrer');\n    }\n    return undefined;\n  },\n};\n", "import { AppPlugin } from '@grafana/data';\nimport { AppPluginSettings } from '@shared/types/AppPluginSettings';\n\nimport { Root } from './app/Root';\nimport { EXPLORE_TOOLBAR_ACTION, PluginExtensionExploreContext, TRACEVIEW_DETAILS_ACTION } from './links';\n\nexport const plugin = new AppPlugin<AppPluginSettings>()\n  .addLink<PluginExtensionExploreContext>(EXPLORE_TOOLBAR_ACTION)\n  .addLink<PluginExtensionExploreContext>(TRACEVIEW_DETAILS_ACTION)\n  .setRootPage(Root);\n"], "names": ["inProgress", "dataWebpackPrefix", "module", "exports", "__WEBPACK_EXTERNAL_MODULE__6089__", "__WEBPACK_EXTERNAL_MODULE__7781__", "__WEBPACK_EXTERNAL_MODULE__8531__", "__WEBPACK_EXTERNAL_MODULE__2007__", "__WEBPACK_EXTERNAL_MODULE__4201__", "__WEBPACK_EXTERNAL_MODULE__3241__", "__WEBPACK_EXTERNAL_MODULE__1308__", "__WEBPACK_EXTERNAL_MODULE__5959__", "__WEBPACK_EXTERNAL_MODULE__8398__", "__WEBPACK_EXTERNAL_MODULE__1159__", "__WEBPACK_EXTERNAL_MODULE__1269__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "id", "loaded", "__webpack_modules__", "call", "m", "amdO", "n", "getter", "__esModule", "d", "a", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "f", "e", "chunkId", "Promise", "all", "keys", "reduce", "promises", "u", "g", "globalThis", "this", "Function", "window", "obj", "prop", "prototype", "hasOwnProperty", "l", "url", "done", "push", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "i", "length", "s", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "indexOf", "location", "origin", "crossOrigin", "integrity", "sri<PERSON><PERSON><PERSON>", "onScriptComplete", "prev", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "fn", "setTimeout", "bind", "type", "target", "head", "append<PERSON><PERSON><PERSON>", "r", "Symbol", "toStringTag", "value", "nmd", "paths", "children", "p", "installedChunks", "j", "installedChunkData", "promise", "resolve", "reject", "error", "Error", "errorType", "realSrc", "message", "name", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "data", "chunkIds", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "slice", "lastIndexOf", "LazyApp", "React", "then", "default", "App", "buildURL", "props", "pyroscopeQuery", "timeRange", "timeRangeParam", "spanSelectorParam", "explorationType", "serviceName", "labelSelector", "match", "datasourceParam", "datasource", "uid", "serviceNameParam", "profileTypeParam", "profileTypeId", "explorationTypeParam", "from", "to", "spanSelector", "URLSearchParams", "toString", "EXPLORE_TOOLBAR_ACTION", "targets", "PluginExtensionPoints", "ExploreToolbarAction", "title", "icon", "description", "path", "configure", "context", "first<PERSON><PERSON>y", "TRACEVIEW_DETAILS_ACTION", "onClick", "_", "refId", "groupBy", "open", "plugin", "AppPlugin", "addLink", "setRootPage", "Suspense"], "sourceRoot": ""}