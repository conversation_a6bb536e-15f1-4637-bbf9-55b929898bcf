{"version": 3, "file": "202.js?_cache=71520d621a6b7c0f04bb", "mappings": "86CAyBA,MAsIMA,EAAaC,IAA0B,CAC3CC,UAAWC,EAAAA,GAAG;aACHF,EAAMG,OAAOC,KAAKC;IAE7BC,UAAWJ,EAAAA,GAAG;kBACEF,EAAMO,QAAQ;IAE9BC,YAAaN,EAAAA,GAAG;kBACAF,EAAMO,QAAQ;MAI1BE,EAAwB,CAAOC,EAAkBC,IAAAA,EAAAA,YACrD,UACQC,EAAaF,EAAUC,GAI7BE,EAAAA,gBAAgBC,QAClB,CAAE,MAAOC,GACPC,QAAQC,MAAM,kCAAmCF,EACnD,CACF,EAVuDJ,GAYjDO,EAAU,CACdC,UAAW,CACTC,UAAW,2BACXC,OAAQ,yBACRC,OAAQ,yBACRC,OAAQ,+BAICX,EAAe,CAAOF,EAAkBC,IAAAA,EAAAA,YACnD,MAAMa,GAAWC,EAAAA,EAAAA,iBAAgBC,MAAM,CACrCC,IAAK,gBAAgBjB,aACrBkB,OAAQ,OACRjB,SAKF,aAF2BkB,EAAAA,EAAAA,eAAcL,IAErBb,IACtB,EAVqDA,GAYrD,EAnLkB,EAAGmB,aACnB,MAAMC,GAAIC,EAAAA,EAAAA,YAAWjC,IACf,QAAEkC,EAAO,OAAEC,EAAM,SAAEC,GAAaL,EAAOM,MACtCC,EAAOC,IAAYC,EAAAA,EAAAA,UAAgB,CACxCjB,QAAQa,aAAAA,EAAAA,EAAUb,SAAU,GAC5BD,OAAQ,GACRmB,YAAaC,QAAQN,aAAAA,EAAAA,EAAUK,eAwBjC,OACE,kBAACE,MAAAA,CAAIC,cAAazB,EAAQC,UAAUC,WAElC,kBAACwB,EAAAA,SAAQA,CAACC,MAAM,qBACZZ,GACA,oCACE,kBAACS,MAAAA,CAAII,UAAWf,EAAE9B,WAAW,wCAC7B,kBAAC8C,EAAAA,OAAMA,CACLD,UAAWf,EAAEzB,UACb0C,QAAQ,UACRC,QAAS,IACPxC,EAAsBqB,EAAOM,KAAKc,GAAI,CACpCjB,SAAS,EACTC,QAAQ,EACRC,cAGL,kBAOJF,GACC,oCACE,kBAACS,MAAAA,CAAII,UAAWf,EAAE9B,WAAW,oCAC7B,kBAAC8C,EAAAA,OAAMA,CACLD,UAAWf,EAAEzB,UACb0C,QAAQ,cACRC,QAAS,IACPxC,EAAsBqB,EAAOM,KAAKc,GAAI,CACpCjB,SAAS,EACTC,QAAQ,EACRC,cAGL,oBAQP,kBAACS,EAAAA,SAAQA,CAACC,MAAM,eAAeC,UAAWf,EAAEvB,aAE1C,kBAAC2C,EAAAA,MAAKA,CAACN,MAAM,UAAUO,YAAY,qDACjC,kBAACC,EAAAA,YAAWA,CACVC,MAAO,GACPX,cAAazB,EAAQC,UAAUE,OAC/B6B,GAAG,UACHK,MAAOlB,aAAAA,EAAAA,EAAOhB,OACdmC,aAAcnB,EAAMG,YACpBiB,YAAa,sBACbC,SArEcC,IACtBrB,EAAS,OACJD,GAAAA,CACHhB,OAAQsC,EAAMC,OAAOL,MAAMM,WAmErBC,QA7EY,IACpBxB,EAAS,OACJD,GAAAA,CACHhB,OAAQ,GACRmB,aAAa,QA8EX,kBAACW,EAAAA,MAAKA,CAACN,MAAM,UAAUO,YAAY,GAAGN,UAAWf,EAAEzB,WACjD,kBAACyD,EAAAA,MAAKA,CACJT,MAAO,GACPJ,GAAG,UACHP,cAAazB,EAAQC,UAAUG,OAC/BuB,MAAO,UACPU,MAAOlB,aAAAA,EAAAA,EAAOf,OACdmC,YAAa,oCACbC,SA5EcC,IACtBrB,EAAS,OACJD,GAAAA,CACHf,OAAQqC,EAAMC,OAAOL,MAAMM,cA6EzB,kBAACnB,MAAAA,CAAII,UAAWf,EAAEzB,WAChB,kBAACyC,EAAAA,OAAMA,CACLiB,KAAK,SACLrB,cAAazB,EAAQC,UAAUI,OAC/B0B,QAAS,IACPxC,EAAsBqB,EAAOM,KAAKc,GAAI,CACpCjB,UACAC,SACAC,SAAU,CACRb,OAAQe,EAAMf,OACdkB,aAAa,GAIfyB,eAAgB5B,EAAMG,iBAClB0B,EACA,CACE7C,OAAQgB,EAAMhB,UAIxB8C,SAAU1B,SAASJ,EAAMf,SAAYe,EAAMG,cAAgBH,EAAMhB,SAClE,wB", "sources": ["webpack://grafana-exploretraces-app/./components/AppConfig/AppConfig.tsx"], "sourcesContent": ["import React, { useState, ChangeEvent } from 'react';\nimport { Button, Field, Input, useStyles2, FieldSet, SecretInput } from '@grafana/ui';\nimport { PluginConfigPageProps, AppPluginMeta, PluginMeta, GrafanaTheme2 } from '@grafana/data';\nimport { FetchResponse, getBackendSrv, locationService } from '@grafana/runtime';\nimport { css } from '@emotion/css';\nimport { lastValueFrom, Observable } from 'rxjs';\n\nexport type JsonData = {\n  apiUrl?: string;\n  isApiKeySet?: boolean;\n};\n\ntype State = {\n  // The URL to reach our custom API.\n  apiUrl: string;\n  // Tells us if the API key secret is set.\n  // Set to `true` ONLY if it has already been set and haven't been changed.\n  // (We unfortunately need an auxiliary variable for this, as `secureJsonData` is never exposed to the browser after it is set)\n  isApiKeySet: boolean;\n  // An secret key for our custom API.\n  apiKey: string;\n};\n\ninterface Props extends PluginConfigPageProps<AppPluginMeta<JsonData>> {}\n\nconst AppConfig = ({ plugin }: Props) => {\n  const s = useStyles2(getStyles);\n  const { enabled, pinned, jsonData } = plugin.meta;\n  const [state, setState] = useState<State>({\n    apiUrl: jsonData?.apiUrl || '',\n    apiKey: '',\n    isApiKeySet: Boolean(jsonData?.isApiKeySet),\n  });\n\n  const onResetApiKey = () =>\n    setState({\n      ...state,\n      apiKey: '',\n      isApiKeySet: false,\n    });\n\n  const onChangeApiKey = (event: ChangeEvent<HTMLInputElement>) => {\n    setState({\n      ...state,\n      apiKey: event.target.value.trim(),\n    });\n  };\n\n  const onChangeApiUrl = (event: ChangeEvent<HTMLInputElement>) => {\n    setState({\n      ...state,\n      apiUrl: event.target.value.trim(),\n    });\n  };\n\n  return (\n    <div data-testid={testIds.appConfig.container}>\n      {/* ENABLE / DISABLE PLUGIN */}\n      <FieldSet label=\"Enable / Disable\">\n        {!enabled && (\n          <>\n            <div className={s.colorWeak}>The plugin is currently not enabled.</div>\n            <Button\n              className={s.marginTop}\n              variant=\"primary\"\n              onClick={() =>\n                updatePluginAndReload(plugin.meta.id, {\n                  enabled: true,\n                  pinned: true,\n                  jsonData,\n                })\n              }\n            >\n              Enable plugin\n            </Button>\n          </>\n        )}\n\n        {/* Disable the plugin */}\n        {enabled && (\n          <>\n            <div className={s.colorWeak}>The plugin is currently enabled.</div>\n            <Button\n              className={s.marginTop}\n              variant=\"destructive\"\n              onClick={() =>\n                updatePluginAndReload(plugin.meta.id, {\n                  enabled: false,\n                  pinned: false,\n                  jsonData,\n                })\n              }\n            >\n              Disable plugin\n            </Button>\n          </>\n        )}\n      </FieldSet>\n\n      {/* CUSTOM SETTINGS */}\n      <FieldSet label=\"API Settings\" className={s.marginTopXl}>\n        {/* API Key */}\n        <Field label=\"API Key\" description=\"A secret key for authenticating to our custom API\">\n          <SecretInput\n            width={60}\n            data-testid={testIds.appConfig.apiKey}\n            id=\"api-key\"\n            value={state?.apiKey}\n            isConfigured={state.isApiKeySet}\n            placeholder={'Your secret API key'}\n            onChange={onChangeApiKey}\n            onReset={onResetApiKey}\n          />\n        </Field>\n\n        {/* API Url */}\n        <Field label=\"API Url\" description=\"\" className={s.marginTop}>\n          <Input\n            width={60}\n            id=\"api-url\"\n            data-testid={testIds.appConfig.apiUrl}\n            label={`API Url`}\n            value={state?.apiUrl}\n            placeholder={`E.g.: http://mywebsite.com/api/v1`}\n            onChange={onChangeApiUrl}\n          />\n        </Field>\n\n        <div className={s.marginTop}>\n          <Button\n            type=\"submit\"\n            data-testid={testIds.appConfig.submit}\n            onClick={() =>\n              updatePluginAndReload(plugin.meta.id, {\n                enabled,\n                pinned,\n                jsonData: {\n                  apiUrl: state.apiUrl,\n                  isApiKeySet: true,\n                },\n                // This cannot be queried later by the frontend.\n                // We don't want to override it in case it was set previously and left untouched now.\n                secureJsonData: state.isApiKeySet\n                  ? undefined\n                  : {\n                      apiKey: state.apiKey,\n                    },\n              })\n            }\n            disabled={Boolean(!state.apiUrl || (!state.isApiKeySet && !state.apiKey))}\n          >\n            Save API settings\n          </Button>\n        </div>\n      </FieldSet>\n    </div>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  colorWeak: css`\n    color: ${theme.colors.text.secondary};\n  `,\n  marginTop: css`\n    margin-top: ${theme.spacing(3)};\n  `,\n  marginTopXl: css`\n    margin-top: ${theme.spacing(6)};\n  `,\n});\n\nconst updatePluginAndReload = async (pluginId: string, data: Partial<PluginMeta<JsonData>>) => {\n  try {\n    await updatePlugin(pluginId, data);\n\n    // Reloading the page as the changes made here wouldn't be propagated to the actual plugin otherwise.\n    // This is not ideal, however unfortunately currently there is no supported way for updating the plugin state.\n    locationService.reload();\n  } catch (e) {\n    console.error('Error while updating the plugin', e);\n  }\n};\n\nconst testIds = {\n  appConfig: {\n    container: 'data-testid ac-container',\n    apiKey: 'data-testid ac-api-key',\n    apiUrl: 'data-testid ac-api-url',\n    submit: 'data-testid ac-submit-form',\n  },\n};\n\nexport const updatePlugin = async (pluginId: string, data: Partial<PluginMeta>) => {\n  const response = getBackendSrv().fetch({\n    url: `/api/plugins/${pluginId}/settings`,\n    method: 'POST',\n    data,\n  }) as unknown as Observable<FetchResponse>;\n\n  const dataResponse = await lastValueFrom(response);\n\n  return dataResponse.data;\n};\n\nexport default AppConfig;\n"], "names": ["getStyles", "theme", "colorWeak", "css", "colors", "text", "secondary", "marginTop", "spacing", "marginTopXl", "updatePluginAndReload", "pluginId", "data", "updatePlugin", "locationService", "reload", "e", "console", "error", "testIds", "appConfig", "container", "<PERSON><PERSON><PERSON><PERSON>", "apiUrl", "submit", "response", "getBackendSrv", "fetch", "url", "method", "lastValueFrom", "plugin", "s", "useStyles2", "enabled", "pinned", "jsonData", "meta", "state", "setState", "useState", "isApiKeySet", "Boolean", "div", "data-testid", "FieldSet", "label", "className", "<PERSON><PERSON>", "variant", "onClick", "id", "Field", "description", "SecretInput", "width", "value", "isConfigured", "placeholder", "onChange", "event", "target", "trim", "onReset", "Input", "type", "secureJsonData", "undefined", "disabled"], "sourceRoot": ""}