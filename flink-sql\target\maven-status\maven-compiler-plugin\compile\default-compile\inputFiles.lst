/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/transformers/Transformer.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/writers/KafkaAvroWriter.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/configs/SchemaConfig.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/writers/Writer.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/transformers/ProjectionTransformer.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/configs/JobConfig.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/configs/WriterConfig.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/configs/TransformerConfig.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/configs/FormatConfig.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/configs/ReaderConfig.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/writers/KafkaPostgreSQLWriter.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/AutoGenJob.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/utils/YamlParser.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/JobGenerator.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/writers/KafkaClickHouseWriter.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/utils/ComponentFactory.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/readers/Reader.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/transformers/WithColumnTransformer.java
/Users/<USER>/Code/ahuy/flink-sql-demo/flink-sql/src/main/java/org/myorg/autogen/readers/KafkaReader.java
