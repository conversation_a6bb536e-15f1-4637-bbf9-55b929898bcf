package org.myorg.autogen.configs;

import java.util.Map;

/**
 * Configuration for data formats (JSON, Avro, etc.)
 */
public class FormatConfig {
    private String type;
    private Map<String, Object> sourceOptions;
    private Map<String, Object> properties;

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, Object> getSourceOptions() {
        return sourceOptions;
    }

    public void setSourceOptions(Map<String, Object> sourceOptions) {
        this.sourceOptions = sourceOptions;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }
}
