"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[351],{5351:(e,t,a)=>{a.r(t),a.d(t,{default:()=>y});var r=a(6089),n=a(8531),l=a(2007),i=a(5959),c=a.n(i),s=a(5914),o=a(2445),m=a(2728),d=a(2993),u=a(5753),p=a(7781),g=a(1792);function f(){const e=(0,l.useStyles2)(h),t=(0,l.useTheme2)();return c().createElement("div",{className:e.wrap},c().createElement("div",{className:e.graphicContainer},c().createElement(g.A,{src:(t.isDark,"/public/plugins/grafana-metricsdrilldown-app/img/logo.svg")})),c().createElement("div",{className:e.text},c().createElement("h3",{className:e.title},"Welcome to Grafana Metrics Drilldown"),c().createElement("p",null,"We noticed there is no Prometheus datasource configured.",c().createElement("br",null),"Add a"," ",c().createElement("a",{className:"external-link",href:p.locationUtil.assureBaseUrl("/connections/datasources/new")},"Prometheus datasource")," ","to view metrics."),c().createElement("br",null),c().createElement("p",null,"Check"," ",c().createElement("a",{href:"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/metrics/",target:"_blank",className:"external-link",rel:"noreferrer"},"our docs")," ","to learn more or",c().createElement("br",null),c().createElement("a",{href:"https://play.grafana.org/a/grafana-metricsdrilldown-app/drilldown",target:"_blank",className:"external-link",rel:"noreferrer"},"try it online")," ","in Grafana Play!")))}const h=e=>({graphicContainer:(0,r.css)({[e.breakpoints.up("md")]:{alignSelf:"flex-end",height:"auto",padding:e.spacing(1),width:"300px"},[e.breakpoints.up("lg")]:{alignSelf:"flex-end",height:"auto",padding:e.spacing(1),width:"400px"},display:"flex",height:"250px",justifyContent:"center",margin:"0 auto",padding:e.spacing(1),width:"200px"}),text:(0,r.css)({alignItems:"center",display:"flex",flexDirection:"column",justifyContent:"center"}),title:(0,r.css)({marginBottom:"1.5rem"}),wrap:(0,r.css)({[e.breakpoints.up("md")]:{flexDirection:"row",margin:"4rem auto auto auto"},alignItems:"center",display:"flex",flexDirection:"column",margin:"0 auto auto auto",padding:"2rem",textAlign:"center"})});var x=a(8732),w=a(1522),E=a(3347);var v=a(7476);const k=(0,i.createContext)(null);(0,s.Js)();const b=Object.values(n.config.datasources).filter(v.aQ);try{m.x.migrate()}catch(e){o.v.error(e,{cause:"User preferences migration"})}function y(e){const t=(0,l.useStyles2)(C),[a]=(0,w.n)();return function(){const e=(0,i.useRef)(!1);(0,i.useEffect)(()=>{if(!e.current){e.current=!0;const a=new URL(window.location.href),r=a.searchParams.get("metric")?"metric-details":"metrics-reducer";var t;const n=null!==(t=a.searchParams.get("uel_epid"))&&void 0!==t?t:"";(0,E.z)("app_initialized",{view:r,uel_epid:n})}},[])}(),a?c().createElement("div",{className:t.appContainer,"data-testid":"metrics-drilldown-app"},c().createElement(d.E,{error:a})):b.length?c().createElement("div",{className:t.appContainer,"data-testid":"metrics-drilldown-app"},c().createElement(k.Provider,{value:e},c().createElement(u.WA.Provider,{value:{trail:u.KF}},c().createElement(x.S,null)))):c().createElement(f,null)}function C(e){return{appContainer:(0,r.css)({display:"flex",flexDirection:"column",height:"100%",backgroundColor:e.colors.background.primary})}}}}]);
//# sourceMappingURL=351.js.map?_cache=7fcf2c7f4d77561a6d7f