CREATE DATABASE cf;

CREATE TABLE IF NOT EXISTS cf.station_status_kafka
(
    station_id String,
    current_status String
) ENGINE = Kafka()
SETTINGS
    kafka_broker_list = 'redpanda:9092',
    kafka_topic_list = 'cf.data_platform.station.status',
    kafka_group_name = 'clickhouse-group-station',
    kafka_format = 'JSONEachRow';

-- Use existing station_status_merge_tree table structure
CREATE TABLE cf.station_status_merge_tree (
    station_id String,
    event_date DateTime,
    offline_count UInt32
) ENGINE = MergeTree()
ORDER BY (event_date, station_id);

-- Update existing materialized view to use new schema
DROP VIEW IF EXISTS cf.station_status_view;
CREATE MATERIALIZED VIEW cf.station_status_view TO cf.station_status_merge_tree AS
SELECT
    station_id,
    now() as event_date,
    CASE WHEN current_status = 'off' THEN 1 ELSE 0 END as offline_count
FROM cf.station_status_kafka;



-- Kafka-backed table for processed orders
CREATE TABLE IF NOT EXISTS cf.processed_orders_kafka
(
    order_id String,
    station_id String,
    total_amount Float64,
    order_status String
) ENGINE = Kafka()
SETTINGS
    kafka_broker_list = 'redpanda:9092',
    kafka_topic_list = 'cf.data_platform.processed.orders',
    kafka_group_name = 'clickhouse-orders-group',
    kafka_format = 'JSONEachRow',
    kafka_row_delimiter = '\n';

-- MergeTree table to store processed orders
CREATE TABLE IF NOT EXISTS cf.processed_orders_merge_tree (
    order_id String,
    station_id String,
    total_amount Float64,
    order_status String,
    ingestion_time DateTime DEFAULT now()
) ENGINE = MergeTree()
ORDER BY (ingestion_time, order_id);

-- Materialized view to populate the MergeTree table from Kafka
CREATE MATERIALIZED VIEW cf.processed_orders_view TO cf.processed_orders_merge_tree AS
SELECT
    order_id,
    station_id,
    total_amount,
    order_status
FROM cf.processed_orders_kafka;
