package org.myorg.autogen.configs;

import java.util.Map;

/**
 * Configuration for data writers (sinks)
 */
public class WriterConfig {
    private String type;
    private String endpoint;
    private String table;
    private String topic;
    private String mode;
    private FormatConfig format;
    private SchemaConfig schema;
    private Map<String, Object> properties;
    private String key; // For S3 or file-based writers
    private Integer numFiles; // For file-based writers

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getTable() {
        return table;
    }

    public void setTable(String table) {
        this.table = table;
    }

    public String getTopic() {
        return topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public FormatConfig getFormat() {
        return format;
    }

    public void setFormat(FormatConfig format) {
        this.format = format;
    }

    public Map<String, Object> getProperties() {
        return properties;
    }

    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public Integer getNumFiles() {
        return numFiles;
    }

    public void setNumFiles(Integer numFiles) {
        this.numFiles = numFiles;
    }

    public SchemaConfig getSchema() {
        return schema;
    }

    public void setSchema(SchemaConfig schema) {
        this.schema = schema;
    }
}
