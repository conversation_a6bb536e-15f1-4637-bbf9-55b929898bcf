"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[140],{7140:(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var r=a(5959),n=a.n(r),i=a(2993),l=a(8732),d=a(1522),o=a(3347),c=a(3314),s=a(7597),u=a(7781),m=a(7985);function p(e){return"string"==typeof e&&u.dateMath.isMathString(e)?e:u.dateMath.toDateTime(new Date(e),{roundUp:!1}).toISOString()}const f=({query:e,initialStart:t,initialEnd:a,dataSource:u})=>{const[f]=(0,d.n)(),{metric:b,labels:w}=(0,s.$9)(e),k=(0,c.ef)({metric:b,initialDS:u.uid,initialFilters:w.map(({label:e,op:t,value:a})=>({key:e,operator:t,value:a})),$timeRange:(_=t,g=a,new m.JZ({from:p(_),to:p(g)})),embedded:!0});var _,g;const h=(0,r.useRef)(!1);return(0,r.useEffect)(()=>{h.current||(h.current=!0,(0,o.z)("exposed_component_viewed",{component:"label_breakdown"}))},[]),n().createElement("div",{"data-testid":"metrics-drilldown-embedded-label-breakdown"},f?n().createElement(i.E,{error:f}):n().createElement(l.A,{trail:k}))}}}]);
//# sourceMappingURL=140.js.map?_cache=c67baffbee112f92d1e6