package org.myorg.autogen;

import org.apache.flink.table.api.EnvironmentSettings;
import org.apache.flink.table.api.TableEnvironment;
import org.apache.flink.table.api.TableResult;
import org.myorg.autogen.configs.JobConfig;
import org.myorg.autogen.configs.TransformerConfig;
import org.myorg.autogen.configs.SchemaConfig;
import org.myorg.autogen.readers.Reader;
import org.myorg.autogen.writers.Writer;
import org.myorg.autogen.transformers.Transformer;
import org.myorg.autogen.utils.ComponentFactory;
import org.myorg.autogen.utils.YamlParser;

import java.io.IOException;
import java.util.List;

/**
 * Main job generator that orchestrates the creation and execution of Flink jobs
 */
public class JobGenerator {
    
    private final TableEnvironment tableEnv;
    
    public JobGenerator() {
        // Create table environment for batch or streaming
        EnvironmentSettings settings = EnvironmentSettings
            .newInstance()
            .inStreamingMode()
            .build();
        
        this.tableEnv = TableEnvironment.create(settings);
    }
    
    /**
     * Generate and execute jobs from YAML configuration file
     * 
     * @param yamlFilePath Path to the YAML configuration file
     * @throws IOException if file cannot be read
     */
    public void generateAndExecuteJobs(String yamlFilePath) throws IOException {
        // Parse YAML configuration
        JobConfig jobConfig = YamlParser.parseJobConfig(yamlFilePath);
        YamlParser.validateJobConfig(jobConfig);
        
        System.out.println("Loaded " + jobConfig.getJobs().size() + " job(s) from configuration");
        
        // Process each job
        for (JobConfig.Job job : jobConfig.getJobs()) {
            System.out.println("\n=== Processing Job: " + job.getName() + " ===");
            processJob(job);
        }
    }
    
    /**
     * Process a single job configuration
     * 
     * @param job Job configuration
     */
    private void processJob(JobConfig.Job job) {
        try {
            // Create source table
            String sourceTableName = job.getName() + "_source";
            createSourceTable(job, sourceTableName);
            
            // Create sink table
            String sinkTableName = job.getName() + "_sink";
            createSinkTable(job, sinkTableName);
            
            // Build transformation query
            String transformationQuery = buildTransformationQuery(job, sourceTableName);
            
            // Execute the job
            executeJob(transformationQuery, sinkTableName);
            
        } catch (Exception e) {
            System.err.println("Error processing job " + job.getName() + ": " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * Create source table based on reader configuration
     */
    private void createSourceTable(JobConfig.Job job, String tableName) {
        Reader reader = ComponentFactory.getReader(job.getReaders().getType());
        reader.createSourceTable(tableEnv, job.getReaders(), tableName);
        System.out.println("Created source table: " + tableName);
    }
    
    /**
     * Create sink table based on writer configuration
     */
    private void createSinkTable(JobConfig.Job job, String tableName) {
        Writer writer = ComponentFactory.getWriter(job.getWriters().getType());
        writer.createSinkTable(tableEnv, job.getWriters(), null, tableName);
        System.out.println("Created sink table: " + tableName);
    }
    
    /**
     * Build transformation query by applying all transformers
     */
    private String buildTransformationQuery(JobConfig.Job job, String sourceTableName) {
        String query = "SELECT * FROM " + sourceTableName;
        
        if (job.getTransformers() != null) {
            for (TransformerConfig transformerConfig : job.getTransformers()) {
                Transformer transformer = ComponentFactory.getTransformer(transformerConfig.getType());
                query = transformer.transform(query, transformerConfig);
                System.out.println("Applied transformer: " + transformerConfig.getType());
            }
        }
        
        System.out.println("Final transformation query:");
        System.out.println(query);
        return query;
    }
    
    /**
     * Execute the job by inserting transformed data into sink table
     */
    private void executeJob(String transformationQuery, String sinkTableName) {
        String insertSql = "INSERT INTO " + sinkTableName + " " + transformationQuery;
        
        System.out.println("Executing job with SQL:");
        System.out.println(insertSql);
        
        TableResult result = tableEnv.executeSql(insertSql);
        System.out.println("Job submitted successfully. Job ID: " + result.getJobClient().get().getJobID());
    }

    /**
     * Get output schema from transformers - looks for the last transformer with output_schema defined
     */
    private SchemaConfig getOutputSchemaFromTransformers(List<TransformerConfig> transformers) {
        if (transformers == null || transformers.isEmpty()) {
            return null;
        }

        // Look for the last transformer that has output_schema defined
        for (int i = transformers.size() - 1; i >= 0; i--) {
            TransformerConfig transformer = transformers.get(i);
            if (transformer.getOutputSchema() != null) {
                return transformer.getOutputSchema();
            }
        }

        return null;
    }
}
