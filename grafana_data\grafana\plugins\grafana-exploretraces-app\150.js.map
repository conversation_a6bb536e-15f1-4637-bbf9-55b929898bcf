{"version": 3, "file": "150.js?_cache=a99ca9bc52a47e729056", "mappings": "mKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,uCACxB,iBAAkB,kCAClB,yBAA0B,yCAE3B,yBAA0B,CACzB,sBAAuB,yCACvB,mBAAoB,2CAErB,gBAAiB,CAChBC,QAAS,0EACTC,SAAU,yCACVC,MAAO,kBAER,wBAAyB,CACxB,wBAAyB,gBACzB,sBAAuB,gBACvB,sBAAuB,gBAExB,iBAAkB,CACjB,iBAAkB,qBAClB,cAAe,kBACf,uBAAwB,qBAEzB,iBAAkB,CACjB,eAAgB,iBAChB,aAAc,kBAEf,oCAAqC,CACpC,eAAgB,aAChB,iBAAkB,2CAEnBC,SAAU,CACT,gCAAiC,yCAElC,YAAa,CACZD,MAAO,CACNA,MAAO,WAGT,2BAA4B,CAC3BE,QAAS,YAEV,qBAAsB,CACrB,uBAAwB,gCACxB,sCAAuC,uDAExC,yBAA0B,CACzB,2DAA4D,yGAC5D,kBAAmB,8CAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,WAEnB,oBAAqB,CACpB,uBAAwB,sBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,6BAEpC,wBAAyB,CACxB,wBAAyB,mBACzB,mBAAoB,oBAErB,yBAA0B,CACzB,2BAA4B,iBAC5B,aAAc,CACb,2BAA4B,qBAE7B,qBAAsB,oBACtB,sBAAuB,iBACvB,eAAgB,CACf,2BAA4B,qBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,WAGb,8CAA+C,CAC9C,mBAAoB,SACpBC,QAAS,uFACT,gDAAiD,uDAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,gCACzB,uBAAwB,6BACxB,gCAAiC,uCACjC,oDAAqD,8FACrD,0BAA2B,yBAC3B,uBAAwB,kCACxB,mBAAoB,2BACpB,mDAAoD,oDACpD,uBAAwB,8BACxB,kDAAmD,wFACnD,iCAAkC,qCAClC,oCAAqC,iDAIxC,6BAA8B,CAC7B,+BAAgC,sBAChC,6BAA8B,sBAE/B,oBAAqB,CACpB,2BAA4B,oBAE7B,8BAA+B,CAC9B,kBAAmB,sBAEpB,2BAA4B,CAC3BC,MAAO,WAER,yBAA0B,CACzB,mBAAoB,4BAErB,4BAA6B,CAC5B,6CAA8C,mEAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,QAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,UAGR,uBAAwB,CACvB,0BAA2B,mBAE5B,wBAAyB,CACxB,2BAA4B,sB", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/pt-BR/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Editar filtro com chave {{keyLabel}}\",\n\t\t\t\"managed-filter\": \"Filtro gerenciado de {{origin}}\",\n\t\t\t\"remove-filter-with-key\": \"Remover filtro com chave {{keyLabel}}\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Remover valor do filtro: {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"Usar valor personalizado: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Se você chegou aqui usando um link, pode haver um bug neste aplicativo.\",\n\t\t\tsubTitle: \"O URL não corresponde a nenhuma página\",\n\t\t\ttitle: \"Não encontrado\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"Recolher cena\",\n\t\t\t\"expand-button-label\": \"Expandir cena\",\n\t\t\t\"remove-button-label\": \"Remover cena\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Detalhes do objeto\",\n\t\t\t\"scene-graph\": \"Gráfico de cena\",\n\t\t\t\"title-scene-debugger\": \"Depurador de cena\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Recolher linha\",\n\t\t\t\"expand-row\": \"Expandir linha\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Comparação\",\n\t\t\t\"button-tooltip\": \"Ativar comparação de intervalo de tempo\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Widget de redimensionamento do painel\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Título\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Explorar\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Carregando painel do plug-in…\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"O plug-in do painel não possui componente de painel\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Renderizar muitas séries em um único painel pode afetar o desempenho e dificultar a leitura dos dados.\",\n\t\t\t\"warning-message\": \"Mostrando apenas {{seriesLimit}} série(s)\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Remover\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Cancelar consulta\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Editar operador de filtro\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Adicionar filtro\",\n\t\t\t\"title-add-filter\": \"Adicionar filtro\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Remover filtro\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Selecionar rótulo\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Selecionar rótulo\",\n\t\t\t\"title-remove-filter\": \"Remover filtro\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Selecionar valor\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"padrão\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"limpar\",\n\t\t\ttooltip: \"Aplicado por padrão neste painel. Se editado, ele é transferido para outros painéis.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Restaura a função groupby definida por este painel.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Valores separados por vírgula\",\n\t\t\t\t\t\"double-quoted-values\": \"Valores entre aspas duplas\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Formatar data de diferentes maneiras\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Formatar variáveis de múltiplos valores usando a sintaxe glob. Por exemplo: {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"Escape HTML de valores\",\n\t\t\t\t\t\"json-stringify-value\": \"Valor convertido em string JSON\",\n\t\t\t\t\t\"keep-value-as-is\": \"Manter o valor como está\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Vários valores são formatados como variável=valor\",\n\t\t\t\t\t\"single-quoted-values\": \"Valores entre aspas simples\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Útil para valores de escape de URL, levando em consideração caracteres de sintaxe URI\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Útil para valores de escape de URL\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Os valores são separados pelo caractere \\\"|\\\"\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Agrupar por seletor\",\n\t\t\t\"placeholder-group-by-label\": \"Agrupar por rótulo\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Selecionar valor\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Carregando opções…\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Aplicar\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"Nenhuma opção encontrada\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Ocorreu um erro ao buscar rótulos. Clique para tentar novamente\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Olá\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Texto\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Digite um valor\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Selecionar valor\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}