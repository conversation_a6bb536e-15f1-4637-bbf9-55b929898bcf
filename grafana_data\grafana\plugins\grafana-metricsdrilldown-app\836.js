"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[619,836],{416:(e,t,n)=>{n.d(t,{V:()=>r});const r={DATASOURCE:"datasource",RECENT_METRICS:"recent-metrics",BOOKMARKS:"bookmarks",METRIC_PREFS:"metric-prefs",BREAKDOWN_SORTBY:"breakdown.sortby",SIDEBAR_SECTION:"sidebar.section"}},619:(e,t,n)=>{n.r(t),n.d(t,{sortSeries:()=>h,wasmSupported:()=>g});var r=n(6944),i=n(7781),a=n(3241),o=n(3616),s=n(4964),l=n(3347);function c(e){var t;const n=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!n)return null;const r=Object.keys(n);return 0===r.length?null:n[r[0]]}const u=(e,t="asc")=>{const n="asc"===t?(e,t)=>(0,s._)(e,t):(e,t)=>(0,s._)(t,e);return e.sort((e,t)=>{const r=c(e);if(!r)return 0;const i=c(t);return i?n(r,i):0})},d=(e,t,n="asc")=>{const r=i.fieldReducers.get(t),a=e.map(e=>{var n;const a=e.fields[1];if(!a)return{value:0,dataFrame:e};var o;var s;return{value:null!==(s=(null!==(o=null===(n=r.reduce)||void 0===n?void 0:n.call(r,a,!0,!0))&&void 0!==o?o:(0,i.doStandardCalcs)(a,!0,!0))[t])&&void 0!==s?s:0,dataFrame:e}});return a.sort("asc"===n?(e,t)=>e.value-t.value:(e,t)=>t.value-e.value),a.map(({dataFrame:e})=>e)},p=e=>{const t=(0,i.outerJoinDataFrames)({frames:e});if(!t)throw new Error("Error while joining frames into a single one");const n=t.fields.filter(e=>e.type===i.FieldType.number).map(e=>new Float64Array(e.values));return r.OutlierDetector.dbscan({sensitivity:.9}).detect(n)},m=(e,t)=>e.seriesResults[t].isOutlier?-e.seriesResults[t].outlierIntervals.length:0,h=(0,a.memoize)((e,t,n="asc")=>{if(!e.length)return[];const r=[...e];if("alphabetical"===t)return u(r,"asc");if("alphabetical-reversed"===t)return u(r,"desc");if("outliers"===t)try{return((e,t="asc")=>{if(!g())throw new Error("WASM not supported");const n=p(e),r=e.map((e,t)=>({value:m(n,t),dataFrame:e}));return r.sort("asc"===t?(e,t)=>e.value-t.value:(e,t)=>t.value-e.value),r.map(({dataFrame:e})=>e)})(r,n)}catch(e){const t=`Error while sorting by outlying series: "${e.toString()}"!`;return(0,o.HA)([t,"Falling back to standard deviation to identify the most variable series."]),d(r,i.ReducerID.stdDev,n)}return d(r,t,n)},(e,t,n="asc")=>{const r=f(e)?e[0].fields[0].values[0]:0,i=f(e)?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0;return`${e.length>0?c(e[0]):""}_${e.length>0?c(e[e.length-1]):""}_${r}_${i}_${e.length}_${t}_${n}`});function f(e){return e.length>0&&e[0].fields.length>0&&e[0].fields[0].values.length>0}const g=()=>{const e="object"==typeof WebAssembly;return e||(0,l.z)("wasm_not_supported",{}),e}},1522:(e,t,n)=>{n.d(t,{n:()=>o});var r=n(5959),i=n(2445);function a(e,t){return e instanceof Error?e:"string"==typeof e?new Error(e):"string"==typeof e.message?new Error(e.message):new Error(t)}function o(){const[e,t]=(0,r.useState)();return(0,r.useEffect)(()=>{const e=e=>{(function(e){var t,n,r,a;if(e.filename&&new URL(e.filename).protocol.endsWith("extension:"))return i.v.error(new Error(`Browser extension error: ${e.message}`),{filename:e.filename,lineno:null===(t=e.lineno)||void 0===t?void 0:t.toString(),colno:null===(n=e.colno)||void 0===n?void 0:n.toString()}),!1;return null!==e.error||!e.message||(i.v.error(new Error(`Non-critical error: ${e.message}`),{filename:e.filename,lineno:null===(r=e.lineno)||void 0===r?void 0:r.toString(),colno:null===(a=e.colno)||void 0===a?void 0:a.toString()}),!1)})(e)&&t(a(e.error,"Uncaught exception!"))},n=e=>{"cancelled"!==e.reason.type?t(a(e.reason,"Unhandled rejection!")):t(void 0)};return window.addEventListener("error",e),window.addEventListener("unhandledrejection",n),()=>{window.removeEventListener("unhandledrejection",n),window.removeEventListener("error",e)}},[]),[e,t]}},2728:(e,t,n)=>{n.d(t,{x:()=>o});var r=n(3347),i=n(2533),a=n(416);const o=new class{migrate(){let e=!1;const t=[{legacyKey:"metricsDrilldownDataSource",newKey:a.V.DATASOURCE},{legacyKey:"metrics-drilldown-recent-metrics/v1",newKey:a.V.RECENT_METRICS},{legacyKey:"grafana.trails.bookmarks",newKey:a.V.BOOKMARKS},{legacyKey:"grafana.trails.breakdown.sort.labels.by",newKey:a.V.BREAKDOWN_SORTBY}];for(const{legacyKey:n,newKey:r}of t){let t=localStorage.getItem(n);if(null!==t){try{t=JSON.parse(t)}catch(e){}this.setItem(r,t),localStorage.removeItem(n),e=!0}}e&&(0,r.z)("user_preferences_migrated",{})}buildStorageKey(e){return`${this.service}.${e}`}getItem(e){const t=this.buildStorageKey(e),n=localStorage.getItem(t);return null===n?null:JSON.parse(n)}setItem(e,t){const n=this.buildStorageKey(e);localStorage.setItem(n,JSON.stringify(t))}removeItem(e){const t=this.buildStorageKey(e);localStorage.removeItem(t)}clear(){localStorage.clear()}constructor(e){var t,n,r;r=void 0,(n="service")in(t=this)?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,this.service=e}}(i.id)},2993:(e,t,n)=>{n.d(t,{E:()=>c});var r=n(6089),i=n(2007),a=n(5959),o=n.n(a),s=n(1159),l=n(6503);function c({error:e}){const t=(0,i.useStyles2)(u),n=(0,s.useNavigate)(),{pathname:r,search:c}=(0,s.useLocation)(),d=(0,a.useCallback)(()=>{const e=new URLSearchParams(c),t=new URLSearchParams;["from","to","timezone"].filter(t=>e.has(t)).forEach(n=>t.set(n,e.get(n))),n({pathname:r,search:t.toString()}),window.location.reload()},[n,r,c]),[p,m]=(0,a.useState)(!1);return o().createElement("div",{className:t.container},o().createElement(l._,{severity:"error",title:"Fatal error!",error:e,errorContext:{handheldBy:"React error boundary"},message:o().createElement(o().Fragment,null,o().createElement("p",{className:t.message},"Please"," ",o().createElement(i.TextLink,{href:"#",onClick:d},"try reloading the page")," ","or, if the problem persists, contact your organization admin. Sorry for the inconvenience."),o().createElement("p",null,o().createElement(i.Collapse,{className:t.callStack,collapsible:!0,label:"View stack trace",isOpen:p,onToggle:()=>m(!p)},o().createElement("pre",null,o().createElement("code",null,e.stack)))))}))}function u(e){return{container:(0,r.css)({margin:e.spacing(2)}),message:(0,r.css)({margin:e.spacing(2,0,1,0)}),callStack:(0,r.css)({backgroundColor:"transparent",border:"0 none","& button":(0,r.css)({paddingLeft:e.spacing(1.5)}),"& button:focus":(0,r.css)({outline:"none",boxShadow:"none"}),"& button > svg":(0,r.css)({marginLeft:e.spacing(-2),marginRight:e.spacing(.5)}),'& [class$="collapse__loader"]':(0,r.css)({display:"none"})})}}},3314:(e,t,n)=>{n.d(t,{y:()=>as,UX:()=>ds,Vy:()=>ss,aO:()=>os,kj:()=>ts,KE:()=>ns,xi:()=>is,FG:()=>us,ef:()=>rs});var r=n(7781),i=n(8531),a=n(7985),o=n(2445);function s(e){var t,n;if(!e)return;const r=null!==(n=e.state.$data)&&void 0!==n?n:null===(t=e.parent)||void 0===t?void 0:t.state.$data;return l(r)?r:null!=(i=r)&&"state"in i&&"transformations"in i.state?s(r):void 0;var i}function l(e){return null!=e&&"state"in e&&"runQueries"in e}var c,u,d,p=n(4137),m=n(6089),h=n(1932),f=n(2007),g=n(5959),b=n.n(g),y=n(3241);class v extends r.BusEventWithPayload{}d="timeseries-data-received",(u="type")in(c=v)?Object.defineProperty(c,u,{value:d,enumerable:!0,configurable:!0,writable:!0}):c[u]=d;class w extends r.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(w,"type","force-sync-y-axis");class S extends r.BusEventWithPayload{}function O(){const e=e=>{let t=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY;const r=a.jh.getTimeRange(e).subscribeToState(()=>{t=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY}),i=e.subscribeToEvent(S,()=>{t=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY}),o=e.subscribeToEvent(w,()=>{let[r,i]=[t,n];const a=x(e).filter(e=>{var t;const{fieldConfig:n,$data:a}=e.state;return(!("min"in n.defaults)||!("max"in n.defaults))&&([r,i]=E((null==a||null===(t=a.state.data)||void 0===t?void 0:t.series)||[],r,i),!0)});r===t&&i===n?P(e,t,n,a):([t,n]=[r,i],P(e,r,i))}),s=e.subscribeToEvent(v,r=>{const{panelKey:i,series:o}=r.payload,[s,l]=E(o,t,n);s!==l&&s!==Number.NEGATIVE_INFINITY&&l!==Number.POSITIVE_INFINITY&&(s!==t||l!==n?([t,n]=[s,l],P(e,s,l)):P(e,t,n,[a.jh.findByKeyAndType(e,i,a.Eb)]))});return()=>{s.unsubscribe(),o.unsubscribe(),i.unsubscribe(),r.unsubscribe()}};return Object.defineProperty(e,"__name__",{value:"syncYAxis",configurable:!1,enumerable:!0,writable:!1}),e}function E(e,t,n){let[r,i]=[t,n];for(const t of e||[]){var a;const e=null===(a=t.fields[1])||void 0===a?void 0:a.values.filter(Boolean);e&&(r=Math.max(r,...e),i=Math.min(i,...e))}return[r,i]}function x(e){return a.jh.findAllObjects(e,e=>e instanceof a.Eb&&"timeseries"===e.state.pluginId)}function P(e,t,n,r){for(const i of r||x(e))i.clearFieldConfigCache(),i.setState({fieldConfig:(0,y.merge)((0,y.cloneDeep)(i.state.fieldConfig),{defaults:{min:n,max:t}})})}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(S,"type","reset-sync-y-axis");var k=n(3347),C=n(416),j=n(2728),_=n(3616);const T={TIMESERIES_AVG:"timeseries-avg",TIMESERIES_SUM:"timeseries-sum",TIMESERIES_STDDEV:"timeseries-stddev",TIMESERIES_PERCENTILES:"timeseries-percentiles",TIMESERIES_MIN_MAX:"timeseries-minmax",TIMESERIES_AGE_TIME_MINUS_AVG:"timeseries-age-time-minus-avg",TIMESERIES_AGE_TIME_MINUS_MIN_MAX:"timeseries-age-time-minus-min-max",HISTOGRAM_HEATMAP:"histogram-heatmap",HISTOGRAM_PERCENTILES:"histogram-percentiles",STATUS_UPDOWN_HISTORY:"status-updown-history",STATUS_UPDOWN_STAT:"status-updown-stat"};var I=n(8162);const D=new Map([...["avg","sum","stddev","quantile","min","max"].map(e=>[e,{name:e,fn:t=>I.GH[e](t)}]),["histogram_quantile",{name:"histogram_quantile",fn:({expr:e,parameter:t})=>`histogram_quantile(${t},${e})`}],["time-avg(metric)",{name:"time-avg(metric)",fn:({expr:e})=>`time()-avg(${e})`}],["time-min(metric)",{name:"time-min(metric)",fn:({expr:e})=>`time()-min(${e})`}],["time-max(metric)",{name:"time-max(metric)",fn:({expr:e})=>`time()-max(${e})`}]]),A=new Set(Object.values(T)),L=new Set(["heatmap","percentiles","stat","statushistory","timeseries"]);function N(e){var t;const n=j.x.getItem(C.V.METRIC_PREFS)||{},r=null===(t=n[e])||void 0===t?void 0:t.config;var i;if(r)return function(e){if(!["id","panelOptions","queryOptions"].every(t=>t in e))return!1;if("string"!=typeof e.id||!A.has(e.id))return!1;if("string"!=typeof e.panelOptions.type||!L.has(e.panelOptions.type))return!1;if(!Array.isArray(e.queryOptions.queries))return!1;if(!e.queryOptions.queries.every(e=>{var t;return!!D.has(e.fn)&&(!["quantile","histogram_quantile"].includes(e.fn)||!(!Array.isArray(null===(t=e.params)||void 0===t?void 0:t.percentiles)||!e.params.percentiles.length)&&e.params.percentiles.every(e=>e>=1&&e<=99))}))return!1;return!0}(r)?r:((0,k.z)("invalid_metric_config",{metricConfig:r}),null===(i=n[e])||void 0===i||delete i.config,j.x.setItem(C.V.METRIC_PREFS,n),void(0,_.HA)([`Invalid configuration found for metric ${e}!`,"The configuration has been deleted and will not be applied."]))}var B=function(e){return e[e.S=160]="S",e[e.M=220]="M",e[e.L=260]="L",e[e.XL=280]="XL",e}({});const M=e=>e.endsWith("_bucket"),$=/_(count|total|sum)$/,R=e=>$.test(e),F=/_up$/,V=e=>"up"===e||F.test(e);function q(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function H(e,t){return(n=function*(){let n=z(e);return"gauge"===n&&(yield t.isNativeHistogram(e))&&(n="native-histogram"),n},function(){var e=this,t=arguments;return new Promise(function(r,i){var a=n.apply(e,t);function o(e){q(a,r,i,o,s,"next",e)}function s(e){q(a,r,i,o,s,"throw",e)}o(void 0)})})();var n}function z(e){return R(e)?"counter":M(e)?"classic-histogram":(e=>e.endsWith("_timestamp_seconds"))(e)?"age":V(e)?"status-updown":"gauge"}const U={[T.TIMESERIES_AGE_TIME_MINUS_AVG]:{id:T.TIMESERIES_AGE_TIME_MINUS_AVG,name:"Average age",panelOptions:{type:"timeseries",description:'Suitable only for metrics that store unix timestamps (usually containing "timestamp_seconds" in their name) to calculate an average age. Calculates the age by subtracting the average timestamp value from current time.'},queryOptions:{queries:[{fn:"time-avg(metric)"}]}},[T.TIMESERIES_AGE_TIME_MINUS_MIN_MAX]:{id:T.TIMESERIES_AGE_TIME_MINUS_MIN_MAX,name:"Minimum and maximum ages",panelOptions:{type:"timeseries",description:'Suitable only for metrics that store unix timestamps (usually containing "timestamp_seconds" in their name) to calculate a minimum and a maximum age. Calculates the ages by subtracting the min and the max timestamp values from current time.'},queryOptions:{queries:[{fn:"time-min(metric)"},{fn:"time-max(metric)"}]}}},G={[T.HISTOGRAM_HEATMAP]:{id:T.HISTOGRAM_HEATMAP,name:"Heatmap (default)",panelOptions:{type:"heatmap",description:"Visualizes the full distribution of histogram data over time using color intensity. Perfect for spotting patterns, identifying performance degradation, and understanding latency distribution changes. Shows density of values across different buckets."},queryOptions:{queries:[]}},[T.HISTOGRAM_PERCENTILES]:{id:T.HISTOGRAM_PERCENTILES,name:"Percentiles",panelOptions:{type:"percentiles",description:"Extracts specific percentile values from histogram data. Essential for SLA monitoring and performance analysis, showing how response times or other metrics behave for different user experience tiers."},queryOptions:{queries:[{fn:"histogram_quantile",params:{percentiles:[99,90,50]}}]}}},K={[T.STATUS_UPDOWN_HISTORY]:{id:T.STATUS_UPDOWN_HISTORY,name:"Status History (default)",panelOptions:{type:"statushistory",description:"Displays binary status changes over time as colored bars (green=up, red=down). Perfect for monitoring service availability, health checks, or any binary state metrics. Shows patterns in uptime/downtime and helps identify recurring issues."},queryOptions:{queries:[{fn:"min"}]}},[T.STATUS_UPDOWN_STAT]:{id:T.STATUS_UPDOWN_STAT,name:"Stat with latest value",panelOptions:{type:"stat",description:'Shows the current status as a single value display with color coding (green=up, red=down). Ideal for dashboards where you need an at-a-glance view of service health or binary state. Uses minimum value to ensure any "down" status is highlighted.'},queryOptions:{queries:[{fn:"min"}]}}};function W(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Q(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){W(e,t,n[t])})}return e}function Y(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const X={[T.TIMESERIES_AVG]:{id:T.TIMESERIES_AVG,name:"Average (default)",panelOptions:{type:"timeseries",description:"Shows the average value across all time series. Ideal for understanding typical behavior and smoothing out variations between different targets. For rate queries, displays average throughput per target."},queryOptions:{queries:[{fn:"avg"}]}},[T.TIMESERIES_SUM]:{id:T.TIMESERIES_SUM,name:"Sum",panelOptions:{type:"timeseries",description:"Aggregates total values across all time series. Perfect for measuring overall system throughput, total resource consumption, or fleet-wide capacity. Essential for rate queries showing total request rates."},queryOptions:{queries:[{fn:"sum"}]}},[T.TIMESERIES_STDDEV]:{id:T.TIMESERIES_STDDEV,name:"Standard deviation",panelOptions:{type:"timeseries",description:"Measures variability and consistency across time series. High values indicate uneven load distribution or inconsistent behavior. Useful for detecting load balancing issues or identifying when some targets behave differently."},queryOptions:{queries:[{fn:"stddev"}]}},[T.TIMESERIES_PERCENTILES]:{id:T.TIMESERIES_PERCENTILES,name:"Percentiles",panelOptions:{type:"percentiles",description:"Displays percentiles to show value distribution. Excellent for SLA monitoring and understanding outlier behavior without being skewed by extreme values. Critical for performance analysis."},queryOptions:{queries:[{fn:"quantile",params:{percentiles:[99,90,50]}}]}},[T.TIMESERIES_MIN_MAX]:{id:T.TIMESERIES_MIN_MAX,name:"Minimum and maximum",panelOptions:{type:"timeseries",description:"Shows the range between lowest and highest values across time series. Useful for capacity planning, identifying idle resources (min), and spotting overloaded targets (max). Helps detect outliers and resource utilization patterns."},queryOptions:{queries:[{fn:"min"},{fn:"max"}]}}},J={[T.TIMESERIES_SUM]:Y(Q({},X[T.TIMESERIES_SUM]),{name:"Sum (default)",id:T.TIMESERIES_SUM}),[T.TIMESERIES_AVG]:Y(Q({},X[T.TIMESERIES_AVG]),{name:"Average"}),[T.TIMESERIES_STDDEV]:X[T.TIMESERIES_STDDEV],[T.TIMESERIES_PERCENTILES]:X[T.TIMESERIES_PERCENTILES],[T.TIMESERIES_MIN_MAX]:X[T.TIMESERIES_MIN_MAX]};function Z(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function ee(e,t){return(n=function*(){switch(yield H(e,t)){case"counter":return Object.values(J);case"classic-histogram":case"native-histogram":return Object.values(G);case"age":return[Object.values(X)[0],...Object.values(U)];case"status-updown":return Object.values(K);default:return Object.values(X)}},function(){var e=this,t=arguments;return new Promise(function(r,i){var a=n.apply(e,t);function o(e){Z(a,r,i,o,s,"next",e)}function s(e){Z(a,r,i,o,s,"throw",e)}o(void 0)})})();var n}class te extends r.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(te,"type","panel-type-changed");var ne=n(2245);function re(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const ie="filters",ae="${filters}",oe="groupby",se="ds",le="${ds}",ce="logsDs",ue="other_metric_filters",de="$__logs__",pe={uid:le};function me(e){return[new a.x0({name:"metric",value:e,hide:ne.zL.hideVariable})]}class he extends r.BusEventWithPayload{}re(he,"type","metric-selected-event");class fe extends r.BusEventBase{}function ge(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}re(fe,"type","refresh-metrics-event");class be extends a.Bs{constructor({metric:e,variant:t,fill:n}){super({key:`select-action-${e}`,metric:e,variant:t||"primary",fill:n||"outline"}),ge(this,"onClick",()=>{this.publishEvent(new he({metric:this.state.metric}),!0)})}}ge(be,"Component",({model:e})=>{const{variant:t,fill:n}=e.useState();return b().createElement(f.Button,{variant:t,fill:n,size:"sm",onClick:e.onClick,"data-testid":`select-action-${e.state.metric}`},"Select")});var ye=function(e){return e.HIGH="HIGH",e.MEDIUM="MEDIUM",e}({});function ve(e,t){return"classic"===t||"native"===t?"heatmap":V(e)?"statushistory":"timeseries"}var we=n(5938);function Se(e){const{metric:t,labelMatchers:n=[],addIgnoreUsageFilter:r=!0,addExtremeValuesFiltering:i=!1}=e,a=n.map(e=>({label:(0,h.Nc)(e.key),operator:e.operator,value:e.value}));r&&a.push({label:"__ignore_usage__",operator:I.md.equal,value:""});!(0,h.Rq)(t)&&a.push({label:(0,h.Nc)(t),operator:I.md.equal,value:"__REMOVE__"}),a.push({label:`\${${ie}:raw}`,operator:I.md.equal,value:"__REMOVE__"});const o=function(e){return e.toString().replaceAll('="__REMOVE__"',"")}(new I.r4({metric:t,values:{},defaultOperator:I.md.equal,defaultSelectors:a}));return i?I.GH.and({left:o,right:`${o} > -Inf`}):o}const Oe="none",Ee="bytes",xe="seconds",Pe="percent",ke="count",Ce={[Ee]:Ee,[xe]:"s",[Pe]:Pe,[ke]:Oe},je=Object.keys(Ce),_e={[Ee]:"Bps",[xe]:Oe,[ke]:"cps",[Pe]:Pe};function Te(e){const t=e.toLowerCase().split("_").slice(-2);for(let e=t.length-1;e>=Math.max(0,t.length-2);e--){const n=t[e];if(je.includes(n))return n}return null}function Ie(e){const t=Te(e);return t&&Ce[t.toLowerCase()]||Oe}function De(e){const t=Te(e);return t&&_e[t]||"cps"}function Ae(e){var t;const{metric:n,histogramType:r,panelConfig:i,queryConfig:o}=e,s=function(e){const{metric:t,isNativeHistogram:n,queryConfig:r}=e,i=Se({metric:t,labelMatchers:r.labelMatchers,addIgnoreUsageFilter:r.addIgnoreUsageFilter,addExtremeValuesFiltering:r.addExtremeValuesFiltering}),a=n?I.GH.sum({expr:I.GH.rate({expr:i})}):I.GH.sum({expr:I.GH.rate({expr:i}),by:["le"]});return{maxDataPoints:r.resolution===ye.HIGH?500:250,queries:[{refId:`${t}-heatmap`,expr:a,format:"heatmap",fromExploreMetrics:!0}]}}({metric:n,isNativeHistogram:"native"===r,queryConfig:o}),l=Ie(n),c=o.data||new a.dt({datasource:pe,maxDataPoints:s.maxDataPoints,queries:s.queries});return a.d0.heatmap().setTitle(i.title).setDescription(i.description).setHeaderActions(i.headerActions({metric:n,panelConfig:i})).setMenu(null===(t=i.menu)||void 0===t?void 0:t.call(i,{metric:n,panelConfig:i})).setShowMenuAlways(Boolean(i.menu)).setData(c).setUnit(l).setOption("calculate",!1).setOption("color",{mode:we.P7.Scheme,exponent:.5,scheme:"Spectral",steps:32,reverse:!1}).setOption("legend",i.legend).build()}var Le=n(1625);const Ne=[99,90,50];function Be(e){const{metric:t,histogramType:n,queryConfig:r}=e,i=R(t),a=Se({metric:t,labelMatchers:r.labelMatchers,addIgnoreUsageFilter:r.addIgnoreUsageFilter,addExtremeValuesFiltering:r.addExtremeValuesFiltering}),o="none"===n?function({metric:e,queryConfig:t,isRateQuery:n,expr:r}){var i;const a=(null===(i=t.queries)||void 0===i?void 0:i.length)?t.queries:[{fn:"quantile",params:{percentiles:[99,90,50]}}],o=[],s=n?I.GH.rate({expr:r}):r;for(const{fn:t,params:r}of a){const i=D.get(t),a=n?`${i.name}(rate)`:i.name;for(const t of r.percentiles){const n=t/100,r=i.fn({expr:s,parameter:n});o.push({refId:`${e}-p${t}-${a}`,expr:r,legendFormat:`${t}th Percentile`,fromExploreMetrics:!0})}}return o}({metric:t,queryConfig:r,isRateQuery:i,expr:a}):function({metric:e,isNativeHistogram:t,queryConfig:n,expr:r}){var i;const a=(null===(i=n.queries)||void 0===i?void 0:i.length)?n.queries:[{fn:"histogram_quantile",params:{percentiles:Ne}}],o=[],s=t?I.GH.sum({expr:I.GH.rate({expr:r})}):I.GH.sum({expr:I.GH.rate({expr:r}),by:["le"]});for(const{fn:t,params:n}of a){const r=D.get(t),i=r.name,a=(null==n?void 0:n.percentiles)||Ne;for(const t of a){const n=t/100,a=r.fn({expr:s,parameter:n});o.push({refId:`${e}-p${t}-${i}`,expr:a,legendFormat:`${t}th Percentile`,fromExploreMetrics:!0})}}return o}({metric:t,isNativeHistogram:"native"===n,queryConfig:r,expr:a});return{isRateQuery:"none"!==n||i,maxDataPoints:r.resolution===ye.HIGH?500:250,queries:o}}function Me(e){var t;const{metric:n,histogramType:r,panelConfig:i,queryConfig:o}=e,s=Be({metric:n,histogramType:r,queryConfig:o}),l=s.isRateQuery?De(n):Ie(n),c=o.data||new a.dt({datasource:pe,maxDataPoints:s.maxDataPoints,queries:s.queries}),u=i.fixedColorIndex||0;return a.d0.timeseries().setTitle(i.title).setDescription(i.description).setHeaderActions(i.headerActions({metric:n,panelConfig:i})).setMenu(null===(t=i.menu)||void 0===t?void 0:t.call(i,{metric:n,panelConfig:i})).setShowMenuAlways(Boolean(i.menu)).setData(c).setUnit(l).setOption("legend",i.legend||{showLegend:!0,placement:"bottom"}).setOption("tooltip",{mode:Le.$N.Multi,sort:Le.xB.Descending}).setCustomFieldConfig("fillOpacity",9).setOverrides(e=>{s.queries.forEach((t,n)=>{e.matchFieldsByQuery(t.refId).overrideColor({mode:"fixed",fixedColor:ss(u+n)})})}).setBehaviors(i.behaviors||[]).build()}function $e({metric:e,queryConfig:t,isRateQuery:n,expr:r}){var i;const a=n?"sum":"avg",o=(null===(i=t.queries)||void 0===i?void 0:i.length)?t.queries:[{fn:a}],s=[];for(const{fn:t}of o){const i=D.get(t),a=i.fn({expr:r}),o=n?`${i.name}(rate)`:i.name;s.push({refId:`${e}-${o}`,expr:a,legendFormat:o,fromExploreMetrics:!0})}return s}var Re=n(6145);const Fe=[{type:Re.dM.ValueToText,options:{0:{color:"red",text:"down"},1:{color:"green",text:"up"}}}];function Ve(e){var t;const{metric:n,panelConfig:r,queryConfig:i}=e,o=function(e){const{metric:t,queryConfig:n}=e,r=R(t),i=Se({metric:t,labelMatchers:n.labelMatchers,addIgnoreUsageFilter:n.addIgnoreUsageFilter,addExtremeValuesFiltering:n.addExtremeValuesFiltering}),a=r?I.GH.rate({expr:i,interval:"$__rate_interval"}):i;return{isRateQuery:r,maxDataPoints:n.resolution===ye.HIGH?500:250,queries:$e({metric:t,queryConfig:n,isRateQuery:r,expr:a})}}({metric:n,queryConfig:i}),s=i.data||new a.dt({datasource:pe,maxDataPoints:o.maxDataPoints,queries:o.queries});return a.d0.stat().setTitle(r.title).setDescription(r.description).setHeaderActions(r.headerActions({metric:n,panelConfig:r})).setMenu(null===(t=r.menu)||void 0===t?void 0:t.call(r,{metric:n,panelConfig:r})).setShowMenuAlways(Boolean(r.menu)).setData(s).setUnit("none").setColor({mode:"fixed",fixedColor:ss(r.fixedColorIndex||0)}).setMappings(Fe).build()}function qe(e){var t;const{metric:n,panelConfig:r,queryConfig:i}=e,o=function(e){const{metric:t,queryConfig:n}=e,r=Se({metric:t,labelMatchers:n.labelMatchers,addIgnoreUsageFilter:n.addIgnoreUsageFilter,addExtremeValuesFiltering:n.addExtremeValuesFiltering}),i=I.GH.min({expr:r});return{maxDataPoints:n.resolution===ye.HIGH?200:100,queries:[{refId:`${t}-status`,expr:i,legendFormat:"status",fromExploreMetrics:!0}]}}({metric:n,queryConfig:i}),s=i.data||new a.dt({datasource:pe,maxDataPoints:o.maxDataPoints,queries:o.queries});return a.d0.statushistory().setTitle(r.title).setDescription(r.description).setHeaderActions(r.headerActions({metric:n,panelConfig:r})).setMenu(null===(t=r.menu)||void 0===t?void 0:t.call(r,{metric:n,panelConfig:r})).setShowMenuAlways(Boolean(r.menu)).setData(s).setUnit("none").setColor({mode:"palette-classic"}).setOption("showValue",Le.yL.Never).setOption("legend",r.legend||{showLegend:!0,placement:"bottom"}).setOption("perPage",0).setMappings(Fe).build()}function He(e){const{metric:t,queryConfig:n}=e,r=R(t),i=Se({metric:t,labelMatchers:n.labelMatchers,addIgnoreUsageFilter:n.addIgnoreUsageFilter,addExtremeValuesFiltering:n.addExtremeValuesFiltering}),a=r?I.GH.rate({expr:i,interval:"$__rate_interval"}):i;return{isRateQuery:r,maxDataPoints:n.resolution===ye.HIGH?500:250,queries:n.groupBy?ze({metric:t,queryConfig:n,isRateQuery:r,expr:a}):Ue({metric:t,queryConfig:n,isRateQuery:r,expr:a})}}function ze({metric:e,queryConfig:t,isRateQuery:n,expr:r}){return[{refId:`${e}-by-${t.groupBy}`,expr:n?I.GH.sum({expr:r,by:[t.groupBy]}):I.GH.avg({expr:r,by:[t.groupBy]}),legendFormat:`{{${t.groupBy}}}`,fromExploreMetrics:!0}]}function Ue({metric:e,queryConfig:t,isRateQuery:n,expr:r}){var i;const a=n?"sum":"avg",o=(null===(i=t.queries)||void 0===i?void 0:i.length)?t.queries:[{fn:a}],s=[];for(const{fn:t}of o){const i=D.get(t),a=i.fn({expr:r}),o=n?`${i.name}(rate)`:i.name;s.push({refId:`${e}-${o}`,expr:a,legendFormat:o,fromExploreMetrics:!0})}return s}function Ge(e){const t=e.fields.find(e=>"Value"===e.name);return!!(t&&(n=t,"entities"in n&&Array.isArray(null===(r=n.entities)||void 0===r?void 0:r.NaN)))&&t.entities.NaN.length===e.length;var n,r}function Ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function We(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Ke(e,t,n[t])})}return e}function Qe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Ye(e){if("timeseries"!==e.state.pluginId)return;const[t]=a.jh.findDescendents(e,a.dt);if(!t)return;const{queries:n}=t.state;if(!(null==n?void 0:n.length))return;const{metric:i,queryConfig:o}=a.jh.getAncestor(e,pt).state;if(R(i))return;const s=t.subscribeToState((n,s)=>{var l,c,u,d;if((null===(l=n.data)||void 0===l?void 0:l.state)!==r.LoadingState.Done||!(null===(c=n.data.series)||void 0===c?void 0:c.length)||n.data.series===(null===(u=s.data)||void 0===u?void 0:u.series))return;const p=null===(d=n.data.series[0].meta)||void 0===d?void 0:d.type;if(p&&!p.startsWith("timeseries"))return;if(!n.data.series.every(Ge))return;const m=He({metric:i,queryConfig:Qe(We({},o),{addExtremeValuesFiltering:!0})});t.setState({queries:m.queries}),t.runQueries(),e.setState({titleItems:b().createElement(Xe,{level:"info",message:"Panel data was re-fetched with a more complex query to handle extremely small values in the series"})}),(0,k.z)("extreme_value_filter_behavior_triggered",{expression:a.jh.interpolate(t,t.state.queries[0].expr)})});return()=>{s.unsubscribe()}}function Xe({message:e,level:t}){const n=(0,f.useStyles2)(Je,t);return b().createElement("div",{className:n.extremeValuedisclaimer},b().createElement(f.Tooltip,{content:e},b().createElement("span",{className:n.warningMessage},b().createElement(f.Icon,{name:"warning"===t?"exclamation-triangle":"info-circle","aria-hidden":"true"}))))}const Je=(e,t)=>({extremeValuedisclaimer:(0,m.css)({label:"extreme-value-disclaimer",display:"flex",alignItems:"center",gap:e.spacing(1)}),warningMessage:(0,m.css)({display:"flex",alignItems:"center",gap:e.spacing(.5),color:"warning"===t?e.colors.warning.main:e.colors.info.main,fontSize:e.typography.bodySmall.fontSize})});var Ze=n(1269);const et=()=>e=>e.pipe((0,Ze.map)(e=>null==e?void 0:e.map((e,t)=>(e.refId=`${e.refId}-${t}`,e))));function tt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function nt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const rt=e=>()=>t=>t.pipe((0,Ze.map)(t=>null==t?void 0:t.map(t=>{var n;return(null==t?void 0:t.fields[1])?((null===(n=t.fields[1].labels)||void 0===n?void 0:n[e])||(t.fields[1].labels=nt(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){tt(e,t,n[t])})}return e}({},t.fields[1].labels),{[e]:`<unspecified ${e}>`})),t):t})));function it(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const at=(e,t)=>()=>n=>n.pipe((0,Ze.map)(n=>null==n?void 0:n.slice(e,t).map(e=>{var t;return e.meta=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){it(e,t,n[t])})}return e}({},e.meta),(t=e.meta).stats||(t.stats=[]),e.meta.stats.unshift({displayName:"seriesCount",value:n.length}),e})));function ot(e){var t;if(e.queryConfig.groupBy)return function(e){var t;const{metric:n,panelConfig:r,queryConfig:i}=e,o=He({metric:n,queryConfig:i}),s=o.isRateQuery?De(n):Ie(n),l=new a.Es({$data:new a.dt({datasource:pe,maxDataPoints:o.maxDataPoints,queries:o.queries}),transformations:[at(0,st),rt(i.groupBy),et]}),{refId:c}=o.queries[0],u=r.fixedColorIndex||0,d=a.d0.timeseries().setTitle(r.title).setDescription(r.description).setHeaderActions(r.headerActions({metric:n,panelConfig:r})).setMenu(null===(t=r.menu)||void 0===t?void 0:t.call(r,{metric:n,panelConfig:r})).setShowMenuAlways(Boolean(r.menu)).setData(l).setUnit(s).setOption("legend",r.legend||{showLegend:!0,placement:"right"}).setOption("tooltip",{mode:Le.$N.Multi,sort:Le.xB.Descending}).setOverrides(e=>{for(let t=0;t<st;t++)e.matchFieldsByQuery(`${c}-${t}`).overrideColor({mode:"fixed",fixedColor:ss(u+t)})}).setBehaviors(r.behaviors).build();return d}(e);const{metric:n,panelConfig:r,queryConfig:i}=e,o=He({metric:n,queryConfig:i}),s=o.isRateQuery?De(n):Ie(n),l=i.data||new a.dt({datasource:pe,maxDataPoints:o.maxDataPoints,queries:o.queries}),c=a.d0.timeseries().setTitle(r.title).setDescription(r.description).setHeaderActions(r.headerActions({metric:n,panelConfig:r})).setMenu(null===(t=r.menu)||void 0===t?void 0:t.call(r,{metric:n,panelConfig:r})).setShowMenuAlways(Boolean(r.menu)).setData(l).setUnit(s).setOption("legend",r.legend||{showLegend:!0,placement:"bottom"}).setCustomFieldConfig("fillOpacity",9).setBehaviors([Ye,...r.behaviors||[]]);if(o.queries.length>1){const e=r.fixedColorIndex||0;c.setOverrides(t=>{o.queries.forEach((n,r)=>{t.matchFieldsByQuery(n.refId).overrideColor({mode:"fixed",fixedColor:ss(e+r)})})})}else c.setColor(r.fixedColorIndex?{mode:"fixed",fixedColor:ss(r.fixedColorIndex)}:void 0);return c.build()}const st=20;function lt(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ut(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ct(e,t,n[t])})}return e}function dt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class pt extends a.Bs{onActivate(e){return(t=function*(){const{metric:t,panelConfig:n}=this.state;this.updateBody(),this.subscribeToStateChanges(e),this.subscribeToEvents(),(yield ts(this).isNativeHistogram(t))&&this.setState({histogramType:"native",panelConfig:e?n:dt(ut({description:"Native Histogram "},n),{type:"heatmap"})})},function(){var e=this,n=arguments;return new Promise(function(r,i){var a=t.apply(e,n);function o(e){lt(a,r,i,o,s,"next",e)}function s(e){lt(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var t}subscribeToStateChanges(e){const{histogramType:t,body:n,panelConfig:i}=this.state;if(!e&&"none"===t){var a;const e=null==n||null===(a=n.state.$data)||void 0===a?void 0:a.subscribeToState(t=>{var n,a,o;if((null===(n=t.data)||void 0===n?void 0:n.state)!==r.LoadingState.Done)return;const s=null===(o=t.data.series[0])||void 0===o||null===(a=o.meta)||void 0===a?void 0:a.type;s&&(s===r.DataFrameType.HeatmapCells&&this.setState({histogramType:"native",panelConfig:dt(ut({description:"Native Histogram "},i),{type:"heatmap"})}),e.unsubscribe())});this._subs.add(e)}this.subscribeToState((e,t)=>{e.histogramType===t.histogramType&&(0,y.isEqual)(e.panelConfig,t.panelConfig)&&(0,y.isEqual)(e.queryConfig,t.queryConfig)||this.updateBody()})}subscribeToEvents(){this.subscribeToEvent(te,e=>{this.setState({panelConfig:dt(ut({},this.state.panelConfig),{type:e.payload.panelType})})})}updateBody(){const{metric:e,panelConfig:t,queryConfig:n,histogramType:r}=this.state;switch(t.type){case"timeseries":return void this.setState({body:ot({metric:e,panelConfig:t,queryConfig:n})});case"heatmap":return void this.setState({body:Ae({metric:e,histogramType:r,panelConfig:t,queryConfig:n})});case"percentiles":return void this.setState({body:Me({metric:e,histogramType:r,panelConfig:t,queryConfig:n})});case"statushistory":return void this.setState({body:qe({metric:e,panelConfig:t,queryConfig:n})});case"stat":return void this.setState({body:Ve({metric:e,panelConfig:t,queryConfig:n})});default:throw new TypeError(`Unsupported panel type "${t.type}"!`)}}update(e,t){const{panelConfig:n,queryConfig:r}=this.state;this.setState({panelConfig:ut({},n,e),queryConfig:ut({},r,t)})}constructor({key:e,metric:t,panelOptions:n,queryOptions:r,discardUserPrefs:i}){const a=M(t)?"classic":"none",o=i?void 0:N(t);super({key:e,metric:t,histogramType:a,panelConfig:ut({type:(null==n?void 0:n.type)||ve(t,a),title:t,height:B.M,headerActions:({metric:e})=>[new be({metric:e})]},n,null==o?void 0:o.panelOptions),queryConfig:ut({resolution:ye.MEDIUM,labelMatchers:[],addIgnoreUsageFilter:!0},r,null==o?void 0:o.queryOptions),body:void 0}),this.addActivationHandler(()=>{this.onActivate(Boolean((null==n?void 0:n.type)||(null==o?void 0:o.panelOptions.type)))})}}function mt(e,t){return{container:m.css`
      width: 100%;
      height: ${t}px;
    `}}ct(pt,"Component",({model:e})=>{const{body:t,panelConfig:n}=e.useState(),r=(0,f.useStyles2)(mt,n.height);return b().createElement("div",{className:r.container,"data-testid":"gmd-vizpanel"},t&&b().createElement(t.Component,{model:t}))});var ht=n(6503);class ft extends r.BusEventWithPayload{}function gt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(ft,"type","configure-panel");class bt extends a.Bs{constructor({metric:e,disabled:t}){var n;const r=j.x.getItem(C.V.METRIC_PREFS)||{};super({metric:e,disabled:void 0!==t&&t,isAlreadyConfigured:Boolean(null===(n=r[e])||void 0===n?void 0:n.config)}),gt(this,"onClick",()=>{this.publishEvent(new ft({metric:this.state.metric}),!0)})}}gt(bt,"Component",({model:e})=>{const t=(0,f.useStyles2)(yt),{isAlreadyConfigured:n,disabled:r}=e.useState(),i=n?"Reconfigure Prometheus function":"Configure Prometheus function";return b().createElement(f.Button,{className:(0,m.cx)(t.selectButton,n&&t.active),"aria-label":i,variant:"secondary",size:"sm",fill:"text",onClick:e.onClick,icon:"cog",tooltip:i,tooltipPlacement:"top",disabled:r,"data-testid":"configure-panel"})});const yt=e=>({selectButton:m.css`
    margin: 0;
    padding: 0;
  `,active:m.css`
    color: ${e.colors.text.maxContrast};
  `});function vt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var wt=function(e){return e.GRID="grid",e.ROWS="rows",e.SINGLE="single",e}({});class St extends a.Bs{getUrlState(){return{[this.state.urlSearchParamName]:this.state.layout}}updateFromUrl(e){const t={},n=e[this.state.urlSearchParamName];n!==this.state.layout&&(t.layout=this.state.options.find(e=>e.value===n)?n:St.DEFAULT_LAYOUT),this.setState(t)}constructor({urlSearchParamName:e,options:t}){super({key:"layout-switcher",urlSearchParamName:e||"layout",options:t||St.DEFAULT_OPTIONS,layout:St.DEFAULT_LAYOUT}),vt(this,"_urlSync",new a.So(this,{keys:[this.state.urlSearchParamName]})),vt(this,"onChange",e=>{(0,k.z)("layout_changed",{layout:e}),this.setState({layout:e})})}}function Ot(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}vt(St,"DEFAULT_OPTIONS",[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]),vt(St,"DEFAULT_LAYOUT","grid"),vt(St,"Component",({model:e})=>{const{options:t,layout:n}=e.useState();return b().createElement(f.RadioButtonGroup,{"aria-label":"Layout switcher",options:t,value:n,onChange:e.onChange,fullWidth:!1})});class Et extends a.Bs{performRepeat(){var e,t;if(this._variableDependency.hasDependencyInLoadingState())return void this.setState({loadingLayout:null===(e=(t=this.state).getLayoutLoading)||void 0===e?void 0:e.call(t),errorLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const n=a.jh.lookupVariable(this.state.variableName,this);if(!(n instanceof a.n8)){const e=new Error("SceneByVariableRepeater: variable is not a MultiValueVariable!");return void o.v.error(e)}var r,i;if(n.state.error)return void this.setState({errorLayout:null===(r=(i=this.state).getLayoutError)||void 0===r?void 0:r.call(i,n.state.error),loadingLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const s=xt(n);var l,c;if(!s.length)return void this.setState({emptyLayout:null===(l=(c=this.state).getLayoutEmpty)||void 0===l?void 0:l.call(c),errorLayout:void 0,loadingLayout:void 0,currentBatchSize:0});this.setState({loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,currentBatchSize:this.state.initialPageSize});const u=s.slice(0,this.state.initialPageSize).map((e,t)=>this.state.getLayoutChild(e,t,s)).filter(Boolean);this.state.body.setState({children:u})}increaseBatchSize(){const e=xt(a.jh.lookupVariable(this.state.variableName,this)),t=this.state.currentBatchSize+this.state.pageSizeIncrement,n=e.slice(this.state.currentBatchSize,t).map((t,n)=>this.state.getLayoutChild(t,this.state.currentBatchSize+n,e)).filter(Boolean);this.state.body.setState({children:[...this.state.body.state.children,...n]}),this.setState({currentBatchSize:t})}useSizes(){const{currentBatchSize:e,pageSizeIncrement:t}=this.useState(),n=a.jh.lookupVariable(this.state.variableName,this).state.options.length,r=n-e;return{increment:r<t?r:t,current:e,total:n}}constructor({variableName:e,body:t,getLayoutChild:n,getLayoutLoading:r,getLayoutError:i,getLayoutEmpty:o,initialPageSize:s,pageSizeIncrement:l}){super({variableName:e,body:t,getLayoutChild:n,getLayoutLoading:r,getLayoutError:i,getLayoutEmpty:o,currentBatchSize:0,initialPageSize:s||6,pageSizeIncrement:l||9,loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0}),Ot(this,"_variableDependency",new a.Sh(this,{variableNames:[this.state.variableName],onVariableUpdateCompleted:()=>this.performRepeat()})),this.addActivationHandler(()=>this.performRepeat())}}function xt(e){const{value:t,text:n,options:r}=e.state;return e.hasAllValue()?r:Array.isArray(t)&&Array.isArray(n)?t.map((e,t)=>({value:e,label:n[t]})):[{value:t,label:n}]}function Pt({label:e,batchSizes:t,onClick:n,tooltip:r}){return b().createElement(f.Button,{variant:"secondary",fill:"outline",onClick:n,tooltip:r,tooltipPlacement:"top"},"Show ",t.increment," more ",1===t.increment?e:`${e}s`," (",t.current,"/",t.total,")")}function kt(e){return null!==e&&"adhoc"===(null==e?void 0:e.state.type)}function Ct(e){return null!==e&&"custom"===(null==e?void 0:e.state.type)}function jt(e){return null!==e&&"query"===(null==e?void 0:e.state.type)}Ot(Et,"Component",({model:e})=>{const{body:t,loadingLayout:n,errorLayout:r,emptyLayout:i}=e.useState();return n?b().createElement(n.Component,{model:n}):r?b().createElement(r.Component,{model:r}):i?b().createElement(i.Component,{model:i}):b().createElement(t.Component,{model:t})});var _t=n(4964);class Tt extends r.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Tt,"type","sort-by-changed");var It=n(3510);function Dt(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}const At={showSuccessAlert:!1,showErrorAlert:!1};function Lt(){return(e=function*(){try{return function(e){const t={};for(const n in e)t[n]={usageType:"alerting-usage",count:e[n]};return t}(function(e){const t={},n=e.filter(e=>(null==e?void 0:e.data.length)>0);for(const e of n){const n=e.data.filter(e=>{var t;return"string"==typeof(null===(t=e.model)||void 0===t?void 0:t.expr)&&"__expr__"!==e.datasourceUid});for(const r of n)try{const e=(0,It.M)(r.model.expr);for(const n of e)t[n]=(t[n]||0)+1}catch(t){o.v.warn(t,{message:`Failed to parse PromQL expression in alert rule ${e.title}`})}}return t}(yield(0,i.getBackendSrv)().get("/api/v1/provisioning/alert-rules",void 0,"grafana-metricsdrilldown-app-alert-rule-metric-usage",At)))}catch(e){const t="string"==typeof e?new Error(e):e;return o.v.error(t,{message:"Failed to fetch alerting rules"}),{}}},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Dt(a,r,i,o,s,"next",e)}function s(e){Dt(a,r,i,o,s,"throw",e)}o(void 0)})})();var e}var Nt=n(7348),Bt=n(7476);function Mt(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function $t(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Mt(a,r,i,o,s,"next",e)}function s(e){Mt(a,r,i,o,s,"throw",e)}o(void 0)})}}function Rt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ft(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const Vt={showSuccessAlert:!1,showErrorAlert:!1},qt=new Map,Ht=(0,Nt.g)((e,t,n)=>$t(function*(){let r=qt.get(e);return r||(r=(0,i.getBackendSrv)().get(`/api/dashboards/uid/${e}`,void 0,`grafana-metricsdrilldown-app-dashboard-metric-usage-${e}`,Vt).then(({dashboard:e})=>Ft(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Rt(e,t,n[t])})}return e}({},e),{url:t})).catch(t=>(n<=5&&o.v.error(t,{dashboardUid:e}),n++,Promise.resolve(null))).finally(()=>{qt.delete(e)}),qt.set(e,r)),r})(),{concurrency:50});function zt(){return $t(function*(){try{const e=yield(0,i.getBackendSrv)().get("/api/search",{type:"dash-db",limit:500},"grafana-metricsdrilldown-app-dashboard-search",Vt);let t=0;return yield Promise.all(e.map(({uid:e,url:n})=>Ht(e,n,t))).then(Gt)}catch(e){const t="string"==typeof e?new Error(e):e;return o.v.error(t,{message:"Failed to fetch dashboard metrics"}),{}}})()}function Ut(e,t,n,r,i){var a;(i[e]||(i[e]={usageType:"dashboard-usage",count:0,dashboards:{}}),i[e].count++,"dashboard-usage"===i[e].usageType)&&(i[e].dashboards[t]={count:((null===(a=i[e].dashboards[t])||void 0===a?void 0:a.count)||0)+1,uid:n||"unknown",url:r})}function Gt(e){const t={},n=e.filter(e=>{var t;return e&&(null==e||null===(t=e.panels)||void 0===t?void 0:t.length)});for(const e of n){const n=e.title||`Dashboard ${e.uid}`,r=e.panels.filter(e=>{var t;return(0,Bt.aQ)(e.datasource)&&"targets"in e&&(null===(t=e.targets)||void 0===t?void 0:t.length)});for(const i of r)for(const r of i.targets){const i="string"==typeof r.expr?r.expr:"",a=(0,It.M)(i);for(const r of a)Ut(r,n,e.uid||"unknown",e.url,t)}}return t}class Kt{getUsageMetrics(e){return this._usageState[e].metrics&&Object.keys(this._usageState[e].metrics).length>0?Promise.resolve(this._usageState[e].metrics):(this._usageState[e].metricsPromise||(this._usageState[e].metricsPromise=this._usageState[e].fetcher().then(t=>(this._usageState[e].metrics=t,this._usageState[e].metricsPromise=void 0,t))),this._usageState[e].metricsPromise)}getUsageForMetric(e,t){return this.getUsageMetrics(t).then(t=>{var n,r;return null!==(r=null===(n=t[e])||void 0===n?void 0:n.count)&&void 0!==r?r:0})}getUsageDetailsForMetric(e,t){return this.getUsageMetrics(t).then(n=>{var r;return null!==(r=n[e])&&void 0!==r?r:"dashboard-usage"===t?{usageType:"dashboard-usage",count:0,dashboards:{}}:{usageType:"alerting-usage",count:0}})}constructor(){!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(this,"_usageState",{"dashboard-usage":{metrics:{},metricsPromise:void 0,fetcher:zt},"alerting-usage":{metrics:{},metricsPromise:void 0,fetcher:Lt}})}}function Wt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Qt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Wt(e,t,n[t])})}return e}function Yt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Xt(){try{const e=j.x.getItem(C.V.RECENT_METRICS)||[];if(!e.length)return[];const t=Date.now()-2592e6,n=e.filter(e=>e.timestamp>t);return n.length!==e.length&&j.x.setItem(C.V.RECENT_METRICS,n),n}catch(e){return o.v.error(e,{message:"Failed to get recent metrics:"}),[]}}const Jt=[{label:"Default",value:"default"},{label:"Dashboard Usage",value:"dashboard-usage"},{label:"Alerting Usage",value:"alerting-usage"}],Zt="metrics-reducer-sort-by";class en extends a.Bs{activationHandler(){const e=a.jh.getVariables(this).getByName(Zt);this.supportedSortByOptions.has(e.getValue())||e.changeValueTo("default"),this._subs.add(e.subscribeToState((e,t)=>{e.value!==t.value&&this.publishEvent(new Tt({sortBy:e.value}),!0)}))}getUsageDetailsForMetric(e,t){return this.usageFetcher.getUsageDetailsForMetric(e,t)}getUsageMetrics(e){return this.usageFetcher.getUsageMetrics(e).then(e=>{const t={};for(const n in e)t[n]=e[n].count;return t})}constructor(e){super(Yt(Qt({},e),{key:"metrics-sorter",$variables:new a.Pj({variables:[new a.yP({name:Zt,label:"Sort by",value:"default",query:Jt.map(e=>`${e.label} : ${e.value}`).join(","),description:"Sort metrics by default (alphabetically, with recently-selected metrics first), by prevalence in dashboard panel queries, or by prevalence in alerting rules"})]}),inputControls:new a.K8({layout:"horizontal"})})),Wt(this,"initialized",!1),Wt(this,"supportedSortByOptions",new Set(["default","dashboard-usage","alerting-usage"])),Wt(this,"usageFetcher",new Kt),this.addActivationHandler(()=>this.activationHandler())}}function tn(e){const t=Xt().map(e=>e.name),n=new Set(t),[r,i]=e.reduce(([e,t],r)=>(n.has(r)?e.push(r):t.push(r),[e,t]),[[],[]]),a=function(e){return[...e].sort((e,t)=>(0,_t._)(e,t))}(i);return[...t.filter(e=>r.includes(e)),...a]}Wt(en,"Component",({model:e})=>{const{inputControls:t}=e.useState();return b().createElement("div",{"data-testid":"sort-by-select"},b().createElement(t.Component,{model:t}))});class nn extends a.mI{onActivate(){this.setState({skipUrlSync:!1}),this.subscribeToState((e,t)=>{e.value&&e.value!==t.value&&j.x.setItem(C.V.DATASOURCE,e.value)})}static getCurrentDataSource(){const e=Object.values(i.config.datasources).filter(e=>(0,Bt.aQ)(e)),t=new URL(window.location.href).searchParams.get(`var-${se}`),n=j.x.getItem(C.V.DATASOURCE),r=e.find(e=>e.uid===t)||e.find(e=>e.uid===n)||e.find(e=>e.isDefault)||e[0];return r?r.uid:(o.v.warn("Cannot find any Prometheus data source!"),"no-data-source-configured")}constructor({initialDS:e}){super({key:se,name:se,pluginId:"prometheus",label:"Data source",description:"Only prometheus data sources are supported",skipUrlSync:!e,value:e||nn.getCurrentDataSource()}),this.addActivationHandler(this.onActivate.bind(this))}}const rn=(e,t)=>e.length===t.length&&(0,y.isEqual)(e,t);function an(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function on(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const sn="metrics-wingman";class ln extends a.fS{constructor(e){super(on(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){an(e,t,n[t])})}return e}({key:sn,name:sn,label:"Metrics"},e),{datasource:pe,query:`label_values({$${ie}}, __name__)`,includeAll:!0,value:"$__all",skipUrlSync:!0,refresh:r.VariableRefresh.onTimeRangeChanged,sort:r.VariableSort.alphabeticalAsc,hide:r.VariableHide.hideVariable}))}}const cn={"11.6.x":function(e){const t=e.languageProvider;return"function"==typeof t.fetchLabelValues&&1===t.fetchLabelValues.length},"12.0.0":function(e){const t=e.languageProvider;return"function"==typeof t.fetchLabelValues&&t.fetchLabelValues.length>1},"12.1.0-plus":function(e){return"function"==typeof e.languageProvider.queryLabelKeys}};function un(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function dn(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){un(a,r,i,o,s,"next",e)}function s(e){un(a,r,i,o,s,"throw",e)}o(void 0)})}}function pn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class mn{getRuntimeDatasource(){return dn(function*(){if(!this.datasource){const e=yield(0,i.getDataSourceSrv)().get(le,{__sceneObject:{value:this.trail}});this.datasource=(0,Bt.aQ)(e)?e:void 0}return this.datasource}).call(this)}init(){this.reset();for(const e of this.subs)e.unsubscribe();this.subs=[];const e=a.jh.findByKeyAndType(this.trail,sn,ln);this.subs.push(e.subscribeToState((e,t)=>{rn(e.options,t.options)||this.onNewMetrics(e.options)}));const t=a.jh.findByKeyAndType(this.trail,se,nn);this.subs.push(t.subscribeToState((e,t)=>dn(function*(){e.value!==t.value&&this.reset()}).call(this))),this.onNewMetrics(e.state.options)}reset(){this.datasource=void 0,this.cache={metadata:new Map,classicHistograms:new Set},this.fetchMetricsMetadata().catch(()=>{})}onNewMetrics(e){for(const t of e){const e=t.value;M(e)&&this.cache.classicHistograms.add(e)}}isNativeHistogram(e){return dn(function*(){if(this.cache.classicHistograms.has(e))return!1;if(this.cache.classicHistograms.has(`${e}_bucket`))return!0;try{const t=yield this.getMetadataForMetric(e);return"histogram"===(null==t?void 0:t.type)}catch(t){return(0,_.HA)([`Error while fetching ${e} metadata!`,t.toString()]),!1}}).call(this)}getMetadataForMetric(e){return dn(function*(){var t;if(this.cache.metadata.has(e))return this.cache.metadata.get(e);const n=yield this.getRuntimeDatasource();if(!n)return;const r=null===(t=(yield n.languageProvider.request(`/api/v1/metadata?metric=${e}`))[e])||void 0===t?void 0:t[0];return r&&this.cache.metadata.set(e,r),r}).call(this)}fetchMetricsMetadata(){return dn(function*(){const e=yield this.getRuntimeDatasource();if(!e)return;const t=function(e){if(cn["12.1.0-plus"](e))return e.languageProvider.queryMetricsMetadata;if(cn["12.0.0"](e)||cn["11.6.x"](e))return()=>Promise.resolve(e.languageProvider.metricsMetadata);throw new Error("Unsupported language provider version")}(e);let n=yield t();if(!n){const r=function(e,t){if(cn["12.1.0-plus"](e))return e.languageProvider.retrieveMetricsMetadata;if(cn["12.0.0"](e)||cn["11.6.x"](e))return()=>{var n,r,i;return(null!==(i=null===(n=(r=e.languageProvider).loadMetricsMetadata)||void 0===n?void 0:n.call(r))&&void 0!==i?i:Promise.resolve()).then(()=>t())};throw new Error("Unsupported language provider version")}(e,t);n=yield r()}if(n)for(const[e,t]of Object.entries(n))this.cache.metadata.set(e,t)}).call(this)}getTagKeys(e){return dn(function*(){const t=yield this.getRuntimeDatasource();if(!t)return[];return yield t.getTagKeys(e)}).call(this)}getTagValues(e){return dn(function*(){const t=yield this.getRuntimeDatasource();if(!t)return[];e.key=function(e){if(""===e||!function(e){return/^".*"$/.test(e)}(e))return e;return e.slice(1,-1)}(e.key);return yield t.getTagValues(e)}).call(this)}static fetchLabels(e){const{timeRange:t,matcher:n}=e,r=e.ds;if(cn["12.1.0-plus"](r))return r.languageProvider.queryLabelKeys(t,n);if(cn["12.0.0"](r))return r.languageProvider.fetchLabelsWithMatch(t,n).then(e=>Object.keys(e));if(cn["11.6.x"](r))return r.languageProvider.fetchLabelsWithMatch(n).then(e=>Object.keys(e));throw new Error("Unsupported language provider version")}static fetchLabelValues(e){const{labelName:t,timeRange:n,matcher:r=""}=e,i=e.ds;if(cn["12.1.0-plus"](i))return i.languageProvider.queryLabelValues(n,t,r);if(cn["12.0.0"](i)){return(r?i.languageProvider.fetchSeriesValuesWithMatch:i.languageProvider.fetchLabelValues)(n,t,r)}if(cn["11.6.x"](i)){return(r?i.languageProvider.fetchSeriesValuesWithMatch:i.languageProvider.fetchLabelValues)(t,r)}throw new Error("Unsupported language provider version")}static getPrometheusDataSourceForScene(e){return dn(function*(){try{const n=a.jh.findByKey(e,se);var t;const r=null!==(t=null==n?void 0:n.state.value)&&void 0!==t?t:"";return yield(0,i.getDataSourceSrv)().get({uid:r})}catch(e){return void(0,_.jx)(e,["Error while getting the Prometheus data source!"])}})()}getPrometheusBuildInfo(){return dn(function*(){const e=yield this.getRuntimeDatasource();if(!e)return;const t=yield e.languageProvider.request("/api/v1/status/buildinfo");return t.application||(t.application="Prometheus",t.repository="https://github.com/prometheus/prometheus"),t.buildDate&&(t.buildDate=t.buildDate.replace(/(\d{4})(\d{2})(\d{2})(.+)/,"$1-$2-$3")),t}).call(this)}constructor(e){pn(this,"trail",void 0),pn(this,"datasource",void 0),pn(this,"cache",{metadata:new Map,classicHistograms:new Set}),pn(this,"subs",[]),this.trail=e}}function hn(e){if(!e)return;const{type:t,help:n,unit:r}=e;return[n,t&&`**Type:** *${t}*`,r&&`**Unit:** ${r}`].join("\n\n")}function fn(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function gn(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){fn(a,r,i,o,s,"next",e)}function s(e){fn(a,r,i,o,s,"throw",e)}o(void 0)})}}const bn="(none)";class yn extends a.UU{query(){return gn(function*(){return{state:r.LoadingState.Done,data:[{name:"Labels",fields:[{name:null,type:r.FieldType.other,values:[],config:{}}],length:0}]}})()}metricFindQuery(e,t){return gn(function*(){var n,r;const i=null===(r=t.scopedVars)||void 0===r||null===(n=r.__sceneObject)||void 0===n?void 0:n.valueOf(),a=yield mn.getPrometheusDataSourceForScene(i);if(!a)return[];var o;const[,s]=null!==(o=e.match(/valuesOf\((.+)\)/))&&void 0!==o?o:[];if(s){return(yield yn.fetchLabelValues(s,i)).map(e=>({value:e,text:e}))}let l=[];try{l=yield this.fetchLabels(a,i,e)}catch(e){(0,_.HA)(["Error while fetching labels! Defaulting to an empty array.",e.toString()])}return[{value:bn,text:"(none)"},...l]}).call(this)}fetchLabels(e,t,n){return gn(function*(){if(!yn.getLabelsMatchAPISupport(e)){const n=yn.getFiltersFromVariable(t),r=yield e.getTagKeys(n);return this.processLabelOptions(r.map(({text:e})=>({value:e,text:e})))}const r=yield mn.fetchLabels({ds:e,timeRange:a.jh.getTimeRange(t).state.value,matcher:n});return this.processLabelOptions(r.map(e=>({value:e,text:e})))}).call(this)}static getLabelsMatchAPISupport(e){try{return e.hasLabelsMatchAPISupport()}catch(e){return(0,_.HA)(["Error while checking if the current data source supports the labels match API! Defaulting to false.",e.toString()]),!1}}static getFiltersFromVariable(e){const t=a.jh.lookupVariable(ie,e);return kt(t)?{filters:t.state.filters}:{filters:[]}}processLabelOptions(e){return e.filter(({value:e})=>!e.startsWith("__")).sort((e,t)=>(0,_t._)(e.value,t.value))}static fetchLabelValues(e,t){return gn(function*(){const n=yield mn.getPrometheusDataSourceForScene(t);if(!n)return[];try{return yield mn.fetchLabelValues({ds:n,labelName:e,timeRange:a.jh.getTimeRange(t).state.value})}catch(t){return(0,_.HA)([`Error while retrieving label "${e}" values! Defaulting to an empty array.`,t.toString()]),[]}})()}testDatasource(){return gn(function*(){return{status:"success",message:"OK"}})()}constructor(){super(yn.uid,yn.uid)}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(yn,"uid","grafana-prometheus-labels-datasource");const vn="wingmanLabelValues";class wn extends a.fS{constructor({labelName:e}){super({name:vn,datasource:{uid:yn.uid},query:`valuesOf(${e})`,isMulti:!1,allowCustomValue:!1,refresh:r.VariableRefresh.onTimeRangeChanged,hide:r.VariableHide.hideVariable,value:"$__all",includeAll:!0})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(wn,"Component",()=>b().createElement(b().Fragment,null));const Sn="labelsWingman";class On extends a.fS{onActivate(){this.subscribeToState((e,t)=>{e.query!==t.query&&(t.query&&this.setState({value:bn}),this.refreshOptions())}),this._subs.add(a.jh.findByKeyAndType(this,se,a.mI).subscribeToState((e,t)=>{e.value!==t.value&&(this.setState({value:bn}),this.refreshOptions())})),this._subs.add(a.jh.findByKeyAndType(this,ie,a.H9).subscribeToState((e,t)=>{e.filterExpression!==t.filterExpression&&this.updateQuery()})),this.updateQuery()}updateQuery(){const e=a.jh.interpolate(this,ae,{});this.setState({query:`{__name__=~".+",${e}}`})}constructor(){super({name:Sn,label:"Group by label",placeholder:"Group by label...",datasource:{uid:yn.uid},query:"",includeAll:!1,isMulti:!1,allowCustomValue:!1,refresh:r.VariableRefresh.onTimeRangeChanged,hide:r.VariableHide.hideVariable}),this.addActivationHandler(this.onActivate.bind(this))}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(On,"Component",({model:e})=>{const t=(0,f.useStyles2)(En),{label:n}=e.useState();return b().createElement("div",{className:t.container},b().createElement(f.Label,{className:t.label},n),b().createElement(a.fS.Component,{model:e}))});const En=e=>({container:m.css`
    display: flex;
    align-items: center;
    gap: 0;

    [class*='input-wrapper'] {
      width: 240px;
    }
  `,label:m.css`
    height: 32px;
    white-space: nowrap;
    margin: 0;
    background-color: ${e.colors.background.primary};
    padding: ${e.spacing(1)};
    border-radius: ${e.shape.radius.default};
    border: 1px solid ${e.colors.border.weak};
    border-right: none;
  `});function xn(){return b().createElement("svg",{stroke:"currentColor",width:"17",height:"16",viewBox:"0 0 17 16",fill:"none"},b().createElement("circle",{cx:"8.92688",cy:"3.63132",r:"2.375",strokeWidth:"1.5"}),b().createElement("path",{d:"M13.6469 4.37965C14.6813 4.76699 15.3235 7.03139 14.9362 8.06582",strokeWidth:"1.5"}),b().createElement("path",{d:"M4.35309 4.37965C3.31866 4.76699 2.67651 7.03139 3.06384 8.06582",strokeWidth:"1.5"}),b().createElement("path",{d:"M10.3408 14.2531C9.75237 14.8415 8.11813 14.7799 7.50903 14.1708",strokeWidth:"1.5"}),b().createElement("circle",{cx:"4.00195",cy:"12.251",r:"2.375",strokeWidth:"1.5"}),b().createElement("circle",{cx:"13.8478",cy:"12.251",r:"2.375",strokeWidth:"1.5"}))}class Pn extends r.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Pn,"type","metrics-variable-activated");class kn extends r.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(kn,"type","metrics-variable-deactivated");class Cn extends r.BusEventWithPayload{}function jn(e){const t=e.state.key;if(!t)throw new TypeError(`Variable "${e.state.name}" has no key. Please provide a key in order to publish its lifecycle events.`);return e.addActivationHandler(()=>{e.publishEvent(new Pn({key:t}),!0),!e.state.loading&&e.state.options.length&&e.publishEvent(new Cn({key:t,options:e.state.options}),!0);const n=e.subscribeToState((n,r)=>{!n.loading&&r.loading&&e.publishEvent(new Cn({key:t,options:n.options}),!0)});return()=>{n.unsubscribe(),e.publishEvent(new kn({key:t}),!0)}}),e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Cn,"type","metrics-variable-loaded");function _n(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Tn(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){_n(a,r,i,o,s,"next",e)}function s(e){_n(a,r,i,o,s,"throw",e)}o(void 0)})}}class In extends a.UU{query(){return Tn(function*(){return{state:r.LoadingState.Done,data:[{name:"Labels",fields:[{name:null,type:r.FieldType.other,values:[],config:{}}],length:0}]}})()}metricFindQuery(e,t){return Tn(function*(){var n,r;const i=null===(r=t.scopedVars)||void 0===r||null===(n=r.__sceneObject)||void 0===n?void 0:n.valueOf(),o=yield mn.getPrometheusDataSourceForScene(i);if(!o)return[];const s=a.jh.getTimeRange(i).state.value;let l=[];const c=e.startsWith("removeRules"),u=c?e.replace("removeRules",""):e;return l=yield mn.fetchLabelValues({ds:o,labelName:"__name__",matcher:u,timeRange:s}),c&&(l=l.filter(e=>!(e=>"ALERTS"===e||"ALERTS_FOR_STATE"===e||e.includes(":"))(e))),l.map(e=>({value:e,text:e}))})()}testDatasource(){return Tn(function*(){return{status:"success",message:"OK"}})()}constructor(){super(In.uid,In.uid)}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(In,"uid","grafana-prometheus-metrics-with-label-values-datasource");const Dn="metrics-with-label-value";class An extends a.fS{onActivate(e,t,n){const i=a.jh.lookupVariable(ie,this);(null==i?void 0:i.state.hide)!==r.VariableHide.hideVariable&&this.setState({query:An.buildQuery(e,t,n)})}static buildQuery(e,t,n){return n?`removeRules{${e}="${t}",${ae}}`:`{${e}="${t}",${ae}}`}constructor({labelName:e,labelValue:t,removeRules:n}){return super({key:`${Dn}-${e}-${t}`,name:Dn,datasource:{uid:In.uid},query:An.buildQuery(e,t,n),isMulti:!1,allowCustomValue:!1,refresh:r.VariableRefresh.onTimeRangeChanged,hide:r.VariableHide.hideVariable,skipUrlSync:!0,value:"$__all",includeAll:!0}),this.addActivationHandler(this.onActivate.bind(this,e,t,n)),jn(this)}}class Ln extends a.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=a.jh.findByKeyAndType(this,"layout-switcher",St),t=this.state.body.state.body,n=(e,n)=>{e.layout!==(null==n?void 0:n.layout)&&t.setState({templateColumns:e.layout===wt.ROWS?ui:ci})};n(e.state),this._subs.add(e.subscribeToState(n))}constructor({index:e,labelName:t,labelValue:n,labelCardinality:i}){super({index:e,labelName:t,labelValue:n,labelCardinality:i,key:`${t||""}-${n||""}`,$variables:new a.Pj({variables:[new An({labelName:t,labelValue:n})]}),body:new Et({variableName:Dn,initialPageSize:3,body:new a.gF({children:[],isLazy:!0,templateColumns:ci,autoRows:si,$behaviors:[new a.Gg.K2({key:"metricCrosshairSync",sync:r.DashboardCursorSync.Crosshair})]}),getLayoutLoading:()=>new a.dM({reactNode:b().createElement(f.Spinner,{inline:!0})}),getLayoutEmpty:()=>new a.dM({reactNode:b().createElement(ht._,{title:"",severity:"info"},"No metrics found for the current filters and time range.")}),getLayoutError:e=>new a.dM({reactNode:b().createElement(ht._,{severity:"error",title:"Error while loading metrics!",error:e})}),getLayoutChild:(e,r)=>{const i=e.value;return new a.xK({body:new li({metric:i,vizPanelInGridItem:new pt({metric:i,panelOptions:{fixedColorIndex:r,headerActions:()=>[new be({metric:i}),new bt({metric:i})]},queryOptions:{labelMatchers:[{key:t,operator:"=",value:n}]}})})})}})}),this.addActivationHandler(this.onActivate.bind(this))}}function Nn(e){return{container:(0,m.css)({background:e.colors.background.canvas,margin:e.spacing(1,0,0,0),"& div:focus-within":{boxShadow:"none !important"}}),containerHeader:(0,m.css)({display:"flex",alignItems:"center",gap:"8px",marginBottom:"-36px",paddingBottom:e.spacing(1.5),borderBottom:`1px solid ${e.colors.border.medium}`}),headerButtons:(0,m.css)({position:"relative",top:"3px",marginLeft:"auto",marginRight:"30px",zIndex:100}),selectButton:(0,m.css)({height:"28px"}),collapsableSectionBody:(0,m.css)({display:"flex",flexDirection:"column",gap:"24px",padding:e.spacing(1)}),groupName:(0,m.css)({display:"flex",alignItems:"center",fontSize:"1.3rem",lineHeight:"1.3rem"}),labelValue:(0,m.css)({fontSize:"16px",marginLeft:"8px"}),index:(0,m.css)({fontSize:"12px",color:e.colors.text.secondary,marginLeft:"8px"}),footer:(0,m.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(1),"& button":{height:"40px"}}),variable:(0,m.css)({display:"none"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Ln,"Component",({model:e})=>{const[t,n]=(0,g.useState)(!1),r=(0,f.useStyles2)(Nn),{index:i,labelName:o,labelValue:s,labelCardinality:l,$variables:c,body:u}=e.useState(),d=c.state.variables[0],{loading:p,error:m}=d.useState(),h=u.useSizes(),y=!p&&!m&&h.total>0&&h.current<h.total;return b().createElement("div",{className:r.container,"data-testid":`${o}-${s}-metrics-group`},b().createElement("div",{className:r.containerHeader},b().createElement("div",{className:r.headerButtons},b().createElement(f.Button,{className:r.selectButton,variant:"secondary",onClick:()=>{var t;const n=a.jh.lookupVariable(ie,e);n.setState({filters:[...n.state.filters,{key:o,operator:"=",value:s}]}),null===(t=a.jh.lookupVariable(Sn,e))||void 0===t||t.changeValueTo(bn)},tooltip:`See metrics with ${o}=${s}`,tooltipPlacement:"top"},"Select"))),b().createElement(f.CollapsableSection,{isOpen:!t,onToggle:()=>n(!t),label:b().createElement("div",{className:r.groupName},b().createElement(xn,null),b().createElement("div",{className:r.labelValue},s),l>1&&b().createElement("div",{className:r.index},"(",i+1,"/",l,")"))},b().createElement("div",{className:r.collapsableSectionBody},b().createElement(u.Component,{model:u})),y&&b().createElement("div",{className:r.footer},b().createElement(Pt,{label:"metric",batchSizes:h,onClick:()=>{u.increaseBatchSize()},tooltip:`Show more metrics for ${o}="${s}"`}))),b().createElement("div",{className:r.variable},b().createElement(d.Component,{key:d.state.name,model:d})))});class Bn extends a.Bs{constructor({labelName:e}){super({key:"metrics-group-list",labelName:e,$variables:new a.Pj({variables:[new wn({labelName:e})]}),body:new Et({variableName:vn,initialPageSize:20,pageSizeIncrement:10,body:new a.gF({children:[],isLazy:!0,templateColumns:"1fr",autoRows:"auto",rowGap:1}),getLayoutLoading:()=>new a.dM({reactNode:b().createElement(f.Spinner,{inline:!0})}),getLayoutEmpty:()=>new a.dM({reactNode:b().createElement(ht._,{title:"",severity:"info"},'No label values found for label "',e,'".')}),getLayoutError:t=>new a.dM({reactNode:b().createElement(ht._,{severity:"error",title:`Error while loading label "${e}" values!`,error:t})}),getLayoutChild:(t,n,r)=>new a.xK({body:new Ln({index:n,labelName:e,labelValue:t.value,labelCardinality:r.length})})})})}}function Mn(e){return{footer:(0,m.css)({display:"flex",justifyContent:"center",alignItems:"center",margin:e.spacing(3,0,1,0),"& button":{height:"40px"}}),variable:(0,m.css)({display:"none"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Bn,"Component",({model:e})=>{const t=(0,f.useStyles2)(Mn),{body:n,$variables:r,labelName:i}=e.useState(),a=r.state.variables[0],{loading:o,error:s}=a.useState(),l=n.useSizes(),c=!o&&!s&&l.total>0&&l.current<l.total;return b().createElement("div",{"data-testid":"metrics-groupby-list"},b().createElement(n.Component,{model:n}),c&&b().createElement("div",{className:t.footer},b().createElement(Pt,{label:`"${i}" value`,batchSizes:l,onClick:()=>{n.increaseBatchSize()}})),b().createElement("div",{className:t.variable},b().createElement(a.Component,{key:a.state.name,model:a})))});const $n="filtered-metrics-wingman";class Rn extends a.yP{onActivate(){const e=a.jh.findByKeyAndType(this,sn,ln),{loading:t,error:n,options:r}=e.state;this.setState({loading:t,error:n,options:r}),this._subs.add(e.subscribeToState(e=>{this.setState({loading:e.loading,error:e.error,options:e.options})}))}constructor(){return super({key:$n,name:$n,label:"Filtered Metrics",loading:!1,error:null,options:[],includeAll:!0,value:"$__all",skipUrlSync:!0}),this.addActivationHandler(this.onActivate.bind(this)),jn(this)}}function Fn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Vn(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class qn extends a.Bs{useCounts(){return this.useState().counts}constructor(e){super(Vn(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Fn(e,t,n[t])})}return e}({},e),{counts:{current:0,total:0}}))}}class Hn extends qn{onActivate(){const e=a.jh.lookupVariable(sn,this),t=a.jh.lookupVariable($n,this);this.setInitCounts(e,t),this._subs.add(e.subscribeToState((e,n)=>{rn(e.options,n.options)||this.setState({counts:{current:t.state.options.length,total:e.options.length}})})),this._subs.add(t.subscribeToState((t,n)=>{t.loading||n.loading||rn(t.options,n.options)||this.setState({counts:{current:t.options.length,total:e.state.options.length}})}))}setInitCounts(e,t){const n={current:0,total:0};!e.state.loading&&e.state.options.length&&(n.total=e.state.options.length),!t.state.loading&&t.state.options.length&&(n.current=t.state.options.length),this.setState({counts:n})}constructor(){super({key:"MetricVariableCountsProvider"}),this.addActivationHandler(this.onActivate.bind(this))}}class zn extends r.BusEventWithPayload{}function Un(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(zn,"type","quick-search-changed");class Gn extends a.Bs{getUrlState(){return{[this.state.urlSearchParamName]:this.state.value}}updateFromUrl(e){const t=e[this.state.urlSearchParamName]||"";t!==this.state.value&&this.setState({value:t})}toggleCountsDisplay(e){this.setState({displayCounts:e})}updateValue(e){""===this.state.value&&""!==e&&(0,k.z)("quick_search_used",{}),this.setState({value:e}),this.notifyValueChange(e)}useHumanFriendlyCountsMessage(){const{targetName:e,countsProvider:t,displayCounts:n}=this.state,r=t.useCounts();return n?r.current===r.total?{tagName:`${r.current}`,tooltipContent:1!==r.current?`${r.current} ${e}s in total`:`1 ${e} in total`}:{tagName:`${r.current}/${r.total}`,tooltipContent:1!==r.current?`${r.current} out of ${r.total} ${e}s in total`:`1 out of ${r.total} ${e}s in total`}:{tagName:"",tooltipContent:""}}constructor({urlSearchParamName:e,targetName:t,countsProvider:n,displayCounts:r}){super({key:"quick-search",urlSearchParamName:e,targetName:t,countsProvider:n,displayCounts:Boolean(r),value:""}),Un(this,"_variableDependency",new a.Sh(this,{variableNames:[se],onReferencedVariableValueChanged:()=>{this.setState({value:""})}})),Un(this,"_urlSync",new a.So(this,{keys:[this.state.urlSearchParamName]})),Un(this,"notifyValueChange",(0,y.debounce)(e=>{this.publishEvent(new zn({searchText:e}),!0)},250)),Un(this,"onChange",e=>{this.updateValue(e.currentTarget.value)}),Un(this,"clear",()=>{this.updateValue("")}),Un(this,"onKeyDown",e=>{"Escape"===e.key&&(e.preventDefault(),this.clear())})}}Un(Gn,"Component",({model:e})=>{const t=(0,f.useStyles2)(Kn),{targetName:n,value:r,countsProvider:i}=e.useState(),{tagName:a,tooltipContent:o}=e.useHumanFriendlyCountsMessage();return b().createElement(f.Input,{value:r,onChange:e.onChange,onKeyDown:e.onKeyDown,placeholder:`Quick search ${n}s`,prefix:b().createElement("i",{className:"fa fa-search"}),suffix:b().createElement(b().Fragment,null,b().createElement(i.Component,{model:i}),a&&b().createElement(f.Tooltip,{content:o,placement:"top"},b().createElement(f.Tag,{className:t.counts,name:a,colorIndex:9})),b().createElement(f.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:e.clear,disabled:!r}))})});const Kn=e=>({counts:m.css`
    margin-right: ${e.spacing(1)};
    border-radius: 11px;
    padding: 2px ${e.spacing(1)};
    color: ${e.colors.text.primary};
    background-color: ${e.colors.background.secondary};
  `});function Wn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Qn(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class Yn extends a.P1{constructor(e){super(Qn(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Wn(e,t,n[t])})}return e}({},e),{key:"list-controls",body:new a.G1({direction:"row",width:"100%",maxHeight:"32px",children:[new a.vA({body:new Gn({urlSearchParamName:"search_txt",targetName:"metric",countsProvider:new Hn})}),new a.vA({width:"auto",body:new en({})}),new a.vA({width:"auto",body:new St({})})]})}))}}function Xn(){return{headerWrapper:(0,m.css)({display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center"}}})}}function Jn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Wn(Yn,"Component",({model:e})=>{const t=(0,f.useStyles2)(Xn),{body:n}=e.useState();return b().createElement("div",{className:t.headerWrapper},b().createElement(n.Component,{model:n}))});class Zn{setInitOptions(e){this.initOptions=(0,y.cloneDeep)(e)}getFilters(){return this.filters}static getFilteredOptions(e,t){let n=e;return t.categories.length>0&&(n=Zn.applyCategoryFilters(n,t.categories)),t.prefixes.length>0&&(n=Zn.applyPrefixFilters(n,t.prefixes)),t.suffixes.length>0&&(n=Zn.applySuffixFilters(n,t.suffixes)),t.names.length>0&&(n=Zn.applyNameFilters(n,t.names)),n}applyFilters(e=this.filters,t={forceUpdate:!1,notify:!0}){const n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Jn(e,t,n[t])})}return e}({},this.filters,e);if(!t.forceUpdate&&(0,y.isEqual)(this.filters,n))return;if(!(n.categories.length||n.prefixes.length||n.suffixes.length||n.names.length))return this.filters=n,this.variable.setState({options:this.initOptions}),void(t.notify&&this.notifyUpdate());this.filters=n;const r=Zn.getFilteredOptions(this.initOptions,this.filters);this.variable.setState({options:r}),t.notify&&this.notifyUpdate()}static applyCategoryFilters(e,t){let n=[];for(const r of t){const t=Zn.buildRegex(r,"i");n=n.concat(e.filter(e=>t.test(e.value)))}return n}static applyPrefixFilters(e,t){const n=t.map(e=>e.includes("|")?`${e.split("|").map(e=>`^${e}([^a-z0-9]|$)`).join("|")}`:`^${e}([^a-z0-9]|$)`).join("|"),r=Zn.buildRegex(`(${n})`);return e.filter(e=>r.test(e.value))}static applySuffixFilters(e,t){const n=t.map(e=>e.includes("|")?`${e.split("|").map(e=>`(^|[^a-z0-9])${e}$`).join("|")}`:`(^|[^a-z0-9])${e}$`).join("|"),r=Zn.buildRegex(`(${n})`);return e.filter(e=>r.test(e.value))}static applyNameFilters(e,t){const[n]=t,r=n.split(",").map(e=>e.trim()).filter(Boolean).map(e=>{try{return new RegExp(e)}catch(e){return null}}).filter(Boolean);return e.filter(e=>r.some(t=>t.test(e.value)))}static buildRegex(e,t){try{return new RegExp(e,t)}catch(e){return new RegExp(".*")}}notifyUpdate(){this.variable.publishEvent(new a.oh(this.variable),!0)}constructor(e){Jn(this,"variable",void 0),Jn(this,"initOptions",[]),Jn(this,"filters",{categories:[],prefixes:[],suffixes:[],names:[]}),this.variable=e}}var er=n(902);const tr=new Map;function nr(e,t){let n=tr.get(e);n||(n=new Map,tr.set(e,n));let r=n.get(t);if(!r){const i=e.split("_"),a=i.slice(0,i.length/2).join("_");r={halfLeven:(0,er.A)(a,t)||0,wholeLeven:(0,er.A)(e,t)||0},n.set(t,r)}return r}function rr(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function ir(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){rr(a,r,i,o,s,"next",e)}function s(e){rr(a,r,i,o,s,"throw",e)}o(void 0)})}}function ar(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class or{sort(){return ir(function*(e=this.sortBy,t={}){const n=this.variable.state.options.map(e=>e.value);if(e===this.sortBy&&rn(n,this.lastMetrics))return;let r;switch(e){case"dashboard-usage":case"alerting-usage":r=yield this.sortByUsage(n,e);break;case"related":i=n,a=t.metric,r=i.sort((e,t)=>{const n=nr(e,a),r=nr(t,a);return n.halfLeven+n.wholeLeven-(r.halfLeven+r.wholeLeven)});break;default:r=tn(n)}var i,a;this.sortBy=e,this.lastMetrics=r,this.variable.setState({options:r.map(e=>({label:e,value:e}))}),this.notifyUpdate()}).apply(this,arguments)}sortByUsage(e,t){return ir(function*(){try{const n=a.jh.findByKeyAndType(this.variable,"metrics-sorter",en);if(!n)return o.v.warn("Metrics sorter not found. Returning unsorted metrics.",{usageType:t}),e;const r=yield n.getUsageMetrics(t);return function(e,t){return[...e].sort((e,n)=>{const r=t[e]||0,i=t[n]||0;return i!==r?i-r:(0,_t._)(e,n)})}(e,r)}catch(n){const r="string"==typeof n?new Error(n):n;return o.v.error(r,{usageType:t}),e}}).call(this)}notifyUpdate(){this.variable.publishEvent(new a.oh(this.variable),!0)}constructor(e){ar(this,"variable",void 0),ar(this,"lastMetrics",void 0),ar(this,"sortBy",void 0),this.variable=e,this.sortBy=void 0,this.lastMetrics=[]}}class sr extends r.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(sr,"type","filters-changed");const lr="Non-rules metrics",cr="Recording rules";class ur extends r.BusEventWithPayload{}function dr({title:e,description:t}){const n=(0,f.useStyles2)(pr);return b().createElement("h6",{className:n.title},b().createElement("span",null,e),b().createElement(f.Tooltip,{content:t,placement:"top"},b().createElement(f.Icon,{name:"info-circle",size:"sm",className:n.infoIcon})))}function pr(e){return{title:(0,m.css)({fontSize:"15px",fontWeight:e.typography.fontWeightLight,borderBottom:`1px solid ${e.colors.border.weak}`,paddingBottom:e.spacing(.5)}),infoIcon:(0,m.css)({marginLeft:e.spacing(1),cursor:"pointer",color:e.colors.text.secondary,position:"relative",top:"-4px"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(ur,"type","section-value-changed");const mr=({label:e,count:t,checked:n,onChange:r})=>{const i=(0,f.useStyles2)(hr);return b().createElement("div",{className:i.checkboxWrapper,title:e},b().createElement(f.Checkbox,{label:e,value:n,onChange:r}),b().createElement("span",{className:i.count},"(",t,")"))};function hr(e){return{checkboxWrapper:(0,m.css)({display:"flex",alignItems:"center",width:"100%","& label *":{fontSize:"14px !important",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}}),count:(0,m.css)({color:e.colors.text.secondary,marginLeft:e.spacing(.5),display:"inline-block"})}}function fr({groups:e,selectedGroups:t,onSelectionChange:n}){const r=(0,f.useStyles2)(gr);return b().createElement(b().Fragment,null,b().createElement("div",{className:r.checkboxListHeader},b().createElement("div",null,t.length," selected"),b().createElement(f.Button,{variant:"secondary",fill:"text",onClick:()=>n([]),disabled:!t.length},"clear")),!e.length&&b().createElement("div",{className:r.noResults},"No results."),e.length>0&&b().createElement("ul",{className:r.checkboxList,"data-testid":"checkbox-filters-list"},e.map(e=>b().createElement("li",{key:e.value,className:r.checkboxItem},b().createElement(mr,{label:e.label,count:e.count,checked:t.some(t=>t.value===e.value),onChange:r=>{const i=r.currentTarget.checked?[...t,{label:e.label,value:e.value}]:t.filter(t=>t.value!==e.value);n(i)}})))))}function gr(e){return{checkboxListHeader:(0,m.css)({display:"flex",justifyContent:"space-between",alignItems:"center",color:e.colors.text.secondary,margin:e.spacing(0),padding:e.spacing(0,0,0,1)}),checkboxList:(0,m.css)({height:"100%",margin:0,padding:e.spacing(0,1,1,1),overflowY:"auto","& .css-1n4u71h-Label":{fontSize:"14px !important"},"&::-webkit-scrollbar":{"-webkit-appearance":"none",width:"7px"},"&::-webkit-scrollbar-thumb":{borderRadius:"4px",backgroundColor:e.colors.secondary.main,"-webkit-box-shadow":`0 0 1px ${e.colors.secondary.shade}`}}),checkboxItem:(0,m.css)({display:"flex",alignItems:"center",width:"100%",padding:e.spacing(.5,0)}),noResults:(0,m.css)({fontStyle:"italic",padding:e.spacing(0,1,1,1)})}}function br(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function yr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){br(e,t,n[t])})}return e}function vr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class wr extends a.Bs{getUrlState(){return{[this.state.key]:this.state.selectedGroups.map(e=>e.value).join(",")}}updateFromUrl(e){const t={};"string"==typeof e[this.state.key]&&e[this.state.key]!==this.state.selectedGroups.map(e=>e.value).join(",")&&(t.selectedGroups=e[this.state.key].split(",").map(e=>({label:e,value:e}))),this.setState(t)}onActivate(){const e=a.jh.lookupVariable(sn,this),t=a.jh.lookupVariable($n,this);this.updateLists(e.state.options),this.updateCounts();const{selectedGroups:n}=this.state;this.setState({loading:t.state.loading,active:n.length>0})}updateLists(e){this.setState({groups:this.state.computeGroups(e),loading:!1})}updateCounts(){var e;const{groups:t,computeGroups:n,type:r}=this.state,i=a.jh.lookupVariable(sn,this).state.options,s=null===(e=a.jh.getAncestor(this,Jr).state.enginesMap.get($n))||void 0===e?void 0:e.filterEngine;if(!s)return void o.v.warn("MetricsFilterSection: No filter engine found");const l=vr(yr({},s.getFilters()),{[r]:[]}),c=Zn.getFilteredOptions(i,l),u=new Map(n(c).map(e=>[e.label,e.count])),d=t.map(e=>{var t;return vr(yr({},e),{count:null!==(t=u.get(e.label))&&void 0!==t?t:0})});this.setState({groups:d,loading:!1})}constructor({key:e,type:t,title:n,description:r,icon:i,computeGroups:o,showHideEmpty:s,showSearch:l,disabled:c,active:u}){super({key:e,type:t,title:n,description:r,icon:i,groups:[],computeGroups:o,selectedGroups:[],loading:!0,showHideEmpty:null==s||s,showSearch:null==l||l,disabled:null!=c&&c,active:null!=u&&u}),br(this,"_variableDependency",new a.Sh(this,{variableNames:[sn,$n],onReferencedVariableValueChanged:e=>{const{name:t,options:n}=e.state;t!==sn?t===$n&&this.updateCounts():this.updateLists(n)}})),br(this,"_urlSync",new a.So(this,{keys:[this.state.key]})),br(this,"onSelectionChange",e=>{this.setState({selectedGroups:e,active:e.length>0}),this.publishEvent(new sr({type:this.state.type,filters:e.map(e=>e.value)}),!0),this.publishEvent(new ur({key:this.state.key,values:e.map(e=>e.label)}),!0),"prefixes"===this.state.type?(0,k.z)("sidebar_prefix_filter_applied",{filter_count:e.length}):"suffixes"===this.state.type&&(0,k.z)("sidebar_suffix_filter_applied",{filter_count:e.length}),"filters-rule"===this.state.key&&e.length>0&&e.forEach(e=>{let t;switch(e.label){case lr:t="non_rules_metrics";break;case cr:t="recording_rules";break;default:return}(0,k.z)("sidebar_rules_filter_selected",{filter_type:t})})}),this.addActivationHandler(this.onActivate.bind(this))}}function Sr(e){return{container:(0,m.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"}),switchContainer:(0,m.css)({display:"flex",alignItems:"center",justifyContent:"flex-end",gap:e.spacing(1)}),switchLabel:(0,m.css)({fontSize:"12px",color:e.colors.text.primary}),searchInput:(0,m.css)({flexBasis:"32px",flexShrink:0,marginBottom:e.spacing(1),padding:e.spacing(0,.5)})}}br(wr,"Component",({model:e})=>{const t=(0,f.useStyles2)(Sr),{groups:n,selectedGroups:r,loading:i,title:a,description:o,showHideEmpty:s,showSearch:l}=e.useState(),[c,u]=(0,g.useState)(!1),[d,p]=(0,g.useState)(""),m=(0,g.useMemo)(()=>{const e=[];return c&&e.push(e=>e.count>0),e.push(e=>e.label.toLowerCase().includes(d.toLowerCase())),n.filter(t=>e.every(e=>e(t)))},[c,n,d]);return b().createElement("div",{className:t.container},b().createElement(dr,{title:a,description:o}),s&&b().createElement("div",{className:t.switchContainer},b().createElement("span",{className:t.switchLabel},"Hide empty"),b().createElement(f.Switch,{value:c,onChange:e=>u(e.currentTarget.checked)})),l&&b().createElement(f.Input,{className:t.searchInput,prefix:b().createElement(f.Icon,{name:"search"}),placeholder:"Search...",value:d,onChange:e=>p(e.currentTarget.value),onKeyDown:e=>{"Escape"===e.key&&(e.preventDefault(),p(""))},suffix:b().createElement(f.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:()=>p("")})}),i&&b().createElement(f.Spinner,{inline:!0}),!i&&b().createElement(fr,{groups:m,selectedGroups:r,onSelectionChange:e.onSelectionChange}))});function Or(e){const t=new Map;for(const r of e){const e=r.value.split(/[^a-z0-9]/i),i=e.length<=1?r.value:e[0];var n;const a=null!==(n=t.get(i))&&void 0!==n?n:[];a.push(r.value),t.set(i||"<none>",a)}const r=new Map;for(const[e,n]of t)r.set(e,n.length);return Array.from(r.entries()).sort((e,t)=>e[1]!==t[1]?t[1]-e[1]:(0,_t._)(e[0],t[0])).map(([e,t])=>({value:e,count:t,label:e}))}function Er(e){const t=new Map;for(const r of e){const e=r.value.split(/[^a-z0-9]/i),i=e.length<=1?r.value:e[e.length-1];var n;const a=null!==(n=t.get(i))&&void 0!==n?n:[];a.push(r.value),t.set(i||"<none>",a)}const r=new Map;for(const[e,n]of t)r.set(e,n.length);return Array.from(r.entries()).sort((e,t)=>e[1]!==t[1]?t[1]-e[1]:(0,_t._)(e[0],t[0])).map(([e,t])=>({value:e,count:t,label:e}))}function xr(e){return/^\w*:.*?(?::\w+)?$/.test(e)}function Pr(e){const t=new Map([["metrics",[]],["rules",[]]]);for(const r of e){const{value:e}=r,i=xr(e)?"rules":"metrics";var n;const a=null!==(n=t.get(i))&&void 0!==n?n:[];a.push(e),t.set(i,a)}return[{value:"^(?!.*:.*)",label:lr,count:t.get("metrics").length},{value:":",label:cr,count:t.get("rules").length}]}function kr(e){return JSON.stringify(function(e){return delete e.actionView,delete e.layout,delete e.refresh,Array.isArray(e["var-filters"])&&(e["var-filters"]=e["var-filters"].filter(Boolean)),e}(e))}function Cr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function jr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Cr(e,t,n[t])})}return e}function _r(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Tr(e){const[t,n]=(0,g.useState)({}),r=ts(e);(0,g.useEffect)(()=>{const e=j.x.getItem(C.V.BOOKMARKS)||[],t={};for(const n of e){const e=kr(n.urlValues);t[e]=_r(jr({},n),{key:e})}n(t)},[]);const{value:i}=a.jh.findByKeyAndType(r,se,nn).useState();return{bookmarks:(0,g.useMemo)(()=>Object.values(t).filter(e=>e.urlValues[`var-${se}`]===i),[t,i]),addBookmark:()=>{const e={urlValues:a.Go.getUrlState(r),createdAt:Date.now()},i=Object.values(t).map(e=>_r(jr({},e),{key:void 0}));j.x.setItem(C.V.BOOKMARKS,[...i,e]);const o=kr(e.urlValues);n(_r(jr({},t),{[o]:_r(jr({},e),{key:o})})),function(){const e=as(p.bw.Drilldown),t=e?b().createElement("i",null,"the Metrics Reducer sidebar"):b().createElement("i",null,"Drilldown > Metrics");(0,_.qq)(["Bookmark created",b().createElement(f.Stack,{gap:2,direction:"row",key:"bookmark-notification"},b().createElement("div",null,"You can view bookmarks under ",t),!e&&b().createElement(f.LinkButton,{fill:"solid",variant:"secondary",href:"/explore/metrics"},"View bookmarks"))])}()},removeBookmark:e=>{delete t[e];const r=Object.values(t).map(e=>_r(jr({},e),{key:void 0}));j.x.setItem(C.V.BOOKMARKS,r),n(jr({},t))},gotoBookmark:e=>{const n=t[e];if(!n){const e=new Error("Bookmark not found!");return void(0,_.jx)(e,[e.toString()])}r.publishEvent(new he({metric:n.urlValues.metric,urlValues:n.urlValues}),!0)}}}const Ir=(e,t,n)=>e.length+2+t.length>n?t.substring(0,n-e.length-5)+"...":t;function Dr(e){const t=(0,f.useStyles2)(Ar),{onSelect:n,onDelete:i,bookmark:a}=e,{createdAt:o,urlValues:s}=a,l=s.metric||"?",c=(e=>{const t=e[`var-${ie}`];return t.length?t.map(e=>e.split("|")):[]})(s),u=Ir("",os(l),27),d=`${e.compactHeight&&c.length>0?t.cardTall:""}`,p=`${t.card} ${e.wide?t.cardWide:""} ${d}`;return b().createElement("article",{"data-testid":`data-trail-card ${l}`},b().createElement(f.Card,{onClick:n,className:p},b().createElement(f.Card.Heading,null,b().createElement("div",{className:t.metricValue},u)),b().createElement(f.Card.Meta,{className:t.meta},c.map(([e,n,r],i)=>b().createElement("div",{key:i,className:t.filter},b().createElement("span",{className:t.secondaryFont},e," ",n),b().createElement("span",{className:t.primaryFont}," ",Ir(e,r,44))))),b().createElement("div",{className:t.deleteButton},b().createElement(f.Card.SecondaryActions,null,b().createElement(f.IconButton,{key:"delete",name:"trash-alt",className:t.secondary,tooltip:"Remove bookmark",tooltipPlacement:"top",onClick:i})))),b().createElement("div",{className:t.date},b().createElement("div",{className:t.secondaryFont},"Date created: "),b().createElement("div",{className:t.primaryFont},o>0&&(0,r.dateTimeFormat)(o,{format:"YYYY-MM-DD"}))))}function Ar(e){return{metricValue:(0,m.css)({display:"inline",color:e.colors.text.primary,fontWeight:500,wordBreak:"break-all"}),card:(0,m.css)({position:"relative",width:"318px",padding:`12px ${e.spacing(2)} ${e.spacing(1)} ${e.spacing(2)}`,alignItems:"start",marginBottom:0,borderTop:`1px solid ${e.colors.border.weak}`,borderRight:`1px solid ${e.colors.border.weak}`,borderLeft:`1px solid ${e.colors.border.weak}`,borderBottom:"none",borderRadius:"2px 2px 0 0"}),cardWide:(0,m.css)({width:"100%"}),cardTall:(0,m.css)({height:"110px"}),secondary:(0,m.css)({color:e.colors.text.secondary,fontSize:"12px"}),date:(0,m.css)({border:`1px solid ${e.colors.border.weak}`,borderRadius:"0 0 2px 2px",padding:`${e.spacing(1)} ${e.spacing(2)}`,backgroundColor:e.colors.background.primary}),meta:(0,m.css)({flexWrap:"wrap",overflow:"hidden",textOverflow:"ellipsis",maxHeight:"36px",margin:0,gridArea:"Meta",color:e.colors.text.secondary,whiteSpace:"nowrap"}),filter:(0,m.css)({overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"}),primaryFont:(0,m.css)({display:"inline",color:e.colors.text.primary,fontSize:"12px",fontWeight:"500",letterSpacing:"0.018px"}),secondaryFont:(0,m.css)({display:"inline",color:e.colors.text.secondary,fontSize:"12px",fontWeight:"400",lineHeight:"18px",letterSpacing:"0.018px"}),deleteButton:(0,m.css)({position:"absolute",bottom:e.spacing(1.5),right:e.spacing(.5)})}}class Lr extends a.Bs{constructor({key:e,title:t,description:n,icon:r,disabled:i}){super({key:e,title:t,description:n,icon:r,disabled:null!=i&&i,active:!1})}}function Nr(e){return{container:(0,m.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%"}),bookmarksList:(0,m.css)({display:"flex",flexDirection:"column",gap:e.spacing(1.5),overflowY:"auto",paddingRight:e.spacing(1)}),emptyState:(0,m.css)({display:"flex",flexDirection:"column",alignItems:"center",height:"100px",color:e.colors.text.secondary,fontStyle:"italic"})}}function Br({labels:e,selectedLabel:t,onSelectLabel:n,onClearSelection:r}){const i=(0,f.useStyles2)(Mr);return b().createElement(b().Fragment,null,b().createElement("div",{className:i.listHeader},b().createElement("div",{className:i.selected},t===bn?"No selection":`Selected: "${t}"`),b().createElement(f.Button,{variant:"secondary",fill:"text",onClick:r,disabled:t===bn},"clear")),!e.length&&b().createElement("div",{className:i.noResults},"No results."),e.length>0&&b().createElement("div",{className:i.list,"data-testid":"labels-list"},b().createElement(f.RadioButtonList,{name:"labels-list",options:e,onChange:n,value:t})))}function Mr(e){return{listHeader:(0,m.css)({display:"flex",justifyContent:"space-between",alignItems:"center",color:e.colors.text.secondary,margin:e.spacing(0),padding:e.spacing(0,0,0,1)}),selected:(0,m.css)({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),list:(0,m.css)({display:"flex",flex:1,flexDirection:"column",gap:0,overflowY:"auto",'& [role="radiogroup"]':{gap:0},"& label":{cursor:"pointer",padding:e.spacing(.5,1),"&:hover":{background:e.colors.background.secondary}},"& label div":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}}),noResults:(0,m.css)({fontStyle:"italic",padding:e.spacing(0,1,1,1)})}}function $r(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Lr,"Component",({model:e})=>{const t=(0,f.useStyles2)(Nr),{title:n,description:r}=e.useState(),{bookmarks:i,gotoBookmark:a,removeBookmark:o}=Tr(e);return b().createElement("div",{className:t.container},b().createElement(dr,{title:n,description:r,"data-testid":"bookmarks-list-sidebar"}),i.length>0?b().createElement("div",{className:t.bookmarksList},i.map(e=>b().createElement(Dr,{key:e.key,bookmark:e,onSelect:()=>{return t=e.key,(0,k.z)("exploration_started",{cause:"bookmark_clicked"}),void a(t);var t},onDelete:()=>{return t=e.key,(0,k.z)("bookmark_changed",{action:"deleted"}),void o(t);var t},wide:!0,compactHeight:!0}))):b().createElement("div",{className:t.emptyState},b().createElement("div",null,"No bookmarks yet for the"),b().createElement("div",null,"current data source.")))});class Rr extends a.Bs{onActivate(){const e=a.jh.lookupVariable(this.state.variableName,this),t=e.state.value;this.setState({active:Boolean(t&&t!==bn)}),this._subs.add(e.subscribeToState(e=>{const t=Boolean(e.value&&e.value!==bn);this.setState({active:t}),this.publishEvent(new ur({key:this.state.key,values:t?[e.value]:[]}),!0)}))}selectLabel(e){a.jh.lookupVariable(this.state.variableName,this).changeValueTo(e);const t=Boolean(e&&e!==bn);this.setState({active:t}),this.publishEvent(new ur({key:this.state.key,values:t?[e]:[]}),!0)}constructor({key:e,variableName:t,title:n,description:r,icon:i,disabled:o,active:s}){super({key:e,variableName:t,title:n,description:r,icon:i,disabled:null!=o&&o,active:null!=s&&s}),$r(this,"onSelectLabel",e=>{(0,k.z)("sidebar_group_by_label_filter_applied",{label:e}),this.selectLabel(e)}),$r(this,"onClearSelection",()=>{this.selectLabel(bn)}),$r(this,"useLabelsBrowser",()=>{const{variableName:e,title:t,description:n}=this.useState(),r=a.jh.lookupVariable(e,this),{loading:i,options:o,value:s}=r.useState(),[l,c]=(0,g.useState)("");return{title:t,description:n,loading:i,selectedLabel:s,labelsList:(0,g.useMemo)(()=>{const e=[e=>e!==bn,e=>e.toLowerCase().includes(l.toLowerCase())];return o.filter(t=>e.every(e=>e(t.value)))},[o,l]),searchValue:l,onInputChange:e=>{c(e.currentTarget.value)},onInputKeyDown:e=>{"Escape"===e.key&&(e.preventDefault(),c(""))},onInputClear:()=>{c("")}}}),this.addActivationHandler(this.onActivate.bind(this))}}function Fr(e){return{container:(0,m.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"}),search:(0,m.css)({marginBottom:e.spacing(1),padding:e.spacing(0,.5)})}}$r(Rr,"Component",({model:e})=>{const t=(0,f.useStyles2)(Fr),{title:n,description:r,loading:i,labelsList:a,selectedLabel:o,searchValue:s,onInputChange:l,onInputKeyDown:c,onInputClear:u}=e.useLabelsBrowser();return b().createElement("div",{className:t.container,"data-testid":"labels-browser"},b().createElement(dr,{title:n,description:r}),b().createElement(f.Input,{className:t.search,prefix:b().createElement(f.Icon,{name:"search"}),placeholder:"Search...",value:s,onChange:l,onKeyDown:c,suffix:b().createElement(f.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:u})}),i&&b().createElement(f.Spinner,{inline:!0}),!i&&b().createElement(Br,{labels:a,selectedLabel:o,onSelectLabel:e.onSelectLabel,onClearSelection:e.onClearSelection}))});class Vr extends a.Bs{onActivate(){}constructor({key:e,title:t,description:n,icon:r,disabled:i}){super({key:e,title:t,description:n,icon:r,disabled:null!=i&&i,active:!1}),this.addActivationHandler(this.onActivate.bind(this))}}function qr(e){return{container:(0,m.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Vr,"Component",({model:e})=>{const t=(0,f.useStyles2)(qr),{title:n,description:r}=e.useState();return b().createElement("div",{className:t.container},b().createElement(dr,{title:n,description:r}))});const Hr=new Map([["rules",function(){return b().createElement("svg",{stroke:"currentColor",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},b().createElement("rect",{x:"1.25",y:"1.625",width:"5.25",height:"5.25",rx:"1",strokeWidth:"1.5"}),b().createElement("circle",{cx:"12.25",cy:"4.25",r:"2.75",strokeWidth:"1.5"}),b().createElement("circle",{cx:"3.75",cy:"11.75",r:"2.75",strokeWidth:"1.5"}),b().createElement("rect",{x:"9.5",y:"9.125",width:"5.25",height:"5.25",rx:"1",strokeWidth:"1.5"}))}],["groups",xn]]);function zr({ariaLabel:e,disabled:t,visible:n,active:i,tooltip:a,iconOrText:o,onClick:s}){const l=(0,f.useStyles2)(Ur);let c,u;return o in r.availableIconsIndex?c=o:u=Hr.has(o)?Hr.get(o):function(){return b().createElement(b().Fragment,null,o)},b().createElement(f.Button,{className:(0,m.cx)(l.button,t&&"disabled",n&&"visible",i&&"active"),size:"md",variant:"secondary",fill:"text",icon:c,"aria-label":e,tooltip:a,tooltipPlacement:"right",onClick:s,disabled:t},u&&b().createElement(u,null))}function Ur(e){return{button:(0,m.css)({margin:0,color:e.colors.text.secondary,"&:hover":{color:e.colors.text.maxContrast,background:"transparent"},"&.disabled:hover":{color:e.colors.text.secondary},"&.visible":{color:e.colors.text.maxContrast},"&.active":{color:e.colors.text.maxContrast}})}}var Gr=n(6455);function Kr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Wr=["filters-rule","filters-prefix","filters-suffix"];class Qr extends a.Bs{onActivate(){var e;const t=this.initOtherMetricsVar();return this.subscribeToEvent(ur,e=>{const{key:t,values:n}=e.payload,{sectionValues:r}=this.state,i=new Map(r).set(t,n);this.setOtherMetricFilters(i),this.setState({sectionValues:i})}),!(null===(e=this.state.visibleSection)||void 0===e?void 0:e.state.key)&&(0,Gr.A)(Gr.j.sidebarOpenByDefault)&&this.setActiveSection(j.x.getItem(C.V.SIDEBAR_SECTION)||"filters-prefix"),()=>{t()}}setOtherMetricFilters(e){const t=a.jh.lookupVariable(ue,this);if(!kt(t))return;const n={"filters-rule":"rule group","filters-prefix":"prefix","filters-suffix":"suffix"},i=Array.from(e.entries()).reduce((e,[t,r])=>(r.length&&Wr.includes(t)&&e.push({key:t,operator:"=",value:r.join(", "),keyLabel:n[t]}),e),[]);t.setState({filters:i,hide:i.length?r.VariableHide.hideLabel:r.VariableHide.hideVariable})}initOtherMetricsVar(){const e=ts(this).state.$variables;if(!e)return()=>{};const t=new a.H9({name:ue,readOnly:!0,skipUrlSync:!0,datasource:null,hide:r.VariableHide.hideVariable,layout:"combobox",applyMode:"manual",allowCustomValue:!0});return e.setState({variables:[...e.state.variables,t]}),this.setOtherMetricFilters(this.state.sectionValues),()=>{e.setState({variables:[...e.state.variables.filter(e=>e!==t)]})}}static getSectionValuesFromUrl(){const e=new URLSearchParams(window.location.search),t=new Map;for(const n of Wr){const r=e.get(n);t.set(n,r?r.split(",").map(e=>e.trim()):[])}const n=e.get(`var-${Sn}`);return Boolean(n&&n!==bn)&&t.set("groupby-labels",[n]),t}setActiveSection(e){const{visibleSection:t,sections:n}=this.state;if(!e||e===(null==t?void 0:t.state.key))return(0,k.z)("metrics_sidebar_toggled",{action:"closed",section:null==t?void 0:t.state.key}),void this.setState({visibleSection:null});var r;j.x.setItem(C.V.SIDEBAR_SECTION,e),(0,k.z)("metrics_sidebar_toggled",{action:"opened",section:e}),"filters-prefix"===e?(0,k.z)("sidebar_prefix_filter_section_clicked",{}):"filters-suffix"===e&&(0,k.z)("sidebar_suffix_filter_section_clicked",{}),this.setState({visibleSection:null!==(r=n.find(t=>t.state.key===e))&&void 0!==r?r:null})}constructor(e){var t,n,r;const i=Qr.getSectionValuesFromUrl();super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Kr(e,t,n[t])})}return e}({key:"sidebar",visibleSection:null,sections:[new wr({key:"filters-rule",type:"categories",title:"Rules filters",description:"Filter metrics and recording rules",icon:"rules",computeGroups:Pr,showHideEmpty:!1,showSearch:!1,active:Boolean(null===(t=i.get("filters-rule"))||void 0===t?void 0:t.length)}),new wr({key:"filters-prefix",type:"prefixes",title:"Prefix filters",description:"Filter metrics based on their name prefix (Prometheus namespace)",icon:"A_",computeGroups:Or,active:Boolean(null===(n=i.get("filters-prefix"))||void 0===n?void 0:n.length)}),new wr({key:"filters-suffix",type:"suffixes",title:"Suffix filters",description:"Filter metrics based on their name suffix",icon:"_Z",computeGroups:Er,active:Boolean(null===(r=i.get("filters-suffix"))||void 0===r?void 0:r.length)}),new Rr({key:"groupby-labels",variableName:Sn,title:"Group by labels",description:"Group metrics by their label values",icon:"groups",active:i.has("groupby-labels")}),new Lr({key:"bookmarks",title:"Bookmarks",description:"Access your saved metrics for quick reference",icon:"star"}),new Vr({key:"settings",title:"Settings",description:"Settings",icon:"cog",disabled:!0})],sectionValues:i},e)),i.set("filters-rule",[]),this.addActivationHandler(this.onActivate.bind(this))}}function Yr(e){return{container:(0,m.css)({position:"relative",display:"flex",flexDirection:"row",height:"100%",overflow:"hidden"}),buttonsBar:(0,m.css)({display:"flex",flexDirection:"column",alignItems:"center",gap:0,width:"42px",padding:0,margin:0,boxSizing:"border-box",border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,backgroundColor:e.colors.background.primary,borderTopLeftRadius:0,borderBottomLeftRadius:0,position:"relative"}),buttonContainer:(0,m.css)({marginTop:e.spacing(1),"&::before":{transition:"0.5s ease",content:'""',position:"absolute",left:0,height:"32px",borderLeft:`2px solid ${e.colors.action.selectedBorder}`,boxSizing:"border-box",opacity:0,visibility:"hidden"},"&:hover::before":{opacity:1,visibility:"visible"},"&.visible::before":{opacity:1,visibility:"visible"},"&.disabled::before":{opacity:0,visibility:"hidden"},"&.active::after":{content:'""',position:"absolute",right:0,width:"8px",height:"8px",backgroundColor:e.colors.action.selectedBorder,borderRadius:"50%",margin:"2px 4px 0 0"}}),content:(0,m.css)({width:"calc(300px - 42px)",boxSizing:"border-box",border:`1px solid ${e.colors.border.weak}`,borderLeft:"none",borderRadius:e.shape.radius.default,backgroundColor:e.colors.background.canvas,padding:e.spacing(1.5)}),closeButton:(0,m.css)({position:"absolute",top:e.spacing(1.5),right:e.spacing(1),margin:0})}}function Xr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Kr(Qr,"Component",({model:e})=>{const t=(0,f.useStyles2)(Yr),{sections:n,visibleSection:r,sectionValues:i}=e.useState();return b().createElement("div",{className:t.container},b().createElement("div",{className:t.buttonsBar,"data-testid":"sidebar-buttons"},n.map(n=>{var a,o;const{key:s,title:l,icon:c,disabled:u,active:d}=n.state,p=(null==r?void 0:r.state.key)===s,h=(null===(a=i.get(s))||void 0===a?void 0:a.length)?`${l}: ${null===(o=i.get(s))||void 0===o?void 0:o.join(", ")}`:l;return b().createElement("div",{key:s,className:(0,m.cx)(t.buttonContainer,p&&"visible",d&&"active",u&&"disabled")},b().createElement(zr,{ariaLabel:l,disabled:u,visible:p,active:d,tooltip:h,onClick:()=>e.setActiveSection(s),iconOrText:c}))})),r&&b().createElement("div",{className:t.content,"data-testid":"sidebar-content"},b().createElement(f.IconButton,{className:t.closeButton,name:"times","aria-label":"Close",tooltip:"Close",tooltipPlacement:"top",onClick:()=>e.setActiveSection("")}),r instanceof wr&&b().createElement(r.Component,{model:r}),r instanceof Rr&&b().createElement(r.Component,{model:r}),r instanceof Lr&&b().createElement(r.Component,{model:r}),r instanceof Vr&&b().createElement(r.Component,{model:r})))});class Jr extends a.Bs{onActivate(){const e=a.jh.lookupVariable(Sn,this).state.value;this.updateBasedOnGroupBy(e),this.subscribeToEvents()}updateBasedOnGroupBy(e){const t=Boolean(e&&e!==bn);a.jh.findByKeyAndType(this,"quick-search",Gn).toggleCountsDisplay(!t),!t&&this.state.body instanceof di||t&&this.state.body instanceof Bn&&this.state.body.state.labelName===e||this.setState({body:t?new Bn({labelName:e}):new di({variableName:$n})})}subscribeToEvents(){this.initVariablesFilteringAndSorting()}initVariablesFilteringAndSorting(){this.subscribeToEvent(Pn,e=>{const{key:t}=e.payload,n=a.jh.findByKey(this,t);this.state.enginesMap.set(t,{filterEngine:new Zn(n),sortEngine:new or(n)})}),this.subscribeToEvent(kn,e=>{this.state.enginesMap.delete(e.payload.key)});const e=a.jh.findByKeyAndType(this,"quick-search",Gn),t=a.jh.findAllObjects(this,e=>e instanceof wr),n=a.jh.findByKeyAndType(this,"metrics-sorter",en).state.$variables.getByName(Zt);this.subscribeToEvent(Cn,r=>{const{key:i,options:a}=r.payload,{filterEngine:o,sortEngine:s}=this.state.enginesMap.get(i);o.setInitOptions(a);const l={names:e.state.value?[e.state.value]:[]};for(const e of t)l[e.state.type]=e.state.selectedGroups.map(e=>e.value);o.applyFilters(l,{forceUpdate:!0,notify:!1}),s.sort(n.state.value)}),this.subscribeToEvent(zn,e=>{const{searchText:t}=e.payload;for(const[,{filterEngine:e,sortEngine:r}]of this.state.enginesMap)e.applyFilters({names:t?[t]:[]}),r.sort(n.state.value)}),this.subscribeToEvent(sr,e=>{const{type:t,filters:r}=e.payload;for(const[,{filterEngine:e,sortEngine:i}]of this.state.enginesMap)e.applyFilters({[t]:r}),i.sort(n.state.value)}),this.subscribeToEvent(Tt,e=>{const{sortBy:t}=e.payload;(0,k.z)("sorting_changed",{from:"metrics-reducer",sortBy:t});for(const[,{sortEngine:e}]of this.state.enginesMap)e.sort(t)})}constructor(){super({$variables:new a.Pj({variables:[new Rn,new On]}),listControls:new Yn({}),sidebar:new Qr({}),body:void 0,enginesMap:new Map}),Xr(this,"_variableDependency",new a.Sh(this,{variableNames:[Sn],onReferencedVariableValueChanged:e=>{this.updateBasedOnGroupBy(e.state.value)}})),function(e){try{for(const t of e)(0,a.pY)({dataSource:t})}catch(e){const{message:t}=e;/A runtime data source with uid (.+) has already been registered/.test(t)||(0,_.jx)(e,["Fail to register all the runtime data sources!","The application cannot work as expected, please try reloading the page or if the problem persists, contact your organization admin."])}}([new yn,new In]),this.addActivationHandler(this.onActivate.bind(this))}}Xr(Jr,"Component",({model:e})=>{var t;const n=null!==(t=(0,i.useChromeHeaderHeight)())&&void 0!==t?t:0,r=(0,f.useStyles2)(ei,n),{$variables:a,body:o,listControls:s,sidebar:l}=e.useState();return b().createElement(b().Fragment,null,b().createElement("div",{className:r.listControls,"data-testid":"list-controls"},b().createElement(s.Component,{model:s})),b().createElement("div",{className:r.body},b().createElement("div",{className:r.sidebar,"data-testid":"sidebar"},b().createElement(l.Component,{model:l})),b().createElement("div",{className:r.list},o&&b().createElement(o.Component,{model:o}))),b().createElement("div",{className:r.variables},null==a?void 0:a.state.variables.map(e=>b().createElement(e.Component,{key:e.state.name,model:e}))))});const Zr=144;function ei(e,t){return{listControls:(0,m.css)({marginBottom:e.spacing(1.5)}),body:(0,m.css)({display:"flex",flexDirection:"row",gap:e.spacing(1),height:`calc(100vh - ${t+Zr}px)`}),list:(0,m.css)({width:"100%",overflowY:"auto"}),sidebar:(0,m.css)({flex:"0 0 auto",overflowY:"auto"}),variables:(0,m.css)({display:"none"})}}function ti({usageType:e,usageCount:t,singularUsageType:n,pluralUsageType:r,icon:i,dashboardItems:a}){const o=(0,f.useStyles2)(ni);return b().createElement("div",{className:o.usageContainer,"data-testid":"usage-data-panel"},"dashboard-usage"===e?b().createElement(b().Fragment,null,b().createElement(f.Dropdown,{placement:"right-start",overlay:b().createElement(f.Menu,{style:{maxWidth:"240px",maxHeight:"245px",overflowY:"auto"}},a.map(e=>b().createElement(f.Menu.Item,{key:e.id,label:"",url:e.url,target:"_blank",className:o.menuItem,component:()=>b().createElement(f.Tooltip,{content:`Used ${e.count} ${1===e.count?"time":"times"} in ${e.label}`,placement:"right"},b().createElement("div",{className:o.menuItemContent},b().createElement(f.Icon,{name:"external-link-alt"})," ",e.label," (",e.count,")"))})))},b().createElement(f.Button,{variant:"secondary",size:"sm",tooltip:`Metric used ${t} ${1===t?"time":"times"} in dashboard queries. Click to view the dashboards.`,className:(0,m.cx)(o.usageItem,o.clickableUsageItem)},b().createElement("span",{"data-testid":e},b().createElement(f.Icon,{name:i,style:{marginRight:"4px"}})," ",t)))):b().createElement(f.Tooltip,{content:`Metric is used in ${t} ${1===t?n:r}`,placement:"top"},b().createElement("span",{className:o.usageItem,"data-testid":e},b().createElement(f.Icon,{name:i})," ",t)))}function ni(e){return{usageContainer:(0,m.css)({display:"flex",flexDirection:"row",justifyContent:"flex-start",gap:"17px",padding:"8px 12px",border:`1px solid ${e.colors.border.weak}`,borderTopWidth:0,backgroundColor:e.colors.background.primary,alignItems:"center"}),usageItem:(0,m.css)({display:"flex",alignItems:"center",gap:"4px",color:e.colors.text.secondary,opacity:"65%"}),clickableUsageItem:(0,m.css)({backgroundColor:"transparent",border:"none"}),menuItem:(0,m.css)({color:e.colors.text.primary,textDecoration:"none","&:hover":{color:e.colors.text.link}}),menuItemContent:(0,m.css)({overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",color:e.colors.text.primary,"&:hover":{color:e.colors.text.link}})}}function ri(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function ii(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ai(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const oi="220px",si="260px";class li extends a.Bs{_onActivate(){let e;try{e=a.jh.getAncestor(this,Jr)}catch(e){return}if(!e.state.enginesMap.get($n))return;const t=a.jh.findByKeyAndType(this,"metrics-sorter",en),n=a.jh.getVariables(t).getByName(Zt);Ct(n)&&(this.updateSortBy(t,n.getValue()),this._subs.add(n.subscribeToState(({value:e})=>{this.updateSortBy(t,e)})))}updateSortBy(e,t){return(n=function*(){if(this.setState({sortBy:t}),this.updateLayout(t),"default"===t)return;const n=yield e.getUsageDetailsForMetric(this.state.metric,t);switch(t){case"dashboard-usage":if("dashboard-usage"!==n.usageType)return;const{dashboards:e}=n;this.setState({usageCount:n.count,singularUsageType:"dashboard panel query",pluralUsageType:"dashboard panel queries",icon:"apps",dashboardItems:Object.entries(e).map(([e,t])=>({id:t.uid,label:e,count:t.count,url:t.url})).sort((e,t)=>t.count-e.count)});break;case"alerting-usage":this.setState({usageCount:n.count,singularUsageType:"alert rule",pluralUsageType:"alert rules",icon:"bell"})}},function(){var e=this,t=arguments;return new Promise(function(r,i){var a=n.apply(e,t);function o(e){ri(a,r,i,o,s,"next",e)}function s(e){ri(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var n}updateLayout(e){const t=a.jh.getAncestor(this,a.gF),n=null==t?void 0:t.state.autoRows,r="default"===e?oi:si;n!==r&&t.setState({autoRows:r})}constructor(e){super(ai(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ii(e,t,n[t])})}return e}({},e),{sortBy:"default",usageCount:0,singularUsageType:"",pluralUsageType:"",icon:"",dashboardItems:[]})),this.addActivationHandler(this._onActivate.bind(this))}}ii(li,"Component",({model:e})=>{const{vizPanelInGridItem:t,sortBy:n,usageCount:r,singularUsageType:i,pluralUsageType:a,icon:s,dashboardItems:l}=e.useState();if(t)return b().createElement("div",{"data-testid":"with-usage-data-preview-panel"},b().createElement(t.Component,{model:t}),"default"!==n&&b().createElement(ti,{usageType:n,usageCount:r,singularUsageType:i,pluralUsageType:a,icon:s,dashboardItems:l}));o.v.log("no viz panel")});const ci="repeat(auto-fit, minmax(400px, 1fr))",ui="1fr";class di extends a.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=a.jh.findByKeyAndType(this,"layout-switcher",St),t=this.state.body.state.body,n=(e,n)=>{e.layout!==(null==n?void 0:n.layout)&&t.setState({templateColumns:e.layout===wt.ROWS?ui:ci})};n(e.state),this._subs.add(e.subscribeToState(n))}constructor({variableName:e}){super({key:"metrics-list",variableName:e,body:new Et({variableName:e,initialPageSize:120,pageSizeIncrement:9,body:new a.gF({children:[],isLazy:!0,templateColumns:ci,autoRows:oi,$behaviors:[new a.Gg.K2({key:"metricCrosshairSync",sync:Re.yV.Crosshair})]}),getLayoutLoading:()=>new a.dM({reactNode:b().createElement(f.Spinner,{inline:!0})}),getLayoutEmpty:()=>new a.dM({reactNode:b().createElement(ht._,{title:"",severity:"info"},"No metrics found for the current filters and time range.")}),getLayoutError:e=>new a.dM({reactNode:b().createElement(ht._,{severity:"error",title:"Error while loading metrics!",error:e})}),getLayoutChild:(e,t)=>{const n=e.value;return new a.xK({body:new li({metric:e.value,vizPanelInGridItem:new pt({metric:n,panelOptions:{fixedColorIndex:t,headerActions:()=>[new be({metric:n}),new bt({metric:n})]}})})})}})}),this.addActivationHandler(this.onActivate.bind(this))}}function pi(e){return{container:(0,m.css)({}),footer:(0,m.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(di,"Component",({model:e})=>{const{variableName:t,body:n}=e.useState(),r=(0,f.useStyles2)(pi),i=a.jh.lookupVariable(t,e),{loading:o,error:s}=i.useState(),l=n.useSizes(),c=!o&&!s&&l.total>0&&l.current<l.total;return b().createElement("div",{"data-testid":"metrics-list"},b().createElement("div",{className:r.container},b().createElement(n.Component,{model:n})),c&&b().createElement("div",{className:r.footer},b().createElement(Pt,{label:"metric",batchSizes:l,onClick:()=>{n.increaseBatchSize()}})))});class mi extends r.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(mi,"type","apply-panel-config");class hi extends r.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(hi,"type","cancel-configure-panel");const fi=[{value:99,label:"P99"},{value:95,label:"P95"},{value:90,label:"P90"},{value:75,label:"P75"},{value:50,label:"P50"}];function gi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function bi(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class yi extends a.Bs{onActivate(){this.initPercentilesParams()}initPercentilesParams(){var e,t,n;const r=this.state.body.state.queryConfig,i=new Set((null===(n=r.queries)||void 0===n||null===(t=n.find(e=>{var t;return null===(t=e.params)||void 0===t?void 0:t.percentiles}))||void 0===t||null===(e=t.params)||void 0===e?void 0:e.percentiles)||[]),a=i.size>0?fi.map(e=>bi(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){gi(e,t,n[t])})}return e}({},e),{checked:i.has(e.value)})):[];this.setState({queryParams:{show:a.length>0,options:a}})}constructor({body:e,presetId:t,isSelected:n,onSelect:r}){super({presetId:t,body:e,isSelected:n,onSelect:r,queryParams:{show:!1,options:[]}}),gi(this,"onTogglePercentile",e=>{var t;const{queryParams:n,body:r}=this.state,i=Number(e.target.value),a=n.options.find(e=>e.value===i);if(!a)return;a.checked=!a.checked;const o=n.options.filter(e=>e.checked);if(!o.length)return;const s=(0,y.cloneDeep)(r.state.queryConfig);null===(t=s.queries)||void 0===t||t.some(e=>{var t;return!!(null===(t=e.params)||void 0===t?void 0:t.percentiles)&&(e.params.percentiles=o.map(e=>e.value),!0)}),r.update({},s),this.setState({queryParams:n})}),gi(this,"onClickPreset",()=>{this.state.onSelect(this.state.presetId)}),this.addActivationHandler(this.onActivate.bind(this))}}function vi(e){return{container:m.css`
      display: flex;
      flex-direction: column;
      gap: ${e.spacing(1)};
      padding: ${e.spacing(1,1,1.25,1)};
      border: 1px solid transparent;
      transition: all 0.2s ease-in-out;

      &:hover {
        border: 1px solid ${e.colors.border.weak};
        border-color: ${e.colors.primary.border};
      }
      &:focus {
        border: 1px solid ${e.colors.border.weak};
        outline: 1px solid ${e.colors.primary.main};
        outline-offset: 1px;
      }
    `,selected:m.css`
      cursor: default;
      border: 1px solid ${e.colors.border.weak};
      border-color: ${e.colors.primary.border};
    `,bodyAndParams:m.css`
      display: flex;
      flex-direction: row;
      gap: ${e.spacing(1.25)};
      width: 100%;
    `,paramsContainer:m.css`
      margin-top: ${e.spacing(1)};
    `,param:m.css`
      display: flex;
      align-items: center;
      gap: ${e.spacing(.5)};
      margin-bottom: ${e.spacing(.5)};
      font-size: 12px;
      cursor: pointer;

      & [type='checkbox'] {
        cursor: pointer;
      }
    `,radioContainer:m.css`
      display: flex;
      align-items: center;
      justify-content: center;

      & [type='radio'] {
        cursor: pointer;
      }
    `}}function wi(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Si(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Oi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Si(e,t,n[t])})}return e}function Ei(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}gi(yi,"Component",({model:e})=>{const t=(0,f.useStyles2)(vi),{body:n,isSelected:r,queryParams:i}=e.useState();return b().createElement("div",{className:(0,m.cx)(t.container,r&&t.selected),onClick:r?void 0:e.onClickPreset},b().createElement("div",{className:(0,m.cx)(t.bodyAndParams)},b().createElement(n.Component,{model:n}),i.show&&b().createElement("div",{className:t.paramsContainer},i.options.map(n=>b().createElement("label",{key:n.value,className:(0,m.cx)("param",t.param),htmlFor:`checkbox-${n.value}`},b().createElement("input",{id:`checkbox-${n.value}`,type:"checkbox",value:n.value,checked:n.checked,onChange:e.onTogglePercentile}),b().createElement("span",null,n.label))))),b().createElement("div",{className:t.radioContainer},b().createElement(f.Tooltip,{content:r?"Current configuration":"Click to select this configuration",placement:"top"},b().createElement("input",{type:"radio",name:"select-config",checked:r}))))});class xi extends a.Bs{onActivate(){this.syncTimeRange(),this.buildBody(),this.subscribeToEvents()}syncTimeRange(){const e=a.jh.getAncestor(this,qo),{from:t,to:n,timeZone:r,value:i}=a.jh.getTimeRange(e).state;a.jh.getTimeRange(this).setState({from:t,to:n,timeZone:r,value:i})}buildBody(){return(e=function*(){const{metric:e}=this.state,t=N(e),n=yield ee(e,ts(this)),i=(t||n[0]).id,o=new a.gF({templateColumns:ci,autoRows:B.M+46,isLazy:!0,$behaviors:[new a.Gg.K2({key:"metricCrosshairSync",sync:r.DashboardCursorSync.Crosshair})],children:n.map((n,r)=>new a.xK({body:new yi({presetId:n.id,isSelected:i===n.id,onSelect:e=>this.onSelectPreset(e),body:new pt({key:`panel-${n.id}`,discardUserPrefs:n.id!==(null==t?void 0:t.id),metric:e,panelOptions:Ei(Oi({},n.panelOptions),{title:n.name,fixedColorIndex:r,headerActions:()=>[]}),queryOptions:n.queryOptions})})}))});this.setState({presets:n,selectedPresetId:i,body:o})},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){wi(a,r,i,o,s,"next",e)}function s(e){wi(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var e}subscribeToEvents(){const{metric:e}=this.state;this.subscribeToEvent(mi,t=>{const{config:n,restoreDefault:r}=t.payload,i=j.x.getItem(C.V.METRIC_PREFS)||{},a=i[e];r&&a?delete i[e].config:i[e]=Ei(Oi({},a),{config:n}),j.x.setItem(C.V.METRIC_PREFS,i)})}static getPanelConfigFromPreset(e){return(0,y.omit)(e,["name","panelOptions.description"])}constructor({metric:e}){super({metric:e,$timeRange:new a.JZ({}),controls:[new a.KE({}),new a.WM({})],isConfirmModalOpen:!1,presets:[],selectedPresetId:void 0,body:void 0}),Si(this,"onSelectPreset",e=>{for(const t of a.jh.findDescendents(this,yi))t.setState({isSelected:t.state.presetId===e});this.setState({selectedPresetId:e})}),Si(this,"onClickRestoreDefault",()=>{this.setState({isConfirmModalOpen:!0})}),Si(this,"onClickConfirmRestoreDefault",()=>{const{metric:e,presets:t}=this.state,[n]=t;n?(this.publishEvent(new mi({metric:e,config:xi.getPanelConfigFromPreset(n),restoreDefault:!0}),!0),this.closeConfirmModal()):(0,_.jx)(new Error(`No default config found for metric ${e}!`),["Cannot restore default configuration."])}),Si(this,"closeConfirmModal",()=>{this.setState({isConfirmModalOpen:!1})}),Si(this,"onClickCancel",()=>{this.publishEvent(new hi({metric:this.state.metric}),!0)}),Si(this,"onClickApplyConfig",()=>{const{metric:e,presets:t,selectedPresetId:n}=this.state,r=a.jh.findByKeyAndType(this,`panel-${n}`,pt);if(!r)throw new Error(`Panel not found for preset id="${n}"!`);const i=t.find(e=>e.id===n);if(!i)throw new Error(`Preset with id="${n}" not found!`);const o=(0,y.cloneDeep)(i);o.queryOptions.queries=r.state.queryConfig.queries,this.publishEvent(new mi({metric:e,config:xi.getPanelConfigFromPreset(o)}),!0)}),this.addActivationHandler(this.onActivate.bind(this))}}function Pi(e){return{controlsContainer:m.css`
      display: flex;
      justify-content: flex-end;
      gap: ${e.spacing(1)};
      margin-bottom: ${e.spacing(2)};
    `,messageContainer:m.css`
      margin: ${e.spacing(2.5,0,1,0)};
    `,controls:m.css`
      display: flex;
    `,formButtonsContainer:m.css`
      display: flex;
      justify-content: center;
      gap: ${e.spacing(2)};
      position: sticky;
      bottom: 0;
      background: ${e.colors.background.primary};
      padding: ${e.spacing(2,0)};
      border-top: 1px solid ${e.colors.border.weak};
    `}}Si(xi,"Component",({model:e})=>{const t=(0,f.useStyles2)(Pi),{metric:n,body:r,controls:i,isConfirmModalOpen:a}=e.useState();return b().createElement("div",null,b().createElement("div",{className:t.controlsContainer},b().createElement(f.Button,{variant:"secondary",size:"md",onClick:e.onClickRestoreDefault},"Restore default config"),b().createElement("div",{className:t.controls},i.map(e=>b().createElement(e.Component,{key:e.state.key,model:e})))),b().createElement("div",{className:t.messageContainer},b().createElement("p",null,"Select a Prometheus function that will be used by default to display the ",n," metric.")),r&&b().createElement(r.Component,{model:r}),b().createElement("div",{className:t.formButtonsContainer},b().createElement(f.Button,{variant:"primary",size:"md",onClick:e.onClickApplyConfig},"Apply"),b().createElement(f.Button,{variant:"secondary",size:"md",onClick:e.onClickCancel},"Cancel")),b().createElement(f.ConfirmModal,{isOpen:a,title:"Restore default configuration",body:`Are you sure you want to restore the default configuration for the ${n} metric?`,confirmText:"Restore",onConfirm:e.onClickConfirmRestoreDefault,onDismiss:e.closeConfirmModal}))});function ki(){(0,k.z)("give_feedback_clicked",{})}const Ci=()=>{const e=(0,f.useStyles2)(ji);return b().createElement("div",{className:e.wrapper},b().createElement("a",{href:"https://forms.gle/dKHDM4GDXVYPny3L6",className:e.feedback,title:"Share your thoughts about Metrics in Grafana.",target:"_blank",rel:"noreferrer noopener",onClick:ki},b().createElement(f.Icon,{name:"comment-alt-message"})," Give feedback"))},ji=e=>({wrapper:(0,m.css)({position:"absolute",top:0,right:0}),feedback:(0,m.css)({color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize,"&:hover":{color:e.colors.text.link}})}),_i=(0,g.memo)(function({size:e}){const t=(0,f.useStyles2)(Ti);return b().createElement("img",{className:(0,m.cx)(t.logo,e),src:"public/plugins/grafana-metricsdrilldown-app/img/logo.svg"})}),Ti=()=>({logo:m.css`
    &.small {
      width: 24px;
      height: 24px;
      margin-right: 4px;
      position: relative;
      top: -2px;
    }

    &.large {
      width: 40px;
      height: 40px;
    }
  `});const Ii=n(5176).t,Di=`https://github.com/grafana/metrics-drilldown/commit/${Ii}`,{buildInfo:Ai}=i.config;function Li(){const e=(0,f.useStyles2)(Mi),{meta:{info:{version:t,updated:n}}}=(0,r.usePluginContext)()||{meta:{info:{version:"?.?.?",updated:"?"}}};return b().createElement("div",{className:e.menuHeader},b().createElement("h5",null,b().createElement(_i,{size:"small"}),"Grafana Metrics Drilldown v",t),b().createElement("div",{className:e.subTitle},"Last update: ",n))}function Ni({model:e}){const t=(0,f.useStyles2)(Mi),n="dev"===Ii,r=n?Ii:Ii.slice(0,8),[i,a]=(0,g.useState)();return(0,g.useEffect)(()=>{e.getPrometheusBuildInfo().then(e=>a(e)).catch(e=>{o.v.warn("Error while fetching Prometheus build info!"),o.v.warn(e),a(void 0)})},[e]),b().createElement(f.Menu,{header:b().createElement(Li,null)},b().createElement(f.Menu.Item,{label:`Commit SHA: ${r}`,icon:"github",onClick:()=>window.open(Di),disabled:n}),b().createElement(f.Menu.Item,{label:"Changelog",icon:"list-ul",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/blob/main/CHANGELOG.md","_blank","noopener,noreferrer")}),b().createElement(f.Menu.Item,{label:"Contribute",icon:"external-link-alt",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/blob/main/docs/contributing.md","_blank","noopener,noreferrer")}),b().createElement(f.Menu.Item,{label:"Documentation",icon:"document-info",onClick:()=>window.open("https://grafana.com/docs/grafana/latest/explore/simplified-exploration/metrics","_blank","noopener,noreferrer")}),b().createElement(f.Menu.Item,{label:"Report an issue",icon:"bug",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/issues/new?template=bug_report.md","_blank","noopener,noreferrer")}),b().createElement(f.Menu.Divider,null),b().createElement(f.Menu.Item,{label:`Grafana ${Ai.edition} v${Ai.version} (${Ai.env})`,icon:"grafana",onClick:()=>window.open(`https://github.com/grafana/grafana/commit/${Ai.commit}`,"_blank","noopener,noreferrer")}),i&&b().createElement(f.Menu.Item,{className:t.promBuildInfo,label:`${i.application||"?"} ${i.version} ${i.buildDate?`(${i.buildDate})`:""}`,icon:"gf-prometheus",onClick:()=>window.open(`${i.repository}/commit/${i.revision}`,"_blank","noopener,noreferrer")}))}function Bi({model:e}){return b().createElement(f.Dropdown,{overlay:()=>b().createElement(Ni,{model:e}),placement:"bottom-end"},b().createElement(f.Button,{icon:"info-circle",variant:"secondary",tooltip:"Plugin info",tooltipPlacement:"top",title:"Plugin info","data-testid":"plugin-info-button"}))}const Mi=e=>({button:m.css`
    position: relative;
    display: flex;
    align-items: center;
    width: 32px;
    height: 32px;
    line-height: 30px;
    border: 1px solid ${e.colors.border.weak};
    border-radius: 2px;
    border-left: 0;
    color: ${e.colors.text.primary};
    background: ${e.colors.background.secondary};

    &:hover {
      border-color: ${e.colors.border.medium};
      background-color: ${e.colors.background.canvas};
    }
  `,menuHeader:m.css`
    padding: ${e.spacing(.5,1)};
    white-space: nowrap;
  `,subTitle:m.css`
    color: ${e.colors.text.secondary};
    font-size: ${e.typography.bodySmall.fontSize};
  `,promBuildInfo:m.css`
    & svg {
      color: #e5502a;
    }
  `});function $i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ri(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){$i(e,t,n[t])})}return e}function Fi(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class Vi extends a.Bs{constructor(e){super(Ri({key:"drawer",isOpen:!1},e)),$i(this,"open",({title:e,subTitle:t,body:n})=>{this.setState(Fi(Ri({},this.state),{isOpen:!0,title:e,subTitle:t,body:n}))}),$i(this,"close",()=>{this.setState({isOpen:!1})})}}$i(Vi,"Component",({model:e})=>{const{isOpen:t,title:n,subTitle:r,body:i}=e.useState();return b().createElement(b().Fragment,null,i&&t&&b().createElement(f.Drawer,{size:"lg",title:n,subtitle:r,closeOnMaskClick:!0,onClose:e.close},b().createElement(i.Component,{model:i})))});class qi extends a.fS{onActivate(){this.filterOptions(),this.subscribeToState((e,t)=>{e.value&&e.value!==t.value&&(0,k.z)("groupby_label_changed",{label:String(e.value)}),e.options!==t.options&&e.options.find(e=>"le"===e.value)&&this.filterOptions(e.options)});const e=a.jh.lookupVariable(ie,this);kt(e)&&e.subscribeToState((e,t)=>{e.filterExpression!==t.filterExpression&&this.changeValueTo("$__all")})}filterOptions(e=this.state.options){this.setState({options:e.filter(e=>"le"!==e.value)})}constructor(){super({name:oe,label:"Group by",datasource:pe,includeAll:!0,defaultToAll:!0,query:"label_names(${metric})",value:"",text:""}),this.addActivationHandler(this.onActivate.bind(this))}}function Hi(e){return{select:m.css`
      width: ${e.spacing(16)};
      & > div {
        width: 100%;
      }
    `}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(qi,"Component",({model:e})=>{const t=(0,f.useStyles2)(Hi);return b().createElement("div",{className:t.select,"data-testid":"breakdown-label-selector"},b().createElement(a.fS.Component,{model:e}))});const zi={OPEN_EXPLORE_LABEL:"Open in explore",COPY_URL_LABEL:"Copy url",BOOKMARK_LABEL:"Bookmark",SELECT_NEW_METRIC_TOOLTIP:"Remove existing metric and choose a new metric"};var Ui=n(7597);function Gi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ki extends a.Bs{onActivate(){const e=a.jh.getAncestor(this,pt);this.setState({currentPanelType:e.state.panelConfig.type}),this._subs.add(e.subscribeToState((e,t)=>{e.panelConfig.type!==t.panelConfig.type&&this.setState({currentPanelType:e.panelConfig.type})}))}constructor({metric:e}){super({metric:e,options:[{value:"percentiles",label:"percentiles"},{value:"heatmap",label:"heatmap"}],currentPanelType:void 0}),Gi(this,"onChange",e=>{(0,k.z)("histogram_panel_type_changed",{panelType:e}),this.publishEvent(new te({panelType:e}),!0)}),this.addActivationHandler(this.onActivate.bind(this))}}Gi(Ki,"Component",({model:e})=>{const{options:t,currentPanelType:n}=e.useState();return t.length?b().createElement(f.RadioButtonGroup,{size:"sm",options:t,value:n,onChange:e.onChange}):null});const Wi=n.p+"ac01ecbc64128d2f3e68.svg";var Qi=n(2533);function Yi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Xi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Yi(e,t,n[t])})}return e}function Ji(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const Zi=`${Qi.id}/investigation/v1`;class ea extends a.Bs{getPanelConfigAndDataFrames(){var e;const t=ds(this,e=>e instanceof a.Eb,a.Eb),n=a.jh.getData(this);return{fieldConfig:null==t?void 0:t.state.fieldConfig,frames:null==n||null===(e=n.state.data)||void 0===e?void 0:e.series}}constructor(e){super(Ji(Xi({},e),{queries:[]})),Yi(this,"_onActivate",()=>{this.subscribeToState(()=>{this.getQueries(),this.getContext()});const e=a.jh.interpolate(this,le);this.setState({dsUid:e})}),Yi(this,"getQueries",()=>{const e=a.jh.getData(this),t=a.jh.findObject(e,l);if(l(t)){const e=this.state.frame?ta(this.state.frame):null,n=t.state.queries.map(n=>Ji(Xi({},n),{expr:a.jh.interpolate(t,n.expr),legendFormat:(null==e?void 0:e.name)?`{{ ${e.name} }}`:a.jh.interpolate(t,n.legendFormat)}));JSON.stringify(n)!==JSON.stringify(this.state.queries)&&this.setState({queries:n})}}),Yi(this,"updateFieldConfigOverrides",()=>{const{fieldConfig:e,frames:t}=this.getPanelConfigAndDataFrames();if(e&&(null==t?void 0:t.length)){for(const i of t)for(const t of i.fields){const i=Object.keys(t.config).map(e=>({id:e,value:t.config[e]})),a=e.overrides.find(e=>{var n,r;return e.matcher.options===(null!==(r=null!==(n=t.config.displayNameFromDS)&&void 0!==n?n:t.config.displayName)&&void 0!==r?r:t.name)&&"byName"===e.matcher.id});var n,r;if(!a)e.overrides.unshift({matcher:{id:"byName",options:null!==(r=null!==(n=t.config.displayNameFromDS)&&void 0!==n?n:t.config.displayName)&&void 0!==r?r:t.name},properties:i});a&&JSON.stringify(a.properties)!==JSON.stringify(i)&&(a.properties=i)}return e}}),Yi(this,"getContext",()=>{const e=this.updateFieldConfigOverrides(),{queries:t,dsUid:n,labelName:r,fieldName:i}=this.state,o=a.jh.getTimeRange(this);if(!o||!t||!n)return;const s={origin:"Metrics Drilldown",type:"timeseries",queries:t,timeRange:Xi({},o.state.value),datasource:{uid:n},url:window.location.href,id:`${JSON.stringify(t)}${r}${i}`,title:r+(i?` > ${i}`:""),logoPath:Wi,drillDownLabel:i,fieldConfig:e};JSON.stringify(s)!==JSON.stringify(this.state.context)&&this.setState({context:s})}),this.addActivationHandler(this._onActivate.bind(this))}}Yi(ea,"Component",({model:e})=>{const{context:t}=e.useState(),{links:n}=(0,i.usePluginLinks)({extensionPointId:Zi,context:t,limitPerPlugin:1}),r=n.find(e=>"grafana-investigations-app"===e.pluginId);return r?b().createElement(f.IconButton,{tooltip:r.description,"aria-label":"add panel to exploration",key:r.id,name:null!==(a=r.icon)&&void 0!==a?a:"panel-add",onClick:e=>{r.onClick&&r.onClick(e)}}):null;var a});const ta=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{},i=Object.keys(r);if(1!==i.length)return;const a=i[0];return{name:a,value:r[a]}};function na(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function ra(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){na(a,r,i,o,s,"next",e)}function s(e){na(a,r,i,o,s,"throw",e)}o(void 0)})}}function ia(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function aa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ia(e,t,n[t])})}return e}function oa(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const sa="Add to investigation",la="investigations_divider",ca="Investigations";class ua extends a.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){var t,n;super(oa(aa({},e),{addExplorationsLink:null===(n=e.addExplorationsLink)||void 0===n||n})),(t=this).addActivationHandler(()=>{let e;try{const r=a.jh.getAncestor(t,a.Eb),i=a.jh.getData(r).state.data;if(!i)throw new Error("Cannot get link to explore, no panel data found");const o=s(r);var n;(null!==(n=null==o?void 0:o.state.queries)&&void 0!==n?n:[]).forEach(e=>{delete e.legendFormat}),e=(0,a.pN)(i,t,i.timeRange,e=>"expr"in e&&"string"==typeof e.expr&&e.expr.includes("__ignore_usage__")?oa(aa({},e),{expr:e.expr.replace(/,?__ignore_usage__="",?/,"")}):e)}catch(e){}const r=[{text:"Navigation",type:"group"},{text:"Explore",iconClassName:"compass",onClick:()=>null==e?void 0:e.then(e=>e&&window.open(e,"_blank")),shortcut:"p x"}];t.setState({body:new a.Lw({items:r})});const i=new ea({labelName:t.state.labelName,fieldName:t.state.fieldName,frame:t.state.frame});var o;(t._subs.add(null==i?void 0:i.subscribeToState(()=>ra(function*(){var e;yield(e=t,ra(function*(){const t=e.state.explorationsButton;if(t){var n;const l=yield da(t);var r;const c=null!==(r=null===(n=e.state.body)||void 0===n?void 0:n.state.items)&&void 0!==r?r:[],u=c.find(e=>e.text===sa);var i,a,o,s;l&&(u?u&&(null===(i=e.state.body)||void 0===i||i.setItems(c.filter(e=>!1===[la,ca,sa].includes(e.text)))):(null===(a=e.state.body)||void 0===a||a.addItem({text:la,type:"divider"}),null===(o=e.state.body)||void 0===o||o.addItem({text:ca,type:"group"}),null===(s=e.state.body)||void 0===s||s.addItem({text:sa,iconClassName:"plus-square",onClick:e=>l.onClick&&l.onClick(e)})))}})())})())),t.setState({explorationsButton:i}),t.state.addExplorationsLink)&&(null===(o=t.state.explorationsButton)||void 0===o||o.activate())})}}ia(ua,"Component",({model:e})=>{const{body:t}=e.useState();return t?b().createElement(t.Component,{model:t}):b().createElement(b().Fragment,null)});const da=e=>ra(function*(){const t=e.state.context;if(i.config.buildInfo.version.startsWith("11."))try{const e=(yield Promise.resolve().then(n.t.bind(n,8531,23))).getPluginLinkExtensions;if(void 0!==e){return e({extensionPointId:Zi,context:t}).extensions[0]}}catch(e){o.v.error(e,{message:"Error importing getPluginLinkExtensions"})}if("function"==typeof i.getObservablePluginLinks){return(yield(0,Ze.firstValueFrom)((0,i.getObservablePluginLinks)({extensionPointId:Zi,context:t})))[0]}})();function pa(e,t){return(null==t?void 0:t.state.embedded)||e.isLight?e.colors.background.primary:e.colors.background.canvas}function ma(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function ha(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){ma(a,r,i,o,s,"next",e)}function s(e){ma(a,r,i,o,s,"throw",e)}o(void 0)})}}const fa=B.XL,ga="topview-panel";class ba extends a.Bs{onActivate(){return ha(function*(){const{metric:e}=this.state,[t]=a.jh.findDescendents(this,pt);if("classic"===t.state.histogramType)return;const n=t.subscribeToState((r,i)=>ha(function*(){if("native"!==i.histogramType&&"native"===r.histogramType){n.unsubscribe();const r=yield ts(this).getMetadataForMetric(e);t.update({description:hn(r),headerActions:()=>[new Ki({metric:e}),new bt({metric:e})]},{})}}).call(this));this._subs.add(n)}).call(this)}constructor({metric:e}){super({metric:e,topView:new a.G1({direction:"column",$behaviors:[new a.Gg.K2({key:"metricCrosshairSync",sync:r.DashboardCursorSync.Crosshair})],children:[new a.vA({minHeight:fa,maxHeight:"40%",body:new pt({key:ga,metric:e,panelOptions:{height:B.XL,headerActions:M(e)?()=>[new Ki({metric:e}),new bt({metric:e})]:()=>[new bt({metric:e})],menu:()=>new ua({labelName:e})},queryOptions:{resolution:ye.HIGH}})}),new a.vA({ySizing:"content",body:new so({})})]}),selectedTab:void 0}),this.addActivationHandler(()=>{this.onActivate()})}}function ya(e,t,n){return{container:(0,m.css)({display:"flex",flexDirection:"column",position:"relative",flexGrow:1}),tabContent:(0,m.css)({height:"100%"}),topView:(0,m.css)({}),sticky:(0,m.css)({display:"flex",flexDirection:"row",background:pa(e,n),position:"sticky",paddingTop:e.spacing(1),marginTop:`-${e.spacing(1)}`,zIndex:10,top:`calc(var(--app-controls-height, 0px) + ${t}px)`}),nonSticky:(0,m.css)({display:"flex",flexDirection:"row"})}}function va(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function wa(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(ba,"Component",({model:e})=>{const{topView:t,selectedTab:n}=e.useState(),{stickyMainGraph:r}=ns(e).useState(),a=(0,i.useChromeHeaderHeight)(),o=ts(e),s=(0,f.useStyles2)(ya,o.state.embedded?0:null!=a?a:0,o);return b().createElement("div",{className:s.container},b().createElement("div",{className:r?(0,m.cx)(s.topView,s.sticky):(0,m.cx)(s.topView,s.nonSticky),"data-testid":"top-view"},b().createElement(t.Component,{model:t})),n&&b().createElement("div",{"data-testid":"tab-content",className:s.tabContent},b().createElement(n.Component,{model:n})))});const Sa={label:"All metric names",value:"all"};class Oa extends a.Bs{getUrlState(){return{metricPrefix:this.state.value}}updateFromUrl(e){"string"!=typeof e.metricPrefix?this.setState({value:Sa.value}):this.state.value!==e.metricPrefix&&this.setState({value:e.metricPrefix})}onActivate(){this.parseMetricPrefixes()}parseMetricPrefixes(){if(this._variableDependency.hasDependencyInLoadingState())return void this.setState({error:void 0,loading:!0});const e=a.jh.lookupVariable(sn,this);if(e.state.error)return void this.setState({error:e.state.error,loading:!1,options:[]});const t=Or(xt(e)),n=[Sa,...t.map(e=>({value:e.value,label:`${e.label} (${e.count})`}))],{value:r}=this.state,i=n.find(e=>e.value===r)?r:Sa.value;this.setState({error:null,loading:!1,options:n}),this.selectOption({value:i,label:i})}constructor(e){super(wa(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){va(e,t,n[t])})}return e}({},e),{key:"related-prefix-filter",loading:!0,error:null,options:[Sa],value:Sa.value})),va(this,"_variableDependency",new a.Sh(this,{variableNames:[sn],onVariableUpdateCompleted:()=>this.parseMetricPrefixes()})),va(this,"_urlSync",new a.So(this,{keys:["metricPrefix"]})),va(this,"selectOption",e=>{const t=null===e?Sa.value:e.value;this.setState({value:t}),this.publishEvent(new sr({type:"prefixes",filters:t===Sa.value?[]:[t]}),!0)}),this.addActivationHandler(this.onActivate.bind(this))}}function Ea(e){return{container:m.css`
      display: flex;

      & > div {
        margin: 0;
      }
    `,label:m.css`
      margin-right: 0;
      background-color: ${e.colors.background.primary};
      border: 1px solid ${e.colors.border.medium};
      border-right: 0 none;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    `,tooltipIcon:m.css`
      margin-left: ${e.spacing(.5)};
    `}}function xa(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Pa(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}va(Oa,"Component",({model:e})=>{const t=(0,f.useStyles2)(Ea),{loading:n,options:r,value:i,error:a}=e.useState();return b().createElement("div",{className:t.container,"data-testid":"prefix-filter-selector"},b().createElement(f.InlineField,{disabled:n,error:a&&a.toString(),label:b().createElement(f.InlineLabel,{width:"auto",className:t.label},b().createElement("span",null,"View by"),b().createElement(f.Tooltip,{content:"View by the metric prefix. A metric prefix is a single word at the beginning of the metric name, relevant to the domain the metric belongs to.",placement:"top"},b().createElement(f.Icon,{className:t.tooltipIcon,name:"info-circle",size:"sm"})))},b().createElement(f.Combobox,{value:i,onChange:e.selectOption,options:r})))});class ka extends a.P1{constructor(e){super(Pa(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){xa(e,t,n[t])})}return e}({},e),{key:"related-list-controls",body:new a.G1({direction:"row",width:"100%",maxHeight:"32px",children:[new a.vA({width:"auto",body:new Oa({})}),new a.vA({body:new Gn({urlSearchParamName:"gmd-relatedSearchText",targetName:"related metric",countsProvider:new Hn,displayCounts:!0})}),new a.vA({width:"auto",body:new St({})})]})}))}}function Ca(){return{headerWrapper:(0,m.css)({display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center"}}})}}xa(ka,"Component",({model:e})=>{const t=(0,f.useStyles2)(Ca),{body:n}=e.useState();return b().createElement("div",{className:t.headerWrapper,"data-testid":"related-list-controls"},b().createElement(n.Component,{model:n}))});class ja extends a.Bs{onActivate(){this.subscribeToEvents()}subscribeToEvents(){this.initVariablesFilteringAndSorting()}initVariablesFilteringAndSorting(){const{metric:e}=this.state,t=new Map;this.subscribeToEvent(Pn,e=>{const{key:n}=e.payload,r=a.jh.findByKey(this,n);t.set(n,{filterEngine:new Zn(r),sortEngine:new or(r)})}),this.subscribeToEvent(kn,e=>{t.delete(e.payload.key)});const n=a.jh.findByKeyAndType(this,"quick-search",Gn);this.subscribeToEvent(Cn,r=>{const{key:i,options:a}=r.payload,{filterEngine:o,sortEngine:s}=t.get(i);o.setInitOptions(a);const l={names:n.state.value?[n.state.value]:[]};o.applyFilters(l,{forceUpdate:!0,notify:!1}),s.sort("related",{metric:e})}),this.subscribeToEvent(zn,n=>{const{searchText:r}=n.payload;for(const[,{filterEngine:n,sortEngine:i}]of t)n.applyFilters({names:r?[r]:[]}),i.sort("related",{metric:e})}),this.subscribeToEvent(sr,n=>{const{type:r,filters:i}=n.payload;for(const[,{filterEngine:n,sortEngine:a}]of t)n.applyFilters({[r]:i}),a.sort("related",{metric:e})})}constructor({metric:e}){super({metric:e,$variables:new a.Pj({variables:[new Rn]}),key:"RelatedMetricsScene",body:new di({variableName:$n}),listControls:new ka({})}),this.addActivationHandler(this.onActivate.bind(this))}}function _a(e){return{body:(0,m.css)({}),list:(0,m.css)({}),listControls:(0,m.css)({margin:e.spacing(1,0,1.5,0)}),variables:(0,m.css)({display:"none"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(ja,"Component",({model:e})=>{const t=(0,f.useStyles2)(_a),{$variables:n,body:r,listControls:i}=e.useState();return b().createElement(b().Fragment,null,b().createElement("div",{className:t.listControls},b().createElement(i.Component,{model:i})),b().createElement("div",{className:t.body},b().createElement("div",{className:t.list,"data-testid":"panels-list"},b().createElement(r.Component,{model:r}))),b().createElement("div",{className:t.variables},null==n?void 0:n.state.variables.map(e=>b().createElement(e.Component,{key:e.state.name,model:e}))))});const Ta=zi.COPY_URL_LABEL,Ia=({trail:e})=>{const[t,n]=(0,g.useState)(Ta);return b().createElement(f.ToolbarButton,{variant:"canvas",icon:"share-alt",tooltip:t,onClick:()=>{if(navigator.clipboard){(0,k.z)("selected_metric_action_clicked",{action:"share_url"});const t=`${i.config.appUrl.endsWith("/")?i.config.appUrl.slice(0,-1):i.config.appUrl}${p.Gy}/${is(e)}`;navigator.clipboard.writeText(t),n("Copied!"),setTimeout(()=>{n(Ta)},2e3)}}})},Da=(e={})=>t=>{const[n]=a.jh.findDescendents(t,a.dt);if(!n)return;const i=t.state.title,o=n.subscribeToState(n=>{var a;if((null===(a=n.data)||void 0===a?void 0:a.state)!==r.LoadingState.Done)return;const{series:o}=n.data;if(!(null==o?void 0:o.length))return;const s={title:`${i} (${o.length})`};var l,c;o.length>st&&(s.description=`Showing only ${st} series out of ${o.length} to keep the data easy to read.`,s.description+="string"==typeof(null===(l=e.description)||void 0===l?void 0:l.ctaText)?` ${null===(c=e.description)||void 0===c?void 0:c.ctaText}`:' Click on "Select" on this panel to view a breakdown of all the label\'s values.');t.setState(s)});return()=>{o.unsubscribe()}};function Aa(){return e=>{var t;if("timeseries"!==e.state.pluginId)return;let n=a.jh.getData(e);n instanceof a.Es&&(n=n.state.$data);const{data:i}=n.state;(null==i?void 0:i.state)===r.LoadingState.Done&&(null===(t=i.series)||void 0===t?void 0:t.length)&&e.publishEvent(new v({panelKey:e.state.key,series:i.series}),!0);const o=n.subscribeToState((t,n)=>{var i,a,o;if((null===(i=t.data)||void 0===i?void 0:i.state)===r.LoadingState.Done&&(null===(a=t.data.series)||void 0===a?void 0:a.length)&&t.data.series!==(null===(o=n.data)||void 0===o?void 0:o.series)){var s;const n=null===(s=t.data.series[0].meta)||void 0===s?void 0:s.type;if(n&&!n.startsWith("timeseries"))return;e.publishEvent(new v({panelKey:e.state.key,series:t.data.series}),!0)}});return()=>{o.unsubscribe()}}}function La(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Na extends a.Bs{constructor(...e){super(...e),La(this,"onClick",()=>{const{label:e}=this.state;(0,k.z)("breakdown_panel_selected",{label:e});const t=a.jh.lookupVariable(oe,this);if(!jt(t))throw new Error("Group by variable not found");t.changeValueTo(e)})}}La(Na,"Component",({model:e})=>b().createElement(f.Button,{variant:"secondary",size:"sm",fill:"outline",onClick:e.onClick},"Select"));class Ba extends a.Bs{onActivate(){this.subscribeToLayoutChange(),this.subscribeToEvents()}subscribeToEvents(){const e=new Map;this.subscribeToEvent(v,t=>{const{panelKey:n,series:r}=t.payload,i=a.jh.findByKeyAndType(this,n,a.Eb);if(1===r.length)return e.has(n)||e.set(n,i.state.headerActions||[]),void i.setState({headerActions:[]});e.has(n)&&i.setState({headerActions:e.get(n)})})}subscribeToLayoutChange(){const e=a.jh.findByKeyAndType(this,"layout-switcher",St),t=this.state.body.state.body,n=(e,n)=>{e.layout!==(null==n?void 0:n.layout)&&t.setState({templateColumns:e.layout===wt.ROWS?ui:ci})};a.Go.syncStateFromSearchParams(e,new URLSearchParams(window.location.search)),n(e.state),this._subs.add(e.subscribeToState(n))}Controls({model:e}){const{layoutSwitcher:t}=e.useState();return b().createElement(f.Field,{label:"View"},b().createElement(t.Component,{model:t}))}constructor({metric:e}){super({key:"metric-labels-list",metric:e,layoutSwitcher:new St({}),body:new Et({variableName:oe,initialPageSize:60,pageSizeIncrement:9,body:new a.gF({children:[],isLazy:!0,templateColumns:ci,autoRows:B.M,$behaviors:[new a.Gg.K2({key:"metricCrosshairSync",sync:r.DashboardCursorSync.Crosshair}),O()]}),getLayoutLoading:()=>new a.dM({reactNode:b().createElement(f.Spinner,{inline:!0})}),getLayoutEmpty:()=>new a.dM({reactNode:b().createElement(ht._,{title:"",severity:"info"},"No labels found for the current filters and time range.")}),getLayoutError:e=>new a.dM({reactNode:b().createElement(ht._,{severity:"error",title:"Error while loading labels!",error:e})}),getLayoutChild:(t,n)=>{const r=t.value;return new a.xK({body:ot({metric:e,panelConfig:{type:"timeseries",height:B.M,title:r,fixedColorIndex:n,behaviors:[Aa(),Da()],headerActions:()=>[new Na({label:r})],menu:()=>new ua({labelName:r}),legend:{placement:"bottom"}},queryConfig:{resolution:ye.MEDIUM,groupBy:r,labelMatchers:[],addIgnoreUsageFilter:!0}})})}})}),this.addActivationHandler(this.onActivate.bind(this))}}function Ma(e){return{container:(0,m.css)({width:"100%"}),footer:(0,m.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}})}}function $a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Ba,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,f.useStyles2)(Ma),r=a.jh.lookupVariable(oe,e),{loading:i,error:o}=r.useState(),s=t.useSizes(),l=!i&&!o&&s.total>0&&s.current<s.total;return b().createElement("div",{"data-testid":"labels-list"},b().createElement("div",{className:n.container},b().createElement(t.Component,{model:t})),l&&b().createElement("div",{className:n.footer},b().createElement(Pt,{label:"label",batchSizes:s,onClick:()=>{t.increaseBatchSize()}})))});class Ra extends a.Bs{constructor(...e){super(...e),$a(this,"onClick",()=>{const{labelName:e,labelValue:t}=this.state;(0,k.z)("label_filter_changed",{label:e,action:"added",cause:"breakdown"}),ts(this).addFilterWithoutReportingInteraction({key:e,operator:"=",value:t})})}}function Fa(e){var t;const n=(null===(t=e.fields[1])||void 0===t?void 0:t.labels)||{},r=Object.keys(n);return 0===r.length?"<unspecified>":n[r[0]]}$a(Ra,"Component",({model:e})=>b().createElement(f.Button,{variant:"secondary",size:"sm",fill:"outline",onClick:e.onClick},"Add to filters"));var Va=n(619);function qa(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ha extends a.Bs{constructor(e){const t=j.x.getItem(C.V.BREAKDOWN_SORTBY);super({key:"breakdown-sort-by",target:e.target,options:Ha.DEFAULT_OPTIONS,value:t&&Ha.DEFAULT_OPTIONS.find(e=>e.value===t)||Ha.DEFAULT_OPTIONS[0]}),qa(this,"onChange",e=>{this.setState({value:e}),j.x.setItem(C.V.BREAKDOWN_SORTBY,e.value)})}}function za(e){return{sortByTooltip:(0,m.css)({display:"flex",gap:e.spacing(1)})}}function Ua(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}qa(Ha,"DEFAULT_OPTIONS",[{value:"outliers",label:"Outlying series",description:"Prioritizes values that show distinct behavior from others within the same label"},{value:"alphabetical",label:"Name [A-Z]",description:"Alphabetical order"},{value:"alphabetical-reversed",label:"Name [Z-A]",description:"Reversed alphabetical order"}]),qa(Ha,"Component",({model:e})=>{const t=(0,f.useStyles2)(za),{value:n,options:r}=e.useState();return b().createElement(f.Field,{"data-testid":"sort-by-select",htmlFor:"sort-by-criteria",label:b().createElement("div",{className:t.sortByTooltip},"Sort by",b().createElement(f.IconButton,{name:"info-circle",size:"sm",variant:"secondary",tooltip:"Sorts values using standard or smart time series calculations."}))},b().createElement(f.Combobox,{id:"sort-by-criteria",placeholder:"Choose criteria",width:20,options:r,value:n,onChange:e.onChange,isClearable:!1}))});class Ga extends a.Bs{performRepeat(e){var t,n,i,a;if(e.state===r.LoadingState.Loading)return void this.setState({loadingLayout:null===(t=(n=this.state).getLayoutLoading)||void 0===t?void 0:t.call(n),errorLayout:void 0,emptyLayout:void 0,currentBatchSize:0});if(e.state===r.LoadingState.Error)return void this.setState({errorLayout:null===(i=(a=this.state).getLayoutError)||void 0===i?void 0:i.call(a,e),loadingLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const o=this.filterAndSort(e.series);var s,l;if(!o.length)return void this.setState({emptyLayout:null===(s=(l=this.state).getLayoutEmpty)||void 0===s?void 0:s.call(l),errorLayout:void 0,loadingLayout:void 0,currentBatchSize:0,counts:{current:0,total:e.series.length}});this.setState({loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,currentBatchSize:this.state.initialPageSize,counts:{current:o.length,total:e.series.length}});const c=o.slice(0,this.state.initialPageSize).map((t,n)=>this.state.getLayoutChild(e,t,n)).filter(Boolean);this.state.body.setState({children:c})}initFilterAndSort(){this.searchText=a.jh.findByKeyAndType(this,"quick-search",Gn).state.value,this.sortBy=a.jh.findByKeyAndType(this,"breakdown-sort-by",Ha).state.value.value}filterAndSort(e){let t=[];if(this.searchText){const n=this.searchText.split(",").map(e=>e.trim()).filter(Boolean).map(e=>{try{return new RegExp(e)}catch(e){return null}}).filter(Boolean);for(let r=0;r<e.length;r+=1){const i=e[r];n.some(e=>e.test(Fa(i)))&&t.push(i)}}else t=e;return this.sortBy&&(t=(0,Va.sortSeries)(t,this.sortBy)),t}filter(e){this.searchText=e;const{data:t}=a.jh.getData(this).state;t&&(this.publishEvent(new S({}),!0),this.performRepeat(t))}sort(e){this.sortBy=e;const{data:t}=a.jh.getData(this).state;t&&(this.publishEvent(new S({}),!0),this.performRepeat(t))}increaseBatchSize(){const{data:e}=a.jh.getData(this).state;if(!e)return;const t=this.state.currentBatchSize+this.state.pageSizeIncrement,n=this.filterAndSort(e.series).slice(this.state.currentBatchSize,t).map((t,n)=>this.state.getLayoutChild(e,t,n)).filter(Boolean);this.state.body.setState({children:[...this.state.body.state.children,...n]}),this.setState({currentBatchSize:t}),this.publishEvent(new w({}),!0)}useSizes(){const{currentBatchSize:e,pageSizeIncrement:t}=this.useState(),{data:n}=a.jh.getData(this).state,r=n?this.filterAndSort(n.series).length:0,i=r-e;return{increment:i<t?i:t,current:e,total:r}}getCounts(){const{data:e}=a.jh.getData(this).state;return{current:0,total:e?e.series.length:0}}constructor({$behaviors:e,body:t,getLayoutChild:n,getLayoutLoading:r,getLayoutError:i,getLayoutEmpty:o,initialPageSize:s,pageSizeIncrement:l,$data:c}){super({key:"breakdown-by-frame-repeater",$behaviors:e,body:t,getLayoutChild:n,getLayoutLoading:r,getLayoutError:i,getLayoutEmpty:o,currentBatchSize:0,initialPageSize:s||120,pageSizeIncrement:l||9,loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,counts:{current:0,total:0},$data:c}),Ua(this,"searchText",""),Ua(this,"sortBy",void 0),this.addActivationHandler(()=>{const e=a.jh.getData(this);if(!e)throw new Error("No data provider found!");this.initFilterAndSort(),this._subs.add(e.subscribeToState(e=>{e.data&&this.performRepeat(e.data)})),e.state.data&&this.performRepeat(e.state.data)})}}Ua(Ga,"Component",({model:e})=>{const{body:t,loadingLayout:n,errorLayout:r,emptyLayout:i}=e.useState();return n?b().createElement(n.Component,{model:n}):r?b().createElement(r.Component,{model:r}):i?b().createElement(i.Component,{model:i}):b().createElement(t.Component,{model:t})});class Ka extends qn{constructor(){super({key:"LabelValuesCountsProvider"}),this.addActivationHandler(()=>{const e=a.jh.findByKeyAndType(this,"breakdown-by-frame-repeater",Ga);this._subs.add(e.subscribeToState((e,t)=>{e.counts!==t.counts&&this.setState({counts:e.counts})}))})}}function Wa(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Qa(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Wa(e,t,n[t])})}return e}function Ya(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class Xa extends a.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToQuickSearchChange(){a.Go.syncStateFromSearchParams(this.state.quickSearch,new URLSearchParams(window.location.search)),this._subs.add(this.subscribeToEvent(zn,e=>{const t=a.jh.findDescendents(this,Ga)[0];t&&t.filter(e.payload.searchText)}))}subscribeToSortByChange(){const{sortBySelector:e}=this.state;this._subs.add(e.subscribeToState((e,t)=>{if(e.value.value!==(null==t?void 0:t.value.value)){const t=a.jh.findDescendents(this,Ga)[0];t&&t.sort(e.value.value)}}))}subscribeToLayoutChange(){const{layoutSwitcher:e}=this.state;a.Go.syncStateFromSearchParams(e,new URLSearchParams(window.location.search));const t=(e,t)=>{e.layout!==(null==t?void 0:t.layout)&&this.updateBody(e.layout)};t(e.state),this._subs.add(e.subscribeToState(t))}updateBody(e){if(e===wt.SINGLE)return void this.setState({body:this.buildSinglePanel()});const t=a.jh.findDescendents(this,Ga)[0],n=t||this.buildByFrameRepeater();n.state.body.setState({templateColumns:e===wt.ROWS?ui:ci}),this.setState({body:n}),t||(this.subscribeToQuickSearchChange(),this.subscribeToSortByChange())}buildSinglePanel(){const{metric:e,label:t}=this.state;return new pt({metric:e,discardUserPrefs:!0,panelOptions:{type:"timeseries",height:B.XL,headerActions:()=>[],behaviors:[Da({description:{ctaText:""}})]},queryOptions:{groupBy:t,data:a.jh.getData(this)}})}buildByFrameRepeater(){const{metric:e,label:t}=this.state,n=N(e);return new Ga({$behaviors:[O(),new a.Gg.K2({key:"metricCrosshairSync",sync:r.DashboardCursorSync.Crosshair})],body:new a.gF({children:[],isLazy:!0,templateColumns:ci,autoRows:B.M}),getLayoutLoading:()=>new a.dM({reactNode:b().createElement(f.Spinner,{inline:!0})}),getLayoutEmpty:()=>new a.dM({reactNode:b().createElement(ht._,{title:"",severity:"info"},"No label values found for the current filters and time range.")}),getLayoutError:e=>new a.dM({reactNode:b().createElement(ht._,{severity:"error",title:"Error while loading metrics!",error:e.errors[0]})}),getLayoutChild:(r,i,o)=>{if(i.length<2)return null;const s=Fa(i),l=!s.startsWith("<unspecified"),c=new pt({metric:e,discardUserPrefs:!0,panelOptions:Ya(Qa({},null==n?void 0:n.panelOptions),{title:s,fixedColorIndex:o,description:"",headerActions:l?()=>[new Ra({labelName:t,labelValue:s})]:()=>[],menu:()=>new ua({labelName:s}),behaviors:[Aa()]}),queryOptions:Ya(Qa({},null==n?void 0:n.queryOptions),{labelMatchers:[{key:t,operator:"=",value:s}]})});return new a.xK({body:c})}})}Controls({model:e}){const t=(0,f.useStyles2)(Ja),{body:n,quickSearch:r,layoutSwitcher:i,sortBySelector:a}=e.useState();return b().createElement(b().Fragment,null,n instanceof Ga&&b().createElement(b().Fragment,null,b().createElement(f.Field,{className:t.quickSearchField,label:"Search"},b().createElement(r.Component,{model:r})),b().createElement(a.Component,{model:a})),b().createElement(f.Field,{label:"View"},b().createElement(i.Component,{model:i})))}constructor({metric:e,label:t}){const n=He({metric:e,queryConfig:{resolution:ye.MEDIUM,labelMatchers:[],addIgnoreUsageFilter:!0,groupBy:t}});super({key:"metric-label-values-list",metric:e,label:t,layoutSwitcher:new St({urlSearchParamName:"breakdownLayout",options:[{label:"Single",value:wt.SINGLE},{label:"Grid",value:wt.GRID},{label:"Rows",value:wt.ROWS}]}),quickSearch:new Gn({urlSearchParamName:"breakdownSearchText",targetName:"label value",countsProvider:new Ka,displayCounts:!0}),sortBySelector:new Ha({target:"labels"}),$data:new a.Es({$data:new a.dt({datasource:pe,maxDataPoints:n.maxDataPoints,queries:n.queries}),transformations:[rt(t)]}),body:void 0}),this.addActivationHandler(this.onActivate.bind(this))}}function Ja(e){return{singlePanelContainer:(0,m.css)({width:"100%",height:"300px"}),listContainer:(0,m.css)({width:"100%"}),listFooter:(0,m.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}}),quickSearchField:(0,m.css)({flexGrow:1})}}Wa(Xa,"Component",({model:e})=>{const{body:t}=e.useState();return b().createElement(b().Fragment,null,t instanceof pt&&b().createElement(Xa.SingleMetricPanelComponent,{model:e}),t instanceof Ga&&b().createElement(Xa.ByFrameRepeaterComponent,{model:e}))}),Wa(Xa,"SingleMetricPanelComponent",({model:e})=>{const t=(0,f.useStyles2)(Ja),{body:n}=e.useState();return b().createElement("div",{"data-testid":"single-metric-panel"},b().createElement("div",{className:t.singlePanelContainer},n instanceof pt&&b().createElement(n.Component,{model:n})))}),Wa(Xa,"ByFrameRepeaterComponent",({model:e})=>{const t=(0,f.useStyles2)(Ja),{body:n}=e.useState(),i=a.jh.getData(e),{state:o,errors:s}=i.useState().data||{},l=n,c=l.useSizes(),u=o!==r.LoadingState.Loading&&!(null==s?void 0:s.length)&&c.total>0&&c.current<c.total;return b().createElement("div",{"data-testid":"label-values-list"},b().createElement("div",{className:t.listContainer},n instanceof Ga&&b().createElement(n.Component,{model:n})),u&&b().createElement("div",{className:t.listFooter},b().createElement(Pt,{label:"label value",batchSizes:c,onClick:()=>{l.increaseBatchSize()}})))});class Za extends a.Bs{onActivate(){const e=this.getVariable();e.subscribeToState((t,n)=>{t.value!==n.value&&this.updateBody(e)}),i.config.featureToggles.enableScopesInMetricsExplore&&this.subscribeToEvent(fe,()=>{this.updateBody(e)}),this.updateBody(e)}getVariable(){const e=a.jh.lookupVariable(oe,this);if(!jt(e))throw new Error("Group by variable not found");return e}updateBody(e){const{metric:t}=this.state;this.setState({body:e.hasAllValue()?new Ba({metric:t}):new Xa({metric:t,label:e.state.value})})}constructor({metric:e}){super({metric:e,body:void 0}),this.addActivationHandler(this.onActivate.bind(this))}}function eo(e){return{container:(0,m.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column",paddingTop:e.spacing(1)}),controls:(0,m.css)({flexGrow:0,display:"flex",gap:e.spacing(2),height:"70px",justifyContent:"space-between",alignItems:"end"}),searchField:(0,m.css)({flexGrow:1})}}function to(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function no(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){to(a,r,i,o,s,"next",e)}function s(e){to(a,r,i,o,s,"throw",e)}o(void 0)})}}function ro(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Za,"Component",({model:e})=>{const t=(0,f.useStyles2)(eo),{body:n}=e.useState(),r=e.getVariable();return b().createElement("div",{className:t.container},b().createElement("div",{className:t.controls},b().createElement(f.Field,{label:"By label"},b().createElement(r.Component,{model:r})),n instanceof Ba&&b().createElement(n.Controls,{model:n}),n instanceof Xa&&b().createElement(n.Controls,{model:n})),b().createElement("div",{"data-testid":"panels-list"},n instanceof Ba&&b().createElement(n.Component,{model:n}),n instanceof Xa&&b().createElement(n.Component,{model:n})))});const io="breakdown",ao="logs",oo=[{displayName:"Breakdown",value:io,getScene:e=>new Za({metric:e.state.metric})},{displayName:"Related metrics",value:"related",getScene:e=>new ja({metric:e.state.metric}),description:"Relevant metrics based on current label filters"},{displayName:"Related logs",value:ao,getScene:e=>e.createRelatedLogsScene(),description:"Relevant logs based on current label filters and time range"}];class so extends a.Bs{constructor(...e){var t;super(...e),ro(t=this,"getLinkToExplore",()=>no(function*(){const e=a.jh.findByKeyAndType(t,ga,pt),n=a.jh.findDescendents(e,a.dt)[0].state.data;if(!n)throw new Error("Cannot get link to explore, no panel data found");const r=a.jh.getAncestor(t,Lo);return(0,a.pN)(n,r,n.timeRange)})()),ro(t,"openExploreLink",()=>no(function*(){(0,k.z)("selected_metric_action_clicked",{action:"open_in_explore"}),t.getLinkToExplore().then(e=>{window.open(e,"_blank")})})()),ro(t,"useBookmarkState",e=>{const{bookmarks:n,addBookmark:r,removeBookmark:i}=Tr(t),[o,s]=(0,g.useState)(),l=(0,g.useMemo)(()=>n.some(e=>e.key===o),[n,o]);(0,g.useEffect)(()=>{const t=e.subscribeToEvent(a.bZ,(0,y.debounce)(()=>s(kr(a.Go.getUrlState(e))),100));return()=>t.unsubscribe()},[e]);return{isBookmarked:l,toggleBookmark:()=>{(0,k.z)("bookmark_changed",{action:l?"toggled_off":"toggled_on"}),l?o&&i(o):r()}}})}}function lo(e){return{actions:(0,m.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,top:16,zIndex:2}}),customTabsBar:(0,m.css)({paddingBottom:e.spacing(1)})}}function co(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function uo(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){co(a,r,i,o,s,"next",e)}function s(e){co(a,r,i,o,s,"throw",e)}o(void 0)})}}ro(so,"Component",({model:e})=>{const t=a.jh.getAncestor(e,Lo),n=(0,f.useStyles2)(lo),r=ts(e),{isBookmarked:i,toggleBookmark:o}=e.useBookmarkState(r),{actionView:s}=t.useState();return b().createElement(f.Box,{paddingY:1,"data-testid":"action-bar"},b().createElement("div",{className:n.actions},b().createElement(f.Stack,{gap:1},r.state.embedded?b().createElement(f.LinkButton,{href:(0,Ui.Rk)(is(r)),variant:"secondary",icon:"arrow-right",tooltip:"Open in Metrics Drilldown",onClick:()=>(0,k.z)("selected_metric_action_clicked",{action:"open_from_embedded"})},"Metrics Drilldown"):b().createElement(f.ToolbarButton,{variant:"canvas",tooltip:zi.SELECT_NEW_METRIC_TOOLTIP,onClick:()=>{(0,k.z)("selected_metric_action_clicked",{action:"unselect"}),r.publishEvent(new he({}))}},"Select new metric"),b().createElement(f.ToolbarButton,{variant:"canvas",icon:"compass",tooltip:zi.OPEN_EXPLORE_LABEL,onClick:e.openExploreLink}),b().createElement(Ia,{trail:r}),b().createElement(f.ToolbarButton,{variant:"canvas",icon:i?b().createElement(f.Icon,{name:"favorite",type:"mono",size:"lg"}):b().createElement(f.Icon,{name:"star",type:"default",size:"lg"}),tooltip:zi.BOOKMARK_LABEL,onClick:o}))),b().createElement(f.TabsBar,{className:n.customTabsBar},oo.map((e,n)=>{const r=e.displayName,i=e.value===ao?t.state.relatedLogsCount:void 0,a=s===e.value,o=b().createElement(f.Tab,{key:n,label:r,counter:i,active:a,onChangeTab:()=>{a||((0,k.z)("metric_action_view_changed",{view:e.value,related_logs_count:t.relatedLogsOrchestrator.checkConditionsMetForRelatedLogs()?i:void 0}),t.setActionView(e.value))}});return e.description?b().createElement(f.Tooltip,{key:n,content:e.description,placement:"top",theme:"info"},o):o})))});const po={job:"service_name",instance:"service_instance_id"};function mo(e){return e in po?po[e]:e}const ho=e=>{let t=!1;return{name:"labelsCrossReference",checkConditionsMetForRelatedLogs:()=>t,getDataSources:()=>uo(function*(){var n;const r=ts(e),o=a.jh.lookupVariable(ie,r);if(!kt(o)||!o.state.filters.length)return t=!1,[];t=!0;const s=o.state.filters.map(({key:e,operator:t,value:n})=>({key:e,operator:t,value:n})),l=null===(n=e.state.$timeRange)||void 0===n?void 0:n.state.value,c=yield(0,Bt.tS)().getHealthyDataSources("loki"),u=yield Promise.all(c.map(({uid:e,name:t})=>uo(function*(){const n=yield function(e,t,n){return uo(function*(){var r;const a=yield(0,i.getDataSourceSrv)().get(e),o=yield null===(r=a.getTagKeys)||void 0===r?void 0:r.call(a,{timeRange:n,filters:t.map(({key:e,operator:t,value:n})=>({key:mo(e),operator:t,value:n}))});if(!Array.isArray(o))return!1;const s=new Set(o.map(e=>e.text));return!!t.map(e=>mo(e.key)).every(e=>s.has(e))&&(yield Promise.all(t.map(e=>uo(function*(){var r;const i=mo(e.key),o=yield null===(r=a.getTagValues)||void 0===r?void 0:r.call(a,{key:i,timeRange:n,filters:t});return!!Array.isArray(o)&&o.some(t=>t.text===e.value)})()))).every(Boolean)})()}(e,s,l);return n?{uid:e,name:t}:null})()));return u.filter(e=>null!==e)})(),getLokiQueryExpr(){const t=ts(e),n=a.jh.lookupVariable(ie,t);if(!kt(n)||!n.state.filters.length)return"";return`{${n.state.filters.map(e=>`${mo(e.key)}${e.operator}"${e.value}"`).join(",")}}`}}};var fo=n(6365);function go(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function bo(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){go(a,r,i,o,s,"next",e)}function s(e){go(a,r,i,o,s,"throw",e)}o(void 0)})}}function yo(e,t,n){if(!t||!n[t])return"";const r=n[t].find(t=>t.name===e);if(!r)return"";return function(e){if(function(e){if(e.trim().length<=2)return!1;let t=!1;const n=fo.K3.parse(e);return n.iterate({enter:({type:e})=>{if(e.id===fo.Yw)return t=!0,!1}}),!t}(e))return e;const t=So(e,fo.MD);if(!t)return"";const n=e.substring(t.from,t.to),r=So(e,fo.AL),i=r?e.substring(r.from,r.to):"";return`${n} ${i}`.trim()}(r.query)}function vo(){return bo(function*(){const e=yield(0,Bt.tS)().getHealthyDataSources("loki"),t={};return yield Promise.all(e.map(e=>bo(function*(){try{const r=function(e,t){if(0===e.length)return[];const n=new Map;return e.forEach(e=>{e.rules.filter(e=>"recording"===e.type).forEach(({type:e,name:r,query:i})=>{if(n.has(r)){const e=n.get(r);e&&(e.hasMultipleOccurrences=!0,n.set(r,e))}else n.set(r,{type:e,name:r,query:i,datasource:{name:t.name,uid:t.uid},hasMultipleOccurrences:!1})})}),Array.from(n.values())}(yield(n=e,bo(function*(){const e={url:`api/prometheus/${n.uid}/api/v1/rules`,showErrorAlert:!1,showSuccessAlert:!1},t=yield(0,Ze.lastValueFrom)((0,i.getBackendSrv)().fetch(e));return t.ok?t.data.data.groups:(o.v.warn(`Failed to fetch recording rules from Loki data source: ${n.name}`),[])})()),e);t[e.uid]=r}catch(e){o.v.warn(e)}var n})())),t})()}const wo=()=>{let e={},t=!1;return{name:"lokiRecordingRules",checkConditionsMetForRelatedLogs:()=>t,getDataSources:n=>bo(function*(){e=yield vo();const r=function(e,t){const n=[];return Object.values(t).forEach(t=>{t.filter(t=>t.name===e).forEach(e=>{n.push(e.datasource)})}),n}(n,e);return t=Boolean(r.length),r})(),getLokiQueryExpr:(t,n)=>yo(t,n,e)}};function So(e,t){let n;return fo.K3.parse(e).iterate({enter:e=>{if(e.type.id===t)return n=e.node,!1}}),n}function Oo(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Eo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class xo{get lokiDataSources(){return this._internalState.lokiDataSources}set lokiDataSources(e){const t=this._internalState.lokiDataSources.map(e=>e.uid).join(","),n=e.map(e=>e.uid).join(",");t&&t===n||(this._internalState.lokiDataSources=e,this._changeHandlers.lokiDataSources.forEach(e=>e(this._internalState.lokiDataSources)))}set relatedLogsCount(e){this._internalState.relatedLogsCount=e,this._changeHandlers.relatedLogsCount.forEach(e=>e(this._internalState.relatedLogsCount))}addLokiDataSourcesChangeHandler(e){this._changeHandlers.lokiDataSources.push(e)}addRelatedLogsCountChangeHandler(e){this._changeHandlers.relatedLogsCount.push(e)}handleFiltersChange(){this.lokiDataSources&&(this.lokiDataSources=[],this.relatedLogsCount=0,this.findAndCheckAllDatasources())}findAndCheckAllDatasources(){return(e=function*(){const e=yield this._dataSourceFetcher.getHealthyDataSources("loki");e.length>0?this.checkLogsInDataSources(e):(this.lokiDataSources=[],this.relatedLogsCount=0)},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Oo(a,r,i,o,s,"next",e)}function s(e){Oo(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var e}getLokiQueries(e,t=100){const{metric:n}=this._metricScene.state,r=this._logsConnectors.reduce((t,r,i)=>{const a=r.getLokiQueryExpr(n,e);var o;a&&(t[null!==(o=r.name)&&void 0!==o?o:`connector-${i}`]=a);return t},{});return Object.keys(r).map(e=>({refId:`RelatedLogs-${e}`,expr:r[e],maxLines:t,supportingQueryType:Qi.id}))}checkLogsInDataSources(e){const t=[];let n=0,i=0;if(0===e.length)return this.lokiDataSources=[],void(this.relatedLogsCount=0);e.forEach(o=>{const s=new a.dt({datasource:{uid:o.uid},queries:[],key:`related_logs_check_${o.uid}`});s.setState({queries:this.getLokiQueries(o.uid)}),s.subscribeToState(a=>{var s;if((null===(s=a.data)||void 0===s?void 0:s.state)===r.LoadingState.Done){var l;if(i++,null===(l=a.data)||void 0===l?void 0:l.series){const e=this.countLogsLines(a);e>0&&(t.push(o),n+=e)}i===e.length&&(this.lokiDataSources=t,this.relatedLogsCount=n)}}),s.activate()})}checkConditionsMetForRelatedLogs(){return this._logsConnectors.some(e=>e.checkConditionsMetForRelatedLogs())}countLogsLines(e){var t,n;return null!==(n=null===(t=e.data)||void 0===t?void 0:t.series.reduce((e,t)=>e+t.length,0))&&void 0!==n?n:0}constructor(e){Eo(this,"_logsConnectors",void 0),Eo(this,"_metricScene",void 0),Eo(this,"_dataSourceFetcher",(0,Bt.tS)()),Eo(this,"_changeHandlers",{lokiDataSources:[],relatedLogsCount:[]}),Eo(this,"_internalState",{relatedLogsCount:0,lokiDataSources:[]}),this._metricScene=e,this._logsConnectors=[wo(),ho(e)]}}function Po(){const e=(0,f.useStyles2)(ko);return b().createElement(f.Stack,{direction:"column",gap:2},b().createElement(f.Alert,{title:"No related logs found",severity:"info"},"We couldn't find any logs related to the current metric with your selected filters."),b().createElement(f.Text,null,"To find related logs, try the following:",b().createElement("ul",{className:e.list},b().createElement("li",null,"Adjust your label filters to include labels that exist in both the current metric and your logs"),b().createElement("li",null,"Select a metric created by a"," ",b().createElement(f.TextLink,{external:!0,href:"https://grafana.com/docs/loki/latest/alert/#recording-rules"},"Loki Recording Rule")),b().createElement("li",null,"Broaden the time range to include more data"))),b().createElement(f.Text,{variant:"bodySmall",color:"secondary"},"Note: Related logs is an experimental feature."))}function ko(e){return{list:(0,m.css)({paddingLeft:e.spacing(2),marginTop:e.spacing(1)})}}function Co({context:e}){const t=(0,g.useMemo)(()=>e,[e]),{links:n,isLoading:r}=(0,i.usePluginLinks)({extensionPointId:"grafana-metricsdrilldown-app/open-in-logs-drilldown/v1",limitPerPlugin:1,context:t}),a=(0,g.useMemo)(()=>n.find(({pluginId:e})=>"grafana-lokiexplore-app"===e),[n]);if(r)return b().createElement(f.LinkButton,{variant:"secondary",size:"sm",disabled:!0},"Loading...");const o=void 0!==a;return b().createElement(f.LinkButton,{href:o?`${i.config.appSubUrl}${a.path}`:`${i.config.appSubUrl}/a/grafana-lokiexplore-app`,target:"_blank",tooltip:o?"Use the Logs Drilldown app to explore these logs":"Navigate to the Logs Drilldown app",variant:"secondary",size:"sm",onClick:()=>(0,k.z)("related_logs_action_clicked",{action:"open_logs_drilldown"})},o?"Open in Logs Drilldown":"Open Logs Drilldown")}function jo(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function _o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function To(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const Io="related_logs/logs_panel_container";class Do extends a.Bs{_onActivate(){return(e=function*(){this.state.orchestrator.addLokiDataSourcesChangeHandler(()=>this.setupLogsPanel()),this.state.orchestrator.lokiDataSources.length?this.setupLogsPanel():(this.setState({loading:!0}),yield this.state.orchestrator.findAndCheckAllDatasources(),this.setState({loading:!1}))},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){jo(a,r,i,o,s,"next",e)}function s(e){jo(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var e}showNoLogsFound(){a.jh.findByKeyAndType(this,Io,a.vA).setState({body:new a.dM({component:Po})}),this.setState({controls:void 0}),this.state.orchestrator.relatedLogsCount=0}_buildQueryRunner(){this._queryRunner=new a.dt({datasource:{uid:"${logsDs}"},queries:[],key:"related_logs/logs_query"}),this._constructLogsDrilldownLinkContext(this._queryRunner.state),this._subs.add(this._queryRunner.subscribeToState(e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)!==r.LoadingState.Done)return;0===this.state.orchestrator.countLogsLines(e)&&this.showNoLogsFound(),this._constructLogsDrilldownLinkContext(e)}))}setupLogsPanel(){if(this._buildQueryRunner(),!this.state.orchestrator.lokiDataSources.length)return void this.showNoLogsFound();a.jh.findByKeyAndType(this,Io,a.vA).setState({body:a.d0.logs().setTitle("Logs").setOption("showLogContextToggle",!0).setOption("showTime",!0).setOption("showControls",!0).setOption("controlsStorageKey","grafana.explore.logs").setData(this._queryRunner).build()});const e=new a.yP({name:ce,label:"Logs data source",query:this.state.orchestrator.lokiDataSources.map(e=>`${e.name} : ${e.uid}`).join(",")});this.setState({$variables:new a.Pj({variables:[e]}),controls:[new a.K8({layout:"vertical"})]}),this._subs.add(e.subscribeToState((e,t)=>{e.value!==t.value&&(0,k.z)("related_logs_action_clicked",{action:"logs_data_source_changed"})})),this.updateLokiQuery()}_constructLogsDrilldownLinkContext(e){var t,n;const r=null!==(n=null===(t=a.jh.lookupVariable(ce,this))||void 0===t?void 0:t.getValue())&&void 0!==n?n:"",i=e.queries,o=[];r&&i.length&&i.forEach(e=>{o.push(To(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){_o(e,t,n[t])})}return e}({},e),{datasource:{uid:r,type:"loki"}}))}),this.setState({logsDrilldownLinkContext:{targets:o,timeRange:a.jh.getTimeRange(this).state}})}updateLokiQuery(){if(!this._queryRunner)return;const e=a.jh.lookupVariable(ce,this);let t;if(Ct(e)&&(t=e.getValue()),!t)return;const n=this.state.orchestrator.getLokiQueries(t);0!==n.length?this._queryRunner.setState({queries:n}):this.showNoLogsFound()}constructor(e){super({loading:!1,controls:[],body:new a.G1({direction:"column",height:"100%",minHeight:500,children:[new a.vA({key:Io,body:void 0})]}),orchestrator:e.orchestrator,logsDrilldownLinkContext:{targets:[]}}),_o(this,"_queryRunner",void 0),_o(this,"_variableDependency",new a.Sh(this,{variableNames:[ce,ie],onReferencedVariableValueChanged:e=>{e.state.name===ie?this.state.orchestrator.handleFiltersChange():e.state.name===ce&&this.updateLokiQuery()}})),this.addActivationHandler(()=>{this._onActivate()})}}function Ao(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}_o(Do,"Component",({model:e})=>{const{controls:t,body:n,logsDrilldownLinkContext:r,loading:i}=e.useState();return i?b().createElement(f.Spinner,null):b().createElement(f.Stack,{gap:1,direction:"column",grow:1,height:"100%"},b().createElement(f.Stack,{gap:1,direction:"row",justifyContent:"space-between",alignItems:"start"},b().createElement(f.Stack,{gap:1},null==t?void 0:t.map(e=>b().createElement(e.Component,{key:e.state.key,model:e}))),b().createElement(Co,{context:r})),b().createElement(n.Component,{model:n}))});class Lo extends a.Bs{_onActivate(){void 0===this.state.actionView&&this.setActionView(io),this.relatedLogsOrchestrator.findAndCheckAllDatasources(),this.relatedLogsOrchestrator.addRelatedLogsCountChangeHandler(e=>{this.setState({relatedLogsCount:e})}),this.subscribeToEvents()}subscribeToEvents(){i.config.featureToggles.enableScopesInMetricsExplore&&this.subscribeToEvent(fe,e=>{var t;null===(t=this.state.body.state.selectedTab)||void 0===t||t.publishEvent(e)})}getUrlState(){return{actionView:this.state.actionView}}updateFromUrl(e){if("string"==typeof e.actionView){if(this.state.actionView!==e.actionView){const t=oo.find(t=>t.value===e.actionView);t&&this.setActionView(t.value)}}else null===e.actionView&&this.setActionView(null)}setActionView(e){const{body:t}=this.state,n=e?oo.find(t=>t.value===e):null;n&&n.value!==this.state.actionView?(t.setState({selectedTab:n.getScene(this)}),this.setState({actionView:n.value})):(t.setState({selectedTab:void 0}),this.setState({actionView:void 0}))}createRelatedLogsScene(){return new Do({orchestrator:this.relatedLogsOrchestrator})}constructor(e){var t,n,r;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Ao(e,t,n[t])})}return e}({$variables:null!==(t=e.$variables)&&void 0!==t?t:(r=e.metric,new a.Pj({variables:[...me(r),new qi]})),body:null!==(n=e.body)&&void 0!==n?n:new ba({metric:e.metric})},e)),Ao(this,"relatedLogsOrchestrator",new xo(this)),Ao(this,"_urlSync",new a.So(this,{keys:["actionView"]})),Ao(this,"_variableDependency",new a.Sh(this,{variableNames:[ie],onReferencedVariableValueChanged:()=>{this.relatedLogsOrchestrator.handleFiltersChange()}})),this.addActivationHandler(this._onActivate.bind(this))}}Ao(Lo,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,f.useStyles2)(No);return b().createElement("div",{className:n.container,"data-testid":"metric-scene"},b().createElement(t.Component,{model:t}))});const No=()=>({container:(0,m.css)({position:"relative",height:"100%",width:"100%",display:"flex",flexDirection:"column"})});function Bo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Mo extends a.Bs{constructor(e){var t,n;super({stickyMainGraph:null!==(t=e.stickyMainGraph)&&void 0!==t&&t,isOpen:null!==(n=e.isOpen)&&void 0!==n&&n}),Bo(this,"onToggleStickyMainGraph",()=>{const e=!this.state.stickyMainGraph;(0,k.z)("settings_changed",{stickyMainGraph:e}),this.setState({stickyMainGraph:e})}),Bo(this,"onToggleOpen",e=>{this.setState({isOpen:e})})}}function $o(e){return{popover:(0,m.css)({display:"flex",padding:e.spacing(2),flexDirection:"column",background:e.colors.background.primary,boxShadow:e.shadows.z3,borderRadius:e.shape.radius.default,border:`1px solid ${e.colors.border.weak}`,zIndex:1,marginRight:e.spacing(2)}),heading:(0,m.css)({fontWeight:e.typography.fontWeightMedium,paddingBottom:e.spacing(2)}),options:(0,m.css)({display:"grid",gridTemplateColumns:"1fr 50px",rowGap:e.spacing(1),columnGap:e.spacing(2)})}}function Ro(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Fo(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Ro(a,r,i,o,s,"next",e)}function s(e){Ro(a,r,i,o,s,"throw",e)}o(void 0)})}}function Vo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Bo(Mo,"Component",({model:e})=>{const{stickyMainGraph:t,isOpen:n}=e.useState(),r=(0,f.useStyles2)($o),i=ts(e),{topScene:a}=i.useState();if(!(a instanceof Lo))return null;return b().createElement(f.Dropdown,{overlay:()=>b().createElement("div",{className:r.popover,onClick:e=>e.stopPropagation()},b().createElement("div",{className:r.heading},"Settings"),a instanceof Lo&&b().createElement("div",{className:r.options},b().createElement("div",null,"Always keep selected metric graph in-view"),b().createElement(f.Switch,{value:t,onChange:e.onToggleStickyMainGraph}))),placement:"bottom",onVisibleChange:e.onToggleOpen},b().createElement(f.ToolbarButton,{icon:"cog",variant:"canvas",isOpen:n,"data-testid":"settings-button"}))});class qo extends a.Bs{getUrlState(){return{metric:this.state.metric}}updateFromUrl(e){this.updateStateForNewMetric(e.metric||void 0)}onActivate(){this.datasourceHelper.init(),this.updateStateForNewMetric(this.state.metric),this.subscribeToEvent(he,e=>this.handleMetricSelectedEvent(e)),this.initFilters(),this.initConfigPrometheusFunction()}updateStateForNewMetric(e){this.state.topScene&&e===this.state.metric||this.setState({metric:e,topScene:e?new Lo({metric:e}):new Jr})}initFilters(){const e=a.jh.lookupVariable(ie,this);kt(e)&&(us(this,e,this.datasourceHelper),null==e||e.setState({useQueriesAsFilterForOptions:Boolean(this.state.metric)}),this.subscribeToState((e,t)=>{if(e.metric!==t.metric){const t=a.jh.lookupVariable(ie,this);kt(t)&&t.setState({useQueriesAsFilterForOptions:Boolean(e.metric)})}}),this._subs.add(null==e?void 0:e.subscribeToState((e,t)=>{this.disableReportFiltersInteraction||e.filters===t.filters||(0,k.h)(e.filters,t.filters)})))}initConfigPrometheusFunction(){this.subscribeToState((e,t)=>{e.metric!==t.metric&&this.state.drawer.close()}),this.subscribeToEvent(ft,e=>Fo(function*(){const{metric:t}=e.payload;H(t,this).catch(()=>z(t)).then(e=>{(0,k.z)("configure_panel_opened",{metricType:e})});const n=yield H(t,this);this.state.drawer.open({title:"Configure the Prometheus function",subTitle:`${t} (${n})`,body:new xi({metric:t})})}).call(this)),this.subscribeToEvent(hi,()=>{this.state.drawer.close()}),this.subscribeToEvent(mi,e=>Fo(function*(){const{metric:t,config:n,restoreDefault:r}=e.payload;H(t,this).catch(()=>z(t)).then(e=>{r?(0,k.z)("default_panel_config_restored",{metricType:e}):(0,k.z)("panel_config_applied",{metricType:e,configId:n.id})}),this.state.drawer.close(),function(e){const t=a.jh.findAllObjects(e,e=>{var t;return Boolean(null===(t=e.state.$behaviors)||void 0===t?void 0:t.some(e=>"syncYAxis"===e.__name__))});for(const e of t)e.publishEvent(new S({}),!0)}(this.state.topScene||this);const i=a.jh.findAllObjects(this.state.topScene||this,e=>e instanceof pt&&e.state.metric===t&&!e.state.queryConfig.groupBy);for(const e of i)e.update(n.panelOptions,n.queryOptions);(0,_.qq)([`Configuration successfully ${r?"restored":"applied"} for metric ${t}!`])}).call(this))}handleMetricSelectedEvent(e){return Fo(function*(){const{metric:t,urlValues:n}=e.payload;t&&function(e){try{const t=Xt(),n=Date.now(),r=t.filter(t=>t.name!==e);r.unshift({name:e,timestamp:n});const i=r.slice(0,6);j.x.setItem(C.V.RECENT_METRICS,i)}catch(t){const n=t instanceof Error?t:new Error(String(t));o.v.error(n,Yt(Qt({},n.cause||{}),{metricName:e}))}}(t);const i=a.jh.lookupVariable(ie,this);kt(i)&&i.setState({baseFilters:Uo(t)}),this._urlSync.performBrowserHistoryAction(()=>{if(this.updateStateForNewMetric(t),n){var e;(null===(e=n[`var-${ie}`])||void 0===e?void 0:e.length)||(n[`var-${ie}`]=[""]);const t=r.urlUtil.renderUrl("",n);a.Go.syncStateFromSearchParams(this,new URLSearchParams(t))}})}).call(this)}addFilterWithoutReportingInteraction(e){const t=a.jh.lookupVariable(ie,this);kt(t)&&(this.disableReportFiltersInteraction=!0,t.setState({filters:[...t.state.filters,e]}),this.disableReportFiltersInteraction=!1)}getPrometheusBuildInfo(){return Fo(function*(){return this.datasourceHelper.getPrometheusBuildInfo()}).call(this)}getMetadataForMetric(e){return Fo(function*(){return this.datasourceHelper.getMetadataForMetric(e)}).call(this)}isNativeHistogram(e){return Fo(function*(){return this.datasourceHelper.isNativeHistogram(e)}).call(this)}constructor(e){var t,n,r,i,o;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Vo(e,t,n[t])})}return e}({$timeRange:null!==(t=e.$timeRange)&&void 0!==t?t:new a.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:Ho(e.initialDS,e.metric,e.initialFilters),controls:null!==(r=e.controls)&&void 0!==r?r:[new a.K8({layout:"vertical"}),new a.N0,new a.KE({}),new a.WM({})],settings:null!==(i=e.settings)&&void 0!==i?i:new Mo({}),createdAt:null!==(o=e.createdAt)&&void 0!==o?o:(new Date).getTime(),dashboardMetrics:{},alertingMetrics:{},drawer:new Vi({})},e)),Vo(this,"disableReportFiltersInteraction",!1),Vo(this,"datasourceHelper",new mn(this)),Vo(this,"_urlSync",new a.So(this,{keys:["metric"]})),this.addActivationHandler(this.onActivate.bind(this))}}function Ho(e,t,n){let o=[new nn({initialDS:e}),new ln,new a.H9({key:ie,name:ie,label:"Filters",addFilterButtonText:"Add label",datasource:pe,hide:r.VariableHide.dontHide,layout:"combobox",filters:null!=n?n:[],baseFilters:Uo(t),applyMode:"manual",allowCustomValue:!0,useQueriesAsFilterForOptions:!1,expressionBuilder:e=>e.filter(e=>"__name__"!==e.key).map(e=>`${(0,h.Nc)(e.key)}${e.operator}"${e.value}"`).join(",")})];return Boolean(i.config.featureToggles.scopeFilters&&i.config.featureToggles.enableScopesInMetricsExplore&&!i.config.buildInfo.version.startsWith("11."))&&o.unshift(new a.Kg({enable:!0})),new a.Pj({variables:o})}function zo(e,t,n){const r=pa(e,n);return{container:(0,m.css)({flexGrow:1,display:"flex",gap:e.spacing(1),flexDirection:"column",padding:e.spacing(1,2),position:"relative",background:r}),body:(0,m.css)({flexGrow:1,display:"flex",flexDirection:"column",minHeight:0}),controls:(0,m.css)({display:"flex",gap:e.spacing(1),padding:e.spacing(1,0),alignItems:"flex-end",flexWrap:"wrap",position:"sticky",background:r,zIndex:e.zIndex.navbarFixed,top:t,borderBottom:`1px solid ${e.colors.border.weak}`}),settingsInfo:(0,m.css)({display:"flex",gap:e.spacing(.5)})}}function Uo(e){return e?[{key:"__name__",operator:"=",value:e}]:[]}function Go(){const e=document.querySelector('[data-testid="app-controls"]');if(!e)return;const{height:t}=e.getBoundingClientRect();document.documentElement.style.setProperty("--app-controls-height",`${t}px`)}function Ko(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Wo(){return new Qo}Vo(qo,"Component",({model:e})=>{const{controls:t,topScene:n,settings:r,embedded:o,drawer:s}=e.useState();var l;const c=null!==(l=(0,i.useChromeHeaderHeight)())&&void 0!==l?l:0,u=o?0:c,d=(0,f.useStyles2)(zo,u,e);return(0,g.useEffect)(()=>{Go();const e=document.querySelector('[data-testid="app-controls"]');if(!e)return;const t=new ResizeObserver(Go);return t.observe(e),()=>{t.disconnect(),document.documentElement.style.removeProperty("--app-controls-height")}},[o,t]),b().createElement(b().Fragment,null,b().createElement("div",{className:d.container},t&&b().createElement("div",{className:d.controls,"data-testid":"app-controls"},b().createElement(Ci,null),t.map(e=>b().createElement(e.Component,{key:e.state.key,model:e})),b().createElement("div",{className:d.settingsInfo},b().createElement(r.Component,{model:r}),b().createElement(Bi,{model:e}))),n&&b().createElement(a.$L,{scene:n,createBrowserHistorySteps:!0,updateUrlOnInit:!0,namespace:e.state.urlNamespace},b().createElement("div",{className:d.body},n&&b().createElement(n.Component,{model:n})))),b().createElement(s.Component,{model:s}))});class Qo extends a.Bs{getSelectedScopes(){return this.selectedScopes}getSelectedScopesNames(){return this.selectedScopes.map(({scope:e})=>e.metadata.name)}setSelectedScopes(e){this.selectedScopes=e,this.notifySubscribers()}onScopesChange(e){return this.onScopesChangeCallbacks.push(e),()=>{this.onScopesChangeCallbacks=this.onScopesChangeCallbacks.filter(t=>t!==e)}}notifySubscribers(){for(const e of this.onScopesChangeCallbacks)e(this.selectedScopes)}get value(){return[]}constructor(){super({}),Ko(this,"selectedScopes",[]),Ko(this,"onScopesChangeCallbacks",[])}}function Yo(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Xo(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Yo(a,r,i,o,s,"next",e)}function s(e){Yo(a,r,i,o,s,"throw",e)}o(void 0)})}}function Jo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Zo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Jo(e,t,n[t])})}return e}function es(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function ts(e){return a.jh.getAncestor(e,qo)}function ns(e){return a.jh.getAncestor(e,qo).state.settings}function rs(e){var t,n;return new qo(Zo({initialDS:null==e?void 0:e.initialDS,$timeRange:null!==(t=null==e?void 0:e.$timeRange)&&void 0!==t?t:new a.JZ({from:"now-1h",to:"now"}),embedded:null!==(n=null==e?void 0:e.embedded)&&void 0!==n&&n,urlNamespace:(null==e?void 0:e.embedded)?"gmd":void 0},e))}function is(e){const t=a.Go.getUrlState(e);return r.urlUtil.renderUrl(p.bw.Drilldown,t)}function as(e){return window.location.pathname.includes(e)}function os(e){return e?e===de?"Logs":e:"All metrics"}function ss(e){const t=i.config.theme2.visualization;return t.getColorByName(t.palette[e%8])}function ls(e){return a.jh.findAllObjects(e,l).flatMap(e=>e.state.queries.map(t=>es(Zo({},t),{expr:a.jh.interpolate(e,t.expr)})))}const cs=1e4;function us(e,t,n){kt(t)&&t.setState({getTagKeysProvider:()=>Xo(function*(){var r;const i={filters:t.state.filters,scopes:null===(r=Wo())||void 0===r?void 0:r.value,queries:t.state.useQueriesAsFilterForOptions?ls(e):[]};return i.queries.length>20&&(i.queries=[]),{replace:!0,values:(yield n.getTagKeys(i)).slice(0,cs)}})(),getTagValuesProvider:(r,i)=>Xo(function*(){var r;const a=t.state.filters.filter(e=>e.key!==i.key),o={key:i.key,filters:a,scopes:null===(r=Wo())||void 0===r?void 0:r.value,queries:t.state.useQueriesAsFilterForOptions?ls(e):[]};o.queries.length>20&&(o.queries=[]);return{replace:!0,values:(yield n.getTagValues(o)).slice(0,cs)}})()})}function ds(e,t,n){const r=a.jh.findObject(e,t);return r instanceof n?r:(null!==r&&o.v.warn(`invalid return type: ${n.toString()}`),null)}},3347:(e,t,n)=>{n.d(t,{h:()=>h,z:()=>p});var r=n(8531),i=n(4137),a=n(5914),o=n(6455),s=n(5176);function l(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){l(e,t,n[t])})}return e}function u(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const d="grafana_explore_metrics_";function p(e,t){var n;((0,r.reportInteraction)(`${d}${e}`,u(c({},t),{meta:{appRelease:r.config.apps[i.s_].version,appVersion:s.t}})),e.includes("sidebar"))&&(null===(n=(0,a.n1)())||void 0===n||n.api.pushEvent(e,u(c({},Object.fromEntries(Object.entries(t).map(([e,t])=>[e,String(t)]))),{defaultOpenSidebar:String((0,o.A)(o.j.sidebarOpenByDefault))})))}function m(e,t){p("label_filter_changed",{label:e,action:t,cause:"adhoc_filter"})}function h(e,t){e.length===t.length?function(e,t){for(const n of t)for(const t of e)n.key===t.key&&n.value!==t.value&&m(n.key,"changed")}(e,t):e.length<t.length?function(e,t){for(const n of t)e.some(e=>e.key===n.key)||m(n.key,"removed")}(e,t):function(e,t){for(const n of e)!t.some(e=>e.key===n.key)&&m(n.key,"added")}(e,t)}},3616:(e,t,n)=>{n.d(t,{HA:()=>c,jx:()=>l,qq:()=>u});var r=n(7781),i=n(8531),a=n(2445);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function l(e,t){const n=t.reduce((e,t,n)=>s(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){o(e,t,n[t])})}return e}({},e),{[`info${n+1}`]:t}),{handheldBy:"displayError"});a.v.error(e,n),(0,i.getAppEvents)().publish({type:r.AppEvents.alertError.name,payload:t})}function c(e){a.v.warn(e),(0,i.getAppEvents)().publish({type:r.AppEvents.alertWarning.name,payload:e})}function u(e){(0,i.getAppEvents)().publish({type:r.AppEvents.alertSuccess.name,payload:e})}},4964:(e,t,n)=>{n.d(t,{_:()=>r});const r=new Intl.Collator("en",{sensitivity:"base"}).compare},5753:(e,t,n)=>{n.d(t,{Hi:()=>o,KF:()=>i,WA:()=>a});var r=n(5959);const i=(0,n(3314).ef)(),a=(0,r.createContext)({trail:i});function o(){return(0,r.useContext)(a)}},6455:(e,t,n)=>{n.d(t,{A:()=>a,j:()=>i});var r=n(8531);const i={sidebarOpenByDefault:"metricsDrilldownDefaultOpenSidebar"};function a(e){var t;return null!==(t=r.config.featureToggles[e])&&void 0!==t&&t}},6503:(e,t,n)=>{n.d(t,{_:()=>c});var r=n(2007),i=n(5959),a=n.n(i),o=n(2445);function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function c({severity:e,title:t,message:n,error:i,errorContext:c,children:u}){let d;return i&&(d="string"==typeof i?new Error(i):i,o.v.error(d,l(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){s(e,t,n[t])})}return e}({},d.cause||{},c),{bannerTitle:t}))),a().createElement(r.Alert,{title:t,severity:e},d&&a().createElement(a().Fragment,null,d.message||d.toString(),a().createElement("br",null)),n,u)}},7476:(e,t,n)=>{n.d(t,{aQ:()=>c,tS:()=>p});var r=n(8531),i=n(2445);function a(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function o(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var o=e.apply(t,n);function s(e){a(o,r,i,s,l,"next",e)}function l(e){a(o,r,i,s,l,"throw",e)}s(void 0)})}}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const l=/^grafana-[0-9a-z]+prometheus-datasource$/;function c(e){return"object"==typeof e&&null!==e&&"type"in e&&"string"==typeof e.type&&("prometheus"===e.type||l.test(e.type))&&"uid"in e&&"string"==typeof e.uid}class u{getHealthyDataSources(e){return o(function*(){const t=this.cache.get(e);if(null==t?void 0:t.length)return t;let n=this.pendingRequests.get(e);n||(n=this.fetchHealthyDataSources(e).finally(()=>{this.pendingRequests.delete(e)}),this.pendingRequests.set(e,n));const r=yield n;return this.cache.set(e,r),r}).call(this)}fetchHealthyDataSources(e){return o(function*(){const t=(0,r.getDataSourceSrv)().getList({logs:!0,type:e,filter:e=>"grafana"!==e.uid}),n=[],a=[];return yield Promise.all(t.map(e=>o(function*(){try{const t=yield(0,r.getBackendSrv)().get(`/api/datasources/uid/${e.uid}/health`,void 0,void 0,{showSuccessAlert:!1,showErrorAlert:!1});"OK"===(null==t?void 0:t.status)?n.push(e):a.push(e)}catch(t){a.push(e)}})())),a.length>0&&i.v.warn(`Found ${a.length} unhealthy ${e} data sources: ${a.map(e=>e.name).join(", ")}`),n})()}constructor(){s(this,"pendingRequests",new Map),s(this,"cache",new Map)}}let d;function p(){return d||(d=new u),d}},8732:(e,t,n)=>{n.d(t,{A:()=>l,S:()=>u});var r=n(5959),i=n.n(r),a=n(1159),o=n(4137),s=n(5753);const l=(0,r.lazy)(()=>n.e(78).then(n.bind(n,9078))),c=()=>{const e=(0,a.useLocation)();return i().createElement(a.Navigate,{to:`${o.bw.Drilldown}${e.search}`,replace:!0})},u=()=>{const{trail:e}=(0,s.Hi)();return i().createElement(a.Routes,null,i().createElement(a.Route,{path:o.bw.Drilldown,element:i().createElement(l,{trail:e})}),i().createElement(a.Route,{path:o.bw.Trail,element:i().createElement(c,null)}),i().createElement(a.Route,{path:"*",element:i().createElement(a.Navigate,{to:o.bw.Drilldown,replace:!0})}))}}}]);
//# sourceMappingURL=836.js.map?_cache=7e113200c3df8b3fb768