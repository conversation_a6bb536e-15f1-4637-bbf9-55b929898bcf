{"version": 3, "file": "722.js?_cache=be0474f2c387037968b8", "mappings": "yrBAMA,SAASA,EAA+B,G,IAAA,iBACtCC,EAAgB,kBAChBC,GAFsC,EAGnCC,EAAAA,EAHmC,GACtCF,mBACAC,sBAGA,MAAME,EAAa,IAAIC,EAAAA,GAAe,CACpCC,MAAOL,EACPM,KAAMN,EAAiBO,IAAID,KAAKE,WAChCC,GAAIT,EAAiBO,IAAIE,GAAGD,a,IASwDN,EANtFC,EAAWO,iBAAkBR,IACvBD,GACFA,EAAkBC,EAAMG,SAI5B,MAAMM,EAAc,IAAIC,EAAAA,G,kUAAiB,EAAET,aAAYU,UAAU,EAAMC,cAAkC,QAAnBZ,EAAAA,EAAMY,qBAANZ,IAAAA,EAAAA,EAAuB,QAAWA,IAElHa,EAAS,IAAIC,gBAAgBC,OAAOC,SAASC,QAGnD,OAFAC,EAAAA,GAAWC,0BAA0BV,EAAaI,GAE3CJ,CACT,CAEe,SAASW,EAAyBC,GAC/C,MAAOZ,IAAea,EAAAA,EAAAA,UAASzB,EAA+BwB,IAE9D,OAAKA,EAAME,QAKT,kBAACC,EAAAA,GAAsBA,CAACC,UAAU,KAAKC,MAAOjB,EAAakB,iBAAiB,EAAOC,2BAA2B,GAC5G,kBAACnB,EAAYoB,UAAS,CAACC,MAAOrB,KALzB,kBAACA,EAAYoB,UAAS,CAACC,MAAOrB,GAQzC,C", "sources": ["webpack://grafana-exploretraces-app/./exposedComponents/EmbeddedTraceExploration/EmbeddedTraceExploration.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { SceneTimeRange, sceneUtils, UrlSyncContextProvider } from '@grafana/scenes';\n\nimport { TraceExploration } from '../../pages/Explore/TraceExploration';\nimport { EmbeddedTraceExplorationState } from 'exposedComponents/types';\n\nfunction buildTraceExplorationFromState({\n  initialTimeRange,\n  onTimeRangeChange,\n  ...state\n}: EmbeddedTraceExplorationState) {\n  const $timeRange = new SceneTimeRange({\n    value: initialTimeRange,\n    from: initialTimeRange.raw.from.toString(),\n    to: initialTimeRange.raw.to.toString(),\n  });\n\n  $timeRange.subscribeToState((state) => {\n    if (onTimeRangeChange) {\n      onTimeRangeChange(state.value);\n    }\n  });\n\n  const exploration = new TraceExploration({ $timeRange, embedded: true, initialMetric: state.initialMetric ?? 'rate', ...state });\n\n  const params = new URLSearchParams(window.location.search);\n  sceneUtils.syncStateFromSearchParams(exploration, params);\n\n  return exploration;\n}\n\nexport default function EmbeddedTraceExploration(props: EmbeddedTraceExplorationState) {\n  const [exploration] = useState(buildTraceExplorationFromState(props));\n\n  if (!props.urlSync) {\n    return <exploration.Component model={exploration} />;\n  }\n\n  return (\n    <UrlSyncContextProvider namespace='td' scene={exploration} updateUrlOnInit={false} createBrowserHistorySteps={true}>\n      <exploration.Component model={exploration} />\n    </UrlSyncContextProvider>\n  );\n}\n"], "names": ["buildTraceExplorationFromState", "initialTimeRange", "onTimeRangeChange", "state", "$timeRange", "SceneTimeRange", "value", "from", "raw", "toString", "to", "subscribeToState", "exploration", "TraceExploration", "embedded", "initialMetric", "params", "URLSearchParams", "window", "location", "search", "sceneUtils", "syncStateFromSearchParams", "EmbeddedTraceExploration", "props", "useState", "urlSync", "UrlSyncContextProvider", "namespace", "scene", "updateUrlOnInit", "createBrowserHistorySteps", "Component", "model"], "sourceRoot": ""}