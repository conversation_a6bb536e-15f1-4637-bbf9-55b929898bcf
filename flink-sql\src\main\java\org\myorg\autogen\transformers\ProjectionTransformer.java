package org.myorg.autogen.transformers;

import org.myorg.autogen.configs.TransformerConfig;

/**
 * Transformer that selects specific columns (projection)
 */
public class ProjectionTransformer implements Transformer {

    @Override
    public String transform(String inputQuery, TransformerConfig config) {
        if (config.getColumns() == null || config.getColumns().isEmpty()) {
            return inputQuery;
        }

        StringBuilder transformedQuery = new StringBuilder();
        transformedQuery.append("SELECT ");

        // Add selected columns
        boolean first = true;
        for (TransformerConfig.ColumnConfig column : config.getColumns()) {
            if (!first) {
                transformedQuery.append(", ");
            }
            // For projection, we use colname as the column name to select
            // Handle both cases: when colname is specified or when it's just a string
            String columnName = column.getColname();
            if (columnName != null) {
                transformedQuery.append("`").append(columnName).append("`");
            }
            first = false;
        }

        transformedQuery.append("\nFROM (");
        transformedQuery.append(inputQuery);
        transformedQuery.append(")");

        return transformedQuery.toString();
    }

    @Override
    public String getTransformerType() {
        return "ProjectionTransformer";
    }
}
