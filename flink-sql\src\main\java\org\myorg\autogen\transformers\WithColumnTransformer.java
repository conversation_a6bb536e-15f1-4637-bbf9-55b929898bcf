package org.myorg.autogen.transformers;

import org.myorg.autogen.configs.TransformerConfig;

/**
 * Transformer that adds new columns with expressions (similar to Spark's withColumn)
 */
public class WithColumnTransformer implements Transformer {

    @Override
    public String transform(String inputQuery, TransformerConfig config) {
        if (config.getColumns() == null || config.getColumns().isEmpty()) {
            return inputQuery;
        }

        StringBuilder transformedQuery = new StringBuilder();
        transformedQuery.append("SELECT *");
        
        // Add new columns with their expressions
        for (TransformerConfig.ColumnConfig column : config.getColumns()) {
            transformedQuery.append(",\n  ");
            transformedQuery.append(column.getExpr());
            transformedQuery.append(" AS ");
            transformedQuery.append(column.getColname());
        }
        
        transformedQuery.append("\nFROM (");
        transformedQuery.append(inputQuery);
        transformedQuery.append(")");
        
        return transformedQuery.toString();
    }

    @Override
    public String getTransformerType() {
        return "WithColumnTransformer";
    }
}
