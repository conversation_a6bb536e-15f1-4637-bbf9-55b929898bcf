{"version": 3, "file": "211.js?_cache=521089bb432d9ae7374f", "mappings": "gRAYO,MAAMA,EAAyB,EAAGC,kBACvC,MAAM,OAAEC,IAAWC,EAAAA,EAAAA,MACZC,EAASC,IAAcC,EAAAA,EAAAA,UAAS,YAYvC,OAAO,kBAACC,EAAAA,cAAaA,CAACC,QAAS,SAAUC,KAAM,YAAaL,QAASA,EAASM,QAV9D,KACVC,UAAUC,YACZD,UAAUC,UAAUC,UAAUX,GAASY,EAAAA,EAAAA,IAAqBb,IAC5DI,EAAW,WACXU,WAAW,KACTV,EAAW,aACV,U,26BCfT,MAAMW,EAAyB,CAC7B,mBACA,2BACA,kBACA,iBACA,wBACA,kCASIC,EAAa,CAAC,cAAe,WAAY,OAAQ,SAEhD,SAASC,GAAwB,QAAEC,EAAO,MAAEC,EAAK,SAAEC,I,IAyCbD,EAxC3C,MAAME,GAASC,EAAAA,EAAAA,YAAWC,GAEpBC,GAAMC,EAAAA,EAAAA,SACV,IACEC,OAAOC,OACLT,EAAQU,OAAO,CAACC,EAAKC,KACnB,GAAIA,EAAKC,MAAO,CACd,MAAMA,EAAQD,EAAKC,MAAMC,MAAMF,EAAKC,MAAME,QAAQ,KAAO,GAGzD,GAAIlB,EAAuBmB,SAASJ,EAAKC,OAAQ,C,IACjCF,EAAd,MAAMM,EAA0B,QAAlBN,EAAAA,EAAiB,mBAAjBA,IAAAA,EAAAA,EAAsB,CAAEE,MAAO,cAAeb,QAAS,IACrEiB,EAAMjB,QAAQkB,KAAK,OAAKN,GAAAA,CAAMC,WAC9BF,EAAiB,YAAIM,CACvB,MAAO,GAAIL,EAAKC,MAAMM,WAAW,aAAc,C,IAC/BR,EAAd,MAAMM,EAAuB,QAAfN,EAAAA,EAAc,gBAAdA,IAAAA,EAAAA,EAAmB,CAAEE,MAAO,WAAYb,QAAS,IAC/DiB,EAAMjB,QAAQkB,KAAK,OAAKN,GAAAA,CAAMC,WAC9BF,EAAc,SAAIM,CACpB,MACE,GAAIL,EAAKC,MAAMM,WAAW,SAAU,C,IACpBR,EAAd,MAAMM,EAAmB,QAAXN,EAAAA,EAAU,YAAVA,IAAAA,EAAAA,EAAe,CAAEE,MAAO,OAAQb,QAAS,IACvDiB,EAAMjB,QAAQkB,KAAK,OAAKN,GAAAA,CAAMC,WAC9BF,EAAU,KAAIM,CAChB,KAAO,C,IACSN,EAAd,MAAMM,EAAoB,QAAZN,EAAAA,EAAW,aAAXA,IAAAA,EAAAA,EAAgB,CAAEE,MAAO,QAASb,QAAS,IACzDiB,EAAMjB,QAAQkB,KAAKN,GACnBD,EAAW,MAAIM,CACjB,CAEJ,CACA,OAAON,GACN,CAAC,IACJS,KAAK,CAACC,EAAGC,IAAMxB,EAAWiB,QAAQM,EAAER,OAASf,EAAWiB,QAAQO,EAAET,QACtE,CAACb,I,IAOwCC,EAJ3C,OACE,kBAACsB,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACC,EAAAA,MAAKA,CAACb,MAAM,qBACX,kBAACc,EAAAA,OAAMA,CACL1B,MAA6B,MAAtBA,aAAAA,EAAAA,EAAO2B,aAA8C,QAAzB3B,EAAAA,SAAe,QAAfA,EAAAA,EAAO2B,kBAAP3B,IAAAA,OAAAA,EAAAA,EAAmB4B,MAAM,YAAzB5B,IAAAA,EAAAA,EAAuC,GAC1E6B,YAAa,sBACb9B,QAASM,EACTJ,SAAW6B,GAAM7B,EAAS6B,EAAEC,IAAKD,GAAuBA,EAAE9B,OAAOgC,KAAK,MACtEC,SAAS,EACTC,aAAAA,EACAC,aAAAA,EACAC,OAAQ,kBAACC,EAAAA,KAAIA,CAACC,KAAK,eAK7B,CAEA,MAAMlC,EAAY,KACT,CACLoB,WAAWe,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACTC,SAAU,QAEV,UAAW,CACTC,MAAO,Y,84BC/CR,MAAMC,UAAsBC,EAAAA,GAwBzBC,oBAAAA,GACN,MAAO,CACL,IAAOC,GACEA,EAAOC,MACZhB,EAAAA,EAAAA,KAAKiB,GACIA,EAAKjB,IAAKkB,I,IAoCXC,EAnCJ,MAAMC,EAASF,EAAGE,OACZD,EAAYC,EAAOC,KAAMC,GAAiB,cAAXA,EAAEf,MAEjCvC,EAAkC,CACtCuD,KAAMC,EAAAA,qBAAqBC,OAC3BC,cAAgBC,IACd,MAAMV,EAAOU,EAAMC,MACbC,EAAeZ,aAAAA,EAAAA,EAAMG,OAAOC,KAAMC,GAAiB,kBAAXA,EAAEf,MAC1CuB,EAAcb,aAAAA,EAAAA,EAAMG,OAAOC,KAAMC,GAAiB,WAAXA,EAAEf,MACzCwB,EAAUF,aAAAA,EAAAA,EAAcpD,OAAOkD,EAAMK,UACrCC,EAASH,aAAAA,EAAAA,EAAarD,OAAOkD,EAAMK,UAEzC,IAAKD,EACH,OAAOJ,EAAM1D,MAGf,MAAMsC,EAAOoB,EAAM1D,MAAS0D,EAAM1D,MAAmB,2BACrD,OACE,kBAACsB,MAAAA,CAAIC,UAAW,qBACd,kBAACD,MAAAA,CACCC,UAAW,YACX0C,MAAO3B,EACPhD,QAAS,KACP4E,KAAKC,aAAa,IAAIC,EAAAA,GAAiB,CAAEN,UAASE,YAAW,KAG9D1B,GAEH,kBAAC+B,EAAAA,KAAIA,CAACC,KAAMJ,KAAKK,iBAAiBT,EAASE,GAASQ,OAAQ,SAAUP,MAAO,mBAC3E,kBAAC5B,EAAAA,KAAIA,CAACC,KAAM,oBAAqBmC,KAAM,WASjD,OAHIvB,SAAiB,QAAjBA,EAAAA,EAAWwB,cAAXxB,IAAAA,OAAAA,EAAAA,EAAmByB,UACrBzB,EAAUwB,OAAOC,OAAOC,YAAc7E,GAEjC,OACFkD,GAAAA,CACHE,eAOd,CAuBQ0B,WAAAA,CAAY7B,G,IAK4BA,EAAAA,EAJ9C,IACEA,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaC,UAC7BhC,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaE,aAC5BjC,aAAAA,EAAAA,EAAM8B,UACN9B,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaG,YAAyB,QAAXlC,EAAAA,EAAKmC,cAALnC,IAAAA,GAAgB,QAAhBA,EAAAA,EAAc,UAAdA,IAAAA,OAAAA,EAAAA,EAAkBoC,UAkBhE,IAAIpC,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaM,OAAQrC,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaG,UACpE,GAA2B,IAAvBlC,EAAKmC,OAAOC,QAA0C,IAA1BpC,EAAKmC,OAAO,GAAGC,OAAc,CAC3D,GAA6B,UAAzBlB,KAAKY,MAAMQ,WAAyBpB,KAAKY,MAAMS,MACjD,OAEFrB,KAAKsB,SAAS,CACZF,UAAW,QACXC,MAAO,IAAIE,EAAAA,GAAgB,CACzBC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAIC,EAAAA,EAAgB,CACxBC,QAASC,EAAAA,GACTC,cAAeC,EAAAA,GACfC,QAAS,eAMrB,KAAoC,SAAzBhC,KAAKY,MAAMQ,WACpBpB,KAAKsB,SAAS,CACZF,UAAW,OACXC,MAAO,IAAIE,EAAAA,GAAgB,CACzBU,UAAW,MACXT,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAMQ,EAAAA,GAAcC,QACjBC,gBAAe,GACfC,aAAcC,GACNA,EACJC,oBAAoB,UACpBC,0BAA0B,UAAU,GACpCD,oBAAoB,gBACpBC,0BAA0B,QAAS,KACnCD,oBAAoB,aACpBC,0BAA0B,QAAS,MAEvCC,mBA3Df,CAME,GAA6B,YAAzBzC,KAAKY,MAAMQ,UACb,OAEFpB,KAAKsB,SAAS,CACZF,UAAW,UACXC,MAAO,IAAIE,EAAAA,GAAgB,CACzBU,UAAW,MACXT,SAAU,CACR,IAAIkB,EAAAA,EAAkB,CACpBC,UAAWC,QAMrB,CA6CF,CAtKA,WAAAC,CAAYjC,GACVkC,MAAM,GACJ1B,UAAW,SACRR,IA2EP,OAAQP,mBAAmB,CAACT,EAAiBE,KAC3C,MAAMiD,GAAwBC,EAAAA,EAAAA,IAAyBhD,MACjDiD,GAAaC,EAAAA,EAAAA,IAAcH,GAE3BI,EAAYC,EAAAA,GAAWC,aAAarD,MAAMY,MAAM9E,MAChDwH,EAAeC,KAAKC,UAAU,CAClC,iBAAoB,CAClBC,OAAOC,EAAAA,EAAAA,YAAWP,EAAUQ,KAC5BC,QAAS,CAAC,CAAEC,MAAO,UAAWC,UAAW,UAAWC,MAAOnE,EAASqD,eACpEe,YAAa,CACXC,MAAO,CACLnE,WAGJmD,gB,IAGWzC,EAAf,MAAM0D,EAAyB,QAAhB1D,EAAAA,EAAAA,OAAO2D,iBAAP3D,IAAAA,EAAAA,EAAoB,GACnC,OAAO4D,EAAAA,QAAQC,UAAU,GAAGH,YAAkB,CAAEI,MAAOhB,EAAciB,cAAe,MAwEtF,OAAOxI,WAAYyI,IACjB,MAAMC,GAAWC,EAAAA,EAAAA,IAA2B1E,MACxCyE,EAASE,aAAeH,IAC1BC,EAASG,cAAcJ,IAEvBK,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAeE,0BACnC,CACET,eA3KNxE,KAAKkF,qBAAqB,KACxBlF,KAAKsB,SAAS,CACZ6D,MAAO,IAAIC,EAAAA,GAAqB,CAC9BC,gBAAiBrF,KAAKrB,2BAG1B,MAAM2G,EAAYlC,EAAAA,GAAWmC,QAAQvF,MAErCA,KAAKW,YAAY2E,EAAU1E,MAAM9B,MACjCkB,KAAKwF,MAAMC,IACTH,EAAUI,iBAAkB5G,IAC1BkB,KAAKW,YAAY7B,EAAKA,UAI9B,EAkKA,EAxLWL,EAwLGkH,YAAY,EAAGC,YAC3B,MAAM,MAAEvE,GAAUuE,EAAM5K,WAClBgB,EAASE,GAAU2J,EAAAA,EAAAA,cACnBpB,GAAWC,EAAAA,EAAAA,IAA2BkB,IACtC,WAAEE,IAAeC,EAAAA,EAAAA,IAAuBH,GAAO5K,W,IAWpC8K,EATjB,GAAKzE,EAIL,OACE,kBAACjE,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAOgK,QACrB,kBAAC5I,MAAAA,CAAIC,UAAWrB,EAAOiK,aAAa,wDACpC,kBAACrK,EAAuBA,CACtBC,QAAyC+B,QAAhCkI,EAAAA,aAAAA,EAAAA,EAAYjI,IAAKD,IAAMsI,EAAAA,EAAAA,UAAStI,WAAhCkI,IAAAA,EAAAA,EAAuC,GAChDhK,MAAO2I,EAASE,WAChB5I,SAAU6J,EAAM7J,YAGpB,kBAACsF,EAAMsE,UAAS,CAACC,MAAOvE,OAMhC,MAAMnF,EAAaiK,IACV,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbC,QAAS,WAET,sBAAuB,CACrBA,QAAS,OACTE,MAAO,QAGT,qBAAsB,CACpBF,QAAS,OACT8H,IAAK,MACLC,eAAgB,gBAChBC,WAAY,SACZ9H,MAAO,OAEPtB,EAAG,CACD8E,QAAS,EACTuE,SAAU,EAEV,SAAU,CACRC,WAAYL,EAAMM,OAAOD,WAAWE,aAK1C,aAAc,CACZC,MAAOR,EAAMM,OAAOG,KAAKC,KACzBC,OAAQ,UACRC,SAAU,QACVC,SAAU,SACVC,aAAc,WAEd,SAAU,CACRC,eAAgB,gBAItBjB,aAAa5H,EAAAA,EAAAA,KAAI,CACfkI,SAAUJ,EAAMgB,WAAWC,GAAGb,SAC9BvE,QAAS,GAAGmE,EAAMkB,QAAQ,QAAQlB,EAAMkB,QAAQ,SAElDrB,QAAQ3H,EAAAA,EAAAA,KAAI,CACVC,QAAS,OACT+H,eAAgB,gBAChBC,WAAY,aACZF,IAAK,WAKLxD,EAAoB,KACxB,MAAM5G,GAASC,EAAAA,EAAAA,YAAWqL,GAE1B,OACE,kBAAClK,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAO+D,OACrB,kBAACwH,EAAAA,EAAQA,CAACC,MAAO,EAAGhJ,MAAO,MAE5B,IAAIiJ,MAAM,IAAI5J,IAAI,CAAC6J,EAAGC,IACrB,kBAACvK,MAAAA,CAAIC,UAAWrB,EAAO4L,IAAKC,IAAKF,GAC9B,IAAIF,MAAM,IAAI5J,IAAI,CAAC6J,EAAGI,IACrB,kBAACC,OAAAA,CAAK1K,UAAWrB,EAAOgM,QAASH,IAAKC,GACpC,kBAACP,EAAAA,EAAQA,CAACC,MAAO,UAS/B,SAASF,EAAkBnB,GACzB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACb4J,OAAQ,OACRzJ,MAAO,OACP0J,SAAU,WACVC,gBAAiBhC,EAAMM,OAAOD,WAAW4B,QACzCC,OAAQ,aAAalC,EAAMM,OAAO4B,OAAOC,OACzCtG,QAAS,QAEXjC,OAAO1B,EAAAA,EAAAA,KAAI,CACTkK,aAAc,SAEhBX,KAAKvJ,EAAAA,EAAAA,KAAI,CACPkK,aAAc,MACdjK,QAAS,OACT+H,eAAgB,iBAElB2B,SAAS3J,EAAAA,EAAAA,KAAI,CACXG,MAAO,QAGb,C,wHC9UO,MAAMgK,UAAmB9J,EAAAA,GAOtB+J,WAAAA,G,IAEJ1C,EADF/F,KAAKwF,MAAMC,IAC+B,QAAxCM,GAAAA,EAAAA,EAAAA,IAAuB/F,MAAMY,MAAMuE,aAAnCY,IAAAA,OAAAA,EAAAA,EAA0CL,iBAAiB,KACzD1F,KAAK0I,gBAIT1I,KAAKwF,MAAMC,KACTM,EAAAA,EAAAA,IAAuB/F,MAAM0F,iBAAiB,CAACiD,EAAUC,K,IACnDD,EAA8BC,GAAhB,QAAdD,EAAAA,EAASxD,aAATwD,IAAAA,OAAAA,EAAAA,EAAgB/H,MAAMiH,QAAuB,QAAfe,EAAAA,EAAUzD,aAAVyD,IAAAA,OAAAA,EAAAA,EAAiBhI,MAAMiH,MACvD7H,KAAK0I,gBAKX1I,KAAKwF,MAAMC,KACToD,EAAAA,EAAAA,IAAkB7I,MAAM0F,iBAAiB,CAACiD,EAAUC,KAC9CD,EAAS7M,QAAU8M,EAAU9M,OAC/BkE,KAAK0I,gBAKX1I,KAAK0I,YACP,CAEQA,UAAAA,GACN1I,KAAKsB,SAAS,CAAEI,KAAM,IAAIjD,EAAc,CAAC,IAC3C,CAlCA,WAAAoE,CAAYjC,GACVkC,M,kUAAM,IAAKlC,IAEXZ,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,ECbK,SAAS+I,EAAchB,GAC5B,GAAIA,EAAKjC,WACP,IAAK,MAAM5I,KAAK6K,EAAKjC,WAAY,C,IAEO5I,EADtC,GAAc,kBAAVA,EAAE2K,IACJ,OAAOmB,SAAS9L,EAAEpB,MAAMmN,WAAyB,QAAb/L,EAAAA,EAAEpB,MAAMoN,aAARhM,IAAAA,OAAAA,EAAAA,EAAeiM,YAAa,IAAK,GAEzE,CAGF,MAAM,IAAIC,MAAM,2BAClB,CAEO,SAASC,EAAetB,GAC7B,GAAIA,EAAKjC,WACP,IAAK,MAAM5I,KAAK6K,EAAKjC,WAAY,C,IAEO5I,EADtC,GAAc,mBAAVA,EAAE2K,IACJ,OAAOmB,SAAS9L,EAAEpB,MAAMmN,WAAyB,QAAb/L,EAAAA,EAAEpB,MAAMoN,aAARhM,IAAAA,OAAAA,EAAAA,EAAeiM,YAAa,IAAK,GAEzE,CAGF,MAAM,IAAIC,MAAM,4BAClB,C,wHDuBE,EArCWZ,EAqCG7C,YAAY,EAAGC,YAC3B,MAAM,KAAElE,GAASkE,EAAM5K,WACvB,OAAO0G,GAAQ,kBAACA,EAAKiE,UAAS,CAACC,MAAOlE,ME9CnC,MAAM4H,EAuCXC,OAAAA,CAAQxB,GAEN/H,KAAKwJ,KAAOC,KAAKC,IAAIX,EAAchB,GAAO/H,KAAKwJ,MAC/CxJ,KAAK2J,MAAQF,KAAKG,IAAIP,EAAetB,GAAO/H,KAAK2J,OACjD3J,KAAK6J,MAAM9M,KAAKgL,EAClB,CAEA+B,QAAAA,CAASC,GACPA,EAAKC,OAAShK,KACdA,KAAKwB,SAASzE,KAAKgN,EACrB,CAEAE,OAAAA,CAAQlC,GACN,OAAOgB,EAAchB,GAAQ/H,KAAKwJ,MAAQH,EAAetB,GAAQ/H,KAAK2J,KACxE,CAEAO,iBAAAA,CAAkBnC,GAChB,MAAM3J,EAAO+L,EAASpC,GAEtB,IAAK,MAAMqC,KAASpK,KAAKwB,SACvB,GAAI4I,EAAMhM,OAASA,EACjB,OAAOgM,EAIX,OAAO,IACT,CAtDA,WAAAvH,EAAY,KACVzE,EAAI,YACJiM,EAAW,cACXC,EAAa,MACbT,EAAK,KACLL,EAAI,MACJG,EAAK,QACLY,IAjBFnM,EAAAA,KAAAA,YAAAA,GACAiM,EAAAA,KAAAA,mBAAAA,GACAC,EAAAA,KAAAA,qBAAAA,GACAT,EAAAA,KAAAA,aAAAA,GACAL,EAAAA,KAAAA,YAAAA,GACAG,EAAAA,KAAAA,aAAAA,GACAnI,EAAAA,KAAAA,gBAAAA,GACAwI,EAAAA,KAAAA,cAAAA,GACAO,EAAAA,KAAAA,eAAAA,GAmBEvK,KAAK5B,KAAOA,EACZ4B,KAAKqK,YAAcA,EACnBrK,KAAKsK,cAAgBA,EACrBtK,KAAK6J,MAAQA,EACb7J,KAAKwJ,KAAOA,EACZxJ,KAAK2J,MAAQA,EACb3J,KAAKwB,SAAW,GAChBxB,KAAKgK,OAAS,KACdhK,KAAKuK,QAAUA,CACjB,EA+BK,SAASC,EAAWC,G,IACDA,EAK6BC,EAAAA,EALrD,MAAMA,EAA8B,QAAZD,EAAAA,EAAE3E,kBAAF2E,IAAAA,OAAAA,EAAAA,EAAcvL,KAAMhC,GAAgB,iBAAVA,EAAE2K,K,IAKrC6C,EAAAA,EACED,EAENA,EAPX,OAAO,IAAInB,EAAS,CAClBE,KAAMT,EAAc0B,GACpBd,MAAON,EAAeoB,GACtBrM,KAAM+L,EAASM,GACfJ,YAA8F,QAAjFK,EAAkC,QAAlCA,EAAAA,aAAAA,EAAAA,EAAiB5O,MAAM6O,mBAAvBD,IAAAA,EAAAA,EAAsCA,SAAsB,QAAtBA,EAAAA,EAAiB5O,aAAjB4O,IAAAA,GAA6B,QAA7BA,EAAAA,EAAwBxB,aAAxBwB,IAAAA,OAAAA,EAAAA,EAA+BE,oBAArEF,IAAAA,EAAAA,EAAqF,GAClGJ,cAAqB,QAANG,EAAAA,EAAErM,YAAFqM,IAAAA,EAAAA,EAAU,GACzBZ,MAAO,CAACY,GACRF,QAAkB,QAATE,EAAAA,EAAE7K,eAAF6K,IAAAA,EAAAA,EAAa,IAE1B,CAEA,SAASN,EAASM,GAChB,IAAII,EAAU,GACd,IAAK,MAAM3N,KAAKuN,EAAE3E,YAAc,GAChB,iBAAV5I,EAAE2K,KAA0B3K,EAAEpB,MAAM6O,cACtCE,EAAU3N,EAAEpB,MAAM6O,aAItB,MAAO,GAAGE,KAAWJ,EAAErM,MACzB,CChBA,SAAS0M,EAAeC,GACtBA,EAAEvB,KAAOwB,OAAOC,iBAChBF,EAAEpB,MAAQqB,OAAOE,iBAEjB,IAAK,MAAMC,KAAKJ,EAAEvJ,SAChBsJ,EAAeK,EAEnB,C,0cC3CA,MAAMC,EAAe,mBAEd,MAAMC,UAA0B3M,EAAAA,GAiB9B+J,WAAAA,G,IAEH,EADFzI,KAAKwF,MAAMC,IACO,QAAhB,EAAAzF,KAAKY,MAAMuE,aAAX,eAAkBO,iBAAkB9E,I,IAC9BA,EAA8CA,EAK9CA,EAA2CA,EAL/C,IAAc,QAAVA,EAAAA,EAAM9B,YAAN8B,IAAAA,OAAAA,EAAAA,EAAYA,SAAUC,EAAAA,aAAaC,UAAqB,QAAVF,EAAAA,EAAM9B,YAAN8B,IAAAA,OAAAA,EAAAA,EAAYA,SAAUC,EAAAA,aAAaG,WAKrF,IAAc,QAAVJ,EAAAA,EAAM9B,YAAN8B,IAAAA,OAAAA,EAAAA,EAAYA,SAAUC,EAAAA,aAAaM,OAAkB,QAAVP,EAAAA,EAAM9B,YAAN8B,IAAAA,OAAAA,EAAAA,EAAYK,OAAOC,QAAQ,C,IAC1DN,EAAd,MAAMnB,EAAkB,QAAVmB,EAAAA,EAAM9B,YAAN8B,IAAAA,OAAAA,EAAAA,EAAYK,OAAO,GAAGhC,OAAO,GAAG3C,OAAO,GACrD,GAAImD,EAAO,CACT,MACM6L,EDpEX,SAAqBC,GAC1B,MAAMD,EAAO,IAAIhC,EAAS,CACxBlL,KAAM,OACNiM,YAAa,GACbC,cAAe,GACfd,KAAMwB,OAAOE,iBACbvB,MAAOqB,OAAOC,iBACdpB,MAAO,GACPU,QAAS,KAGX,GAAIgB,GAAUA,EAAOrK,OAAS,EAC5B,IAAK,MAAM+C,KAASsH,EAAQ,C,IACtBtH,EAAJ,GAA+B,KAAb,QAAdA,EAAAA,EAAMuH,gBAANvH,IAAAA,OAAAA,EAAAA,EAAgB/C,QAClB,MAAM,IAAIkI,MAAM,mCAGlB,MAAMqC,EAAiBzC,SAAS/E,EAAMyH,mBAAqB,IAAK,IAE1DC,EAAK1H,EAAMuH,SAAS,GAE1BG,EAAG9B,MAAM5M,KAAK,CAAC2O,EAAIC,IAAO9C,EAAc6C,GAAM7C,EAAc8C,IAG5D,IAAIC,EAAoBR,EAExBR,EAAeQ,GACf,IAAK,MAAMvD,KAAQ4D,EAAG9B,MAAO,CAM3B,IAJA9B,EAAKnI,QAAUqE,EAAMsG,QACrBxC,EAAK2D,kBAAoB,IAAG1C,SAASjB,EAAK2D,kBAAmB,IAAMD,GAGzC,OAAnBK,EAAQ9B,SACT8B,EAAQ7B,QAAQlC,IAGpB+D,EAAUA,EAAQ9B,OAIpB,MAAMI,EAAQ0B,EAAQ5B,kBAAkBnC,GACxC,GAAIqC,EAAO,CACTA,EAAMb,QAAQxB,GAEd+D,EAAU1B,EACV,QACF,CAGA,MAAM2B,EAAUvB,EAAWzC,GAC3BgE,EAAQxB,QAAUtG,EAAMsG,QACxBuB,EAAQhC,SAASiC,GACjBD,EAAUC,CACZ,CACF,CAGF,OAAOT,CACT,CCSyBU,CADIzI,KAAK0I,MAAMxM,IAE5B6L,EAAK9J,SAASvE,KAAK,CAACC,EAAGC,IAAM+O,EAAW/O,GAAK+O,EAAWhP,IAExD8C,KAAKsB,SAAS,CACZ6K,SAAS,EACTb,OACAjK,MAAO,IAAIE,EAAAA,GAAgB,CACzB0G,OAAQ,OACRmE,KAAM,OACN5K,SAAUxB,KAAKqM,UAAUf,MAG/B,CACF,OArBEtL,KAAKsB,SAAS,CAAE6K,SAAS,MAwBjC,CAEQE,SAAAA,CAAUf,GAChB,OAAOA,EAAK9J,SAAS3D,IAAKuM,GACjB,IAAI3I,EAAAA,GAAc,CACvBwG,OAAQ,IACRzJ,MAAO,OACP8N,UAAW,QACX5K,KAAM1B,KAAKuM,SAASnC,KAG1B,CAEQmC,QAAAA,CAASjB,GACf,MAAMnI,EAAYC,EAAAA,GAAWC,aAAarD,MACpCwM,EAAOrJ,EAAUvC,MAAM9E,MAAM0Q,KAC7BC,EAAKtJ,EAAUvC,MAAM9E,MAAM2Q,GAE3BC,GAAYC,EAAAA,EAAAA,IAAa3M,MAE/B,OAAOkC,EAAAA,GAAcqJ,SAClBqB,SAAS,iBAAiBtB,EAAKjB,gBAAgB6B,EAAWZ,kBAC1DuB,UAAU,sBAA8B,CAACjN,EAAiBE,KAClD,CACLC,MAAO,aACPK,KAAM,IACNhF,QAAS,IAAMsR,EAAU9M,EAASE,GAClClF,OAAQ,CAAC,EACT0F,OAAQ,WAGXwM,QACC,IAAIC,EAAAA,GAAc,CAChBjO,KAAM,CACJ8B,MAAOC,EAAAA,aAAaM,KACpBgC,UAAW,CACTqJ,OACAC,KACA9I,IAAK,CAAE6I,OAAMC,OAEfxL,OAAQ,CACN,KACKjB,KAAKgN,UAAU1B,SAM3B7I,OACL,CAEQuK,SAAAA,CAAU1B,GAChB,MAAMrH,EAAQjE,KAAKiN,SAAS3B,EAAMF,GAC5B8B,EAAYjJ,EAAM,GAAGoG,YAAc,IAAMpG,EAAM,GAAGqG,cAExD,OAAO6C,EAAAA,EAAAA,iBAAgB,CACrB/O,KAAM,SAAS8O,IACfrJ,MAAO,SAASqJ,IAChBjO,OAAQ,CACN,CACEb,KAAM,aACNgB,KAAMgO,EAAAA,UAAUC,MAChB/Q,OAAQ2H,EAAMpG,IAAKD,GAAMA,EAAE0P,aAE7B,CACElP,KAAM,UACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQ2H,EAAMpG,IAAKD,GAAMA,EAAE2M,UAE7B,CACEnM,KAAM,SACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQ2H,EAAMpG,IAAKD,GAAMA,EAAE4P,SAE7B,CACEpP,KAAM,eACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQ2H,EAAMpG,IAAKD,GAAMA,EAAE6P,eAE7B,CACErP,KAAM,cACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQ2H,EAAMpG,IAAKD,GAAMA,EAAEyM,cAE7B,CACEjM,KAAM,gBACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQ2H,EAAMpG,IAAKD,GAAMA,EAAE0M,gBAE7B,CACElM,KAAM,WACNgB,KAAMgO,EAAAA,UAAUM,OAChBpR,OAAQ2H,EAAMpG,IAAKD,GAAMA,EAAE+P,WAE7B,CACEvP,KAAM,YACNgB,KAAMgO,EAAAA,UAAUM,OAChBpR,OAAQ2H,EAAMpG,IAAKD,GAAMA,EAAEgQ,YAE7B,CACExP,KAAM,aACNgB,KAAMgO,EAAAA,UAAUM,OAChBpR,OAAQ2H,EAAMpG,IAAKD,GAAMA,EAAEiQ,eAInC,CAEQZ,QAAAA,CAASlD,EAAgByD,GAC/B,MAAMM,EAAe/D,EAAKF,MAAMtN,OAC9B,CAACC,EAAK2O,K,IAAOA,EAAAA,E,MAAqE,WAAzD,QAAZA,EAAAA,EAAErF,kBAAFqF,IAAAA,GAAoC,QAApCA,EAAAA,EAAcjM,KAAMhC,GAAgB,WAAVA,EAAE2K,YAA5BsD,IAAAA,OAAAA,EAAAA,EAA+CrP,MAAM6O,aAA0BnO,EAAM,EAAIA,GACtG,GAIF,IAAIoR,EAAY,KACZJ,IAAWpC,IACbwC,EACE7D,EAAKF,MAAMtN,OAAO,CAACC,EAAK2O,IAAM3O,EAAMwM,SAASmC,EAAEO,kBAAmB,IAAK,GAAK3B,EAAKF,MAAM3I,OAAS,KAGpG,MAAM5E,EAAS,CACb,CAGEgR,WAAYvD,EAAKF,MAAMlN,OAAO,GAAGkB,IAAKD,IAAO,CAC3CmQ,QAAS,WACTxD,QAAS3M,EAAEgC,QACX4N,OAAQ5P,EAAE4P,UAEZjD,QAASR,EAAKQ,QACdiD,OAAQzD,EAAKF,MAAM,GAAG2D,OACtBC,aAAcD,EACdnD,YAAaN,EAAKM,YAClBC,cAAeP,EAAKO,cACpBuD,WAAYC,EAAe,EAAI,EAAc,EAC7CH,SAAU5D,EAAKF,MAAMtN,OAAO,CAACC,EAAK2O,IAAM3O,EAAMwM,SAASmC,EAAE6C,cAAe,IAAK,GAAKjE,EAAKF,MAAM3I,OAAS,IACtG0M,cAIJ,IAAK,MAAMxD,KAASL,EAAKvI,SACvBlF,EAAOS,QAAQiD,KAAKiN,SAAS7C,EAAOL,EAAKF,MAAM,GAAG2D,SAEpD,OAAOlR,CACT,CA7LA,WAAAuG,CAAYjC,GACVkC,MAAM,GACJqC,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI8I,EAAAA,GAAiB,CAC1BhL,WAAYiL,EAAAA,GACZtK,QAAS,CAACuK,EAAWvN,EAAMwN,WAE7B/I,gBAAiBgJ,EAAAA,KAEnBlC,SAAS,GACNvL,IAGLZ,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EAiSF,SAASmO,EAAWC,GAClB,IAAIE,EACAC,EAAiB,GACrB,OAAQH,GACN,IAAK,SACHE,EAAc,iBACdC,EAAiB,iBACjB,MACF,IAAK,WACHD,EAAc,cAAcE,EAAAA,KAC5BD,EAAiB,cAAcE,EAAAA,KAC/B,MACF,QACEH,EAAc,gBAIlB,MAAO,CACLzK,MAAO,IACPE,MAAO,IAAI2K,EAAAA,MACTH,EAAerN,OAAS,MAAMqN,IAAmB,aACxCD,oGACXxK,UAAW,UACX6K,UAAW,MACXC,MAAO,IACPC,KAAM,GACNC,QAAS,GAEb,CA5IE,EAhMWzD,EAgMG1F,YAAY,EAAGC,Y,IAWvBT,EA6FCA,EAvGL,MAAM,KAAEmG,EAAI,QAAEa,EAAO,MAAE9K,EAAK,MAAE8D,GAAUS,EAAM5K,WACxCgB,EAASE,GAAU2J,EAAAA,EAAAA,cACnBM,GAAQN,EAAAA,EAAAA,aAERlL,GAAcqI,EAAAA,EAAAA,IAAyB4C,IACvC,MAAE9J,GAAUnB,EAAYkO,oBAAoB7N,WAE5CoT,EAAStS,EAEf,IAKImK,EALA8I,EAAY5C,KAAYb,aAAAA,EAAAA,EAAM9J,SAASN,SACvCiE,SAAiB,QAAjBA,EAAAA,EAAOvE,MAAM9B,YAAbqG,IAAAA,OAAAA,EAAAA,EAAmBvE,SAAUC,EAAAA,aAAaM,OAC5C4N,GAAY,GAId,IAAIC,EAAW,GACf,OAAQZ,GACN,IAAK,OACHnI,EACE,oCACE,kBAAC7I,MAAAA,KAAI,+EACL,kBAACA,MAAAA,KAAI,uFAGT4R,EAAW,SACX,MACF,IAAK,SACH/I,EACE,oCACE,kBAAC7I,MAAAA,KAAI,8EACL,kBAACA,MAAAA,KAAI,uFAGT4R,EAAW,QACX,MACF,IAAK,WACH/I,EACE,oCACE,kBAAC7I,MAAAA,KAAI,uFACL,kBAACA,MAAAA,KAAI,uFAGT4R,EAAW,OAIf,MAAMC,EAAUC,GAAqBd,GAE/Be,EACJ,oCACE,kBAACC,EAAAA,KAAIA,CAACC,cAAe,SAAUnU,QAAQ,MACpC2G,EAAAA,IAEH,kBAACuN,EAAAA,KAAIA,CAACC,cAAe,SAAUnU,QAAQ,QACrC,kBAACkC,MAAAA,CAAIC,UAAWrB,EAAOsT,UAAU,2BACNN,EAAS,2FACnBA,EAAS,oDAG5B,kBAACO,EAAAA,MAAKA,CAACnJ,IAAK,GAAKE,WAAY,UAC3B,kBAACnI,EAAAA,KAAIA,CAACC,KAAK,gBACX,kBAACgR,EAAAA,KAAIA,CAACC,cAAe,SAAUnU,QAAQ,QAAO,mDAKhD,kBAACkC,MAAAA,CAAIC,UAAWrB,EAAOwT,iBAAiB,kBAEtC,kBAACpS,MAAAA,CAAIC,UAAWrB,EAAOyT,QACrB,kBAACC,EAAAA,WAAUA,CACTvU,KAAK,oBACLwU,KAAK,QACLpP,KAAM,KACND,OAAQ,SACRF,KACE,yGAGD,GAAG6O,EAAQW,oBAOtB,OACE,kBAACL,EAAAA,MAAKA,CAACtN,UAAW,SAAUmE,IAAK,GAC/B,kBAAChJ,MAAAA,CAAIC,UAAWrB,EAAOiK,aAAcA,GACpC8I,GACC,kBAACQ,EAAAA,MAAKA,CAACtN,UAAW,SAAUmE,IAAK,GAC/B,kBAACmB,EAAAA,EAAQA,CACPC,MAAO,EACPS,OAAQ,IACR4H,UAAW1J,EAAMM,OAAOD,WAAWE,UACnCoJ,eAAgB3J,EAAMM,OAAOD,WAAW4B,YAK5C2G,GAAazD,GAAQA,EAAK9J,SAASN,OAAS,GAC5C,kBAAC9D,MAAAA,CAAIC,UAAWrB,EAAO+T,eAAgB1O,GAAS,kBAACA,EAAMsE,UAAS,CAACC,MAAOvE,MAGzE8D,SAAiB,QAAjBA,EAAAA,EAAOvE,MAAM9B,YAAbqG,IAAAA,OAAAA,EAAAA,EAAmBvE,SAAUC,EAAAA,aAAaM,QAASmK,aAAAA,EAAAA,EAAM9J,SAASN,SACjE,kBAAC8O,EAAAA,EAAUA,CAACpO,QAASuN,EAAenN,QAAS,YAqCvD,MAAM9F,EAAaiK,IACV,CACLF,aAAa5H,EAAAA,EAAAA,KAAI,CACfkI,SAAUJ,EAAMgB,WAAWC,GAAGb,SAC9BvE,QAAS,GAAGmE,EAAMkB,QAAQ,SAE5B0I,eAAe1R,EAAAA,EAAAA,KAAI,CACjBC,QAAS,OACT2R,cAAe,SACf7J,IAAKD,EAAMkB,QAAQ6I,GAEnB,oCAAqC,CACnClJ,SAAU,OACV,yCAA0C,CACxC1I,QAAS,SAIb,2EAA4E,CAC1EA,QAAS,QAIX,mBAAoB,CAClBA,QAAS,QAIX,qCAAsC,CACpC,wBAAyB,CACvBwI,OAAQ,SAGZ,+BAAgC,CAC9BA,OAAQ,qBAGZwI,UAAUjR,EAAAA,EAAAA,KAAI,CACZ0I,SAAU,QACVoJ,OAAQ,WAEVV,QAAQpR,EAAAA,EAAAA,KAAI,CACV+R,WAAYjK,EAAMkB,QAAQ,KAE5BmI,iBAAiBnR,EAAAA,EAAAA,KAAI,CACnBC,QAAS,OACT+H,eAAgB,gBAChBC,WAAY,aAKlB,SAAS4F,EAAWZ,GAClB,IAAI9D,EAAQ8D,EAAKzB,MAAM3I,OACvB,IAAK,MAAMkJ,KAASkB,EAAK9J,SACvBgG,GAAS0E,EAAW9B,GAEtB,OAAO5C,CACT,C,cC5ZO,SAAS6I,GAAgB,QAAExU,EAAO,gBAAEyU,EAAe,MAAExU,EAAK,SAAEC,EAAQ,QAAEwU,GAAU,EAAK,MAAE3K,I,IAgFrD4K,EAA0BC,EA/EjE,MAAMzU,GAASC,EAAAA,EAAAA,YAAWC,GACpBiK,GAAQN,EAAAA,EAAAA,cACR,SAAEU,GAAaJ,EAAMgB,YAEpBuJ,EAAaC,IAAkB3V,EAAAA,EAAAA,UAAiB,KAChD4V,EAAiBC,IAAsB7V,EAAAA,EAAAA,WAAkB,IAEzD8V,EAAgBC,IAAqB/V,EAAAA,EAAAA,UAAiB,GACvDgW,GAAoBC,EAAAA,EAAAA,QAAuB,OAE3C,eAAEC,IAAmBlO,EAAAA,EAAAA,IAAyB4C,GAAO5K,YACrD,QAAE8T,IAAYqC,EAAAA,EAAAA,IAAmBvL,GAAO5K,YACtCc,MAAOsS,IAAWvF,EAAAA,EAAAA,IAAkBjD,GAAO5K,WAC7CoW,EAAchD,GAEpBiD,EAAAA,EAAAA,GAAkB,CAChBC,IAAKN,EACLO,SAAU,KACR,MAAMC,EAAUR,EAAkBS,QAC9BD,GACFT,EAAkBS,EAAQE,gBAKhC,MAAMlB,GAAepU,EAAAA,EAAAA,SAAQ,KAC3B,IAAIuV,EAAoB,EACxB,OAAOrB,EACJsB,OAAQC,IAEP,IAAIC,IAAWjW,EAAQqD,KAAM6S,GAAMA,EAAEjW,QAAU+V,GAG/C,OAAI/C,EAAQ5P,KAAMC,GAAMA,EAAE0I,MAAQgK,IAAsB,MAAf1S,EAAE6S,UAAmC,OAAf7S,EAAE6S,aAM7DlD,EAAQ5P,KAAMC,GAAgB,oBAAVA,EAAE0I,OACxBiK,EAASA,GAAiB,aAAPD,GAA4B,oBAAPA,GAKtB,SAAhBT,GAA0C,WAAhBA,IAC5BU,EAASA,GAAiB,WAAPD,GAGdC,KAERjU,IAAKoU,IAAe,CACnBvV,MAAOuV,EAAUC,QAAQC,EAAAA,GAAW,IAAID,QAAQE,EAAAA,GAAe,IAC/DxL,KAAMqL,EACNnW,MAAOmW,KAERL,OAAQS,IACP,MAAMzL,EAAOyL,EAAO3V,OAAS2V,EAAOzL,MAAQ,GACtC0L,GAAYC,EAAAA,EAAAA,aAAY3L,EAAML,GAAU/H,MAC9C,OAAImT,EAAoBW,EA/DD,GACA,IA8D+DxB,IACpFa,GAAqBW,EAhEA,IAiEd,MAKZ,CAAChC,EAAiBzU,EAASiT,EAASsC,EAAa7K,EAAUuK,IAExDL,GAAmBrU,EAAAA,EAAAA,SAAQ,KAC/B,MAAMoW,EAAM3W,EAAQ+V,OAAQC,IAAQrB,EAAatR,KAAMuT,I,IAAoBZ,E,OAAbY,EAAG3W,SAAkB,QAAR+V,EAAAA,EAAG/V,aAAH+V,IAAAA,OAAAA,EAAAA,EAAUpU,eACrF,OAAOiV,GAAgBF,EAAK9B,IAC3B,CAACA,EAAa7U,EAAS2U,IAEpBmC,EAA4B9W,GACzBA,EACJ+V,OAAQC,I,IAAmCA,E,OAA3Be,EAAAA,GAAkB/V,SAAiB,QAARgV,EAAAA,EAAG/V,aAAH+V,IAAAA,OAAAA,EAAAA,EAAUpU,cACrDI,IAAKgU,I,IAAiBA,E,MAAT,CAAEnV,MAAe,QAARmV,EAAAA,EAAGnV,aAAHmV,IAAAA,OAAAA,EAAAA,EAAUK,QAAQC,EAAAA,GAAW,IAAID,QAAQE,EAAAA,GAAe,IAAKtW,MAAO+V,EAAG/V,S,IAG7EoV,EAArB,MAAM2B,EAAuD,QAAxC3B,EAAAA,QAAAA,EAAiC,QAAfV,EAAAA,EAAa,UAAbA,IAAAA,OAAAA,EAAAA,EAAiB1U,aAAnCoV,IAAAA,EAAAA,EAA+D,QAAnBT,EAAAA,EAAiB,UAAjBA,IAAAA,OAAAA,EAAAA,EAAqB3U,OAGtFgX,EAAAA,EAAAA,WAAU,KACJD,IAAiBtC,GAAWK,IAC9B7U,EAAS8W,GAAc,GACvBhC,GAAmB,KAEpB,CAAC/U,EAAO+W,EAActC,EAASxU,EAAU6U,KAE5CkC,EAAAA,EAAAA,WAAU,KACJxC,EAAgBpP,OAAS,GAC3B2P,GAAmB,IAEpB,CAACP,KAEJwC,EAAAA,EAAAA,WAAU,KACJhE,EAAQiE,KAAM5T,GAAMA,EAAE0I,MAAQ/L,IAChC+U,GAAmB,IAEpB,CAAC/B,EAAShT,IAEb,MAAMkX,EAAgBzC,EAAU,CAAC,CAAE7T,MAAOuW,EAAAA,GAAKnX,MAAOmX,EAAAA,KAAS,GACzDC,EAAuB3C,EAAU0C,EAAAA,GAAM,GAE7C,OACE,kBAAC1V,EAAAA,MAAKA,CAACb,MAAM,YACX,kBAACU,MAAAA,CAAIkU,IAAKN,EAAmB3T,UAAWrB,EAAOsB,WAC5CkT,EAAatP,OAAS,GACrB,kBAACiS,EAAAA,iBAAgBA,CAACtX,QAAS,IAAImX,KAAkBxC,GAAe1U,MAAOA,EAAOC,SAAUA,IAE1F,kBAACyB,EAAAA,OAAMA,CACL1B,MAAOA,GAAS6W,EAAyBlC,GAAkBsC,KAAMnV,GAAMA,EAAE9B,QAAUA,GAASA,EAAQ,KACpG6B,YAAa,mBACb9B,QAAS8W,EAAyBlC,GAClC1U,SAAWqX,I,IACWA,EAApB,MAAMC,EAA6B,QAAfD,EAAAA,aAAAA,EAAAA,EAAUtX,aAAVsX,IAAAA,EAAAA,EAAmBF,EACvCnX,EAASsX,IAEXhW,UAAWrB,EAAOsX,OAClBtV,aAAAA,EACAuV,cAAe,CAACzX,GAAiB2T,aAChB,iBAAXA,GACFkB,EAAe7U,IAGnB0X,YAAa,IAAM7C,EAAe,IAClC1S,aAAAA,KAKV,CAEA,SAAS/B,EAAUiK,GACjB,MAAO,CACLmN,QAAQjV,EAAAA,EAAAA,KAAI,CACV0I,SAAUZ,EAAMkB,QAAQ,MAE1B/J,WAAWe,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,KAGzB,CAEO,MAAMqL,GAAkB,CAAC7W,EAAyCkI,KACvE,GAAuB,IAAnBlI,EAAQqF,OACV,MAAO,GAGT,GAAqB,IAAjB6C,EAAM7C,OACR,OAAOrF,EAAQc,MAAM,EAAG8W,EAAAA,IAG1B,MAAMC,EAAiB3P,EAAM6L,cAC7B,OAAO/T,EACJ+V,OAAQ+B,MACHA,EAAI7X,OAAS6X,EAAI7X,MAAMoF,OAAS,IAC3ByS,EAAI7X,MAAM8T,cAAc/S,SAAS6W,IAI3C/W,MAAM,EAAG8W,EAAAA,K,yHC3KP,MAAMG,WAAuBlV,EAAAA,GAC3BmV,QAAAA,EAAS,MAAEjO,IAChB,MAAM,OAAEkO,EAAM,QAAEjY,GAAY+J,EAAM5K,WAElC,OACE,kBAACuC,EAAAA,MAAKA,CAACb,MAAM,QACX,kBAACyW,EAAAA,iBAAgBA,CAACtX,QAASA,EAASC,MAAOgY,EAAQ/X,SAAU6J,EAAMmO,iBAGzE,C,kBATK,YAWL,QAAOA,iBAAkBD,IACvB9T,KAAKsB,SAAS,CAAEwS,YAChBjP,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAeiP,oBAAqB,CAC7GC,OAAQH,K,EAIZ,GAlBWF,GAkBGjO,YAAY,EAAGC,YAC3B,MAAM,QAAEsO,EAAO,QAAErY,EAAO,OAAEiY,GAAWlO,EAAM5K,WAErCmZ,EAAQtY,EAAQuY,UAAWrC,GAAMA,EAAEjW,QAAUgY,GACnD,IAAe,IAAXK,EACF,OAAO,KAGT,MAAMF,EAASC,EAAQC,GAEvB,OAAO,kBAACF,EAAOtO,UAAS,CAACC,MAAOqO,M,+CCxC7B,MAAMI,GAAmB,IACvBnS,EAAAA,GAAcoS,aAClBzH,UAAU,SAAU,CAAE0H,YAAY,IAClC1H,UAAU,UAAW,CAAE2H,KAAMC,EAAAA,mBAAmBC,QAChDC,qBAAqB,cAAe,I,0BCHlC,SAASC,KACd,OAAQC,IACN,MAAMC,EAAS,IAAIC,IAEbC,EAAWH,EAASI,iBAAiBC,EAAAA,GAA8BC,IACvE,MAAMlU,EAASkU,EAAMC,QAAQnU,OAE7BA,SAAAA,EAAQoU,QAAS5K,IACfA,EAAExL,OAAOtC,MAAM,GAAG0Y,QAASlW,IACzB2V,EAAOQ,IAAI7K,EAAE5G,MAAiB4F,KAAKG,OAAOzK,EAAE7C,OAAOsV,OAAQ2D,GAAMA,SAa3E,SAA8BV,EAAuBjL,GAEnD,MAAM0K,EAAalR,EAAAA,GAAWoS,eAAeX,EAAW9C,GAAMA,aAAa0D,EAAAA,IAE3E,IAAK,MAAM1K,KAAKuJ,EACdvJ,EAAE2K,wBAEF3K,EAAEzJ,SAAS,CACTqU,aAAaC,EAAAA,GAAAA,QAAMC,EAAAA,GAAAA,WAAU9K,EAAEnK,MAAM+U,aAAc,CAAEG,SAAU,CAAElM,UAGvE,CApBMmM,CAAqBlB,EAAUpL,KAAKG,OAAOkL,EAAOxY,aAGpD,MAAO,KACL0Y,EAASgB,eAGf,C,85BCEO,SAASC,GACdC,EACAzR,EACA0R,GAEA,MACM/H,GADmBpL,EAAAA,EAAAA,IAAyBkT,GAClBrN,oBAAoBlE,WAC9CZ,GAAQqS,EAAAA,GAAAA,GAAqB,CAAEhI,SAAQiI,WAAY5R,EAAS6R,iBAC5DC,EAA2C,CAAC,EAElD,OAAO,IAAI3C,GAAe,CACxB4C,WAAY,CAAC5B,MACbzP,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAIsR,GAAAA,EAAgB,CACzBC,cAAe,GACfzT,WAAYiL,EAAAA,GACZtK,QAAS,CAACG,KAEZsB,gBAAiB,KACZsR,EAAAA,GAAAA,IAAyBhK,EAAAA,EAAAA,IAAauJ,IACzC,IAAOtX,GACEA,EAAOC,MACZhB,EAAAA,EAAAA,KAAKiB,IACHA,EAAKuW,QAASnY,IAAM0Z,EAAAA,EAAAA,aAAY,CAAEC,MAAO3Z,EAAE+B,OAAO,GAAI6X,SAAU,CAACC,EAAAA,UAAUnN,QACpE9K,EAAK7B,KAAK,CAACC,EAAGC,K,IACXA,EAAAA,EAAuCD,EAAAA,EAA/C,QAAyB,QAAjBC,EAAAA,EAAE8B,OAAO,GAAG2B,aAAZzD,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB6Z,aAAnB7Z,IAAAA,OAAAA,EAAAA,EAA0ByM,MAAO,KAAuB,QAAjB1M,EAAAA,EAAE+B,OAAO,GAAG2B,aAAZ1D,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB8Z,aAAnB9Z,IAAAA,OAAAA,EAAAA,EAA0B0M,MAAO,WAO5F/N,QAAS,CACP,CAAEC,MAAO,SAAUY,MAAO,UAC1B,CAAEZ,MAAO,OAAQY,MAAO,QACxB,CAAEZ,MAAO,OAAQY,MAAO,SAE1BoX,OAAQ,OACRI,QAAS,CACP,IAAI3S,EAAAA,GAAgB,CAClBU,UAAW,SACXT,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChB6K,UAAW,IACX5K,MAAkB,aAAX0M,EAAwBiG,KAAmB4C,QAAQ,KAAO5C,MAAoB5R,aAI3F,IAAIyU,GAAAA,GAAgB,CAClBxV,KAAM,IAAIyV,EAAAA,GAAmB,CAC3BC,gBAAiBC,EAAAA,GACjBC,SAAU,QACVC,QAAQ,EACR/V,SAAU,KAEZgW,SAAS,EACTC,eAAgBA,GAAelB,EAAQmB,EAAAA,GAAejT,EAAU2J,EAAQ+H,KAE1E,IAAIe,GAAAA,GAAgB,CAClBxV,KAAM,IAAIyV,EAAAA,GAAmB,CAC3BC,gBAAiB,MACjBE,SAAU,QACVC,QAAQ,EACR/V,SAAU,KAEZgW,SAAS,EACTC,eAAgBA,GAAelB,EAAQmB,EAAAA,GAAejT,EAAU2J,EAAQ+H,OAIhF,CAEO,SAASsB,GACdlB,EACAoB,EACAlT,EACA2J,EACA+H,GAEA,MAAO,CAACrX,EAAiBW,K,IAMNX,EALjB,MAAM8Y,EAAmBnY,EAAMrB,KAAOmY,EAAO9W,EAAMrB,WAAQyZ,EAErDC,EAAW,IAAI/K,EAAAA,GAAc,CACjCjO,KAAM,SACDA,GAAAA,CACHiZ,YAA6B,QAAhBjZ,EAAAA,EAAKiZ,mBAALjZ,IAAAA,OAAAA,EAAAA,EAAkB8S,OAAQ1U,GAAMA,EAAE2G,QAAUpE,EAAMoE,OAC/D5C,OAAQ,CACN,SACKxB,GAAAA,CACHR,OAAQQ,EAAMR,OAAOhC,KAAK,CAACC,EAAGC,K,IAAsCA,EAAhCD,EAAAA,E,OAAQ,QAARA,EAAAA,EAAE8a,cAAF9a,IAAAA,GAAgB,QAAhBA,EAAAA,EAAU+a,cAAV/a,IAAAA,OAAAA,EAAAA,EAAkBgb,eAAsB,QAAR/a,EAAAA,EAAE6a,cAAF7a,IAAAA,OAAAA,EAAAA,EAAU8a,SAAU,MAAO,W,IAOrGL,EADF,GAAIA,EAEF,OAD2B,QAA3BA,EAAAA,EAAiBhX,MAAMc,YAAvBkW,IAAAA,GAAAA,EAA6BtW,SAAS,CAAE6D,MAAO2S,IACxCF,EAGT,MAAM7T,EAAQX,EAAAA,GAAW+U,YACvB1T,GACA2T,EAAAA,GAAAA,GAAqB,CACnBhK,SACAiK,aAAc,GAAG5T,EAAS6R,mBAAkBgC,EAAAA,EAAAA,KAAiBZ,EAAAA,EAAAA,IAAcjY,SAIzE4B,GAAoB,aAAX+M,EAAwBiG,KAAmB4C,QAAQ,MAAOsB,EAAAA,GAAAA,GAAgBnK,IACtFxB,SAAS+K,EAASlY,EAAOgF,EAAS6R,iBAClCkC,QAAQ,IAAIC,GAAAA,GAAU,CAAE1U,QAAO2U,YAAYhB,EAAAA,EAAAA,IAAcjY,MACzDqN,QAAQgL,GAELa,EAAUxC,EAAU1W,GACtBkZ,GACFtX,EAAMuX,iBAAiBD,GAGzB,MAAME,EAAW,IAAIC,EAAAA,GAAiB,CACpCpX,KAAML,EAAMoB,UAMd,OAJIhD,EAAMrB,OACRmY,EAAO9W,EAAMrB,MAAQya,GAGhBA,EAEX,CCzIO,SAASE,IAAsB,YAAE9S,EAAW,KAAE+S,IACnD,MACMhd,EAgBR,SAAmBmK,GACjB,MAAO,CACL8S,UAAU5a,EAAAA,EAAAA,KAAI,CACZC,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,GACnBf,WAAY,SACZtE,QAAS,GAAGmE,EAAMkB,QAAQ,QAAQlB,EAAMkB,QAAQ,SAElD6R,UAAU7a,EAAAA,EAAAA,KAAI,CACZC,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,GACnBf,WAAY,WAEdqN,KAAKtV,EAAAA,EAAAA,KAAI,CACPC,QAAS,eACTE,MAAO2H,EAAMkB,QAAQ,GACrBY,OAAQ9B,EAAMkB,QAAQ,IACtB8R,aAAchT,EAAMkB,QAAQ,MAGlC,CApCiBnL,EADD2J,EAAAA,EAAAA,cAGd,OACE,kBAACzI,MAAAA,CAAIC,UAAWrB,EAAOid,UACrB,kBAAC7b,MAAAA,CAAIC,UAAWrB,EAAOkd,UAAWjT,GACjC+S,EAAK9X,OAAS,GACb8X,EAAKnb,IAAK8V,GACR,kBAACvW,MAAAA,CAAIC,UAAWrB,EAAOkd,SAAUrR,IAAK8L,EAAIjX,OACxC,kBAACU,MAAAA,CAAIC,UAAWrB,EAAO2X,IAAKyF,MAAO,CAAEjR,gBAAiBwL,EAAIhN,SAC1D,kBAACvJ,MAAAA,KAAKuW,EAAIjX,SAKtB,C,yHCWO,MAAM2c,WAAiC3a,EAAAA,GAcpC+J,WAAAA,GACN,MAAMhE,GAAW6U,EAAAA,EAAAA,IAAmBtZ,MAEpCyE,EAASiB,iBAAiB,KACxB1F,KAAKuZ,QAAQ9U,MAGfsB,EAAAA,EAAAA,IAAuB/F,MAAM0F,iBAAiB,KAC5C1F,KAAKuZ,QAAQ9U,KAGfzE,KAAKuZ,QAAQ9U,EACf,CAEQ+U,gCAAAA,GACN,MAAM/U,GAAW6U,EAAAA,EAAAA,IAAmBtZ,MACpCyE,EAASG,cAAc6U,EAAAA,GAAwB,IAC/CzZ,KAAKuZ,QAAQ9U,EACf,CAEQiV,mBAAAA,CAAoBtE,IAC1BvQ,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAe4U,iCACnCvE,EAEJ,CAlCA,WAAAvS,CAAYjC,GACVkC,M,mUAAM,IACDlC,IAPP,QAAUgZ,sBAAsB,IAAIC,EAAAA,GAAyB7Z,KAAM,CACjE8Z,cAAe,CAACC,EAAAA,GAAaC,EAAAA,IAC7BR,iCAAkCxZ,KAAKwZ,iCAAiC1Q,KAAK9I,SAuC/E,QAAQuZ,UAAW9U,IACjBzE,KAAKsB,SAAS,CACZI,KAAMuU,GAAkBjW,KAAMyE,EAAWhF,GAAqB,CAC5D,IAAIwa,GAAAA,GAAmB,CAAExa,QAAOya,SAAUzV,EAAS6R,eAAgBlb,QAAS4E,KAAK0Z,4BAKvF,QAAO3d,WAAW,CAACD,EAAeqe,KAChC,MAAM1V,GAAW6U,EAAAA,EAAAA,IAAmBtZ,MAChCyE,EAAS6R,iBAAmBxa,IAC9B2I,EAASG,cAAc9I,OAAO+b,GAAYsC,IAE1CtV,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAeqV,2BACnC,CACE5C,QAAS1b,OAhDfkE,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EA4IF,SAAS9D,GAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbgc,SAAU,EACV/b,QAAS,OACTgO,UAAW,OACX2D,cAAe,WAEjBqK,SAASjc,EAAAA,EAAAA,KAAI,CACXgc,SAAU,EACV/b,QAAS,OACTic,WAAYpU,EAAMkB,QAAQ,KAE5BmT,UAAUnc,EAAAA,EAAAA,KAAI,CACZgc,SAAU,EACV/b,QAAS,OACTgI,WAAY,MACZF,IAAKD,EAAMkB,QAAQ,KAErBoT,eAAepc,EAAAA,EAAAA,KAAI,CACjBgc,SAAU,EACV/b,QAAS,OACT+H,eAAgB,aAElBqU,OAAOrc,EAAAA,EAAAA,KAAI,CACTsc,YAAaxU,EAAMkB,QAAQ,KAE7BmQ,SAASnZ,EAAAA,EAAAA,KAAI,CACXG,MAAO,SAEToc,cAAcvc,EAAAA,EAAAA,KAAI,CAChBC,QAAS,OACT+H,eAAgB,YAChBwU,aAAc,OACdrc,MAAO,OACPyR,cAAe,QAGrB,C,yHA7HE,GAjEWoJ,GAiEG1T,YAAY,EAAGC,YAC3B,MAAQ9J,MAAOgf,IAAiBxB,EAAAA,EAAAA,IAAmB1T,GAAO5K,WACpDwc,EAAUsD,EACVC,EAAevD,EAAQ3a,SAASsV,EAAAA,KAAc6I,EAAAA,GAAoBne,SAAS2a,GAAWyD,EAAAA,GAAOC,EAAAA,IAC5FR,EAAOS,IAAYngB,EAAAA,EAAAA,UAAS+f,IAC7B,KAAErZ,GAASkE,EAAM5K,WACjBgB,GAASC,EAAAA,EAAAA,YAAWC,KAEpB,WAAE4J,IAAeC,EAAAA,EAAAA,IAAuBH,GAAO5K,WAC/CogB,EAAaV,IAAUQ,EAAAA,GAAW9I,EAAAA,GAAgBD,EAAAA,GACxD,IAAIkJ,EAAqBvV,aAAAA,EAAAA,EAAY8L,OAAQ0J,GAASA,EAAKze,SAASue,IAChEV,IAAUO,EAAAA,KACZI,EAAqBA,aAAAA,EAAAA,EAAoBE,OAAOP,EAAAA,KAGlD,MAAMrgB,GAAcqI,EAAAA,EAAAA,IAAyB4C,IACrC9J,MAAOsS,GAAWzT,EAAYkO,oBAAoB7N,WAapDiL,EAZiB,CAACmI,IACtB,OAAQA,GACN,IAAK,OACH,MAAO,+DACT,IAAK,SACH,MAAO,6DACT,IAAK,WACH,MAAO,oDACT,QACE,MAAM,IAAIhF,MAAM,0BAGFoS,CAAepN,GAQnC,OANA0E,EAAAA,EAAAA,WAAU,KACJ4H,IAAUK,GACZI,EAASJ,IAEV,CAACvD,IAGF,kBAACpa,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACyb,GAAqBA,CACpB9S,YAAaA,EACb+S,KACa,aAAX5K,EACI,GACA,CACE,CAAE1R,MAAO,OAAQiK,MAAO,SACxB,CAAEjK,MAAO,QAASiK,MAAO,UAKnC,kBAACvJ,MAAAA,CAAIC,UAAWrB,EAAOwe,WACpBa,aAAAA,EAAAA,EAAoBna,SACnB,kBAAC9D,MAAAA,CAAIC,UAAWrB,EAAO4e,cACrB,kBAACxd,MAAAA,CAAIC,UAAWrB,EAAO0e,OACrB,kBAACnd,EAAAA,MAAKA,CAACb,MAAM,SACX,kBAACyW,EAAAA,iBAAgBA,CACftX,SAAS4f,EAAAA,EAAAA,IAAuB,CAACP,EAAAA,GAAUD,EAAAA,KAC3Cnf,MAAO4e,EACP3e,SAAUof,MAKhB,kBAAC/d,MAAAA,CAAIC,UAAWrB,EAAOwb,SACrB,kBAACnH,EAAeA,CACdxU,SAAS4f,EAAAA,EAAAA,IAAuBJ,GAChC/K,gBAAiBoK,IAAUQ,EAAAA,GAAWzB,EAAAA,GAA0BuB,EAAAA,GAChElf,MAAO0b,EACPzb,SAAU6J,EAAM7J,SAChB6J,MAAOA,MAKdlE,aAAgBkS,IACf,kBAACxW,MAAAA,CAAIC,UAAWrB,EAAOye,eACrB,kBAAC/Y,EAAKmS,SAAQ,CAACjO,MAAOlE,MAI5B,kBAACtE,MAAAA,CAAIC,UAAWrB,EAAOse,SAAU5Y,GAAQ,kBAACA,EAAKiE,UAAS,CAACC,MAAOlE,QC5KjE,MAAMga,WAAuBhd,EAAAA,GAW1B+J,WAAAA,GACNzI,KAAK0I,YACP,CAEQA,UAAAA,GACN1I,KAAKsB,SAAS,CAAEI,KAAM,IAAI2X,GAAyB,CAAC,IACtD,CAZA,WAAAxW,CAAYjC,GACVkC,M,mUAAM,IAAKlC,IALb,QAAUgZ,sBAAsB,IAAIC,EAAAA,GAAyB7Z,KAAM,CACjE8Z,cAAe,CAACE,EAAAA,OAMhBha,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EAUA,GAnBW0b,GAmBG/V,YAAY,EAAGC,YAC3B,MAAM,KAAElE,GAASkE,EAAM5K,WACvB,OAAO0G,GAAQ,kBAACA,EAAKiE,UAAS,CAACC,MAAOlE,M,8BCgDnC,SAASia,GAAiBC,GAC/B,IAAKA,EAAW1a,OAAS,MAAO,GAEhC0a,EAAW3e,KAAK,CAACC,EAAGC,IAAMD,EAAIC,GAE9B,MACM0e,GADcD,EAAWA,EAAW1a,OAAS,GAAK0a,EAAW,IAC5B,IAEjCE,EAAmC,KADfC,EAAAA,GAAAA,IAAoBF,EAAkB,IAE1DG,EAAU,IAAIjH,IAEpB,IAAK,MAAMkH,KAAaL,EAAY,CAClC,MAAMM,EAAYzS,KAAK0S,MAAMF,EAAYH,GAAgBA,EACzDE,EAAQ1G,IAAI4G,GAAYF,EAAQI,IAAIF,IAAc,GAAK,EACzD,CAGA,OAAOzU,MAAM+E,KAAKwP,EAAQK,WACvBxe,IAAI,EAAEye,EAAM9U,MAAY,CAAE8U,OAAM9U,WAChCvK,KAAK,CAACC,EAAGC,IAAMD,EAAEof,KAAOnf,EAAEmf,KAC/B,CAEO,SAASC,GAA0B3a,GACxC,OAAKA,EACEA,EAAQsQ,QAAQ,OAAQ,KAAKsK,OADb,EAEzB,C,o4BC1EO,MAAMC,WAAwB/d,EAAAA,GAgC3BiC,WAAAA,CAAY7B,G,IAK4BA,EAAAA,EAedA,EAAAA,EAnBhC,IACEA,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaC,UAC7BhC,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaE,cAC5BjC,aAAAA,EAAAA,EAAM8B,SACN9B,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaG,aAAyB,QAAXlC,EAAAA,EAAKmC,cAALnC,IAAAA,GAAgB,QAAhBA,EAAAA,EAAc,UAAdA,IAAAA,OAAAA,EAAAA,EAAkBoC,QAE9DlB,KAAKsB,SAAS,CACZF,UAAW,UACXC,MAAO,IAAIE,EAAAA,GAAgB,CACzBU,UAAW,MACXT,SAAU,CACR,IAAIkB,EAAAA,EAAkB,CACpBC,UAAWC,eAKd,IACJ9D,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaM,OAAQrC,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaG,WAC3C,IAAvBlC,EAAKmC,OAAOC,SAA4B,QAAXpC,EAAAA,EAAKmC,cAALnC,IAAAA,GAAgB,QAAhBA,EAAAA,EAAc,UAAdA,IAAAA,OAAAA,EAAAA,EAAkBoC,SAiB3C,KACJpC,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaM,OAAQrC,aAAAA,EAAAA,EAAM8B,SAAUC,EAAAA,aAAaG,YACnElC,EAAKmC,OAAOC,OAAS,EACrB,CACA,MAAMwb,EAAkB1c,KAAK2c,yBAAyB7d,GAEtDkB,KAAKsB,SAAS,CACZF,UAAW,OACXsb,kBACArb,MAAO,IAAIE,EAAAA,GAAgB,CACzBC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAMQ,EAAAA,GAAcC,QACjB0K,UAAU,aAAc+P,GAAAA,GAAgBC,IACxCza,gBAAe,GACfC,aAAcC,GACNA,EACJC,oBAAoB,WACpBC,0BAA0B,QAAS,KACnCD,oBAAoB,eACpBC,0BAA0B,QAAS,KACnCD,oBAAoB,eACpBC,0BAA0B,QAAS,KACnCD,oBAAoB,aACpBC,0BAA0B,QAAS,MAEvCC,cAKb,OA9CEzC,KAAKsB,SAAS,CACZF,UAAW,QACXsb,gBAAiB,EACjBrb,MAAO,IAAIE,EAAAA,GAAgB,CACzBC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAIC,EAAAA,EAAgB,CACxBC,QAASC,EAAAA,GACTC,cAAeC,EAAAA,GACfC,QAAS,eAsCvB,CAEQ8a,oBAAAA,GACN,MAAO,IAAOle,GACLA,EAAOC,MACZhB,EAAAA,EAAAA,KAAKiB,GACIA,EAAKjB,IAAKkB,IACf,MAAMge,EAAehe,EAAGE,OAAOC,KAAMC,GAAiB,sBAAXA,EAAEf,MACvC4e,EAAYje,EAAGE,OAAOC,KAAMC,GAAiB,mBAAXA,EAAEf,MACpC6e,EAAele,EAAGE,OAAOC,KAAMC,GAAiB,iBAAXA,EAAEf,MACvC8e,EAAYne,EAAGE,OAAOC,KAAMC,GAAiB,SAAXA,EAAEf,MAG1C,IAAI+e,EAAqB,GACrBC,EAAkB,GAClBC,EAAwB,GACxBC,EAA0B,GAC1BC,EAAqB,GACrBC,EAA4D,GAEhE,MATgBT,IAAiBA,EAAazgB,OAAO4E,QASxC,CACX,MAAMuc,ED5Jb,SAA6BV,EAA6BC,EAA2BE,EAAwBD,GAClH,MAAMI,EAAc,IAAItI,IAClBqI,EAAQ,IAAIrI,IACZuI,EAAgB,IAAIvI,IACpBwI,EAAW,IAAIxI,IACfyI,EAAa,IAAIzI,IAGjB2I,EAAoB,IAAI3I,IAE9B,IAAK,IAAIpN,EAAI,EAAGA,EAAIoV,EAAazgB,OAAO4E,OAAQyG,IAAK,CACnD,MAAM/F,EAAUmb,EAAazgB,OAAOqL,GAC9BvI,EAAO4d,aAAAA,EAAAA,EAAW1gB,OAAOqL,GACzBsU,EAAYiB,aAAAA,EAAAA,EAAW5gB,OAAOqL,GAC9BgW,EAAUV,aAAAA,EAAAA,EAAc3gB,OAAOqL,GAErC,GAAI/F,EAAS,CACX,MAAMgc,EAAoBrB,GAA0B3a,GAWpD,GAVAyb,EAAY/H,IAAIsI,GAAoBP,EAAYjB,IAAIwB,IAAsB,GAAK,IAE1ER,EAAMS,IAAID,IAAsBxe,GACnCge,EAAM9H,IAAIsI,EAAmBxe,IAG1Bme,EAASM,IAAID,IAAsBD,GACtCJ,EAASjI,IAAIsI,EAAmBD,GAG9B1B,EAAW,CACb,MAAM6B,EAAmC,iBAAd7B,EAAyB8B,WAAW9B,GAAaA,EACvEyB,EAAkBG,IAAID,IACzBF,EAAkBpI,IAAIsI,EAAmB,IAE3CF,EAAkBtB,IAAIwB,GAAoB7gB,KAAK+gB,GAG3CA,GADoBR,EAAclB,IAAIwB,IAAsB,IAE9DN,EAAchI,IAAIsI,EAAmBE,EAEzC,CACF,CACF,CAGA,IAAK,MAAOlc,EAASga,KAAe8B,EAAkBrB,UAAW,CAC/D,MAAM2B,EAAiBrC,GAAiBC,GACxC4B,EAAWlI,IAAI1T,EAASoc,EAC1B,CAEA,MAAMC,EAAgBxW,MAAM+E,KAAK6Q,EAAYhB,WAAWpf,KAAK,CAACC,EAAGC,IAAMA,EAAE,GAAKD,EAAE,IAEhF,MAAO,CACLigB,SAAUc,EAAcpgB,IAAI,EAAE+D,KAAaA,GAC3Cwb,MAAOa,EAAcpgB,IAAI,EAAE+D,KAAawb,EAAMhB,IAAIxa,IAAY,IAC9Dyb,YAAaY,EAAcpgB,IAAI,EAAE,CAAE2J,KAAWA,GAC9C+V,SAAUU,EAAcpgB,IAAI,EAAE+D,KAAa2b,EAASnB,IAAIxa,IAAY,IACpE4b,WAAYS,EAAcpgB,IAAI,EAAE+D,KAAa4b,EAAWpB,IAAIxa,IAAY,IACxE0b,cAAeW,EAAcpgB,IAAI,EAAE+D,MACjC,MAAMsc,EAAaZ,EAAclB,IAAIxa,GAErC,IAAKsc,EACH,MAAO,GAGT,MACMC,EADMC,KAAKC,MACIH,EAErB,OAAIC,EAAS,IACJ,WACEA,EAAS,KAEX,GADS1U,KAAK0S,MAAMgC,EAAS,YAE3BA,EAAS,MAEX,GADO1U,KAAK0S,MAAMgC,EAAS,aAI3B,GADM1U,KAAK0S,MAAMgC,EAAS,gBAKzC,CC2EiCG,CAAoBvB,EAAcC,EAAWE,EAAWD,GAC3EE,EAAWM,EAAWN,SACtBC,EAAQK,EAAWL,MACnBC,EAAcI,EAAWJ,YACzBC,EAAgBG,EAAWH,cAC3BC,EAAWE,EAAWF,SACtBC,EAAaC,EAAWD,UAC1B,CAEA,MAAM3hB,EAAkC,CACtCuD,KAAMC,EAAAA,qBAAqBC,OAC3BC,cAAgBC,IACd,MAAM+e,EAAa/e,EAAM1D,MACzB,OAAOkE,KAAKwe,oBAAoBD,KAIpC,OAAO,SACFxf,GAAAA,CACHmC,OAAQic,EAASjc,OACjBjC,OAAQ,CACN,CACEb,KAAM,UACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQ6gB,EACR3c,OAAQ,CACNie,MAAOtB,EAASjc,OAAS,EAAI,CAAClB,KAAK0e,kBAAoB,KAG3D,CACEtgB,KAAM,OACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQ8gB,EACR5c,OAAQ,CAAC,GAEX,CACEpC,KAAM,gBACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQihB,EACR/c,OAAQ,CAAC,GAEX,CACEpC,KAAM,cACNgB,KAAMgO,EAAAA,UAAUM,OAChBpR,OAAQ+gB,EACR7c,OAAQ,CAAC,GAEX,CACEpC,KAAM,YACNgB,KAAMgO,EAAAA,UAAUC,MAChB/Q,OAAQkhB,EACRhd,OAAQ,CACNC,OAAQ,CACNC,YAAa7E,KAInB,CACEuC,KAAM,YACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQghB,EACR9c,OAAQ,CAAC,SAQzB,CAuFQke,cAAAA,GACN,MAAO,CACL3e,MAAO,iCACP4e,IAAK,GACLvjB,QAAU+Z,I,IACSA,EAAjB,MAAMtV,EAAuB,QAAZsV,EAAAA,EAAMva,cAANua,IAAAA,OAAAA,EAAAA,EAActV,SAC/B,QAAiBgY,IAAbhY,EAAwB,C,IACVsV,EAAAA,EAAAA,EAAhB,MAAMvT,EAAsB,QAAZuT,EAAAA,EAAMva,cAANua,IAAAA,GAAmB,QAAnBA,EAAAA,EAAc0B,aAAd1B,IAAAA,GAA2B,QAA3BA,EAAAA,EAAqB7Y,cAArB6Y,IAAAA,OAAAA,EAAAA,EAA8BtV,GAC1C+B,KACFiD,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAe6Z,2BAC1F5e,KAAK6e,2BAA2Bjd,GAEpC,GAGN,CA8BQkd,iBAAAA,CAAkBhjB,GACxB,OAAOA,EACJoW,QAAQ,YAAa,KACrBA,QAAQ,OAAQ,KAChBA,QAAQ,MAAO,QACfA,QAAQ,KAAM,KACdsK,MACL,CAEQG,wBAAAA,CAAyB7d,G,IAC1BA,EAAL,KAAKA,SAAY,QAAZA,EAAAA,EAAMmC,cAANnC,IAAAA,OAAAA,EAAAA,EAAe,IAClB,OAAO,EAGT,MAAMigB,EAAmBjgB,EAAKmC,OAAO,GAAGhC,OAAOC,KAAM2X,GAAyB,gBAAfA,EAAMzY,MACrE,OAAK2gB,aAAAA,EAAAA,EAAkBziB,QAIhByiB,EAAiBziB,OAAOC,OAAO,CAACyiB,EAAeljB,IAAkBkjB,GAASljB,GAAS,GAAI,GAHrF,CAIX,CAEOmjB,kBAAAA,GACL,OAAOjf,KAAKY,MAAM8b,iBAAmB,CACvC,CA1VA,WAAA7Z,CAAYjC,GACVkC,MAAM,IACJqC,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI8I,EAAAA,GAAiB,CAC1BhL,WAAYiL,EAAAA,GACZtK,QAAS,CCxCV,CACLC,MAAO,IACPE,MAAO,IAAI2K,EAAAA,wJACX5K,UAAW,UACX6K,UAAW,QACXC,MAAO,IACPC,KAAM,GACNC,QAAS,ODmCLzJ,gBAAiB,KAEnBjE,UAAW,SACRR,IAsLP,QAAQ4d,sBAAuBD,IAC7B,MAAMviB,GAASC,EAAAA,EAAAA,YAAWC,IAEpBgjB,EAAgB,KACpB,MAAM/Y,GAAQN,EAAAA,EAAAA,aAEd,IAAK0Y,IAAeA,EAAWrd,OAC7B,OAAO,kBAAC9D,MAAAA,CAAIC,UAAWrB,EAAOmjB,kBAAkB,WAGlD,MAAMC,EAAcb,EAAW1gB,IAAKwhB,GAAUA,EAAM7X,OAC9C8X,EAAaf,EAAW1gB,IAAKwhB,GAAUA,EAAM/C,MAE7CiD,EAAmBH,EAAYxN,OAAQ2D,GAAMiK,SAASjK,KAAOkK,MAAMlK,IACnEmK,EAAkBJ,EAAW1N,OAAQ2D,GAAMiK,SAASjK,KAAOkK,MAAMlK,IACvE,GAAIgK,EAAiBre,OAAS,GAAKwe,EAAgBxe,OAAS,EAC1D,OAAO,kBAAC9D,MAAAA,CAAIC,UAAWrB,EAAOmjB,kBAAkB,mBAGlD,MAAMQ,EAAWlW,KAAKC,OAAO6V,GACvBK,EAAWnW,KAAKG,OAAO2V,GACvBM,EAAUpW,KAAKC,OAAOgW,GACtBI,EAAUrW,KAAKG,OAAO8V,GAGtBK,EAAaH,EAAWD,EACxBK,EAAYF,EAAUD,EAGtBI,EAAgC,IAAfF,EAAmB,EAAIA,EACxCG,EAA8B,IAAdF,EAAkB,EAAIA,EAEtCG,EAAgB,CACpBC,EAAG,CACDhiB,KAAM,QACNgB,KAAMgO,EAAAA,UAAUM,OAChBpR,OAAQijB,EACR/e,OAAQ,CAAC,EACTI,MAAO,CACL6C,MAAO,CACLiG,IAAKiW,EACL/V,IAAKgW,EACLS,MAAOJ,KAIbriB,EAAG,CACDQ,KAAM,OACNgB,KAAMgO,EAAAA,UAAUkP,KAChBhgB,OAAQojB,EACRlf,OAAQ,CAAC,EACTI,MAAO,CACL6C,MAAO,CACLiG,IAAKmW,EACLjW,IAAKkW,EACLO,MAAOH,MAMf,OACE,kBAAC9iB,MAAAA,CAAIC,UAAWrB,EAAOskB,oBACrB,kBAACC,EAAAA,UAASA,CACR/hB,MAAO,IACPyJ,OAAQ,GACRuY,UAAWL,EACXha,MAAOA,EACP3F,OAAQ,CACNC,OAAQ,CACNggB,UAAWC,GAAAA,GAAeC,KAC1BC,YAAa,EACbC,UAAW1a,EAAMM,OAAOD,WAAWE,UACnCoa,UAAW,EACXC,WAAYC,GAAAA,GAAeC,YAQvC,OAAO,kBAAC/B,EAAAA,QAoBV,QAAQL,6BAA8BqC,IACpC,MAAMC,GAAkBhQ,EAAAA,EAAAA,IAAmBnR,MAC3C,IAAKmhB,EACH,OAGF,MAAMC,GAAsBrb,EAAAA,EAAAA,IAAuB/F,MACnDohB,SAAAA,EAAqBC,cAAc,aAEnC,MAAMC,EAAiBH,EAAgBvgB,MAAMkO,SAAW,GAClDyS,EAAiBvhB,KAAK8e,kBAAkBoC,GAExCM,EAAsBF,EAAelN,UAAWxC,GAA0B,4BAAfA,EAAO/J,KAElE4Z,EAAY,CAChB5Z,IAAK,0BACLmK,SAAU,IACVlW,MAAOylB,GAGHG,EACJF,GAAuB,EACnBF,EAAezjB,IAAI,CAACsB,EAAGwI,IAAOA,IAAM6Z,EAAsBC,EAAYtiB,GACtE,IAAImiB,EAAgBG,GAE1BN,EAAgB7f,SAAS,CAAEwN,QAAS4S,MAlTZ1hB,KAAKY,MAAMuE,MACnB7D,SAAS,CACvB+D,gBAAiB,IAAIgJ,EAAAA,GAAwCrO,KAAK8c,0BAGpE9c,KAAKkF,qBAAqB,KACxB,MAAMyc,EAAkB3hB,KAAKY,MAAMuE,MAEnCnF,KAAKwF,MAAMC,IACTkc,EAAgBjc,iBAAiB,CAACiD,EAAUC,KACtCD,EAAS7J,OAAS8J,EAAU9J,MAC9BkB,KAAKW,YAAYgI,EAAS7J,UAKpC,EA+TA,GA7VW2d,GA6VG9W,YAAY,EAAGC,YAC3B,MAAM5J,GAASC,EAAAA,EAAAA,YAAWC,IACpBiK,GAAQN,EAAAA,EAAAA,cACR,MAAExE,EAAK,UAAED,GAAcwE,EAAM5K,WAEnC,OACE,kBAACoC,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAOiK,aAAa,8EAGrB,YAAd7E,GACC,kBAAChE,MAAAA,CAAIC,UAAWrB,EAAO4lB,kBACrB,kBAACra,EAAAA,EAAQA,CACPC,MAAO,GACPS,OAAQ,GACR4H,UAAW1J,EAAMM,OAAOD,WAAWE,UACnCoJ,eAAgB3J,EAAMM,OAAOD,WAAW4B,WAI7C/G,GAAS,kBAACA,EAAMsE,UAAS,CAACC,MAAOvE,OAM1C,MAAMnF,GAAaiK,IACV,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACT2R,cAAe,SACf7J,IAAKD,EAAMkB,QAAQ,GACnBY,OAAQ,SAEVhC,aAAa5H,EAAAA,EAAAA,KAAI,CACfkI,SAAUJ,EAAMgB,WAAWC,GAAGb,SAC9BvE,QAAS,GAAGmE,EAAMkB,QAAQ,SAE5Bua,kBAAkBvjB,EAAAA,EAAAA,KAAI,CACpB2D,QAASmE,EAAMkB,QAAQ,KAEzBiZ,oBAAoBjiB,EAAAA,EAAAA,KAAI,CACtBG,MAAO,QACPF,QAAS,OACTgI,WAAY,SACZD,eAAgB,WAElB8Y,kBAAkB9gB,EAAAA,EAAAA,KAAI,CACpBkI,SAAUJ,EAAMgB,WAAW0a,UAAUtb,SACrCI,MAAOR,EAAMM,OAAOG,KAAKF,UACzB1E,QAASmE,EAAMkB,QAAQ,OAKvBzE,GAAoB,KACxB,MAAM5G,GAASC,EAAAA,EAAAA,YAAWqL,IACpBnB,GAAQN,EAAAA,EAAAA,aAEd,OACE,kBAACzI,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACiK,EAAAA,EAAQA,CACPC,MAAO,GACPS,OAAQ,GACR4H,UAAW1J,EAAMM,OAAOD,WAAWE,UACnCoJ,eAAgB3J,EAAMM,OAAOD,WAAW4B,YAMhD,SAASd,GAAkBnB,GACzB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACb2D,QAASmE,EAAMkB,QAAQ,KAG7B,C,0BE7cO,SAASya,GAAgBC,G,IAY1BA,EAGAA,EAUkBA,EACFA,EAzBpB,IAAIC,EAAW,GAEf,IAAKD,EACH,MAAO,KAGLA,EAAUhe,QACZie,GAAYD,EAAUhe,OAGxB,MAAM4J,EAAW,IACK,QAAlBoU,EAAAA,EAAUpU,gBAAVoU,IAAAA,OAAAA,EAAAA,EAAoBvV,KAAKtL,SAC3ByM,EAAS5Q,KAAK,eAAeglB,EAAUpU,SAASnB,SAE5B,QAAlBuV,EAAAA,EAAUpU,gBAAVoU,IAAAA,OAAAA,EAAAA,EAAoBtV,GAAGvL,SACzByM,EAAS5Q,KAAK,eAAeglB,EAAUpU,SAASlB,MAE9CkB,EAASzM,SACP8gB,EAAS9gB,SACX8gB,GAAY,QAEdA,GAAYrU,EAAS7P,KAAK,SAG5B,MAAMmkB,EAAmC,QAAnBF,EAAAA,EAAU5e,iBAAV4e,IAAAA,OAAAA,EAAAA,EAAqBvV,KACrC0V,EAAiC,QAAnBH,EAAAA,EAAU5e,iBAAV4e,IAAAA,OAAAA,EAAAA,EAAqBtV,GACzC,MAAO,IAAIuV,SACTC,GAAiBC,EAAc,KAAqB,IAAhBD,MAA6C,IAAdC,IAA6B,IAEpG,C,o4BChBO,SAASC,GACdjM,EACAzR,EACA0R,EACA/H,G,IAKwBtP,EAHxB,MAAMqE,EAAYC,EAAAA,GAAWC,aAAa6S,GACpCpX,EAAOsE,EAAAA,GAAWmC,QAAQ2Q,GAC1BjE,EAAYxN,EAAS6R,eACrB8L,EAAiC,QAAftjB,EAAAA,EAAK8B,MAAM9B,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBmC,OAAO/B,KAAMmjB,GAAMA,EAAEjkB,OAAS6T,GACjEqQ,EAA2B,GAC3BtjB,EAAYojB,aAAAA,EAAAA,EAAiBnjB,OAAOC,KAAMC,GAAiB,UAAXA,EAAEf,MAClDmkB,EAAgBH,aAAAA,EAAAA,EAAiBnjB,OAAOC,KAAMC,GAAiB,aAAXA,EAAEf,MACtDokB,EAAiBJ,aAAAA,EAAAA,EAAiBnjB,OAAOC,KAAMC,GAAiB,cAAXA,EAAEf,MAI7D,GAAIY,GAAaujB,GAAiBC,EAChC,IAAK,IAAI7a,EAAI,EAAGA,EAAI3I,EAAU1C,OAAO4E,OAAQyG,IACtC3I,EAAU1C,OAAOqL,KAAQ4a,EAAcjmB,OAAOqL,IAAO6a,EAAelmB,OAAOqL,KAIhF2a,EAAYvlB,KAAK,CACfqB,KAAMY,EAAU1C,OAAOqL,GAAGuK,QAAQ,KAAM,IACxChR,OAAQ,EACRjC,OAAQ,CACN,CACEb,KAAM,QACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQ,CAAC,WAAY,cACrBkE,OAAQ,CAAC,GAEX,SACK+hB,GAAAA,CACHjmB,OAAQ,CAACimB,EAAcjmB,OAAOqL,IAC9BqQ,OAAQ,CACN,CAAC/F,GAAYjT,EAAU1C,OAAOqL,IAEhCnH,OAAQ,CACNiiB,YAAa,cAGjB,SACKD,GAAAA,CACHlmB,OAAQ,CAACkmB,EAAelmB,OAAOqL,SAOzC,OAAO,IAAIuP,GAAAA,GAAgB,CACzB/R,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI4H,EAAAA,GAAc,CACvBjO,KAAM,CACJqE,UAAWA,EAAUvC,MAAM9E,MAC3B8E,MAAOC,EAAAA,aAAaM,KACpBF,OAAQqhB,KAGZjd,gBAAiB,CACf,IAAOzG,GACEA,EAAOC,MACZhB,EAAAA,EAAAA,KAAKiB,IACHA,EAAKuW,QAASnY,IAAM0Z,EAAAA,EAAAA,aAAY,CAAEC,MAAO3Z,EAAE+B,OAAO,GAAI6X,SAAU,CAACC,EAAAA,UAAUnN,QACpE9K,EAAK7B,KAAK,CAACC,EAAGC,K,IACXA,EAAAA,EAAuCD,EAAAA,EAA/C,QAAyB,QAAjBC,EAAAA,EAAE8B,OAAO,GAAG2B,aAAZzD,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB6Z,aAAnB7Z,IAAAA,OAAAA,EAAAA,EAA0ByM,MAAO,KAAuB,QAAjB1M,EAAAA,EAAE+B,OAAO,GAAG2B,aAAZ1D,IAAAA,GAAwB,QAAxBA,EAAAA,EAAmB8Z,aAAnB9Z,IAAAA,OAAAA,EAAAA,EAA0B0M,MAAO,WAO5FlI,KAAM,IAAIyV,EAAAA,GAAmB,CAC3BC,gBAAiBC,EAAAA,GACjBC,SAAU,QACVC,QAAQ,EACR/V,SAAU,KAEZiW,eAAgBA,GAjE+B,CAAC,EAiETiL,GAAUvM,EAAW/H,IAEhE,CAEA,MAAMsU,GAAY3jB,GACTA,EAAGX,MAAQ,oBAGpB,SAASqZ,GACPlB,EACAoB,EACAxB,EACA/H,GAEA,MAAO,CAACtP,EAAiBW,KACvB,MAAMmY,EAAmBnY,EAAMrB,KAAOmY,EAAO9W,EAAMrB,WAAQyZ,EAErDC,EAAW,IAAI/K,EAAAA,GAAc,CACjCjO,KAAM,SACDA,GAAAA,CACHmC,OAAQ,CACN,MACKxB,Q,IAOTmY,EADF,GAAIA,EAEF,OAD2B,QAA3BA,EAAAA,EAAiBhX,MAAMc,YAAvBkW,IAAAA,GAAAA,EAA6BtW,SAAS,CAAE6D,MAAO2S,IACxCF,EAGT,MAAMvW,GAAQshB,EAAAA,GAAAA,GAAevU,GAAQxB,SAAS+K,EAASlY,IAAQqN,QAAQgL,GAEjEa,EAAUxC,EAAU1W,GACtBkZ,GACFtX,EAAMuX,iBAAiBD,GAGzB,MAAME,EAAW,IAAIC,EAAAA,GAAiB,CACpCpX,KAAML,EAAMoB,UAMd,OAJIhD,EAAMrB,OACRmY,EAAO9W,EAAMrB,MAAQya,GAGhBA,EAEX,CCnIO,MAAM+J,WAA+BlkB,EAAAA,I,gBAChB,EAAGkH,WACtBA,EAAMhF,MAAMqR,UAKf,kBAAC4Q,EAAAA,OAAMA,CAAC3nB,QAAQ,YAAYqF,KAAK,KAAKoP,KAAK,QAAQvU,QAAS,IAAMwK,EAAMhF,MAAMxF,WAAW,WAJlF,M,GAFGuK,e,GADHid,I,oOCgCN,MAAME,WAAkCpkB,EAAAA,GAcrC+J,WAAAA,GACN,MAAMhE,GAAW6U,EAAAA,EAAAA,IAAmBtZ,MAEpCyE,EAASG,cAAcqO,EAAAA,IAEvBjT,KAAK+iB,aAELte,EAASiB,iBAAiB,CAACiD,EAAUC,KAC/BD,EAAS7M,QAAU8M,EAAU9M,OAC/BkE,KAAKuZ,QAAQ9U,MAIjBue,EAAAA,EAAAA,IAAyBhjB,MAAM0F,iBAAiB,KAC9C1F,KAAK+iB,aACL/iB,KAAKuZ,QAAQ9U,MAGfsB,EAAAA,EAAAA,IAAuB/F,MAAM0F,iBAAiB,CAACiD,EAAUC,MAClDqa,EAAAA,GAAAA,SAAQta,EAASoZ,UAAWnZ,EAAUmZ,aACzC/hB,KAAK+iB,aACL/iB,KAAKuZ,QAAQ9U,MAIjBrB,EAAAA,GAAWC,aAAarD,MAAM0F,iBAAiB,KAC7C1F,KAAK+iB,eAGP/iB,KAAKuZ,QAAQ9U,EACf,CAMQse,UAAAA,GACN,MAAMG,GAAiBnd,EAAAA,EAAAA,IAAuB/F,MACxCmjB,EAAiB/f,EAAAA,GAAWC,aAAarD,MACzCwM,EAAO2W,EAAeviB,MAAM9E,MAAM0Q,KAAK4W,OACvC3W,EAAK0W,EAAeviB,MAAM9E,MAAM2Q,GAAG2W,OACnCC,GAAgBL,EAAAA,EAAAA,IAAyBhjB,MAAMY,MAAM9E,MACrDuf,EAAqBrb,KAAKsjB,sBAAsBD,GAEtDrjB,KAAKsB,SAAS,CACZ6D,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI8I,EAAAA,GAAiB,CAC1BhL,WAAYiL,EAAAA,GACZtK,QAAS,CAACuK,GAAW3B,EAAMC,EAAIqV,GAAgBoB,EAAetiB,MAAMmhB,eAEtE1c,gBAAiB,CACf,IAAOzG,GACEA,EAAOC,MACZhB,EAAAA,EAAAA,KAAKiB,IACH,MAAMykB,EAAgBC,GAA0B1kB,GAChD,OAAOzC,OAAOggB,QAAQkH,GACnB3R,OAAO,EAAEK,EAAWvK,MAAQ2T,EAAmBxe,SAASoV,IACxDpU,IAAI,EAAEoU,EAAWwR,KAAYC,GAAsBzR,EAAWwR,IAC9DxmB,KAAK,CAACC,EAAGC,KACR,MAAMwmB,GAAWC,EAAAA,GAAAA,GAAyB1mB,GACpC2mB,GAAWD,EAAAA,GAAAA,GAAyBzmB,GAC1C,OAAOsM,KAAKqa,IAAID,EAASE,eAAiBta,KAAKqa,IAAIH,EAASI,wBAQ9E,CAEQvK,gCAAAA,GACN,MAAM/U,GAAW6U,EAAAA,EAAAA,IAAmBtZ,MACpCyE,EAASG,cAAcqO,EAAAA,IACvBjT,KAAKuZ,QAAQ9U,EACf,CAEQiV,mBAAAA,CAAoBtE,IAC1BvQ,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAeif,kCACnC5O,EAEJ,CA3FA,WAAAvS,CAAYjC,GACVkC,M,mUAAM,IACDlC,IAPP,QAAUgZ,sBAAsB,IAAIC,EAAAA,GAAyB7Z,KAAM,CACjE8Z,cAAe,CAACC,EAAAA,GAAakK,EAAAA,IAC7BzK,iCAAkCxZ,KAAKwZ,iCAAiC1Q,KAAK9I,SA2C/E,QAAQsjB,wBAAyBD,GACN,sBAAlBA,EAAwC,CAAC,WAAY,mBAAqB,IAoDnF,QAAQ9J,UAAW9U,IACjB,MAAMyf,GAAmBlhB,EAAAA,EAAAA,IAAyBhD,MAClDA,KAAKsB,SAAS,CACZI,KACE+C,EAAS0f,eAAiB1f,EAASE,aAAesO,EAAAA,IAC9CmR,EAAAA,GAAAA,IACG3kB,GACC,IAAImjB,GAAuB,CACzB3Q,UAAWxS,EAAMrB,KACjBhD,QAAS,IAAM4E,KAAKjE,SAAS0D,EAAMrB,MAAQ,MAE/C8lB,EAAiBG,qBAEnBlC,GACEniB,KACAyE,EACChF,GAAqB,CACpB,IAAIwa,GAAAA,GAAmB,CACrBxa,QACAya,SAAUzV,EAAS6R,eACnBlb,QAAS4E,KAAK0Z,uBAGlBwK,EAAiBG,yBAK7B,QAAOtoB,WAAW,CAACD,EAAeqe,MACfb,EAAAA,EAAAA,IAAmBtZ,MAC3B4E,cAAc9I,OAAO+b,GAAYsC,IAE1CtV,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAeuf,uCACnC,CAAExoB,YA3HJkE,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EAoLK,SAASmO,GAAW3B,EAAcC,EAAY8X,GACnD,MACMC,EAAY,IADN7W,EAAAA,GAAAA,UAASlB,EAAKD,EAAM,KACPiY,eACzB,MAAO,CACL5gB,MAAO,IACPE,MAAO,IAAI2K,EAAAA,iBAA+B6V,KAC1CG,KAAMF,EACN1gB,UAAW,UACX6K,UAAW,QACXC,MAAO,IACPC,KAAM,GACNC,QAAS,GAEb,CAnEE,GA1IWgU,GA0IGnd,YAAY,EAAGC,YAC3B,MAAM,KAAElE,GAASkE,EAAM5K,WACjByJ,GAAW6U,EAAAA,EAAAA,IAAmB1T,GAC9Bse,GAAmBlhB,EAAAA,EAAAA,IAAyB4C,IAC5C,WAAEE,IAAeC,EAAAA,EAAAA,IAAuBH,GAAO5K,WAC/CgB,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACyb,GAAqBA,CACpB9S,YAAY,qGACZ+S,KAAM,CACJ,CACEtc,MAAO,WACPiK,MAC2C,aAAzCud,EAAiBG,oBACbM,GAAAA,IACAC,EAAAA,EAAAA,YAAWC,cAAcC,eAAe,oBAEhD,CACEpoB,MAAO,YACPiK,MAC2C,aAAzCud,EAAiBG,oBACbU,GAAAA,IACAH,EAAAA,EAAAA,YAAWC,cAAcC,eAAe,qBAKpD,kBAAC1nB,MAAAA,CAAIC,UAAWrB,EAAOwe,WACpB1U,aAAAA,EAAAA,EAAY5E,SACX,kBAAC9D,MAAAA,CAAIC,UAAWrB,EAAO4e,cACrB,kBAACvK,EAAeA,CACdxU,SAAS4f,EAAAA,EAAAA,IAAuB3V,GAChCwK,gBAAiB0K,EAAAA,GACjBlf,MAAO2I,EAAS6R,eAChBva,SAAU6J,EAAM7J,SAChBwU,SAAS,EACT3K,MAAOA,KAIZlE,aAAgBkS,IACf,kBAACxW,MAAAA,CAAIC,UAAWrB,EAAOye,eACrB,kBAAC/Y,EAAKmS,SAAQ,CAACjO,MAAOlE,MAI5B,kBAACtE,MAAAA,CAAIC,UAAWrB,EAAOse,SAAU5Y,GAAQ,kBAACA,EAAKiE,UAAS,CAACC,MAAOlE,QAqBxE,MAAM8hB,GAA6BC,GAC1BA,EAAOlnB,OAAO,CAACC,EAAkCyE,KACtD,MAAM+jB,EAAc/jB,EAAOhC,OAAOC,KAAM2X,GAAyB,WAAfA,EAAMzX,MAClD6lB,EAAiB5oB,OAAO6oB,MAAKF,aAAAA,EAAAA,EAAahN,SAAU,CAAC,GAAG9Y,KAAM2I,IAASA,EAAI7K,WAAW,OAI5F,OAHIioB,IACFzoB,EAAIyoB,GAAkB,IAAKzoB,EAAIyoB,IAAmB,GAAKhkB,IAElDzE,GACN,CAAC,GAGAknB,GAAwB,CAACzR,EAAmBwR,KAChD,MAAM0B,EAAsB,CAC1B/mB,KAAM6T,EACNpO,MAAOoO,EACPhT,OAAQ,GACRiC,OAAQ,GAGJkkB,EAAwB,CAC5BhnB,KAAM,QACNgB,KAAMgO,EAAAA,UAAUG,OAChBjR,OAAQ,GACRkE,OAAQ,CAAC,EACTwX,OAAQ,CAAE,CAAC/F,GAAYA,IAEnBsQ,EAAuB,CAC3BnkB,KAAM,WACNgB,KAAMgO,EAAAA,UAAUM,OAChBpR,OAAQ,GACRkE,OAAQ,CAAC,GAELgiB,EAAwB,CAC5BpkB,KAAM,YACNgB,KAAMgO,EAAAA,UAAUM,OAChBpR,OAAQ,GACRkE,OAAQ,CAAC,GAGLlE,EAASmnB,EAAOlnB,OAAO,CAACC,EAA8BiD,K,IAE9CulB,EADZ,MAAMA,EAAcvlB,EAAMR,OAAOC,KAAM2X,GAAyB,WAAfA,EAAMzX,MACjDimB,EAAML,SAAmB,QAAnBA,EAAAA,EAAahN,cAAbgN,IAAAA,OAAAA,EAAAA,EAAsB/S,GAIlC,OAHIoT,IACF7oB,EAAI6oB,GAAO,IAAK7oB,EAAI6oB,IAAQ,GAAKL,IAE5BxoB,GACN,CAAC,GAEE8oB,EAAgBC,GAAoB9B,EAAQ,WAAYnnB,GACxDkpB,EAAiBD,GAAoB9B,EAAQ,YAAannB,GAchE,OAZA6oB,EAASjkB,OAAS7E,OAAO6oB,KAAK5oB,GAAQ4E,OAEtC7E,OAAOggB,QAAQ/f,GAAQ+Y,QAAQ,EAAEvZ,EAAOmD,M,IAGpCA,EAGAA,EALFmmB,EAAe9oB,OAAOS,KAAKjB,GAC3BymB,EAAcjmB,OAAOS,M,QACnBkC,EAAAA,EAAOC,KAAM2X,I,IAAUA,E,MAAkC,gBAAtB,QAAZA,EAAAA,EAAMmB,cAANnB,IAAAA,OAAAA,EAAAA,EAA4B,sBAAnD5X,IAAAA,OAAAA,EAAAA,EAAwE3C,OAAO,IAAKgpB,GAEtF9C,EAAelmB,OAAOS,M,QACpBkC,EAAAA,EAAOC,KAAM2X,I,IAAUA,E,MAAkC,iBAAtB,QAAZA,EAAAA,EAAMmB,cAANnB,IAAAA,OAAAA,EAAAA,EAA4B,sBAAnD5X,IAAAA,OAAAA,EAAAA,EAAyE3C,OAAO,IAAKkpB,KAGzFL,EAASlmB,OAAS,CAACmmB,EAAgB7C,EAAeC,GAC3C2C,GAGT,SAASI,GAAoB9B,EAAqBgC,EAAkBnpB,GAElE,MAAMopB,EAAkBrpB,OAAOC,OAAOA,GAAQC,OAAO,CAACyiB,EAAO/f,KAC3D,MAAM4X,EAAQ5X,EAAOC,KAAM2X,I,IAAUA,E,OAAY,QAAZA,EAAAA,EAAMmB,cAANnB,IAAAA,OAAAA,EAAAA,EAA4B,eAAM,IAAI4O,OAC3E,OAAOzG,IAASnI,aAAAA,EAAAA,EAAOva,OAAO,KAAM,IACnC,GAEH,IAAI0iB,EAAQyE,EAAOlnB,OAAO,CAACopB,EAAclmB,K,IAEnCoX,EADJ,MAAMA,EAAQpX,EAAMR,OAAOC,KAAMC,GAAiB,WAAXA,EAAEC,MACzC,OAAIyX,SAAa,QAAbA,EAAAA,EAAOmB,cAAPnB,IAAAA,OAAAA,EAAAA,EAA6B,eAAM,IAAI4O,WAClC5O,EAAMva,OAAO,GAEfqpB,GACN,GAOH,OAAI3G,EAAQ0G,GAME,IAAV1G,GAAyB,IAAVA,EALU,IAApB0G,EAAwB,EAAIA,EAS9B1G,CACT,CAEA,SAAS9iB,GAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbgc,SAAU,EACV/b,QAAS,OACTgO,UAAW,OACX2D,cAAe,WAEjBqK,SAASjc,EAAAA,EAAAA,KAAI,CACXgc,SAAU,EACV/b,QAAS,OACTic,WAAYpU,EAAMkB,QAAQ,KAE5BmT,UAAUnc,EAAAA,EAAAA,KAAI,CACZgc,SAAU,EACV/b,QAAS,OACTgI,WAAY,MACZF,IAAKD,EAAMkB,QAAQ,KAErBoT,eAAepc,EAAAA,EAAAA,KAAI,CACjBgc,SAAU,EACV/b,QAAS,OACT+H,eAAgB,aAElBuU,cAAcvc,EAAAA,EAAAA,KAAI,CAChBC,QAAS,OACT+H,eAAgB,YAChBwU,aAAc,OACdrc,MAAO,OACPyR,cAAe,WAGrB,C,yHC7WO,MAAM2V,WAAwBlnB,EAAAA,GAW3B+J,WAAAA,GACN,MACM2F,GADYvF,EAAAA,EAAAA,IAAkB7I,MACX2E,WAEnBkhB,GAAkB9f,EAAAA,EAAAA,IAAuB/F,MAC/C,IAAK6lB,EAAgBjlB,MAAMmhB,UAAW,CACpC,MAAMA,GAAY+D,EAAAA,GAAAA,GAA6B1X,GAC3C2T,GACF8D,EAAgBvkB,SAAS,CAAEygB,aAE/B,CAEA/hB,KAAK0I,YACP,CAEQA,UAAAA,GACN1I,KAAKsB,SAAS,CAAEI,KAAM,IAAIohB,GAA0B,CAAC,IACvD,CAvBA,WAAAjgB,CAAYjC,GACVkC,M,mUAAM,IAAKlC,IALb,QAAUgZ,sBAAsB,IAAIC,EAAAA,GAAyB7Z,KAAM,CACjE8Z,cAAe,CAACE,EAAAA,OAMhBha,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EAqBA,GA9BW4lB,GA8BGjgB,YAAY,EAAGC,YAC3B,MAAM,KAAElE,GAASkE,EAAM5K,WACvB,OAAO0G,GAAQ,kBAACA,EAAKiE,UAAS,CAACC,MAAOlE,M,eCvBnC,MAAMqkB,GAAiD,CAC5D,CAAEtD,YA4HJ,SAA8B/a,GAC5B,MAAO,WACT,EA9HuC5L,MAAO,YAAakqB,STapD,WACL,OAAO,IAAIvkB,EAAAA,GAAc,CACvBC,KAAM,IAAIga,GAAe,CAAC,IAE9B,GShBE,CAAE+G,YAAavT,GAAsBpT,MAAO,YAAakqB,SjBuZpD,SAA6B5X,GAClC,OAAO,IAAI3M,EAAAA,GAAc,CACvBC,KAAM,IAAI2J,EAAkB,CAAE+C,YAElC,GiB1ZE,CAAEqU,YA8HJ,SAA+B/a,GAC7B,MAAO,YACT,EAhIwC5L,MAAO,aAAckqB,SDwBtD,WACL,OAAO,IAAIvkB,EAAAA,GAAc,CACvBC,KAAM,IAAIkkB,GAAgB,CAAC,IAE/B,GC3BE,CAAEnD,YAgJJ,SAA+B/a,GAC7B,MAAO,YACT,EAlJwC5L,MAAO,aAAckqB,SPibtD,WACL,OAAO,IAAIvkB,EAAAA,GAAc,CACvBC,KAAM,IAAI+a,GAAgB,CAAC,IAE/B,GOpbE,CACEgG,YA0IJ,SAA2BrU,GACzB,MAAkB,WAAXA,EAAsB,iBAA8B,aAAXA,EAAwB,cAAgB,QAC1F,EA3IItS,MAAO,YACPkqB,SrBiBG,WACL,OAAO,IAAIvkB,EAAAA,GAAc,CACvBC,KAAM,IAAI8G,EAAW,CAAC,IAE1B,IqBfO,MAAMyd,WAAqBvnB,EAAAA,IAuH3B,SAASwQ,GAAqBd,GACnC,OAAQA,GACN,IAAK,OACH,MAAO,oBACT,IAAK,SACH,MAAO,oBACT,IAAK,WACH,MAAO,qBAEb,CAUA,SAASlS,GAAUiK,GACjB,MAAO,CACLwS,SAASta,EAAAA,EAAAA,KAAI,CACX,CAAC8H,EAAM+f,YAAYC,GAAGhgB,EAAM+f,YAAY5pB,OAAO8pB,KAAM,CACnDle,SAAU,WACVyB,MAAO,EACP0c,IAAK,EACLC,OAAQ,KAIhB,E,6GApJE,CADWL,GACGtgB,YAAY,EAAGC,Y,IAWPxE,EAAAA,EAAAA,EA2DhBA,EArEJ,MAAMpF,GAASC,EAAAA,EAAAA,YAAWC,KACnBwgB,EAAiB6J,IAAsBvrB,EAAAA,EAAAA,UAAS,GAEjDwrB,GAAczgB,EAAAA,EAAAA,IAAuBH,GACrCjL,GAAcqI,EAAAA,EAAAA,IAAyB4C,IAEvC,WAAE6gB,GAAeD,EAAYxrB,YAC3Bc,MAAOsS,GAAWzT,EAAYkO,oBAAoB7N,YACpD,mBAAE0rB,GAAuB/rB,EAAYK,WACrCoG,EAAYgC,EAAAA,GAAWmC,QAAQK,GAAO5K,WACtC2rB,EAA4B,QAAdvlB,EAAAA,EAAUtC,YAAVsC,IAAAA,GAAsB,QAAtBA,EAAAA,EAAgBH,cAAhBG,IAAAA,GAA2B,QAA3BA,EAAAA,EAAyB,UAAzBA,IAAAA,OAAAA,EAAAA,EAA6BF,OAE3C0lB,EAAeb,GAAuBnU,OAAQiV,IAC/B,eAAfA,EAAK/qB,OAAqC,WAAXsS,OAK3BsY,aAAAA,EAAAA,EAAoBxlB,SAAUwlB,EAAmB7pB,SAASgqB,EAAK/qB,SAInEqlB,GAAkBhQ,EAAAA,EAAAA,IAAmBvL,GACrCkhB,GAAwB9D,EAAAA,EAAAA,IAAyBpd,GACjDzC,EAAYC,EAAAA,GAAWC,aAAauC,IACpC,QAAEkJ,GAAYqS,EAAgBnmB,YAC5Bc,MAAOunB,GAAkByD,EAAsB9rB,YAC/Cc,MAAOirB,GAAmB5jB,EAAUnI,WAkD5C,OAhDA8X,EAAAA,EAAAA,WAAU,KACR,GAAe,WAAX1E,EAEF,YADAmY,EAAmB,GAIrB,MAAMS,GAAkBC,EAAAA,EAAAA,IAAmBrhB,GAC3C,IAAKohB,EAEH,YADAT,EAAmB,GAIrBA,EAAmBS,EAAgB/H,sBACnC,MAAMiI,EAAeF,EAAgBthB,iBAAiB,CAACiD,EAAUC,KAC3DD,EAAS+T,kBAAoB9T,EAAU8T,iBACzC6J,EAAmB5d,EAAS+T,iBAAmB,KAInD,MAAO,KACLwK,EAAalR,gBAEd,CAAC5H,EAAQxI,EAAO6gB,EAAY3X,EAASuU,EAAe0D,KAEvDjU,EAAAA,EAAAA,WAAU,K,IAQN1R,EAPF,IAAIolB,EAAY5lB,MAAMumB,WAKtB,OACExsB,EAAYiG,MAAMwmB,WACJ,QAAdhmB,EAAAA,EAAUtC,YAAVsC,IAAAA,OAAAA,EAAAA,EAAgBR,SAAUC,EAAAA,aAAaM,WACvB0W,IAAhB8O,GACAA,EAAc,IAEdH,EAAYllB,SAAS,CAAE6lB,YAAY,SACnCX,EAAYnF,cAAc,mBAP5B,GAUC,CAAe,QAAdjgB,EAAAA,EAAUtC,YAAVsC,IAAAA,OAAAA,EAAAA,EAAgBR,MAAOjG,EAAYiG,MAAMwmB,SAAUZ,EAAaG,KAEpEU,EAAAA,GAAAA,GAAS,KACqB,IAAxBT,EAAa1lB,QACfslB,EAAYnF,cAAcuF,EAAa,GAAG9qB,SAIlB,IAAxB8qB,EAAa1lB,OACR,KAIP,kBAAComB,EAAAA,IAAGA,KACF,kBAAClqB,MAAAA,CAAIC,UAAWrB,EAAO2c,SACrB,kBAACpJ,EAAAA,MAAKA,CAACnJ,IAAK,GACV,kBAAC1L,EAAsBA,CAACC,YAAaA,MAIzC,kBAAC4sB,EAAAA,QAAOA,KACLX,EAAa/oB,IAAI,CAAC2pB,EAAKrT,IAEpB,kBAACsT,EAAAA,IAAGA,CACF5f,IAAKsM,EACLzX,MAAO8qB,EAAI/E,YAAYrU,GACvB0F,OAAQ2S,IAAee,EAAI1rB,MAC3B4rB,YAAa,IAAMlB,EAAYnF,cAAcmG,EAAI1rB,OACjD6rB,QACgB,cAAdH,EAAI1rB,MAAwB6qB,EAA4B,eAAda,EAAI1rB,MAAyB4gB,OAAkB7E,S,6JC3GpG,MAAM+P,WAAqBlpB,EAAAA,GAoDxB+J,WAAAA,GACNzI,KAAKsB,SAAS,CACZ6D,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAIsR,GAAAA,EAAgB,CACzBC,cAAqC,aAAtB1W,KAAKY,MAAMwN,OAAwB,GAAK,GACvDnL,WAAYiL,EAAAA,GACZtK,QAAS,CAAuB,aAAtB5D,KAAKY,MAAMwN,QAAwByZ,EAAAA,GAAAA,MAAwBzR,EAAAA,GAAAA,GAAqB,CAAEhI,OAAQpO,KAAKY,MAAMwN,OAAQ0Z,QAAQ,OAEjIziB,gBACwB,aAAtBrF,KAAKY,MAAMwN,OACP,KAAI2Z,EAAAA,GAAAA,MACJ,KAAIpR,EAAAA,GAAAA,IAAyBhK,EAAAA,EAAAA,IAAa3M,UAElDqB,MAAOrB,KAAKgoB,YAAYhoB,KAAKY,MAAMwN,SAEvC,CAEQ4Z,WAAAA,CAAY5Z,GAClB,OAAO,IAAI7M,EAAAA,GAAgB,CACzBU,UAAW,MACXT,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAiB,aAAX0M,EAAwBpO,KAAKioB,sBAAwBjoB,KAAKkoB,oBAAoB9Z,OAI5F,CAEQ8Z,mBAAAA,CAAoB9Z,GAC1B,MAAM/M,GAAQkX,EAAAA,GAAAA,GAAgBnK,GAAQhM,gBAAe,GAAM+lB,eAAe,eAU1E,MATe,SAAX/Z,EACF/M,EAAMsT,qBAAqB,YAAa,UACpB,WAAXvG,GACT/M,EAAMuL,SAAS,eAAe+H,qBAAqB,YAAa,WAAWyT,SAAS,CAClFC,WAAY,gBACZ7T,KAAM,UAIHnT,EAAMoB,OACf,CAEQwlB,mBAAAA,GACN,OAAOK,EAAAA,GAAAA,MACJ1b,SAAS,yBACTxK,gBAAe,GACf+lB,eAAe,eACf1lB,OACL,CAnGA,WAAAI,CAAYjC,GACVkC,M,mUAAM,EACJylB,aAAa,GACV3nB,IAGLZ,KAAKkF,qBAAqB,KACxBlF,KAAKyI,cACL,MAAM3J,EAAOsE,EAAAA,GAAWmC,QAAQvF,MAEhCA,KAAKwF,MAAMC,IACT3G,EAAK4G,iBAAkB5G,I,IACQA,EAEzBA,EAkBOA,EApBXkB,KAAKsB,SAAS,CAAEinB,aAAsB,QAATzpB,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaG,aAElD,QAATlC,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaM,KACJ,IAA5BrC,EAAKA,KAAKmC,OAAOC,QAA+C,IAA/BpC,EAAKA,KAAKmC,OAAO,GAAGC,SAAgBsnB,EAAAA,EAAAA,IAAoB1pB,GAC3FkB,KAAKsB,SAAS,CACZD,MAAO,IAAIE,EAAAA,GAAgB,CACzBC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAIC,EAAAA,EAAgB,CACxB8mB,SAAU,aAOpBzoB,KAAKsB,SAAS,CACZD,MAAOrB,KAAKgoB,YAAYhoB,KAAKY,MAAMwN,WAGrB,QAATtP,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaC,SAC3Cd,KAAKsB,SAAS,CACZD,MAAO,IAAIE,EAAAA,GAAgB,CACzBU,UAAW,SACXymB,UAAWC,GACX1gB,OAAQ0gB,GACRnnB,SAAU,CACR,IAAIkB,EAAAA,EAAkB,CACpBC,UAAW,KAAMC,EAAAA,GAAAA,IAAkB,cASrD,EA2FF,SAAS1G,GAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbuqB,KAAM,EACNpqB,MAAO,OACPF,QAAS,OACT2R,cAAe,SACf5H,OAAQ,aAAalC,EAAMM,OAAO4B,OAAOC,OACzC6Q,aAAc,MACd3S,WAAYL,EAAMM,OAAOD,WAAW4B,QACpCmS,WAAY,MAEZ,yBAA0B,CACxBsO,YAAa,eAGf,mBAAoB,CAClBvqB,QAAS,UAGbwqB,eAAezqB,EAAAA,EAAAA,KAAI,CACjBC,QAAS,OACTgI,WAAY,SACZ4B,SAAU,WACVme,IAAK,MACL7c,KAAM,MACN8c,OAAQ,IAEVyC,WAAW1qB,EAAAA,EAAAA,KAAI,CACbyI,OAAQ,UACR4hB,UAAWC,GAEX,iCAAoC,CAClC1gB,OAAQ0gB,GACR3hB,SAAU,UAGZ,SAAU,CACRR,WAAYL,EAAMM,OAAOD,WAAWE,UACpCsiB,MAAO,CACL7gB,gBAAiB,UACjBE,OAAQ,oBACRvB,OAAQ,cAIdmiB,aAAa5qB,EAAAA,EAAAA,KAAI,CACfC,QAAS,UAEX4qB,kBAAkB7qB,EAAAA,EAAAA,KAAI,CACpB6J,SAAU,WACVme,IAAK,MACL1c,MAAO,MACP2c,OAAQ,IAGd,C,8/BA/FE,GAtGWsB,GAsGGjiB,YAAY,EAAGC,YAC3B,MAAM,MAAEvE,EAAK,YAAEknB,GAAgB3iB,EAAM5K,WAC/BgB,GAASC,EAAAA,EAAAA,YAAWC,IACpBgoB,GAAmBlhB,EAAAA,EAAAA,IAAyB4C,GAE5CujB,EAAe,MACnBtkB,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBskB,OAAQpkB,EAAAA,GAAoBokB,OAAOC,eAAgB,CACxFjb,OAAQxI,EAAMhF,MAAMwN,OACpBkb,SAAU,UAEZpF,EAAiBqF,uBAAuB3jB,EAAMhF,MAAMwN,SAGtD,GAAK/M,EAIL,OACE,kBAACjE,MAAAA,CAAIC,WAAWgB,EAAAA,EAAAA,KAAI,CAACrC,EAAOsB,UAAWtB,EAAO+sB,YAAa3tB,QAAS+tB,GAClE,kBAAC/rB,MAAAA,CAAIC,UAAWrB,EAAO8sB,eACrB,kBAACU,EAAAA,gBAAeA,CACdnsB,UAAWrB,EAAOitB,YAClB7qB,KAAM,UAAUwH,EAAMhF,MAAMwN,SAC5BvS,QAAS,CAAC,CAAEkE,MAAO,GAAIjE,MAAO,aAC9BC,SAAU,IAAMotB,IAChBrtB,MAAO,kBAGVysB,GACC,kBAACnrB,MAAAA,CAAIC,UAAWrB,EAAOktB,kBACrB,kBAACO,GAAAA,EAAkBA,CAAClB,aAAa,EAAMmB,SAAU,MAGrD,kBAACroB,EAAMsE,UAAS,CAACC,MAAOvE,OCxGzB,MAAMsoB,WAA6BjrB,EAAAA,GAYhC+J,WAAAA,GAEN,MACMmhB,EADS,IAAIC,gBAAgBC,OAAOR,SAASS,QACtB3N,IAAI,cAC7BwN,GAAiB7D,GAAuB7mB,KAAMqW,GAAMA,EAAEzZ,QAAU8tB,IAClE5pB,KAAKsB,SAAS,CAAEmlB,WAAYmD,IAG9B5pB,KAAK0I,aAEL,MACMshB,GADchnB,EAAAA,EAAAA,IAAyBhD,MACV6I,oBACnC7I,KAAKwF,MAAMC,IACTukB,EAAetkB,iBAAiB,CAACiD,EAAUC,KACzC,GAAID,EAAS7M,QAAU8M,EAAU9M,MAAO,CACtC,MAAMimB,GAAY+D,EAAAA,GAAAA,GAA6Bnd,EAAS7M,OACpDimB,GACF/hB,KAAKsB,SAAS,CAAEygB,cAElB/hB,KAAKiqB,kBAAkBthB,EAAS7M,OAChCkE,KAAKkqB,sBAAsBvhB,EAAS7M,OACpCkE,KAAK0I,YACP,KAKJ1I,KAAKkqB,sBAAsBF,EAAerlB,YAE1C3E,KAAKwF,MAAMC,IACTzF,KAAK0F,iBAAiB,CAACiD,EAAUC,K,IAETD,EAAAA,EADtB,MAAMxF,EAAYC,EAAAA,GAAWC,aAAarD,MACpCmqB,EAAkC,QAAlBxhB,EAAAA,EAASoZ,iBAATpZ,IAAAA,GAA6B,QAA7BA,EAAAA,EAAoBxF,iBAApBwF,IAAAA,OAAAA,EAAAA,EAA+B6D,KAOrD,GALI2d,GAAiBA,EAAgBhnB,EAAUvC,MAAM9E,MAAM0Q,KAAK4W,QAC9DpjB,KAAKsB,SAAS,CAAEygB,eAAWlK,MAIxBoL,EAAAA,GAAAA,SAAQta,EAASoZ,UAAWnZ,EAAUmZ,WAAY,EAClCzI,EAAAA,EAAAA,IAAmBtZ,MAC3B4E,cAAcqO,EAAAA,IACzBjT,KAAKiqB,kBAAkBD,EAAerlB,WACxC,KAIJ3E,KAAKwF,MAAMC,KACT2kB,EAAAA,EAAAA,IAAsBpqB,MAAM0F,iBAAiB,KAC3C1F,KAAKqqB,sBAITrqB,KAAKwF,MAAMC,KACTf,EAAAA,EAAAA,IAA2B1E,MAAM0F,iBAAiB,KAChD1F,KAAKiqB,kBAAkBD,EAAerlB,eAI1C3E,KAAKiqB,kBAAkBD,EAAerlB,YACtC3E,KAAKqqB,kBACP,CAEA3hB,UAAAA,GACE,MACM0F,GADmBpL,EAAAA,EAAAA,IAAyBhD,MAClB6I,oBAAoBlE,WAC9C2lB,EAAgBvE,GAAuB7mB,KAAMqW,GAAMA,EAAEzZ,QAAUkE,KAAKY,MAAM6lB,YAEhFzmB,KAAKsB,SAAS,CACZI,KAAM6oB,GACJnc,EACAkc,EAAgB,CAACA,aAAAA,EAAAA,EAAetE,SAAS5X,SAA6ByJ,UAI5CA,IAA1B7X,KAAKY,MAAM6lB,YACbzmB,KAAKqhB,cAAc,YAEvB,CAEU6I,qBAAAA,CAAsB9b,GAC9B,GAAe,WAAXA,GACF,IAAKpO,KAAKY,MAAMomB,gBAAiB,CAC/B,MAAMA,EAAkB,IAAIvK,GAAgB,CAAC,GAC7Czc,KAAKsB,SAAS,CACZ0lB,oBAIFvrB,WAAW,KACTurB,EAAgBwD,YACf,EACL,OAGIxqB,KAAKY,MAAMomB,iBACbhnB,KAAKsB,SAAS,CACZ0lB,qBAAiBnP,GAIzB,CAEcwS,gBAAAA,G,yBAaZI,EAZA,MAAMA,QAAWC,EAAAA,EAAAA,oBAAmBtO,IAAIuO,EAAAA,GAAqB,CAAEC,cAAe,CAAE9uB,MAAOkE,QAEvF,IAAKyqB,EACH,OAGF,MACM5uB,EAAU,CACdsH,UAFgBC,EAAAA,GAAWC,aAAarD,MAEnBY,MAAM9E,MAC3BgT,QAAS,IAGE,QAAb2b,EAAAA,EAAGI,kBAAHJ,IAAAA,GAAAA,EAAAA,KAAAA,EAAgB5uB,GAASivB,KAAMC,IAC7B,IAAI7F,EAA0B,GAE5BA,EADE,SAAU6F,EACL,EAA4BjsB,KAE5BisB,EAET,MAAMjlB,EAAaof,EAAKrnB,IAAKmtB,GAAMA,EAAEpkB,MACjCd,IAAe9F,KAAKY,MAAMkF,YAC5B9F,KAAKsB,SAAS,CAAEwE,gBAGtB,E,+KAAA,W,MAEAmlB,WAAAA,GACE,MAAO,CACLxE,WAAYzmB,KAAKY,MAAM6lB,WACvB1E,UAAW/hB,KAAKY,MAAMmhB,UAAYxe,KAAKC,UAAUxD,KAAKY,MAAMmhB,gBAAalK,EAE7E,CAEAqT,aAAAA,CAAc5uB,GACZ,GAAiC,iBAAtBA,EAAOmqB,YAChB,GAAIzmB,KAAKY,MAAM6lB,aAAenqB,EAAOmqB,WAAY,CAC/C,MAAM6D,EAAgBvE,GAAuB7mB,KAAMqW,GAAMA,EAAEzZ,QAAUQ,EAAOmqB,YACxE6D,GACFtqB,KAAKqhB,cAAciJ,EAAcxuB,MAErC,OAC+B,OAAtBQ,EAAOmqB,YAChBzmB,KAAKqhB,cAAc,aAGrB,GAAgC,iBAArB/kB,EAAOylB,UAAwB,CACxC,MAAMoJ,EAAe5nB,KAAK0I,MAAM3P,EAAOylB,YAClCkB,EAAAA,GAAAA,SAAQkI,EAAcnrB,KAAKY,MAAMmhB,YACpC/hB,KAAKsB,SAAS,CAAEygB,UAAWoJ,GAE/B,CACF,CAEAC,qBAAAA,CAAsBD,GACpBnrB,KAAKqrB,SAASC,4BAA4B,KACxCtrB,KAAKsB,SAAS,CAAEygB,UAAWoJ,KAE/B,CAEO9J,aAAAA,CAAcoF,GACnB,MAAM,KAAE/kB,GAAS1B,KAAKY,MAChB0pB,EAAgBvE,GAAuB7mB,KAAMqW,GAAMA,EAAEzZ,QAAU2qB,GAE/DrY,GADmBpL,EAAAA,EAAAA,IAAyBhD,MAClB6I,oBAAoBlE,WAEpD,GAAIjD,EAAKd,MAAMY,SAASN,OAAS,GAC3BopB,EAAe,CACjB,IAAIpU,EAGFA,EAFiB,eAAfuQ,GAA+BzmB,KAAKY,MAAMomB,gBAEpC,IAAIvlB,EAAAA,GAAc,CACxBC,KAAM1B,KAAKY,MAAMomB,kBAGXsD,EAActE,SAAS5X,GAGjC1M,EAAKJ,SAAS,CACZE,SAAU,IAAIE,EAAKd,MAAMY,SAAS7E,MAAM,EAAG,GAAIuZ,MAEjDrR,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAewmB,oBAAqB,CAC7GC,UAAWxrB,KAAKY,MAAM6lB,WACtBgF,UAAWhF,IAEbzmB,KAAKsB,SAAS,CAAEmlB,WAAY6D,EAAcxuB,OAC5C,CAEJ,CAEQmuB,iBAAAA,CAAkB7b,G,IAER1J,EADhB,MAAMqd,EAAY/hB,KAAKY,MAAMmhB,U,IACbrd,EAAhB,MAAMF,EAA+D,QAArDE,EAAyC,QAAzCA,GAAAA,EAAAA,EAAAA,IAA2B1E,MAAM2E,kBAAjCD,IAAAA,OAAAA,EAAAA,EAA6CjH,kBAA7CiH,IAAAA,EAAAA,EAA2D,GAE3E1E,KAAKsB,SAAS,CACZ6D,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAI8I,EAAAA,GAAiB,CAC1BhL,WAAYiL,EAAAA,GACZtK,QAAS,CAACuK,GAAWC,EAAQ5J,EAASud,IACtC2J,WAAYC,GAAuB5J,KAErC1c,gBAAiB,IAAIgJ,EAAAA,MAA2Cud,OAGtE,CAxNA,YAAmBhrB,G,IAETA,EADRkC,MAAM,IACJpB,KAAgB,QAAVd,EAAAA,EAAMc,YAANd,IAAAA,EAAAA,EAAc,IAAIW,EAAAA,GAAgB,CAAEC,SAAU,MACjDZ,IALP,QAAUyqB,WAAW,IAAIQ,EAAAA,GAAyB7rB,KAAM,CAAEklB,KAAM,CAAC,aAAc,gBAQ7EllB,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EAmNA,GA7NW2pB,GA6NJhkB,YAAY,EAAGC,YACpB,MAAM,KAAElE,GAASkE,EAAM5K,WACjBgB,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,oCACE,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAO+D,OACrB,kBAAC+rB,EAAAA,QAAOA,CAACxR,QAAS,kBAACyR,GAAAA,MAAsBC,UAAW,cAAeC,aAAAA,GACjE,kBAAClkB,OAAAA,CAAK1K,UAAWrB,EAAOkwB,MAAM,sBACT,kBAAC/tB,EAAAA,KAAIA,CAACC,KAAM,mBAIrC,kBAACsD,EAAKiE,UAAS,CAACC,MAAOlE,OAM/B,MAAMqqB,GAAoB,KACxB,MAAM/vB,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAACqT,EAAAA,MAAKA,CAACtN,UAAW,SAAUmE,IAAK,GAC/B,kBAAChJ,MAAAA,CAAIC,UAAWrB,EAAOlB,QAAQiF,OAAO,0BACtC,kBAACgI,OAAAA,CAAK1K,UAAWrB,EAAOlB,QAAQqxB,UAAU,oFAG1C,kBAAC/uB,MAAAA,CAAIC,UAAWrB,EAAOlB,QAAQ8L,MAC7B,kBAACxJ,MAAAA,KACC,kBAAC2K,OAAAA,CAAK1K,UAAWrB,EAAOlB,QAAQsxB,WAAW,QAAW,yFAGxD,kBAAChvB,MAAAA,KACC,kBAAC2K,OAAAA,CAAK1K,UAAWrB,EAAOlB,QAAQsxB,WAAW,UAAa,iEAG1D,kBAAChvB,MAAAA,KACC,kBAAC2K,OAAAA,CAAK1K,UAAWrB,EAAOlB,QAAQsxB,WAAW,YAAe,2FAK9D,kBAAChvB,MAAAA,CAAIC,UAAWrB,EAAOlB,QAAQuxB,QAC7B,kBAAC3c,EAAAA,WAAUA,CACTvU,KAAK,oBACLwU,KAAK,QACLpP,KAAM,KACND,OAAQ,SACRF,KACE,gIAEFhF,QAAS,KACPyJ,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBskB,OAAQpkB,EAAAA,GAAoBokB,OAAOkD,2BAE7E,yBAQT,SAASpwB,GAAUiK,GACjB,MAAO,CACLpG,OAAO1B,EAAAA,EAAAA,KAAI,CACT3B,MAAO,QACP4B,QAAS,OACT8H,IAAKD,EAAMkB,QAAQklB,KACnBhmB,SAAUJ,EAAMgB,WAAW0a,UAAUtb,SACrCimB,cAAermB,EAAMkB,QAAQklB,KAC7BjmB,WAAY,WAEd4lB,MAAM7tB,EAAAA,EAAAA,KAAI,CACR3B,MAAO,OACPoK,OAAQ,YAEVhM,QAAS,CACP4B,MAAO,UACPqD,OAAO1B,EAAAA,EAAAA,KAAI,CACTkI,SAAU,OACVkmB,WAAY,MAEdN,UAAU9tB,EAAAA,EAAAA,KAAI,CACZkK,aAAcpC,EAAMkB,QAAQ6I,KAE9BtJ,MAAMvI,EAAAA,EAAAA,KAAI,CACR3B,MAAO,OACPiK,MAAOR,EAAMM,OAAOG,KAAKF,UAEzBtJ,IAAK,CACHmL,aAAcpC,EAAMkB,QAAQklB,QAGhCH,WAAW/tB,EAAAA,EAAAA,KAAI,CACb3B,MAAO,YACPiK,MAAOR,EAAMM,OAAOG,KAAKwB,UAE3BikB,QAAQhuB,EAAAA,EAAAA,KAAI,CACVkK,aAAcpC,EAAMkB,QAAQklB,QAIpC,CAEA,MAAMG,GAAoB,IACb/D,IAAqB+D,GAAoB,GAAK,EAEpD,SAASve,GAAW/O,EAAsBoF,EAAiBud,GAChE,MAAMrR,EAA0B,KAAZlM,EAAiB,aAAaA,KAAa,GAC/D,IAAImoB,EAAY,GAChB,OAAQvtB,GACN,IAAK,SACHutB,EAAY,qBACZ,MACF,IAAK,WACH,GAAI5K,EAAW,C,IAETA,EAGAA,EAJJ,MAAMpU,EAAW,IACK,QAAlBoU,EAAAA,EAAUpU,gBAAVoU,IAAAA,OAAAA,EAAAA,EAAoBvV,KAAKtL,SAC3ByM,EAAS5Q,KAAK,eAAeglB,EAAUpU,SAASnB,SAE5B,QAAlBuV,EAAAA,EAAUpU,gBAAVoU,IAAAA,OAAAA,EAAAA,EAAoBtV,GAAGvL,SACzByM,EAAS5Q,KAAK,eAAeglB,EAAUpU,SAASlB,MAE9CkB,EAASzM,SACXyrB,GAAa,MAAQhf,EAAS7P,KAAK,QAEvC,CACK6uB,EAAUzrB,SACbyrB,EAAY,iBAAiBle,EAAAA,MAInC,MAAO,CACL5K,MAAO,IACPE,MAAO,IAAI2K,EAAAA,KAAmBie,KAAajc,IAC3C5M,UAAW,UACX6K,UAAW,QACXC,MAAO,IACPC,KAAM,GACNC,QAAS,GAEb,CAEA,SAAS6c,GAAuB5J,G,IACPA,EACFA,EADrB,MAAME,EAAoD,MAAnCF,SAAoB,QAApBA,EAAAA,EAAW5e,iBAAX4e,IAAAA,OAAAA,EAAAA,EAAsBvV,OAAQ,GAC/C0V,EAAgD,MAAjCH,SAAoB,QAApBA,EAAAA,EAAW5e,iBAAX4e,IAAAA,OAAAA,EAAAA,EAAsBtV,KAAM,GACjD,OAAOwV,GAAiBC,EACpB,IAAI0K,EAAAA,GAAe,CACjBpgB,KAAMyV,EAAc4K,QAAQ,GAC5BpgB,GAAIyV,EAAY2K,QAAQ,GACxB/wB,MAAO,CACL0Q,MAAMsgB,EAAAA,EAAAA,UAAS7K,GACfxV,IAAIqgB,EAAAA,EAAAA,UAAS5K,GACbve,IAAK,CAAE6I,MAAMsgB,EAAAA,EAAAA,UAAS7K,GAAgBxV,IAAIqgB,EAAAA,EAAAA,UAAS5K,YAGvDrK,CACN,CAEA,SAAS0S,GAAgBnc,EAAwB5M,GAC/C,MAAMurB,EAEA,IAAInF,GADG,SAAXxZ,EACqB,CAAEA,OAAQ,UACV,CACfA,OAAQ,SAGV4e,EAEA,IAAIpF,GADG,aAAXxZ,EACqB,CACfA,OAAQ,UAEO,CAAEA,OAAQ,aAEjC,OAAO,IAAI7M,EAAAA,GAAgB,CACzBU,UAAW,SACXuU,WAAY,CACV,IAAIyW,EAAAA,GAAAA,GAAqB,CACvBplB,IAAK,sBACLqlB,KAAMC,EAAAA,oBAAoBC,aAG9B5rB,SAAU,CACR,IAAID,EAAAA,GAAgB,CAClBU,UAAW,MACXorB,QAAS,UACT7rB,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChB6K,UAAWogB,GACXhE,UAAWgE,GACXluB,MAAO,MACPkD,KAAM,IAAI4rB,EAAAA,GAAS,CAAC,KAEtB,IAAI/rB,EAAAA,GAAgB,CAClBU,UAAW,SACXqK,UAAWogB,GACXhE,UAAWgE,GACXlrB,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChB6K,UAAWqc,GACXD,UAAWC,GACX1gB,OAAQ0gB,GAERjnB,KAAMqrB,IAER,IAAItrB,EAAAA,GAAc,CAChB6K,UAAWqc,GACXD,UAAWC,GACX1gB,OAAQ0gB,GAER0E,QAAS,OAET3rB,KAAMsrB,UAMhB,IAAIvrB,EAAAA,GAAc,CAChB4rB,QAAS,UACT3rB,KAAM,IAAIukB,GAAa,CAAC,QAEtBzkB,GAAY,KAGtB,CAEA,MAAMoqB,GAA0B,CAC9B,IAAOhtB,GACEA,EAAOC,MACZhB,EAAAA,EAAAA,KAAKiB,GACIA,EAAKjB,IAAKkB,GAAQ,SACpBA,GAAAA,CACHE,OAAQF,EAAGE,OAAO2S,OAAQzS,IAAOA,EAAEf,KAAKpB,WAAW,mBAK3D,CACEuwB,GAAI,SACJ1xB,QAAS,CACPoD,OAAQ,CAAC,EACThC,KAAM,CACJ,CACE4Z,MAAO,WACP2W,MAAM,MAKd,CACED,GAAI,WACJ1xB,QAAS,CACP4xB,YAAa,CACX,aAAc,EACdxV,OAAQ,EACR,gBAAiB,EACjB,aAAc,EACdyV,SAAU,EACV,UAAW,EACX,mBAAoB,EACpB,2BAA4B,EAC5B,iBAAkB,EAClB,kBAAmB,EACnB,wBAAyB,GACzB,iCAAkC,M,8FC9hBnC,MAAM/rB,UAAwBjD,EAAAA,I,EACT,EAAGkH,YAC3B,MAAM,QAAEhE,EAAO,cAAEE,EAAa,SAAE2mB,EAAQ,QAAEzmB,GAAY4D,EAAM5K,WAC5D,OAAO,kBAACgV,EAAAA,EAAUA,CAACpO,QAASA,EAASE,cAAeA,EAAe2mB,SAAUA,EAAUzmB,QAASA,M,EAFpF2D,e,EADHhE,G,4UCGN,MAAMsY,UAA2Bvb,EAAAA,G,kBAAjC,YACL,OAAOtD,UAAU,K,IAGA,EAFf,MAAMqJ,GAAW0M,EAAAA,EAAAA,IAAmBnR,M,IAErB,EAAf,MAAMgY,EAA8D,QAArD,EAA4C,QAA5C,EAAAhY,KAAKY,MAAMnB,MAAMR,OAAOC,KAAMC,GAAMA,EAAE6Y,eAAtC,eAA+CA,cAA/C,QAAyD,CAAC,EACzE,GAAIhY,KAAKY,MAAMsZ,UACb,IAAKlC,EAAOhY,KAAKY,MAAMsZ,UACrB,YAGF,GAAmC,IAA/B7d,OAAO6oB,KAAKlN,GAAQ9W,OACtB,O,IAIc,EAAlB,MAAMysB,EAA+B,QAAnB,EAAA3tB,KAAKY,MAAMsZ,gBAAX,QAAuB7d,OAAO6oB,KAAKlN,GAAQ,GACvDlc,GAAQ4b,EAAAA,EAAAA,IAAc1X,KAAKY,MAAMnB,MAAOO,KAAKY,MAAMsZ,UAEzD0T,EAAanpB,EAAUkpB,EAAW7xB,GAElCkE,KAAKY,MAAMxF,QAAQ,CAAEuyB,e,EAGvB,EAvBW1T,EAuBGtU,YAAY,EAAGC,Y,IACfA,EACEA,EACAiR,EAAAA,EAFFjR,EAAZ,MAAMiC,EAA2B,QAArBjC,EAAW,QAAXA,EAAAA,EAAMhF,aAANgF,IAAAA,OAAAA,EAAAA,EAAasU,gBAAbtU,IAAAA,EAAAA,EAAyB,GAC/BiR,EAAmB,QAAXjR,EAAAA,EAAMhF,aAANgF,IAAAA,OAAAA,EAAAA,EAAanG,MAAMR,OAAO2S,OAAQhU,GAAiB,SAAXA,EAAEwB,M,IAC1CyX,EAAd,MAAM/a,EAAiC,QAAzB+a,EAAAA,SAAU,QAAVA,EAAAA,EAAQ,UAARA,IAAAA,GAAkB,QAAlBA,EAAAA,EAAYmB,cAAZnB,IAAAA,OAAAA,EAAAA,EAAqBhP,UAArBgP,IAAAA,EAAAA,EAA6B,GAG3C,OAFqBgX,GAAmB1c,EAAAA,EAAAA,IAAmBvL,GAAQiC,EAAK/L,EAAMoW,QAAQ,KAAM,KASrF,qCALH,kBAAC2Q,EAAAA,OAAMA,CAAC3nB,QAAQ,UAAUqF,KAAK,KAAKoP,KAAK,OAAOvU,QAASwK,EAAMxK,QAASD,KAAM,eAAe,oBAS9F,MAAMyyB,EAAe,CAACnpB,EAAgC/H,EAAeZ,KAI1E,MAAMgyB,EAAoBrpB,EAAS7D,MAAMkO,QAAQ8C,OAAQzS,GAAMA,EAAE0I,MAAQkmB,EAAAA,IAAsB5uB,EAAE0I,MAAQnL,GAIzGsxB,QAAQC,UAAU,KAAM,IAExBxpB,EAASnD,SAAS,CAChBwN,QAAS,IACJgf,EACH,CACEjmB,IAAKnL,EACLsV,SAAU,IACVlW,MAAOA,OAMF+xB,EAAqB,CAACjoB,EAA6BiC,EAAa/L,KAC1DqV,EAAAA,EAAAA,IAAmBvL,GACpBhF,MAAMkO,QAAQ5P,KAAMC,GAAMA,EAAE0I,MAAQA,GAAO1I,EAAErD,QAAUA,E,4DC5ElE,SAAS+rB,IACd,MAAO,CACLhkB,MAAO,IACPE,MAAO,IAAI2K,EAAAA,wDACX5K,UAAW,UACX6K,UAAW,QACXC,MAAO,IACPC,KAAM,GACNC,QAAS,GAEb,C,icCMO,MAAMof,EAAqB1uB,IAChC,MAAM,MAAE2U,EAAK,KAAE/U,EAAI,MAAE1C,EAAK,WAAEyxB,EAAU,MAAEryB,EAAK,WAAEsyB,EAAU,IAAEzP,GAAQnf,EAC7DxD,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,OACE,kBAACkB,MAAAA,CAAIyK,IAAKsM,GACG,IAAVA,GACC,kBAAC/W,MAAAA,CAAIC,UAAWrB,EAAOqyB,WACrB,kBAACtmB,OAAAA,KAAMomB,GACP,kBAACpmB,OAAAA,CAAK1K,UAAWrB,EAAOoyB,YAAaA,IAIzC,kBAAChxB,MAAAA,CACCC,UAAWrB,EAAO4L,IAClBC,IAAKsM,EACL/Y,QAAS,MACPyJ,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBwpB,KAAMtpB,EAAAA,GAAoBspB,KAAKC,kBAAmB,CACvFnvB,OACA+U,QACArY,UAEF0yB,EAAAA,gBAAgBzxB,KAAK4hB,KAGvB,kBAACvhB,MAAAA,CAAIC,UAAW,YAAaX,GAE7B,kBAACU,MAAAA,CAAIC,UAAWrB,EAAOyT,QACrB,kBAAC1H,OAAAA,CAAK1K,UAAWrB,EAAOyyB,YAAa3yB,GACrC,kBAACqC,EAAAA,KAAIA,CAACd,UAAWrB,EAAO0yB,WAAYtwB,KAAK,cAAcmC,KAAK,WAOtE,SAASrE,EAAUiK,GACjB,MAAO,CACLkoB,WAAWhwB,EAAAA,EAAAA,KAAI,CACbsI,MAAOR,EAAMM,OAAOG,KAAKF,UACzBpI,QAAS,OACT+H,eAAgB,gBAChBC,WAAY,SACZtE,QAAS,KAAKmE,EAAMkB,QAAQ,MAAMlB,EAAMkB,QAAQ,MAAMlB,EAAMkB,QAAQ,OAEtE+mB,YAAY/vB,EAAAA,EAAAA,KAAI,CACd8R,OAAQ,eAEVvI,KAAKvJ,EAAAA,EAAAA,KAAI,CACPC,QAAS,OACT+H,eAAgB,gBAChBC,WAAY,SACZF,IAAKD,EAAMkB,QAAQ,GACnBrF,QAAS,GAAGmE,EAAMkB,QAAQ,QAASlB,EAAMkB,QAAQ,KAEjD,UAAW,CACTc,gBAAiBhC,EAAMwoB,OAASxoB,EAAMM,OAAOD,WAAWE,UAAYP,EAAMM,OAAOD,WAAW4B,QAC5FtB,OAAQ,UACR,YAAa,CACXI,eAAgB,gBAItBuI,QAAQpR,EAAAA,EAAAA,KAAI,CACVC,QAAS,OACTgI,WAAY,WAEdmoB,YAAYpwB,EAAAA,EAAAA,KAAI,CACdsI,MAAO,UACP3E,QAAS,KAAKmE,EAAMkB,QAAQ,KAC5B7I,MAAO,gBAETkwB,YAAYrwB,EAAAA,EAAAA,KAAI,CACdyI,OAAQ,UACRqJ,OAAQ,KAAKhK,EAAMkB,QAAQ,SAAUlB,EAAMkB,QAAQ,OAGzD,CClFO,MAAMunB,EAAuBpvB,I,IAgC7ByB,EA/BL,MAAM,OAAEA,EAAM,KAAE7B,GAASI,EACnBxD,GAASC,EAAAA,EAAAA,YAAWC,GAEpBwmB,EAAY3jB,I,IAET8vB,EADP,MAAMA,EAAc9vB,EAAGE,OAAOC,KAAMC,GAAiB,SAAXA,EAAEf,M,IACrCywB,EAAP,OAAoE,QAA7DA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAa7W,cAAb6W,IAAAA,OAAAA,EAAAA,EAAsB,yBAAyB3c,QAAQ,KAAM,WAA7D2c,IAAAA,EAAAA,EAAoE,0BAGvEC,EAAU/vB,IACd,MACMgwB,EAAS,CACb,cAAe,2BAFGrM,EAAS3jB,KAG3B,aAAc,UAEhB,OAAOqF,EAAAA,QAAQC,UAAU2qB,EAAAA,GAAoBD,IAGzCE,EAAgBlwB,I,IAGlB8vB,EAFF,MAAMA,EAAc9vB,EAAGE,OAAOC,KAAMC,GAAiB,SAAXA,EAAEf,M,IAE1CywB,EADF,OAMK,QALHA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAavyB,cAAbuyB,IAAAA,OAAAA,EAAAA,EAAqBtyB,OAAO,CAACqB,EAAGpB,IACb,iBAANoB,GAAmB6hB,MAAM7hB,GAG7BpB,EAFEoB,EAAIpB,EAGZ,UALHqyB,IAAAA,EAAAA,EAKS,GAIb,OACE,kBAACzxB,MAAAA,CAAIC,UAAWrB,EAAOsB,WAGT,QAFX2D,EAAAA,EACEhE,KAAK,CAACC,EAAGC,IAAM8xB,EAAa9xB,GAAK8xB,EAAa/xB,IAC9CP,MAAM,EAAG,WAFXsE,IAAAA,OAAAA,EAAAA,EAGGpD,IAAI,CAACkB,EAAIoV,IACT,kBAACpM,OAAAA,CAAKF,IAAKsM,GACT,kBAAC+Z,EAAiBA,CAChB9uB,KAAMA,EACN+U,MAAOA,EACPzX,MAAOgmB,EAAS3jB,GAChBovB,WAAW,UACXryB,MAAOmzB,EAAalwB,GACpBqvB,WAAW,eACXzP,IAAKmQ,EAAO/vB,SAQ1B,SAAS7C,EAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACb2D,QAAS,GAAGmE,EAAMkB,QAAQ,SAGhC,C,0kBCzDO,MAAM6nB,EAAqB1vB,IAChC,MAAM,OAAEyB,EAAM,KAAE7B,GAASI,EACnBxD,GAASC,EAAAA,EAAAA,YAAWC,GAEpBizB,EAAWluB,EAAO,GAAGhC,OAAOC,KAAMC,GAAiB,aAAXA,EAAEf,MAChD,GAAI+wB,GAAYA,EAAS7yB,OAAQ,C,IACN6yB,EA0DpBzvB,EA1DL,MAAM0vB,EAAmBD,SACRxnB,QADQwnB,EAAAA,EAAU7yB,OAChCuB,IAAI,CAAC6J,EAAGC,IAAMA,UADQwnB,IAAAA,OAAAA,EAAAA,EAErBlyB,KAAK,CAACC,EAAGC,KAAMgyB,aAAAA,EAAAA,EAAU7yB,OAAOa,KAAKgyB,aAAAA,EAAAA,EAAU7yB,OAAOY,KACpDmyB,EAAepuB,EAAO,GAAGhC,OAAOpB,IAAKsB,GAClC,E,kUAAA,IACFA,GAAAA,CACH7C,OAAQ8yB,aAAAA,EAAAA,EAAkBvxB,IAAK8J,GAAMxI,EAAE7C,OAAOqL,OAI5C+a,EAAW,CAAC4M,EAAsCC,EAAmCpb,KACzF,IAAIzX,EAAQ,GAOZ,OANI4yB,aAAAA,EAAAA,EAAmBhzB,OAAO6X,MAC5BzX,EAAQ4yB,EAAkBhzB,OAAO6X,KAE/Bob,aAAAA,EAAAA,EAAgBjzB,OAAO6X,MACzBzX,EAAyB,IAAjBA,EAAMwE,OAAequB,EAAejzB,OAAO6X,GAAS,GAAGzX,MAAU6yB,EAAejzB,OAAO6X,MAEzE,IAAjBzX,EAAMwE,OAAe,iCAAmCxE,GAG3DoyB,EAAS,CACblvB,EACAD,EACA2vB,EACAnb,KAEA,KAAKxU,GAAgBA,EAAYrD,OAAO6X,IAAWmb,GAAsBA,EAAkBhzB,OAAO6X,IAEhG,OADAqb,QAAQC,MAAM,oCACPC,EAAAA,GAAOC,QAGhB,MAAMZ,EAAS,CACbnvB,UACAE,OAAQH,EAAYrD,OAAO6X,GAC3B,cAAe,2BAA2Bmb,EAAkBhzB,OAAO6X,KACnE,aAAc,YAGhB,OAAO/P,EAAAA,QAAQC,UAAU2qB,EAAAA,GAAoBD,IAGzCa,EAAc,CAACC,EAAkC1b,IAChD0b,GAAkBA,EAAcvzB,QAI9BwzB,EAAAA,EAAAA,IAAeD,EAAcvzB,OAAO6X,GAAS,KAH3C,qBAMLzU,EAAe2vB,EAAanwB,KAAMC,GAAiB,kBAAXA,EAAEf,MAC1CuB,EAAc0vB,EAAanwB,KAAMC,GAAiB,WAAXA,EAAEf,MACzCmxB,EAAiBF,EAAanwB,KAAMC,GAAiB,cAAXA,EAAEf,MAC5CkxB,EAAoBD,EAAanwB,KAAMC,GAAiB,iBAAXA,EAAEf,MAC/CyxB,EAAgBR,EAAanwB,KAAMC,GAAiB,aAAXA,EAAEf,MAEjD,OACE,kBAAChB,MAAAA,CAAIC,UAAWrB,EAAOsB,WACpBoC,SAAoB,QAApBA,EAAAA,EAAcpD,cAAdoD,IAAAA,OAAAA,EAAAA,EAAsB7B,IAAI,CAAC+B,EAASuU,IACnC,kBAACpM,OAAAA,CAAKF,IAAKsM,GACT,kBAAC+Z,EAAiBA,CAChB9uB,KAAMA,EACN+U,MAAOA,EACPzX,MAAOgmB,EAAS4M,EAAmBC,EAAgBpb,GACnDga,WAAW,QACXryB,MAAO8zB,EAAYC,EAAe1b,GAClCia,WAAW,WACXzP,IAAKmQ,EAAOlvB,EAASD,EAAa2vB,EAAmBnb,OAMjE,CACA,OAAO,MAGT,SAASjY,EAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACb2D,QAAS,GAAGmE,EAAMkB,QAAQ,SAGhC,CCzFO,MAAM0oB,EAAuBvwB,I,IAgC7ByB,EA/BL,MAAM,OAAEA,EAAM,KAAE7B,GAASI,EACnBxD,GAASC,EAAAA,EAAAA,YAAWC,GAEpBwmB,EAAY3jB,I,IAET8vB,EADP,MAAMA,EAAc9vB,EAAGE,OAAOC,KAAMC,GAAiB,SAAXA,EAAEf,M,IACrCywB,EAAP,OAAoE,QAA7DA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAa7W,cAAb6W,IAAAA,OAAAA,EAAAA,EAAsB,yBAAyB3c,QAAQ,KAAM,WAA7D2c,IAAAA,EAAAA,EAAoE,0BAGvEC,EAAU/vB,IACd,MACMgwB,EAAS,CACb,cAAe,2BAFGrM,EAAS3jB,KAG3B,aAAc,YAEhB,OAAOqF,EAAAA,QAAQC,UAAU2qB,EAAAA,GAAoBD,IAGzCa,EAAe7wB,I,IAGjB8vB,EAFF,MAAMA,EAAc9vB,EAAGE,OAAOC,KAAMC,GAAiB,SAAXA,EAAEf,M,IAE1CywB,EADF,OAMK,QALHA,EAAAA,SAAmB,QAAnBA,EAAAA,EAAavyB,cAAbuyB,IAAAA,OAAAA,EAAAA,EAAqBtyB,OAAO,CAACqB,EAAGpB,IACb,iBAANoB,GAAmB6hB,MAAM7hB,GAG7BpB,EAFEoB,EAAIpB,EAGZ,UALHqyB,IAAAA,EAAAA,EAKS,GAIb,OACE,kBAACzxB,MAAAA,CAAIC,UAAWrB,EAAOsB,WAGT,QAFX2D,EAAAA,EACEhE,KAAK,CAACC,EAAGC,IAAMyyB,EAAYzyB,GAAKyyB,EAAY1yB,IAC5CP,MAAM,EAAG,WAFXsE,IAAAA,OAAAA,EAAAA,EAGGpD,IAAI,CAACkB,EAAIoV,IACT,kBAACpM,OAAAA,CAAKF,IAAKsM,GACT,kBAAC+Z,EAAiBA,CAChB9uB,KAAMA,EACN+U,MAAOA,EACPzX,MAAOgmB,EAAS3jB,GAChBovB,WAAW,UACXryB,OAAOg0B,EAAAA,EAAAA,IAAiC,IAAlBF,EAAY7wB,IAClCqvB,WAAW,MACXzP,IAAKmQ,EAAO/vB,SAQ1B,SAAS7C,EAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACb2D,QAAS,GAAGmE,EAAMkB,QAAQ,SAGhC,CCzDO,MAAM2oB,EAAsBxwB,IACjC,MAAM,OAAEyB,EAAM,KAAE7B,EAAI,QAAEwC,GAAYpC,EAC5BxD,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,GAAI0F,EACF,OACE,kBAACxE,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAO4F,SACrB,kBAACzD,EAAAA,KAAIA,CAACd,UAAWrB,EAAOb,KAAMiD,KAAK,qBAAqBmC,KAAK,OAC5DqB,IAMT,GAAIX,GAAUA,EAAOC,OAAS,EAC5B,OAAQ9B,GACN,IAAK,iBACH,OAAO,kBAAC8vB,EAAiBA,CAACjuB,OAAQA,EAAQ7B,KAAMA,IAClD,IAAK,mBACH,OAAO,kBAACwvB,EAAmBA,CAAC3tB,OAAQA,EAAQ7B,KAAMA,IACpD,IAAK,mBACH,OAAO,kBAAC2wB,EAAmBA,CAAC9uB,OAAQA,EAAQ7B,KAAMA,IAGxD,OAAO,kBAAChC,MAAAA,CAAIC,UAAWrB,EAAOsB,WAAW,mBAG3C,SAASpB,EAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACb2D,QAAS,GAAGmE,EAAMkB,QAAQ,SAE5BlM,MAAMkD,EAAAA,EAAAA,KAAI,CACR8R,OAAQ,KAAKhK,EAAMkB,QAAQ,SAAUlB,EAAMkB,QAAQ,OAErDzF,SAASvD,EAAAA,EAAAA,KAAI,CACXC,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,KACnB8I,OAAQ,GAAGhK,EAAMkB,QAAQ,UACzB7I,MAAO,QAGb,CC3CO,MAAMyxB,UAA4BvxB,EAAAA,I,UAiBzC,SAASwxB,EAAQ9wB,GACf,OAAQA,GACN,IAAK,mBACH,MAAO,aACT,IAAK,iBACH,MAAO,YAGT,QACE,MAAO,uBAEb,CAEA,SAASlD,EAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbgK,OAAQ,aAAalC,EAAMwoB,OAASxoB,EAAMM,OAAO4B,OAAO8nB,OAAShqB,EAAMM,OAAO4B,OAAOC,OACrF6Q,aAAchT,EAAMkB,QAAQ,IAC5BkB,aAAcpC,EAAMkB,QAAQ,GAC5B7I,MAAO,SAETuB,OAAO1B,EAAAA,EAAAA,KAAI,CACTsI,MAAOR,EAAMwoB,OAASxoB,EAAMM,OAAOG,KAAKF,UAAYP,EAAMM,OAAOG,KAAKwB,QACtED,gBAAiBhC,EAAMwoB,OAASxoB,EAAMM,OAAOD,WAAWE,UAAYP,EAAMM,OAAOD,WAAW4B,QAC5FgoB,oBAAqBjqB,EAAMkB,QAAQ,IACnCgpB,qBAAsBlqB,EAAMkB,QAAQ,IACpC/I,QAAS,OACT+H,eAAgB,SAChBC,WAAY,SACZC,SAAU,SACVvE,QAAS,GAAGmE,EAAMkB,QAAQ,QAAQlB,EAAMkB,QAAQ,OAElDipB,WAAWjyB,EAAAA,EAAAA,KAAI,CACb+R,WAAYjK,EAAMkB,QAAQ,KAGhC,C,EApD4B,EAAGzB,YAC3B,MAAM,OAAE3E,EAAM,MAAElB,EAAK,KAAEX,EAAI,QAAEwC,GAAYgE,EAAM5K,WACzCgB,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,OACE,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAO+D,OACrB,kBAAC5B,EAAAA,KAAIA,CAACC,KAAM8xB,EAAQ9wB,GAAOmB,KAAK,OAChC,kBAACwH,OAAAA,CAAK1K,UAAWrB,EAAOs0B,WAAYvwB,IAEtC,kBAACiwB,EAAkBA,CAAC/uB,OAAQA,EAAQ7B,KAAMA,EAAMwC,QAASA,O,EAVjD+D,e,EADHsqB,G,8+BCoBN,MAAMM,UAAuB7xB,EAAAA,GAClC,WAAAmE,CAAYjC,GACVkC,MAAM,GACJqC,MAAO,IAAI8I,EAAAA,GAAiB,CAC1BhL,WAAYiL,EAAAA,GACZtK,QAAS,CAAC,KAAEC,MAAO,IAAKC,UAAW,UAAW6K,UAAW,QAASC,MAAO,IAAOhO,EAAMmD,OAAK,CAAEysB,UAAW,QAEvG5vB,IAGLZ,KAAKkF,qBAAqB,KACxB,MAAMpG,EAAOsE,EAAAA,GAAWmC,QAAQvF,MAEhCA,KAAKwF,MAAMC,IACT3G,EAAK4G,iBAAkB5G,I,IACjBA,EAA0CA,EAmDnCA,EAjDPA,EAFJ,IAAa,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaM,OAAiB,QAATrC,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaG,UAC9E,IACW,QAATlC,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaM,MACL,IAA5BrC,EAAKA,KAAKmC,OAAOC,QAA+C,IAA/BpC,EAAKA,KAAKmC,OAAO,GAAGC,QAajD,GAAIpC,EAAKA,KAAKmC,OAAOC,OAAS,EAAG,C,IAa3BpC,EAZX,GAAmB,mBAAf8B,EAAMxB,MAA6BwB,EAAM6vB,oBAC3CzwB,KAAKsB,SAAS,CACZD,MAAO,IAAIE,EAAAA,GAAgB,CACzBC,SAAU,CACR,IAAIyuB,EAAoB,CACtBhvB,OAAQnC,EAAKA,KAAKmC,OAClBlB,MAAOa,EAAMb,MACbX,KAAMwB,EAAMxB,iBAKf,IAAa,QAATN,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaM,KAAM,C,IACtBrC,EAAAA,EAA3B,IAAI4xB,GAAWC,EAAAA,EAAAA,IAA6B,QAAjB7xB,EAAS,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWmC,cAAXnC,IAAAA,EAAAA,EAAqB,IAChD,GAAI4xB,aAAAA,EAAAA,EAAUxvB,OAAQ,CACpB,MAAM,YAAE0vB,IAAgBC,EAAAA,EAAAA,IAAuBH,G,IAOqB9vB,EALpEZ,KAAKsB,SAAS,CACZD,MAAO,IAAIE,EAAAA,GAAgB,CACzBC,SAAU,CACR,IAAI+uB,EAAe,CACjBxsB,MAAO,CACLA,MAAO,oCAAoC6sB,KAA2B,QAAZhwB,EAAAA,EAAMgR,cAANhR,IAAAA,EAAAA,EAAgB,OAE5Eb,MAAOa,EAAMb,MACbX,KAAMwB,EAAMxB,KACZqxB,qBAAqB,QAK/B,CACF,CACF,OA7CEzwB,KAAKsB,SAAS,CACZD,MAAO,IAAIE,EAAAA,GAAgB,CACzBC,SAAU,CACR,IAAIyuB,EAAoB,CACtBruB,QAASkvB,GAAiBlwB,EAAMb,MAAM6P,eACtC7P,MAAOa,EAAMb,MACbX,KAAMwB,EAAMxB,iBAwCJ,QAATN,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAauI,MAC3CpJ,KAAKsB,SAAS,CACZD,MAAO,IAAIE,EAAAA,GAAgB,CACzBC,SAAU,CACR,IAAIyuB,EAAoB,CACtBruB,QAASmvB,GAAgBjyB,GACzBiB,MAAOa,EAAMb,MACbX,KAAMwB,EAAMxB,YAMpBY,KAAKsB,SAAS,CACZD,MAAO,IAAIE,EAAAA,GAAgB,CACzBU,UAAW,SACXymB,UAAWC,EAAAA,GACX1gB,OAAQ0gB,EAAAA,GACRnnB,SAAU,CACR,IAAIkB,EAAAA,EAAkB,CACpBC,UAAW,IAAMC,eASnC,EAkBF,SAAS1G,IACP,MAAO,CACLoB,WAAWe,EAAAA,EAAAA,KAAI,CACbE,SAAU,QACVC,MAAO,2BAGb,CAvBE,EAjGW+xB,EAiGG5qB,YAAY,EAAGC,YAC3B,MAAM,MAAEvE,GAAUuE,EAAM5K,WAClBgB,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,GAAKmF,EAIL,OACE,kBAACjE,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAAC+D,EAAMsE,UAAS,CAACC,MAAOvE,OAezB,MAAMuB,EAAoB,KAC/B,MAAM5G,GAASC,EAAAA,EAAAA,YAAWqL,GAE1B,OACE,kBAAClK,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAO+D,OACrB,kBAACwH,EAAAA,EAAQA,CAACC,MAAO,EAAGhJ,MAAO,OAE7B,kBAACpB,MAAAA,CAAIC,UAAWrB,EAAOg1B,iBACpB,IAAIvpB,MAAM,KAAK5J,IAAI,CAAC6J,EAAGC,IACtB,kBAACvK,MAAAA,CAAIC,UAAWrB,EAAO4L,IAAKC,IAAKF,GAC/B,kBAACvK,MAAAA,CAAIC,UAAWrB,EAAOi1B,SACrB,kBAAC1pB,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACpK,MAAAA,CAAIC,UAAWrB,EAAOk1B,UACrB,kBAAC3pB,EAAAA,EAAQA,CAACC,MAAO,UAS/B,SAASF,EAAkBnB,GACzB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbgK,OAAQ,aAAalC,EAAMwoB,OAASxoB,EAAMM,OAAO4B,OAAO8nB,OAAShqB,EAAMM,OAAO4B,OAAOC,OACrF6Q,aAAchT,EAAMkB,QAAQ,IAC5BkB,aAAcpC,EAAMkB,QAAQ,GAC5B7I,MAAO,SAETuB,OAAO1B,EAAAA,EAAAA,KAAI,CACTsI,MAAOR,EAAMM,OAAOG,KAAKF,UACzByB,gBAAiBhC,EAAMM,OAAOD,WAAWE,UACzCH,SAAU,SACVvE,QAAS,GAAGmE,EAAMkB,QAAQ,QAAQlB,EAAMkB,QAAQ,KAChD8pB,UAAW,WAEbH,iBAAiB3yB,EAAAA,EAAAA,KAAI,CACnB2D,QAAS,QAAQmE,EAAMkB,QAAQ,OAEjCO,KAAKvJ,EAAAA,EAAAA,KAAI,CACPC,QAAS,OACT+H,eAAgB,kBAElB4qB,SAAS5yB,EAAAA,EAAAA,KAAI,CACX8R,OAAQ,QACR3R,MAAO,UAET0yB,UAAU7yB,EAAAA,EAAAA,KAAI,CACZG,MAAO,SAGb,CCjNO,MAAM4yB,EAAkB,IAC7B,kBAACC,MAAAA,CAAIC,MAAM,6BAA6B9yB,MAAM,KAAKyJ,OAAO,KAAKspB,QAAQ,YAAY5hB,KAAK,QACtF,kBAAC6hB,OAAAA,CACCnP,EAAE,u9CACF1S,KAAK,UACLiR,YAAY,UAKL6Q,EAAiB,IAC5B,kBAACJ,MAAAA,CAAIC,MAAM,6BAA6B9yB,MAAM,KAAKyJ,OAAO,KAAKspB,QAAQ,YAAY5hB,KAAK,QACtF,kBAAC6hB,OAAAA,CACCnP,EAAE,u9CACF1S,KAAK,UACLiR,YAAY,U,o4BCTlB,MAAM8Q,EAAiB3C,IAGrBA,EAAO4C,OAAOC,EAAAA,IACd7C,EAAO4C,OAAO,OAAOE,EAAAA,MACrB9C,EAAO4C,OAAO,OAAOG,EAAAA,MACd/C,GAGIgD,EAAsB,KACjC,MAAMC,GAAUC,EAAAA,EAAAA,wBAEhB,MAAO,CACLC,aAAc,IAAMA,GAAaF,GACjCG,eAAiBC,GAAuBD,GAAeH,EAASI,GAChEC,eAAiBD,GAAuBC,GAAeL,EAASI,GAChEE,eAAgB,IAAMA,GAAeN,KAsB5BO,EAAqBH,IAChC,IAAKA,IAAaA,EAASrD,OACzB,OAAOC,EAAAA,GAGT,MAAMD,EAAS,IAAIlF,gBAAgBuI,EAASrD,QACtCyD,EAAcn2B,OAAOo2B,YAAY1D,EAAO1S,WAExCvN,EAAUigB,EAAO2D,OAAO,OAAO3Y,EAAAA,MAE/B4E,EAAMva,EAAAA,QAAQC,UAAU2qB,EAAAA,GAAoB,E,kUAAA,IAC7CwD,GAAAA,CACH,CAAC,OAAOzY,EAAAA,MAAgBjL,KAG1B,OAAO6P,GAGHgU,GAAe,CAAOX,EAAwBY,IAAAA,EAAAA,YAClD,UACQZ,EAAQa,QAAQC,EAAAA,GAAkBvvB,KAAKC,UAAUovB,GACzD,CAAE,MAAOG,GACPvD,QAAQC,MAAM,uCAAwCsD,EACxD,CACF,EANoDH,GAQvCV,GAAsBF,GAAAA,EAAAA,YACjC,IACE,MAAMl2B,QAAck2B,EAAQgB,QAAQF,EAAAA,IACpC,OAAIh3B,EACKyH,KAAK0I,MAAMnQ,GAEb,EACT,CAAE,MAAOi3B,GAEP,OADAvD,QAAQC,MAAM,wCAAyCsD,GAChD,EACT,CACF,EAXmCf,GAatBM,GAAwBN,GAAAA,EAAAA,YACnC,MAAMI,EA3CC,CAAErD,OADM2C,EAAc,IAAI7H,gBAAgBC,OAAOR,SAASS,SACzCtsB,YA8CxB,aAFqB40B,GAAeL,EAASI,WAGrCD,GAAeH,EAASI,IACvB,UAEDa,GAAYjB,EAASI,IACpB,EAEX,EAXqCJ,GAa/BiB,GAAc,CAAOjB,EAAwBI,IAAAA,EAAAA,YACjD,MAAMQ,QAAkBV,GAAaF,GACrCY,EAAU71B,KAAKq1B,SACTO,GAAaX,EAASY,EAC9B,EAJmDR,GAMtCD,GAAiB,CAAOH,EAAwBI,IAAAA,EAAAA,YAC3D,MACMc,SADwBhB,GAAaF,IACDpgB,OAAQuhB,IAAoBC,GAAkBhB,EAAUe,UAC5FR,GAAaX,EAASkB,EAC9B,EAJ6Dd,GAMhDC,GAAiB,CAAOL,EAAwBI,IAAAA,EAAAA,YAE3D,aADwBF,GAAaF,IACpBjf,KAAM5V,GAAMi2B,GAAkBhB,EAAUj1B,GAC3D,EAH6Di1B,GAKhDgB,GAAoB,CAAChB,EAAoBe,KACpD,MAAME,EAAiB3B,EAAc,IAAI7H,gBAAgBuI,EAASrD,SAC5DuE,EAAuB5B,EAAc,IAAI7H,gBAAgBsJ,EAAepE,SAExEwE,EAAY,OAAOxZ,EAAAA,KACnByZ,EAAe/rB,MAAM+E,KAAK6mB,EAAenO,QAAQtT,OAAO6hB,GAAKA,IAAMF,GACnEG,EAAajsB,MAAM+E,KAAK8mB,EAAqBpO,QAAQtT,OAAO6hB,GAAKA,IAAMF,GAG7E,GAAIC,EAAatyB,SAAWwyB,EAAWxyB,OACrC,OAAO,EAIT,MAAMyyB,EAAeH,EAAaI,MAAM/rB,GACtCyrB,EAAqBzV,IAAIhW,IAAQwrB,EAAejX,IAAIvU,KAASyrB,EAAqBlX,IAAIvU,IAExF,IAAK8rB,EACH,OAAO,EAIT,MAAME,EAAkBR,EAAeX,OAAOa,GACxCO,EAAgBR,EAAqBZ,OAAOa,GAClD,OAAIM,EAAgB3yB,SAAW4yB,EAAc5yB,QAMtC2yB,EAAgBD,MAAMhiB,GAAUkiB,EAAcj3B,SAAS+U,K,eCxIzD,MAAMmiB,GAAe,EAAG3B,eAC7B,IAAI,WAAE3L,EAAU,cAAEpD,EAAa,OAAEjV,EAAM,QAAEU,GDkBV,CAACsjB,IAChC,IAAKA,IAAaA,EAASrD,OACzB,MAAO,CAAEtI,WAAY,GAAIpD,cAAe,GAAIvU,QAAS,GAAIV,OAAQ,IAGnE,MAAM2gB,EAAS,IAAIlF,gBAAgBuI,EAASrD,Q,IACzBA,EACGA,EAEPA,EACf,MAAO,CAAEtI,WAJgCuN,QAAtBjF,EAAAA,EAAO3S,IAAI4X,EAAAA,WAAXjF,IAAAA,EAAAA,EAA2B,GAIzB1L,cAH0B4Q,QAAzBlF,EAAAA,EAAO3S,IAAI6X,EAAAA,WAAXlF,IAAAA,EAAAA,EAA8B,GAGhBjgB,QAFpBigB,EAAO2D,OAAO,OAAO3Y,EAAAA,MAAejc,KAAKo2B,EAAAA,IAEZ9lB,OADA,QAA9B2gB,EAAAA,EAAO3S,IAAI,OAAOpC,EAAAA,aAAlB+U,IAAAA,EAAAA,EAAmC,KC3BGoF,CAAkB/B,GACvE,MAAMp2B,GAASC,EAAAA,EAAAA,YAAWC,IA2B1B,OAJA4S,EAPuC,EAACA,EAAiBuU,KACvD,MAAM+Q,EAfuB,CAAC/Q,IAC9B,MAAMgR,GAAaC,EAAAA,GAAAA,IAAgBjR,GACnC,IAAKgR,IAAeA,EAAWziB,OAC7B,MAAO,GAET,MAAMA,EAASyiB,EAAWziB,OAE1B,OAAIA,EAAO/J,KAAO+J,EAAOI,eAA6B6F,IAAjBjG,EAAO9V,MACnC,GAAG8V,EAAO/J,OAAO+J,EAAOI,YAAYJ,EAAO9V,QAE7C,IAKqBy4B,CAAuBlR,GACnD,IAAImR,EAAe1lB,EAAQpR,MAAMw2B,EAAAA,IAEjC,OADAM,EAAeA,EAAa5iB,OAAOzS,GAAKA,IAAMi1B,GACvCI,EAAa12B,KAAKo2B,EAAAA,KAGjBO,CAA+B3lB,EAASuU,GAClDvU,EAAUA,EAAQoD,QAAQ,SAAU,OACpCpD,EAAUA,EAAQoD,QAAQE,EAAAA,GAAe,IAAIF,QAAQC,EAAAA,GAAW,IAAID,QAAQwiB,EAAAA,GAAY,IAGtF,kBAACt3B,MAAAA,CAAI2C,MAAO+O,GACV,kBAAC1R,MAAAA,KACC,kBAACD,IAAAA,KAAGw3B,GAAoBvmB,IAAY,OAAI,kBAACjR,IAAAA,KAAGkmB,EAAcnR,QAAQ,IAAK,MAAS,KAAGuU,EAAW,KAEhG,kBAACrpB,MAAAA,CAAIC,UAAWrB,EAAO8S,SACpBA,KAMT,SAAS5S,KACP,MAAO,CACL4S,SAASzQ,EAAAA,EAAAA,KAAI,CACX4I,aAAc,WACdD,SAAU,SACV4tB,gBAAiB,EACjBt2B,QAAS,cACTu2B,gBAAiB,aAGvB,C,8TCjDO,MAAMC,GAAY,KACvB,MAAM94B,GAASC,EAAAA,EAAAA,YAAWC,KACpB,aAAEg2B,EAAY,eAAEC,GAAmBJ,KAClCa,EAAWD,IAAgB33B,EAAAA,EAAAA,UAAqB,KAChD+T,EAAWgmB,IAAgB/5B,EAAAA,EAAAA,WAAkB,IAC7Cg6B,EAAYC,IAAiBj6B,EAAAA,EAAAA,WAAkB,IAEtD8X,EAAAA,EAAAA,WAAU,KACe,eACrBiiB,GAAa,GACb,IACE,MAAMG,QAAwBhD,IAC9BS,EAAauC,EACf,CAAE,MAAOzF,GACPD,QAAQC,MAAM,2BAA4BA,GAC1CkD,EAAa,GACf,CAAE,QACAoC,GAAa,EACf,CACF,EAXuB,IActB,IAiBH,OAAIhmB,EAEA,kBAAC3R,MAAAA,KACC,kBAACA,MAAAA,CAAIC,UAAWrB,EAAOgK,QACrB,kBAACmvB,KAAAA,KAAG,sBAEN,kBAAC/3B,MAAAA,CAAIC,UAAWrB,EAAOmQ,SACrB,kBAACipB,EAAAA,mBAAkBA,CAACxuB,KAAK,2BAO/B,kBAACxJ,MAAAA,KACC,kBAACA,MAAAA,CAAIC,UAAWrB,EAAOgK,QACrB,kBAACmvB,KAAAA,KAAG,sBAEgB,IAArBvC,EAAU1xB,OACT,kBAACm0B,IAAAA,CAAEh4B,UAAWrB,EAAOs5B,aAAa,qDAElC,kBAACl4B,MAAAA,CAAIC,UAAWrB,EAAO42B,WACpBA,EAAU/0B,IAAI,CAACu0B,EAAoBzqB,IAClC,kBAACvK,MAAAA,CACCC,UAAWrB,EAAOo2B,SAClBvqB,IAAKF,EACLvM,QAAS,IFwEK,CAACg3B,KAC3BvtB,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBwpB,KAAMtpB,EAAAA,GAAoBspB,KAAKiH,wBACtE,MAAM5W,EAAM4T,EAAkBH,GAC9B5D,EAAAA,gBAAgBzxB,KAAK4hB,IE3EM6W,CAAapD,IAE5B,kBAACh1B,MAAAA,CAAIC,UAAWrB,EAAOy5B,cACrB,kBAAC1B,GAAYA,CAAC3B,SAAUA,KAE1B,kBAACh1B,MAAAA,CAAIC,UAAWrB,EAAO05B,QACrB,kBAAC7S,EAAAA,OAAMA,CACL3nB,QAAQ,YACRyU,KAAK,OACLxU,KAAK,YACLw6B,SAAUX,EACV55B,QAAU23B,GApDI,EAAOX,EAAoBjd,IAAAA,GAAAA,YACvDA,EAAMygB,kBACNX,GAAc,GAEd,UACQ9C,EAAeC,GACrB,MAAMyD,QAAyB3D,IAC/BS,EAAakD,EACf,CAAE,MAAOpG,GACPD,QAAQC,MAAM,2BAA4BA,EAC5C,CAAE,QACAwF,GAAc,EAChB,CACF,EAbyD9f,GAoDzB2gB,CAAsB1D,EAAUW,WAWlE,SAAS72B,GAAUiK,GACjB,MAAO,CACLH,QAAQ3H,EAAAA,EAAAA,KAAI,CACV8yB,UAAW,SACX,GAAM,CACJhhB,OAAQ,KAGZyiB,WAAWv0B,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACTy3B,SAAU,OACV3vB,IAAKD,EAAMkB,QAAQ,GACnB8I,OAAQ,GAAGhK,EAAMkB,QAAQ,QAAQlB,EAAMkB,QAAQ,OAC/ChB,eAAgB,WAElB+rB,UAAU/zB,EAAAA,EAAAA,KAAI,CACZC,QAAS,OACT2R,cAAe,SACf5J,eAAgB,gBAChBS,OAAQ,UACRtI,MAAO,QACP6J,OAAQ,aAAalC,EAAMM,OAAO4B,OAAO8nB,SACzChX,aAAchT,EAAM6vB,MAAMC,OAAOC,QAEjC,UAAW,CACT/tB,gBAAiBhC,EAAMwoB,OAASxoB,EAAMM,OAAOD,WAAWE,UAAYP,EAAMM,OAAOD,WAAW4B,WAGhGqtB,cAAcp3B,EAAAA,EAAAA,KAAI,CAChB2D,QAAS,GAAGmE,EAAMkB,QAAQ,QAAQlB,EAAMkB,QAAQ,UAAUlB,EAAMkB,QAAQ,OACxEL,SAAU,WAEZ8H,SAASzQ,EAAAA,EAAAA,KAAI,CACX4I,aAAc,WACdD,SAAU,SACV4tB,gBAAiB,EACjBt2B,QAAS,cACTu2B,gBAAiB,aAEnBa,QAAQr3B,EAAAA,EAAAA,KAAI,CACVC,QAAS,OACT+H,eAAgB,aAElBivB,aAAaj3B,EAAAA,EAAAA,KAAI,CACf8R,OAAQ,GAAGhK,EAAMkB,QAAQ,QAAQlB,EAAMkB,QAAQ,OAC/C8pB,UAAW,WAEbhlB,SAAS9N,EAAAA,EAAAA,KAAI,CACXC,QAAS,OACT+H,eAAgB,SAChB8J,OAAQ,GAAGhK,EAAMkB,QAAQ,SAG/B,CCnIO,MAAM8uB,WAAoBz3B,EAAAA,IAgFjC,SAASxC,GAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,GACnB4I,cAAe,SACfE,OAAQ,OAAOhK,EAAMkB,QAAQ,OAC7BhB,eAAgB,WAElBL,QAAQ3H,EAAAA,EAAAA,KAAI,CACVC,QAAS,OACTgI,WAAY,SACZ6B,gBAAiBhC,EAAMwoB,OAASxoB,EAAMM,OAAOD,WAAWE,UAAYP,EAAMM,OAAOD,WAAW4B,QAC5F+Q,aAAchT,EAAMkB,QAAQ,IAC5B0uB,SAAU,OACV1vB,eAAgB,SAChBrE,QAASmE,EAAMkB,QAAQ,GACvBjB,IAAKD,EAAMkB,QAAQ,KAErB+uB,sBAAsB/3B,EAAAA,EAAAA,KAAI,CACxBC,QAAS,OACTgI,WAAY,WAEdvG,OAAO1B,EAAAA,EAAAA,KAAI,CACT8R,OAAQ,SAAShK,EAAMkB,QAAQ,OAGjCgvB,eAAeh4B,EAAAA,EAAAA,KAAI,CACjBiI,WAAY,SACZD,eAAgB,aAChB/H,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,KAErBivB,mBAAmBj4B,EAAAA,EAAAA,KAAI,CACrB6I,eAAgB,YAChB,UAAW,CACTA,eAAgB,eAIpBqvB,WAAWl4B,EAAAA,EAAAA,KAAI,CACb8yB,UAAW,SACX,GAAM,CACJhhB,OAAQ,QAAQhK,EAAMkB,QAAQ,UAIlC3K,OAAO2B,EAAAA,EAAAA,KAAI,CACTkI,SAAU,SAEZiwB,sBAAsBn4B,EAAAA,EAAAA,KAAI,CACxBiI,WAAY,SACZF,IAAKD,EAAMkB,QAAQ,GACnB/I,QAAS,OACT+H,eAAgB,gBAChB7H,MAAO,SAETi4B,WAAWp4B,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,KAErBmT,UAAUnc,EAAAA,EAAAA,KAAI,CACZC,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,KAGzB,C,0HChKO,SAAeqvB,GAAmBjyB,G,qBACvC,MAAMkyB,EAAQvzB,EAAAA,GAAW+U,YAAY1T,EAAUkmB,EAAAA,IACzCiM,QAAoBlM,EAAAA,EAAAA,oBAAmBtO,IAAIua,GACjD,KAAMC,aAAuBC,EAAAA,uBAE3B,MADArH,QAAQC,MAAM,IAAIrmB,MAAM,4CAClB,IAAIA,MAAM,2CAGlB,MAAMnG,EAAa2zB,EACnB,GAAI3zB,GAAcA,EAAW4nB,WAAY,CACvC,MAAME,QAAgB9nB,EAAW4nB,aAEjC,OAAIpjB,MAAMqvB,QAAQ/L,GAET,CAAE7Y,SAAS,EAAM5V,OAWvB,SAAoB4oB,GACzB,MAAM6R,EAAqB7R,EAAKtT,OAAQ6hB,I,IAAMA,E,OAAM,QAANA,EAAAA,EAAE7sB,YAAF6sB,IAAAA,OAAAA,EAAAA,EAAQ52B,SAASuV,EAAAA,MACzD4kB,EAAiB9R,EAAKtT,OAAQ6hB,I,IAAMA,E,OAAM,QAANA,EAAAA,EAAE7sB,YAAF6sB,IAAAA,OAAAA,EAAAA,EAAQ52B,SAASsV,EAAAA,MACrD8kB,EAAkB/R,EAAKtT,OAAQ6hB,I,IAC3BA,EAAoCA,EACtCA,EAAiCA,EADvC,SAAc,QAANA,EAAAA,EAAE7sB,YAAF6sB,IAAAA,OAAAA,EAAAA,EAAQ52B,SAASuV,EAAAA,OAAyB,QAANqhB,EAAAA,EAAE7sB,YAAF6sB,IAAAA,OAAAA,EAAAA,EAAQ52B,SAASsV,EAAAA,OACjD,QAANshB,EAAAA,EAAE7sB,YAAF6sB,IAAAA,OAAAA,EAAAA,EAAQ52B,SAAS63B,EAAAA,OAAsB,QAANjB,EAAAA,EAAE7sB,YAAF6sB,IAAAA,OAAAA,EAAAA,EAAQ52B,SAASq6B,EAAAA,OACyB,IAA5EtkB,EAAAA,GAAkB2I,OAAO4b,EAAAA,IAA6Bv6B,QAAQ62B,EAAE7sB,SAEvE,MAAO,IAAImwB,KAAuBC,KAAmBC,EACvD,CArB2BG,CAAWrM,KAGhCyE,QAAQC,MAAM,IAAIrmB,MAAM,yCACjB,CAAE9M,OAAQ,IAErB,CAEE,OADAkzB,QAAQC,MAAM,IAAIrmB,MAAM,uDACjB,CAAE9M,OAAQ,GAErB,E,qSDRE,CADW65B,GACGxwB,YAAY,EAAGC,YAC3B,MAAM0oB,EAAO+I,GAAazxB,GACpB0xB,GAAWC,EAAAA,EAAAA,gBACX,SAAE/c,GAAa8T,EAAKtzB,WACpBgB,GAASC,EAAAA,EAAAA,YAAWC,IACpBiK,GAAQN,EAAAA,EAAAA,aAER2xB,EAAapN,GAAsBkE,GACnCmJ,EAAiBC,GAAsBpJ,GAE7C,OACE,kBAAClxB,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAOgK,QACrB,kBAAC5I,MAAAA,CAAIC,UAAWrB,EAAOo6B,sBACpBjwB,EAAMwoB,OAAS,kBAAC8C,EAAcA,MAAM,kBAACL,EAAeA,MACrD,kBAACuG,KAAAA,CAAGt6B,UAAWrB,EAAO+D,OAAO,mCAE/B,kBAAC3C,MAAAA,KACC,kBAACi4B,IAAAA,KAAE,oEACH,kBAACj4B,MAAAA,CAAIC,UAAWrB,EAAOq6B,eACrB,kBAACxT,EAAAA,OAAMA,CAAC3nB,QAAQ,UAAUE,QAAS,MAC/ByJ,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBwpB,KAAMtpB,EAAAA,GAAoBspB,KAAKsJ,wBACtEN,EAAStI,EAAAA,MACR,cAEH,kBAAC7wB,EAAAA,KAAIA,CAACC,KAAK,cAAcmC,KAAK,QAEhC,kBAACmP,EAAAA,WAAUA,CACTvU,KAAK,oBACLwU,KAAK,OACLpP,KAAM,KACND,OAAQ,SACRF,KACE,sFAEF/C,UAAWrB,EAAOs6B,kBAClBl7B,QAAS,KAAMyJ,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBwpB,KAAMtpB,EAAAA,GAAoBspB,KAAKuJ,6BACtF,yBAOP,kBAAC/C,GAASA,MAEV,kBAAC13B,MAAAA,CAAIC,UAAWrB,EAAOu6B,WACrB,kBAACpB,KAAAA,KAAG,0CAGN,kBAAC5lB,EAAAA,MAAKA,CAACnJ,IAAK,GACV,kBAAChJ,MAAAA,CAAIC,UAAWrB,EAAOw6B,sBACrB,kBAACp5B,MAAAA,CAAIC,UAAWrB,EAAOy6B,WACpBe,GACC,kBAACjoB,EAAAA,MAAKA,CAACnJ,IAAK,EAAGE,WAAY,UACzB,kBAAClJ,MAAAA,CAAIC,UAAWrB,EAAOU,OAAO,eAC9B,kBAAC86B,EAAW7xB,UAAS,CAACC,MAAO4xB,KAGhCC,GACC,kBAACloB,EAAAA,MAAKA,CAACnJ,IAAK,EAAGE,WAAY,UACzB,kBAAClJ,MAAAA,CAAIC,UAAWrB,EAAOU,OAAO,UAC9B,kBAAC+6B,EAAe9xB,UAAS,CAACC,MAAO6xB,MAKvC,kBAACr6B,MAAAA,CAAIC,UAAWrB,EAAOwe,UACpBA,aAAAA,EAAAA,EAAU3c,IAAKi6B,GACd,kBAACA,EAAQnyB,UAAS,CAACkC,IAAKiwB,EAAQl3B,MAAMiH,IAAKjC,MAAOkyB,WCxClE,MAAMC,GAAgBnmB,IACpB,IAAKA,EACH,MAAO,GAGT,IAAIyT,EAAMzT,EAAO9V,MACjB,OAAIupB,SAA6C,KAARA,EAChC,IAGJ2S,GAASC,KAAK5S,IAAS,CAAC,QAAQxoB,SAAS+U,EAAO/J,MAChC,iBAARwd,GAAqBA,EAAIroB,WAAW,MAASqoB,EAAI6S,SAAS,OACnE7S,EAAM,IAAIA,MAIP,GAAGzT,EAAO/J,MAAM+J,EAAOI,WAAWqT,M,yHCtBpC,MAAM8S,WAAaz5B,EAAAA,GAYhB+J,WAAAA,GACN,MAAM0a,EAAiB/f,EAAAA,GAAWC,aAAarD,MACzCy3B,EAAiBC,GAAsB13B,MAC7Cy3B,EAAen2B,SAAS,CACtBo1B,mBAAoBA,KAGtBtM,GAAsBpqB,MAAM0F,iBAAkBiD,IACxCA,EAAS7M,OACXs8B,aAAavF,QAAQwF,EAAAA,GAAmB1vB,EAAS7M,MAAM2B,cAI3Di6B,GAAsB13B,MAAM0F,iBAAiB,CAACiD,EAAUC,KACtD,GAAID,EAASmG,UAAYlG,EAAUkG,QAAS,CAC1C9O,KAAKs4B,YAAYnV,EAAgBxa,EAASmG,SAG1CspB,aAAavF,QAAQ0F,EAAAA,GAAyBh1B,KAAKC,UAAUmF,EAASmG,UAEtE,MAAM4S,EAAa/Y,EAASmG,QAAQ8C,OAAQzS,IAAOyJ,EAAUkG,QAAQ5P,KAAMs5B,GAAOA,EAAG3wB,MAAQ1I,EAAE0I,MAC3F6Z,EAAWxgB,OAAS,IACtB2D,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBwpB,KAAMtpB,EAAAA,GAAoBspB,KAAKmK,eAAgB,CACpF5wB,IAAK6Z,EAAW,GAAG7Z,KAGzB,IAGFsb,EAAezd,iBAAiB,CAACiD,EAAUC,KACrCD,EAAS7M,MAAM0Q,OAAS5D,EAAU9M,MAAM0Q,MAAQ7D,EAAS7M,MAAM2Q,KAAO7D,EAAU9M,MAAM2Q,IACxFzM,KAAKs4B,YAAYnV,EAAgBsU,EAAe72B,MAAMkO,WAG1D9O,KAAKs4B,YAAYnV,EAAgBsU,EAAe72B,MAAMkO,QACxD,CAEAwpB,WAAAA,CAAYnV,EAAoCrU,GAC9C,MAAMtC,EAAO2W,EAAeviB,MAAM9E,MAAM0Q,KAAK4W,OACvC3W,EAAK0W,EAAeviB,MAAM9E,MAAM2Q,GAAG2W,OAEnCoB,EAAY,IADN7W,EAAAA,EAAAA,UAASlB,EAAKD,EAAM,KACPiY,eACnBiU,EDxDH,SAAmC5pB,GACxC,MAAM6pB,EAAO7pB,EACV8C,OAAQzS,GAAMA,EAAE0I,KAAO1I,EAAE6S,UAAY7S,EAAErD,OACvC+B,IAAK+T,GAAWmmB,GAAanmB,IAC7B9T,KAAKo2B,EAAAA,IACR,OAAOyE,EAAKz3B,OAAS,MAAMy3B,IAAS,EACtC,CCkD4BC,CAA0B9pB,GAElD9O,KAAKsB,SAAS,CACZI,KAAM,IAAIyV,EAAAA,GAAmB,CAC3B3V,SAAU,CACR,IAAI2V,EAAAA,GAAmB,CACrBG,SAAU,cACVuhB,UAAW,EACXC,OAAQ,EACRt3B,SAAU,CACR,IAAIsX,EAAAA,GAAiB,CACnBpX,KAAM,IAAI6uB,EAAe,CACvBxsB,MAAO,CACLA,MAAO,0CAA0C20B,oDACjDhU,KAAMF,GAERzkB,MAAO,mBACPX,KAAM,uBAGV,IAAI0Z,EAAAA,GAAiB,CACnBpX,KAAM,IAAI6uB,EAAe,CACvBxsB,MAAO,CACLA,MAAO,wBAAwB20B,oEAC/BhU,KAAMF,GAERzkB,MAAO,gBACPX,KAAM,uBAGV,IAAI0Z,EAAAA,GAAiB,CACnBpX,KAAM,IAAI6uB,EAAe,CACvBxsB,MAAO,CACLA,MAAO,sBAAsB20B,sCAE/B34B,MAAO,cACPX,KAAM,iBACNwS,OAAQ8mB,aAQxB,CAlGA,YAAmB93B,G,IAEHA,EACAA,EACFA,EA6GQm4B,EAAuCC,EAhH3Dl2B,M,mUAAM,EACJ4oB,WAA4B,QAAhB9qB,EAAAA,EAAM8qB,kBAAN9qB,IAAAA,EAAAA,EAAoB,IAAIgsB,EAAAA,GAAe,CAAC,GACpDqM,WAA4B,QAAhBr4B,EAAAA,EAAMq4B,kBAANr4B,IAAAA,EAAAA,GA8GMm4B,EA9G6Bn4B,EAAMm4B,eA8GIC,EA9GYp4B,EAAMo4B,UA+GxE,IAAIE,EAAAA,GAAiB,CAC1BzC,UAAW,CACT,IAAI0C,EAAAA,GAAmB,CACrB/6B,KAAMg7B,EAAAA,GACN18B,MAAO,cACPZ,MAAOk9B,EACPK,SAAU,UAEZ,IAAIC,EAAAA,GAAqB,CACvBl7B,KAAMm7B,EAAAA,GACNt2B,WAAYiL,EAAAA,GACZ+F,OAAQ,WACRnF,QAASiqB,EACTS,kBAAkB,QA3HpBhf,SAAwB,QAAd5Z,EAAAA,EAAM4Z,gBAAN5Z,IAAAA,EAAAA,EAAkB,CAAC,IAAI64B,EAAAA,GAAgB,CAAC,GAAI,IAAIC,EAAAA,GAAmB,CAAC,KAC3E94B,IAGLZ,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EA4HF,SAAS9D,GAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACb8R,OAAQ,GAAGhK,EAAMkB,QAAQ,UACzB7I,MAAO,MAEP,4BAA6B,CAC3BA,MAAO,SAIf,CA5CE,GArGW25B,GAqGJxyB,YAAY,EAAGC,YACpB,MAAM,KAAElE,GAASkE,EAAM5K,WACjBgB,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAAC64B,GAAYxwB,UAAS,CAACC,MAAOA,IAC7BlE,GAAQ,kBAACA,EAAKiE,UAAS,CAACC,MAAOlE,O,eCrHjC,SAASsB,GAAyB4C,GACvC,OAAOxC,EAAAA,GAAWu2B,YAAY/zB,EAAOg0B,EAAAA,GACvC,CAEO,SAASvC,GAAazxB,GAC3B,OAAOxC,EAAAA,GAAWu2B,YAAY/zB,EAAOuyB,GACvC,CAEO,SAASpyB,GAAuBH,GACrC,OAAOxC,EAAAA,GAAWu2B,YAAY/zB,EAAO+jB,EAAAA,GACvC,CAEO,SAAS1C,GAAmBrhB,GACjC,MAAMi0B,EAAuB9zB,GAAuBH,GACpD,OAAOi0B,aAAAA,EAAAA,EAAsBj5B,MAAMomB,eACrC,CAEO,SAAS8S,GAAqBd,EAAoBD,GACvD,OAAO,IAAIa,EAAAA,GAAiB,CAC1BZ,YACAD,eAAgBA,QAAAA,EAAkB,GAClCrN,WAAY,IAAIkB,EAAAA,GAAe,CAAEpgB,KAAM,UAAWC,GAAI,SAE1D,CAUO,SAASskB,GAAgBjyB,G,IACvBA,EAAAA,EAAAA,EAAP,OAAiC,QAA1BA,EAAAA,SAAU,QAAVA,EAAAA,EAAMA,YAANA,IAAAA,GAAiB,QAAjBA,EAAAA,EAAY2wB,aAAZ3wB,IAAAA,OAAAA,EAAAA,EAAmB8C,eAAnB9C,IAAAA,EAAAA,EAA8B,iCACvC,CAEO,SAASgyB,GAAiBiJ,GAC/B,MAAO,sEAAsEA,IAC/E,CAEO,SAASv+B,GAAqBb,GACnC,MAAMo0B,EAASiL,EAAAA,GAAW/O,YAAYtwB,GACtC,OAG8B2B,EAHPyyB,EAIhB3qB,EAAAA,QAAQC,UAAU2qB,EAAAA,GAAoB1yB,GADxC,IAAyBA,CAFhC,CAMO,SAAS4G,GAAcvI,GAC5B,OAAOyI,EAAAA,GAAW+U,YAAYxd,EAAagwB,EAAAA,GAC7C,CAMO,SAASlP,GAAuB3V,GACrC,OAAOA,EAAWjI,IAAKoU,IAAe,CAAEvV,MAAOuV,EAAWnW,MAAOmW,IACnE,CAiBO,SAASyF,GAAcjY,EAAkBkuB,G,IAC/BluB,EAAf,MAAMuY,EAA6C,QAApCvY,EAAAA,EAAMR,OAAOC,KAAMC,GAAiB,WAAXA,EAAEC,aAA3BK,IAAAA,OAAAA,EAAAA,EAA+CuY,OAE9D,IAAKA,EACH,MAAO,YAGT,MAAMkN,EAAO7oB,OAAO6oB,KAAKlN,GAAQpG,OAAQ6hB,GAAY,MAANA,GAC/C,OAAoB,IAAhBvO,EAAKhkB,OACA,YAGF8W,EAAO2V,GAAazI,EAAK,IAAIhT,QAAQ,KAAM,GACpD,CAEO,SAASoH,GAAmBpD,GACjC,MAAMzR,EAAWrB,EAAAA,GAAW62B,eAAeC,EAAAA,EAAahkB,GACxD,KAAMzR,aAAoB01B,EAAAA,IACxB,MAAM,IAAI/wB,MAAM,+BAElB,OAAO3E,CACT,CAEO,SAASC,GAA2BwR,GACzC,MAAMzR,EAAWrB,EAAAA,GAAW62B,eAAeG,EAAAA,GAAuBlkB,GAClE,KAAMzR,aAAoB01B,EAAAA,IACxB,MAAM,IAAI/wB,MAAM,wCAElB,OAAO3E,CACT,CAEO,SAAS41B,GAA4BnkB,GAC1C,MAAMzR,EAAWrB,EAAAA,GAAW62B,eAAepI,EAAAA,GAAuB3b,GAClE,KAAMzR,aAAoB01B,EAAAA,IACxB,MAAM,IAAI/wB,MAAM,wCAElB,OAAO3E,CACT,CAEO,SAAS61B,GAAmCpkB,GACjD,MAAMzR,EAAWrB,EAAAA,GAAW62B,eAAenI,EAAAA,GAA+B5b,GAC1E,KAAMzR,aAAoB01B,EAAAA,IACxB,MAAM,IAAI/wB,MAAM,gDAElB,OAAO3E,CACT,CAEO,SAASoE,GAAkBqN,GAChC,MAAMzR,EAAWrB,EAAAA,GAAW62B,eAAejgB,EAAAA,GAAY9D,GACvD,KAAMzR,aAAoB01B,EAAAA,IACxB,MAAM,IAAI/wB,MAAM,6BAElB,OAAO3E,CACT,CAEO,SAAS0M,GAAmB+E,GACjC,MAAMzR,EAAWrB,EAAAA,GAAW62B,eAAelgB,EAAAA,GAAa7D,GACxD,KAAMzR,aAAoB60B,EAAAA,IACxB,MAAM,IAAIlwB,MAAM,8BAElB,OAAO3E,CACT,CAEO,SAASue,GAAyB9M,GACvC,MAAMzR,EAAWrB,EAAAA,GAAW62B,eAAehW,EAAAA,GAAoB/N,GAC/D,KAAMzR,aAAoB81B,GAAAA,GACxB,MAAM,IAAInxB,MAAM,qCAElB,OAAO3E,CACT,CAEO,SAASizB,GAAsBxhB,GACpC,MAAMzR,EAAWrB,EAAAA,GAAW62B,eAAeV,EAAAA,GAAiBrjB,GAC5D,KAAMzR,aAAoB60B,EAAAA,IACxB,MAAM,IAAIlwB,MAAM,kCAElB,OAAO3E,CACT,CAEO,SAAS2lB,GAAsBlU,GACpC,MAAMzR,EAAWrB,EAAAA,GAAW62B,eAAeb,EAAAA,GAAgBljB,GAC3D,KAAMzR,aAAoB00B,EAAAA,IACxB,MAAM,IAAI/vB,MAAM,iCAElB,OAAO3E,CACT,CAEO,SAAS+1B,GAAetkB,G,IAETpX,EADpB,MAAMA,EAAOsE,EAAAA,GAAWmC,QAAQ2Q,GAAOtV,MAAM9B,KACvC27B,EAAc37B,SAAa,QAAbA,EAAAA,EAAM47B,eAAN57B,IAAAA,OAAAA,EAAAA,EAAe67B,QAAQ,GAC3C,OAAOF,EAAc,EAAgC/V,UAAO7M,CAC9D,CAEO,SAAS+iB,GAAoBpT,GAClC,MAAe,eAARA,GAAgC,cAARA,CACjC,CAEO,SAASqT,GAAe3kB,GAC7B,OAAOrN,GAAkBqN,GAAOlb,WAAWc,KAC7C,CAEO,SAAS0sB,GAAoB1pB,G,IAC3BA,EAAAA,EAAAA,EAAP,OAA6E+Y,QAAtE/Y,EAAAA,SAAU,QAAVA,EAAAA,EAAMA,YAANA,IAAAA,GAA4B,QAA5BA,EAAAA,EAAYmC,OAAO,GAAGhC,cAAtBH,IAAAA,OAAAA,EAAAA,EAA8BiU,KAAMwC,GAAMA,EAAEjZ,OAAOs3B,MAAOb,QAAYlb,IAANkb,WAAhEj0B,IAAAA,GAAAA,CACT,CAEO,MAAMk5B,GAAW,gBAEX1f,GAAoBxc,GAC1Bk8B,GAASC,KAAKn8B,IAA2B,iBAAVA,GAAuBA,EAAMkB,WAAW,MAASlB,EAAMo8B,SAAS,KAG7Fp8B,EAFE,IAAIA,KAKF64B,GAAuBmG,I,IAAgBA,E,OAAAA,SAAQ,QAARA,EAAAA,EAAM,UAANA,IAAAA,OAAAA,EAAAA,EAAUC,gBAAgBD,aAAAA,EAAAA,EAAKn+B,MAAM,KAAM,IAElFgQ,GAAgBuJ,GACpB,CAACtW,EAAiBE,KACvBoW,EAAMjW,aAAa,IAAIC,EAAAA,GAAiB,CAAEN,UAASE,YAAW,G,iBCtOlE,IAAIjC,EAAM,CACT,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,IACX,aAAc,IACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,IACX,aAAc,IACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,IACX,aAAc,IACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,OAAQ,GACR,UAAW,GACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,gBAAiB,KACjB,aAAc,KACd,gBAAiB,KACjB,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,IACX,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,EACX,aAAc,EACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,WAAY,KACZ,cAAe,KACf,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,IACR,UAAW,KACX,aAAc,KACd,UAAW,IACX,OAAQ,KACR,UAAW,KACX,WAAY,KACZ,cAAe,KACf,UAAW,KACX,aAAc,KACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,YAAa,IACb,eAAgB,IAChB,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,QAAS,GACT,WAAY,GACZ,OAAQ,KACR,UAAW,KACX,QAAS,KACT,WAAY,KACZ,QAAS,KACT,aAAc,KACd,gBAAiB,KACjB,WAAY,KACZ,UAAW,IACX,aAAc,IACd,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,UAAW,KACX,OAAQ,KACR,YAAa,IACb,eAAgB,IAChB,UAAW,KACX,OAAQ,KACR,UAAW,KACX,aAAc,KACd,gBAAiB,KACjB,OAAQ,KACR,UAAW,KACX,UAAW,KACX,aAAc,KACd,UAAW,GACX,aAAc,GACd,UAAW,KACX,aAAc,KACd,UAAW,KACX,aAAc,MAIf,SAASm9B,EAAeC,GACvB,IAAI1N,EAAK2N,EAAsBD,GAC/B,OAAOE,EAAoB5N,EAC5B,CACA,SAAS2N,EAAsBD,GAC9B,IAAIE,EAAoBppB,EAAElU,EAAKo9B,GAAM,CACpC,IAAIlI,EAAI,IAAI3pB,MAAM,uBAAyB6xB,EAAM,KAEjD,MADAlI,EAAEqI,KAAO,mBACHrI,CACP,CACA,OAAOl1B,EAAIo9B,EACZ,CACAD,EAAe9V,KAAO,WACrB,OAAO7oB,OAAO6oB,KAAKrnB,EACpB,EACAm9B,EAAeK,QAAUH,EACzBI,EAAOC,QAAUP,EACjBA,EAAezN,GAAK,I,m+BC5Qb,MAAMiO,UAAiC98B,EAAAA,GAC5C,WAAAmE,CAAYjC,GACVkC,MAAM,OAAKlC,GAAAA,CAAOgD,QAAS,MAK7B,OAAQ6E,cAAc,KACpBzI,KAAKwF,MAAMC,IACTzF,KAAK0F,iBAAiB,KACpB1F,KAAKy7B,aACLz7B,KAAK07B,kBAKX,OAAiBD,aAAa,KAC5B,MAAM38B,EAAOsE,EAAAA,GAAWmC,QAAQvF,MAC1B27B,EAAcv4B,EAAAA,GAAWw4B,WAAW98B,EAAM+8B,GAEhD,GAAIA,EAAcF,GAAc,CAC9B,MAAM/3B,EAAU+3B,EAAY/6B,MAAMgD,QAAQ/F,IAAKi+B,GAAO,OACjDA,GAAAA,CACH/3B,MAAO/D,KAAKY,MAAMmD,SAGhBR,KAAKC,UAAUI,KAAaL,KAAKC,UAAUxD,KAAKY,MAAMgD,UACxD5D,KAAKsB,SAAS,CAAEsC,WAEpB,IAGF,OAAiB83B,aAAa,KAC5B,MAAM,QAAE93B,EAAO,MAAEm4B,EAAK,WAAErjB,EAAU,KAAEtZ,EAAO,gBAAmBY,KAAKY,MAC7DuC,EAAYC,EAAAA,GAAWC,aAAarD,MAE1C,IAAKmD,IAAcS,IAAYm4B,EAC7B,OAEF,MAAMC,EAAM,CACVphC,OAAQ,iBACRwE,OACAwE,UACAT,UAAW,KAAKA,EAAUvC,MAAM9E,OAChCmH,WAAY,CAAEg5B,IAAKF,GACnBpd,IAAKmL,OAAOR,SAASlpB,KACrBmtB,GAAI,GAAGhqB,KAAKC,UAAUI,KACtB7D,MAAO,GAAG2Y,IACVwjB,SAAUC,GAER54B,KAAKC,UAAUw4B,KAASz4B,KAAKC,UAAUxD,KAAKY,MAAMm5B,UACpD/5B,KAAKsB,SAAS,CAAEy4B,QAASiC,MA/C3Bh8B,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EAmDF,SAAS67B,EAAc9pB,GACrB,OAAOA,aAAa9D,EAAAA,EACtB,C,4SC7DO,MAAMmuB,UAA4B19B,EAAAA,GA+B/BspB,WAAAA,GACN,MAAM3mB,EAAQa,EAAAA,GAAcqJ,SAASnJ,gBAAe,GAIpD,OAHIpC,KAAKY,MAAMd,QACbuB,EAAMwL,UAAU,gBAAwB7M,KAAKY,MAAMd,QAE9CuB,CACT,CApCA,WAAAwB,CAAYjC,GACVkC,M,kUAAM,EACJqC,MAAO,IAAI8I,EAAAA,GAAiB,CAC1BhL,WAAYiL,EAAAA,GACZtK,QAAS,CAAC,CAAEC,MAAO,IAAKE,MAAOnD,EAAMhB,QAASkE,UAAW,eAExDlD,IAGLZ,KAAKkF,qBAAqB,KACxB,MAAMpG,EAAOsE,EAAAA,GAAWmC,QAAQvF,MAEhCA,KAAKwF,MAAMC,IACT3G,EAAK4G,iBAAkB5G,I,IACjBA,EAIOA,GAJE,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaM,KACpCnB,KAAKsB,SAAS,CACZD,MAAOrB,KAAKgoB,cAAcvlB,WAEV,QAAT3D,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaC,SAC3Cd,KAAKsB,SAAS,CACZD,MAAO,IAAIqB,EAAAA,EAAkB,CAC3BC,UAAWC,UAOzB,EAUA,EAvCWw5B,EAuCGz2B,YAAY,EAAGC,YAC3B,MAAM,MAAEvE,GAAUuE,EAAM5K,WAClBgB,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,GAAKmF,EAIL,OACE,kBAACjE,MAAAA,CAAIC,UAAWrB,EAAOqgC,gBACrB,kBAACh7B,EAAMsE,UAAS,CAACC,MAAOvE,OAMhC,MAAMuB,EAAoB,KACxB,MAAM5G,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,OACE,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAOgK,QACrB,kBAACuB,EAAAA,EAAQA,CAACC,MAAO,EAAGhJ,MAAO,KAC3B,kBAAC+I,EAAAA,EAAQA,CAACC,MAAO,EAAGhJ,MAAO,MAE7B,kBAAC+I,EAAAA,EAAQA,CAACC,MAAO,EAAGhJ,MAAO,QAC3B,kBAACpB,MAAAA,CAAIC,UAAWrB,EAAO6B,KACrB,kBAAC0J,EAAAA,EAAQA,CAACC,MAAO,IACjB,kBAACD,EAAAA,EAAQA,CAACC,MAAO,EAAGS,OAAQ,MAG9B,kBAAC7K,MAAAA,CAAIC,UAAWrB,EAAO+L,MACrB,kBAACA,OAAAA,CAAK1K,UAAWrB,EAAOsgC,UACtB,kBAAC/0B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACO,OAAAA,CAAK1K,UAAWrB,EAAOugC,MACtB,kBAACh1B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAACpK,MAAAA,CAAIC,UAAWrB,EAAO+L,MACrB,kBAACA,OAAAA,CAAK1K,UAAWrB,EAAOwgC,UACtB,kBAACj1B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACO,OAAAA,CAAK1K,UAAWrB,EAAOygC,MACtB,kBAACl1B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAACpK,MAAAA,CAAIC,UAAWrB,EAAO+L,MACrB,kBAACA,OAAAA,CAAK1K,UAAWrB,EAAO0gC,UACtB,kBAACn1B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACO,OAAAA,CAAK1K,UAAWrB,EAAO2gC,MACtB,kBAACp1B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAACpK,MAAAA,CAAIC,UAAWrB,EAAO+L,MACrB,kBAACA,OAAAA,CAAK1K,UAAWrB,EAAO4gC,UACtB,kBAACr1B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACO,OAAAA,CAAK1K,UAAWrB,EAAO6gC,MACtB,kBAACt1B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAACpK,MAAAA,CAAIC,UAAWrB,EAAO+L,MACrB,kBAACA,OAAAA,CAAK1K,UAAWrB,EAAO8gC,UACtB,kBAACv1B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACO,OAAAA,CAAK1K,UAAWrB,EAAO+gC,MACtB,kBAACx1B,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAACpK,MAAAA,CAAIC,UAAWrB,EAAO+L,MACrB,kBAACA,OAAAA,CAAK1K,UAAWrB,EAAOghC,UACtB,kBAACz1B,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACO,OAAAA,CAAK1K,UAAWrB,EAAOihC,MACtB,kBAAC11B,EAAAA,EAAQA,CAACC,MAAO,QAO3B,SAAStL,EAAUiK,GACjB,MAAO,CACLk2B,gBAAgBh+B,EAAAA,EAAAA,KAAI,CAClBC,QAAS,OACT2J,OAAQ,OAER,oDAAqD,CACnDjB,SAAU,QAGZ,mBAAoB,CAClB1I,QAAS,UAGbhB,WAAWe,EAAAA,EAAAA,KAAI,CACb4J,OAAQ,oBACRzJ,MAAO,oBACP0J,SAAU,WACVC,gBAAiBhC,EAAMM,OAAOD,WAAW4B,QACzCC,OAAQ,aAAalC,EAAMM,OAAO4B,OAAOC,OACzCtG,QAAS,QAEXgE,QAAQ3H,EAAAA,EAAAA,KAAI,CACVkK,aAAc,OACdjK,QAAS,OACT+H,eAAgB,kBAElBxI,KAAKQ,EAAAA,EAAAA,KAAI,CACP6+B,UAAW,OACX30B,aAAc,SAEhBR,MAAM1J,EAAAA,EAAAA,KAAI,CACRC,QAAS,SAEXg+B,UAAUj+B,EAAAA,EAAAA,KAAI,CACZG,MAAO,QAET+9B,MAAMl+B,EAAAA,EAAAA,KAAI,CACR+R,WAAY,KACZ5R,MAAO,QAETg+B,UAAUn+B,EAAAA,EAAAA,KAAI,CACZG,MAAO,QAETi+B,MAAMp+B,EAAAA,EAAAA,KAAI,CACR+R,WAAY,MACZ5R,MAAO,QAETk+B,UAAUr+B,EAAAA,EAAAA,KAAI,CACZG,MAAO,MACP4R,WAAY,OAEdusB,MAAMt+B,EAAAA,EAAAA,KAAI,CACR+R,WAAY,MACZ5R,MAAO,QAETo+B,UAAUv+B,EAAAA,EAAAA,KAAI,CACZG,MAAO,MACP4R,WAAY,OAEdysB,MAAMx+B,EAAAA,EAAAA,KAAI,CACR+R,WAAY,MACZ5R,MAAO,QAETs+B,UAAUz+B,EAAAA,EAAAA,KAAI,CACZG,MAAO,MACP4R,WAAY,QAEd2sB,MAAM1+B,EAAAA,EAAAA,KAAI,CACR+R,WAAY,MACZ5R,MAAO,QAETw+B,UAAU3+B,EAAAA,EAAAA,KAAI,CACZG,MAAO,MACP4R,WAAY,QAEd6sB,MAAM5+B,EAAAA,EAAAA,KAAI,CACR+R,WAAY,MACZ5R,MAAO,QAGb,C,sICvNO,MAAM2+B,UAAyBz+B,EAAAA,GAS5B+J,WAAAA,GACNzI,KAAK0I,cAEoB1F,EAAAA,EAAAA,IAAyBhD,MAEjC0F,iBAAiB,CAACiD,EAAUC,KACvCD,EAAS/I,UAAYgJ,EAAUhJ,SAAW+I,EAAS7I,SAAW8I,EAAU9I,SAC1EE,KAAK0I,cACL7D,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAeq4B,WAAY,CACpGx9B,QAAS+I,EAAS/I,QAClBE,OAAQ6I,EAAS7I,WAIzB,CAEQ4I,UAAAA,GACN,MAAMwb,GAAmBlhB,EAAAA,EAAAA,IAAyBhD,MAE9CkkB,EAAiBtjB,MAAMhB,QACzBI,KAAKsB,SAAS,CACZI,KAAM,IAAI06B,EAAoB,CAC5Bx8B,QAASskB,EAAiBtjB,MAAMhB,QAChCE,OAAQokB,EAAiBtjB,MAAMd,WAInCE,KAAKsB,SAAS,CACZI,KAAM,IAAIC,EAAAA,EAAgB,CACxBC,QAAS,uBAIjB,CAzCA,WAAAiB,CAAYjC,GACVkC,M,kUAAM,IACDlC,IAGLZ,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EAqCA,EA5CWm9B,EA4CGx3B,YAAY,EAAGC,YAC3B,MAAM,KAAElE,GAASkE,EAAM5K,WACvB,OAAO0G,GAAQ,kBAACA,EAAKiE,UAAS,CAACC,MAAOlE,M,kCC3CnC,MAAM27B,UAA6B3+B,EAAAA,GAShC+J,WAAAA,GACNzI,KAAKs9B,yBAEL,MAAMC,GAAgBnT,EAAAA,EAAAA,IAAsBpqB,MAC5CA,KAAKwF,MAAMC,IACT83B,EAAc73B,iBAAiB,CAACiD,EAAUC,KACpCD,EAAS7M,QAAU8M,EAAU9M,QAC/BkE,KAAKw9B,cACLx9B,KAAKs9B,4BAIb,CAEQA,sBAAAA,GACN,MAAMC,GAAgBnT,EAAAA,EAAAA,IAAsBpqB,MAGtCqe,GAAMyO,EAAAA,EAAAA,YACNtgB,GAAOsgB,EAAAA,EAAAA,UAASzO,GAAKof,SAAS,EAAG,UACjCC,EAAmB,IAAI9Q,EAAAA,GAAe,CAC1CpgB,KAAMA,EAAKmxB,cACXlxB,GAAI4R,EAAIsf,gBAGJC,EAAgB,IAAI3vB,EAAAA,GAAiB,CACzCyI,cAAe,EACfzT,WAAY,CAAEg5B,IAAK4B,OAAON,EAAc38B,MAAM9E,QAC9C4vB,WAAYgS,EACZ95B,QAAS,CAAC,CACRC,MAAO,qBACPE,MAAO,cACPD,UAAW,UACX6K,UAAW,QACXC,MAAO,EACPC,KAAM,EACNC,QAAS,OAIb9O,KAAKwF,MAAMC,IACTm4B,EAAcl4B,iBAAkB9E,I,IAC1BA,EAAJ,IAAc,QAAVA,EAAAA,EAAM9B,YAAN8B,IAAAA,OAAAA,EAAAA,EAAYA,SAAUC,EAAAA,aAAauI,MAAO,C,IAC5BxI,EAAAA,EAAAA,IAAU,QAAVA,EAAAA,EAAM9B,YAAN8B,IAAAA,GAAkB,QAAlBA,EAAAA,EAAYk9B,cAAZl9B,IAAAA,GAAuB,QAAvBA,EAAAA,EAAqB,UAArBA,IAAAA,OAAAA,EAAAA,EAAyBgB,UAAW,IAGxC/E,SAAS,oCACnBmD,KAAKsB,SAAS,CAAEy8B,UAAU,GAE9B,KAIJH,EAAcpT,UAChB,CAEOgT,WAAAA,GACLx9B,KAAKsB,SAAS,CACZy8B,UAAU,GAEd,CApEA,WAAAl7B,GACEC,MAAM,CACJi7B,UAAU,IAGZ/9B,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EAiEF,MAGag+B,EAAqE,EAAGC,eACnF,MAAM,SAAEF,GAAaE,EAASjjC,WAE9B,OAAK+iC,EAKH,kBAACG,EAAAA,MAAKA,CACJC,SAAS,UACTp+B,MAbsB,kCAetB,kBAACs1B,IAAAA,KAduB,8NAgBtB,kBAAC3lB,EAAAA,WAAUA,CACTvU,KAAK,oBACLwU,KAAK,OACLpP,KAAK,KACLD,OAAO,SACPF,KAAK,oEACN,wBAhBE,M,iCCpEJ,SAASg+B,GAAuB,YAAE/zB,EAAW,MAAEzE,IACpD,MAAM,UAAEmJ,EAAWpM,UAAW07B,IAAmCC,EAAAA,EAAAA,oBAC/D,oDAEKn7B,EAAWo7B,IAAgBvjC,EAAAA,EAAAA,YAelC,OAbA8X,EAAAA,EAAAA,WAAU,KACR,MAAMqQ,EAAiB/f,EAAAA,GAAWC,aAAauC,GAC/C24B,EAAapb,EAAeviB,MAAM9E,OAElC,MAAM0iC,EAAMrb,EAAezd,iBAAkB9E,IAC3C29B,EAAa39B,EAAM9E,SAGrB,MAAO,KACL0iC,EAAIxoB,gBAEL,CAACpQ,KAEAmJ,GAAcsvB,GAAmCl7B,EAKnD,kBAACk7B,EAAAA,CACC99B,KAAK,KACL3B,OAAO,mBACPmF,MAAO,CACL06B,MAAOt7B,EAAUqJ,KAAKkyB,UACtBC,IAAKx7B,EAAUsJ,GAAGiyB,UAClBE,WAAYv0B,EACZw0B,WAAY,WAEdC,kBAAkB,IAbb,IAgBX,C,w8BCpBA,MAAMC,EAAc,CAClBC,GAAI,CAAExgC,MAAO,MAAOD,SAAU,KAC9B6nB,GAAI,CAAE5nB,MAAO,MAAOD,SAAU,KAC9B0gC,GAAI,CAAEzgC,MAAO,MAAOD,SAAU,MAGzB,SAAS2gC,GAAO,SACrB19B,EAAQ,QACR29B,EAAO,iBACPC,GAAmB,EAAI,kBACvBC,GAAoB,EAAI,MACxBt/B,EAAK,SACLosB,EAAQ,KACR5rB,EAAO,KAAI,KACX++B,IAEA,MAAOC,EAAaC,EAAaC,GAuGnC,WAKE,MAAOF,EAAaG,IAAkB1kC,EAAAA,EAAAA,eAA6B6c,GAE7D8nB,GAAcC,EAAAA,EAAAA,aAAa7M,IAC/B2M,EAAeG,EAAqB9M,EAAE+M,WACrC,IAEGC,GAAcH,EAAAA,EAAAA,aAAa7M,IAC/B,MAAMiN,EAAQjN,EAAEkN,QAAQ,GACxBP,EAAeG,EAAqBG,EAAMF,WACzC,IAEGI,GAAYN,EAAAA,EAAAA,aACf7M,IACCoN,SAASC,oBAAoB,YAAaT,GAC1CQ,SAASC,oBAAoB,UAAWF,IAE1C,CAACP,IAGGU,GAAaT,EAAAA,EAAAA,aAChB7M,IACCoN,SAASC,oBAAoB,YAAaL,GAC1CI,SAASC,oBAAoB,WAAYC,IAE3C,CAACN,IAGH,SAASP,EAAYzM,GACnBA,EAAE6C,kBACF7C,EAAEuN,iBAEFH,SAASI,iBAAiB,YAAaZ,GACvCQ,SAASI,iBAAiB,UAAWL,EACvC,CAEA,SAAST,EAAa1M,GACpBA,EAAE6C,kBACF7C,EAAEuN,iBAEFH,SAASI,iBAAiB,YAAaR,GACvCI,SAASI,iBAAiB,WAAYF,EACxC,CAEA,MAAO,CAACd,EAAaC,EAAaC,EACpC,CAxJmDe,GAE3CxkC,GAASC,EAAAA,EAAAA,YAAWC,GACpBukC,GAAgBxkC,EAAAA,EAAAA,YAAWykC,EAAkBngC,GAC7CogC,GAAa1kC,EAAAA,EAAAA,YAAW2kC,EAAAA,eAExBC,EAAaC,EAAAA,OAAa,OAC1B,YAAEC,EAAW,WAAEC,IAAeC,EAAAA,EAAAA,GAAU,CAAC,EAAGJ,IAC5C,aAAEK,IAAiBC,EAAAA,EAAAA,GACvB,CACEC,eAAe,EACfC,QAAQ,EACRlC,WAEF0B,IAmJF/tB,EAAAA,EAAAA,WAAU,KACR,GAAKqtB,SAASz+B,KAMd,OAFAy+B,SAASz+B,KAAK4/B,UAAU77B,IAAI,oBAErB,KACL06B,SAASz+B,KAAK4/B,UAAU5L,OAAO,sBAEhC,IAvJH,MAAMpb,EAAU,gBAACld,MAAAA,CAAIC,UAAWrB,EAAOse,SAAU9Y,GAC3C+/B,EAAgBhC,QAAAA,EAAeR,EAAYx+B,GAAM/B,MACjDD,EAAWwgC,EAAYx+B,GAAMhC,SAEnC,OACE,gBAACijC,EAAAA,EAAQA,CACPC,MAAM,EACNtC,QAASA,EACTnT,UAAU,QACV0V,aAAc,qBACdrkC,UAAWrB,EAAO2lC,cAClBC,cAAe5lC,EAAO6lC,OACtBC,WAAY,CACVC,QAAStB,GAEXzkC,OAAQ,CACN+lC,QAAS,CACPvjC,MAAO+iC,EACPhjC,aAGJC,MAAO,GACPwjC,OAAQ,CACNC,cAAc,EACdC,WAAYlmC,EAAOmmC,cAErBC,cAAepmC,EAAOqmC,KACtBC,aAAclD,EACdmD,WAAY,CACVN,cAAc,EACdC,WAAYlmC,EAAOumC,aAGrB,gBAACC,EAAAA,GAAUA,CAACC,cAAAA,EAAaC,SAAAA,EAAQC,WAAAA,GAC/B,gBAACvlC,MAAAA,EAAAA,EAAAA,CACCwlC,aACmB,iBAAV7iC,EACH8iC,EAAAA,GAAUC,WAAW5D,OAAO6D,QAAQhjC,MAAMA,GAC1C8iC,EAAAA,GAAUC,WAAW5D,OAAO6D,QAAQhjC,MAAM,YAEhD1C,UAAWrB,EAAOsB,WACd4jC,EACAH,GAAAA,CACJzvB,IAAKuvB,IAEL,gBAACzjC,MAAAA,CACCC,WAAW2lC,EAAAA,EAAAA,IAAGrC,EAAWsC,mBAAoBjnC,EAAOknC,SACpD1D,YAAaA,EACbC,aAAcA,IAEhB,gBAACriC,MAAAA,CAAIC,WAAW2lC,EAAAA,EAAAA,IAAGhnC,EAAOgK,OAAQm9B,QAAQ7D,IAAStjC,EAAOonC,iBACxD,gBAAChmC,MAAAA,CAAIC,UAAWrB,EAAO2c,SACrB,gBAAC0qB,EAAAA,WAAUA,CACTjlC,KAAK,QACLlD,QAAQ,YACRE,QAAS+jC,EACTmE,cAAaT,EAAAA,GAAUC,WAAW5D,OAAO6D,QAAQQ,MACjDzoC,SAASiQ,EAAAA,EAAAA,GAAE,0BAA2B,YAGxB,iBAAVhL,EACN,gBAAC3C,MAAAA,CAAIC,UAAWrB,EAAOwnC,cACrB,gBAACp0B,EAAAA,KAAIA,EAAAA,CAACoC,QAAQ,MAASwvB,GACpBjhC,GAEFosB,GACC,gBAAC/uB,MAAAA,CAAIC,UAAWrB,EAAOmwB,SAAUmX,cAAaT,EAAAA,GAAUC,WAAW5D,OAAO6D,QAAQ5W,UAC/EA,IAKPpsB,EAEDu/B,GAAQ,gBAACliC,MAAAA,CAAIC,UAAWrB,EAAOynC,aAAcnE,IAE9CD,EAA8B,gBAACqE,EAAAA,gBAAeA,CAACC,sBAAAA,GAAsBrpB,GAAjDA,IAKhC,CAqDA,SAASulB,EAAqBC,GAC5B,IAAI8D,EAAczD,SAASz+B,KAAKmiC,aAAe/D,EAAUK,SAASz+B,KAAKoiC,YAEvE,MAAO,GADYr6B,KAAKC,IAAI,EAAey2B,SAASz+B,KAAKgQ,YAAe,IAAK,IAAImb,QAAQ,MAE3F,CAgBA,MAAM3wB,EAAaiK,I,IAyFUA,EAAAA,EAxF3B,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACT2R,cAAe,SACfhI,OAAQ,OACR2gB,KAAM,QACNtc,UAAW,OACXpE,SAAU,aAEZ25B,QAAQxjC,EAAAA,EAAAA,KAAI,CACVgoB,IAAK,EACLne,SAAU,sBAEV,6BAA8B,CAC5B67B,UAAW59B,EAAM69B,QAAQC,MAG7BtC,eAAetjC,EAAAA,EAAAA,KAAI,CACjB8J,gBAAiB,GAAGhC,EAAMM,OAAOD,WAAW4B,qBAC5C9J,QAAS,OACT0I,SAAU,mBACViJ,cAAe,WAEjBkyB,cAAc9jC,EAAAA,EAAAA,KAAI,CAChB,WAAY,CACV6lC,UAAW,mBACXC,WAAY,kBAEZ,WAAY,CACVA,WAAY,GAAGh+B,EAAMi+B,YAAYC,OAAO,0BACxCH,UAAW,oBAQjB7B,MAAMhkC,EAAAA,EAAAA,KAAI,CAER8J,gBAAiB,yBAEjBD,SAAU,sBAEV,WAAY,CACVC,gBAAiB,GAAGhC,EAAM28B,WAAWwB,QAAQ99B,wBAC7C+9B,OAAQ,EACRjqB,QAAS,KACT9Q,KAAM,EACNtB,SAAU,WACVyB,MAAO,EACP0c,IAAK,KAGTkc,YAAYlkC,EAAAA,EAAAA,KAAI,CACd,WAAY,CACVmmC,QAAS,EAET,WAAY,CACVA,QAAS,EACTL,WAAYh+B,EAAMi+B,YAAYC,OAAO,eAI3Cr+B,QAAQ3H,EAAAA,EAAAA,KAAI,CACV3B,MAAO,gBACP2d,SAAU,EACVrY,QAASmE,EAAMkB,QAAQ,EAAG,EAAG,GAC7Bo9B,aAAc,aAAat+B,EAAMM,OAAO4B,OAAOC,SAEjD86B,gBAAgB/kC,EAAAA,EAAAA,KAAI,CAClBomC,aAAc,SAEhB9rB,SAASta,EAAAA,EAAAA,KAAI,CACX6J,SAAU,WACVyB,MAAOxD,EAAMkB,QAAQ,GACrBgf,IAAKlgB,EAAMkB,QAAQ,KAErBm8B,cAAcnlC,EAAAA,EAAAA,KAAI,CAChB3B,MAAO,eACPgoC,aAAc,eAEhBvY,UAAU9tB,EAAAA,EAAAA,KAAI,CACZ3B,MAAO,kBACPiK,MAAOR,EAAMM,OAAOG,KAAKF,UACzB6T,WAAYpU,EAAMkB,QAAQ,KAE5BiT,SAASjc,EAAAA,EAAAA,KAAI,CACX2D,QAASmE,EAAMkB,QAAwC,QAAhClB,EAAuB,QAAvBA,EAAAA,EAAM28B,WAAWjB,cAAjB17B,IAAAA,OAAAA,EAAAA,EAAyBnE,eAAzBmE,IAAAA,EAAAA,EAAoC,GAC3D8B,OAAQ,OACRoS,SAAU,EACV/N,UAAW,IAEbm3B,aAAaplC,EAAAA,EAAAA,KAAI,CACf3B,MAAO,cACPioC,YAAax+B,EAAMkB,QAAQ,GAC3B8I,OAAQhK,EAAMkB,QAAQ,GAAI,GAAI,GAAI,KAEpC67B,SAAS7kC,EAAAA,EAAAA,KAAI,CACXgoB,IAAK,EACL7c,KAAMrD,EAAMkB,SAAS,GACrBk9B,OAAQ,EACRr8B,SAAU,WACVoe,OAAQngB,EAAMmgB,OAAOse,UAK3B,SAASlE,EAAiBv6B,EAAsB5F,GAC9C,OAAOlC,EAAAA,EAAAA,KAAI,CACT3B,MAAO,0BAA0B6D,IACjCyG,SAAU,mBAEV,CAACb,EAAM+f,YAAY2e,KAAK,OAAQ,CAC9BrmC,MAAO,eAAe2H,EAAMkB,QAAQ,iBACpC9I,SAAU,iBAGhB,CC/UO,MAAMumC,EAAc,EACzBtjC,WACAzB,QACAshC,SACAlC,UACA/X,YAAW,EACX2d,iBAAgB,EAChBC,0BAEA,MAAMhpC,GAASC,EAAAA,EAAAA,YAAWC,GAI1B,OAAKmlC,GAFoB0D,IAAkB3d,EAQvC,kBAAC8X,EAAMA,CAAC3+B,KAAK,KAAKR,MAAOA,EAAOo/B,QAASA,GACtC39B,GAML,kBAACpE,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAOipC,cACrB,kBAACpiB,EAAAA,OAAMA,CAAC3nB,QAAQ,UAAUyU,KAAK,OAAOpP,KAAK,KAAKpF,KAAM,aAAcC,QAAS+jC,GAAS,sBAGrF/X,GAAY4d,GAEdxjC,GAnBI,MAwBLtF,EAAaiK,IAA0B,CAC3C7I,WAAWe,EAAAA,EAAAA,KAAI,CACb4J,OAAQ,OACRzJ,MAAO,OACPgI,WAAYL,EAAMM,OAAOD,WAAW4B,QACpCpG,QAASmE,EAAMkB,QAAQ,GACvB/I,QAAS,OACT2R,cAAe,SACf/H,SAAU,WACVme,IAAK,EACL7c,KAAM,EACN8c,OAAQ,IAEV2e,cAAc5mC,EAAAA,EAAAA,KAAI,CAChBC,QAAS,OACT+H,eAAgB,gBAChBC,WAAY,SACZkmB,cAAermB,EAAMkB,QAAQ,GAE7B8tB,GAAI,CACFhlB,OAAQ,OCxEP,SAASyoB,EAA0B9pB,GACxC,MAAM6pB,EAAO7pB,EACV8C,OAAQzS,GAAMA,EAAE0I,KAAO1I,EAAE6S,UAAY7S,EAAErD,OACvC+B,IAAK+T,GAOV,SAAsBA,GACpB,IAAIyT,EAAMzT,EAAO9V,OAEf,CAAC,0CAA2C,iCAAiCe,SAAS+U,EAAO/J,OAC3FmwB,EAAS3S,IACR,CACC,SACA,OACA,cACA,YACA,WACA,gBACA,iBACA,wBACAxoB,SAAS+U,EAAO/J,MACjB,CAAC,OAAQ,SAAShL,SAASwoB,KAiBHvpB,EAhBFupB,EAiBH,iBAAVvpB,GAAsBA,EAAMoF,QAAU,GAAK82B,EAASl8B,EAAMa,MAAM,GAAI,MAASb,EAAMkB,WAAW,MAAQlB,EAAMo8B,SAAS,MAAUp8B,EAAMkB,WAAW,MAAQlB,EAAMo8B,SAAS,SAfhK,iBAAR7S,IAETA,EAAMA,EAAInT,QAAQ,SAAWzH,GAAM,KAAKA,KACxC4a,EAAM,IAAIA,MAWhB,IAA+BvpB,EAP7B,MAAO,GAAG8V,EAAO/J,MAAM+J,EAAOI,WAAWqT,GAC3C,CAjCqB0S,CAAanmB,IAC7B9T,KAAK,MAGR,OAAO66B,EAAKz3B,OAASy3B,EAAO,MAC9B,CA8BA,SAASX,EAASl8B,GAChB,OAAgB,MAATA,GAA2B,KAAVA,IAAiB2jB,MAAMzU,OAAOlP,EAAM2B,WAAW+e,QACzE,C,g4BC/BO,MAAM0oB,UAAiC5L,EAAAA,GAK5C,WAAAz2B,CAAYrD,G,IAOEA,EANZsD,MAAM,CACJqiC,oBAAqB,aACrB/mC,KAAM2b,EAAAA,GACN9W,WAAYiL,EAAAA,GACZk3B,KAAMC,EAAAA,aAAaC,UACnBrxB,OAAQ,WACRnF,SAA8B,QAApBtP,EAAAA,EAAMu5B,sBAANv5B,IAAAA,EAAAA,EAAwB,IAAI3B,IAAKsB,GAAO,OAC7CA,GAAAA,CACHomC,SAAU/lC,EAAM4nB,SAChBxsB,OAAQ4E,EAAMgmC,gBAEhBhM,kBAAkB,EAClBiM,kBAAmB7M,IAjBvB,OAAQG,sBAAR,GACA,OAAQyM,oBAAR,GACA,OAAQpe,gBAAR,GAkBEpnB,KAAK+4B,eAAiBv5B,EAAMu5B,eAC5B/4B,KAAKwlC,aAAehmC,EAAMgmC,aAC1BxlC,KAAKonB,SAAW5nB,EAAM4nB,SAGtBpnB,KAAK0F,iBAAkBiD,IACrB,GAAIA,EAASmG,SAAW9O,KAAKonB,SAAU,CACrC,IAAIse,GAAa,EACjB,MAAMC,EAAiBh9B,EAASmG,QAAQjR,IAAK+T,I,IAEb,EAO9B,OAPiD,QAAnB,EAAA5R,KAAK+4B,sBAAL,eAAqB75B,KAChD0mC,GACCA,EAAc/9B,MAAQ+J,EAAO/J,KAC7B+9B,EAAc5zB,WAAaJ,EAAOI,UAClC4zB,EAAc9pC,QAAU8V,EAAO9V,UAGL8V,EAAO2zB,UAAY3zB,EAAOhX,SAAWoF,KAAKwlC,cACtEE,GAAa,EACN,OACF9zB,GAAAA,CACH2zB,UAAU,EACV3qC,OAAQoF,KAAKwlC,gBAIV5zB,IAIL8zB,GACF1lC,KAAKsB,SAAS,CAAEwN,QAAS62B,GAE7B,GAEJ,E,6/BCOF,MACME,GAAYC,2BAEZC,GAAmB,GAAGF,GAAWnoC,MAAM,KAAK,eAE3C,MAAMk8B,WAAyBl7B,EAAAA,GAiB7B+J,WAAAA,GACAzI,KAAKY,MAAMolC,UACdhmC,KAAKsB,SAAS,CAAE0kC,SAuXb,IAAIrc,EAAAA,GAAqB,CAAC,KApX/B3pB,KAAKwF,MAAMC,IACTzF,KAAKiV,iBAAiB/U,EAAAA,GAAmBiV,IACvCnV,KAAKimC,yBAAyB9wB,EAAMC,QAAQxV,SAC5CI,KAAKsB,SAAS,CAAE1B,QAASuV,EAAMC,QAAQxV,QAASE,OAAQqV,EAAMC,QAAQtV,YAItEE,KAAKY,MAAMhB,SACbI,KAAKimC,yBAAyBjmC,KAAKY,MAAMhB,SAGrBwD,EAAAA,GAAW62B,eAAeb,EAAAA,GAAgBp5B,MAClD0F,iBAAkBiD,IAC1BA,EAAS7M,OACXs8B,aAAavF,QAAQwF,EAAAA,GAAmB1vB,EAAS7M,MAAM2B,cAIvDuC,KAAKY,MAAMg9B,gBACR59B,KAAKY,MAAMg9B,cAAcsI,UAC5BlmC,KAAKY,MAAMg9B,cAAcpT,WAG/B,CAEAS,WAAAA,GACE,MAAO,CAAErrB,QAASI,KAAKY,MAAMhB,QAASE,OAAQE,KAAKY,MAAMd,OAC3D,CAEAorB,aAAAA,CAAc5uB,GACZ,MAAM6pC,EAA8C,CAAC,GAEjD7pC,EAAOsD,SAAWtD,EAAOwD,UAC3BqmC,EAAYvmC,QAAUtD,EAAOsD,QAAWtD,EAAOsD,aAAqBiY,EACpEsuB,EAAYrmC,OAASxD,EAAOwD,OAAUxD,EAAOwD,YAAoB+X,GAGnE7X,KAAKsB,SAAS6kC,EAChB,CAEOt9B,iBAAAA,GACL,MAAMpE,EAAWrB,EAAAA,GAAW62B,eAAejgB,EAAAA,GAAYha,MACvD,KAAMyE,aAAoB01B,EAAAA,IACxB,MAAM,IAAI/wB,MAAM,6B,IAIO,EADpB3E,EAASE,YACZF,EAASG,cAAsC,QAAxB,EAAA5E,KAAKY,MAAMwlC,qBAAX,QAA4B,QAGrD,OAAO3hC,CACT,CAWO4f,iBAAAA,GACL,OAAOrkB,KAAK6I,oBAAoBlE,UAClC,CAEO0hC,WAAAA,GACLrmC,KAAKsB,SAAS,CAAE1B,aAASiY,EAAW/X,YAAQ+X,GAC9C,CAEQouB,wBAAAA,CAAyBrmC,GAC/B,MAAMskB,GAAmBlhB,EAAAA,EAAAA,IAAyBhD,MAC5C+7B,GAAQ74B,EAAAA,EAAAA,IAAcghB,GAEtByX,EAAc,IAAI1tB,EAAAA,GAAiB,CACvChL,WAAY,CAAEg5B,IAAKF,GACnBn4B,QAAS,CACP,CACEC,MAAO,IACPE,MAAOnE,EACPkE,UAAW,cAKXwiC,EAA2B,IAAI9K,EAAAA,EAAyB,CAC5Dz3B,MAAOnE,EACPR,KAAM,QACN28B,QACA52B,MAAOw2B,IAGT2K,EAAyB9b,WACzBxqB,KAAKsB,SAAS,CAAEglC,6BAChBtmC,KAAKwF,MAAMC,IACT6gC,EAAyB5gC,iBAAiB,KACxC1F,KAAKumC,6BAIT5K,EAAYnR,WAEZxqB,KAAKwF,MAAMC,IACTk2B,EAAYj2B,iBAAkB9E,I,IACxBA,EAA2CA,EAAAA,EAA/C,IAAc,QAAVA,EAAAA,EAAM9B,YAAN8B,IAAAA,OAAAA,EAAAA,EAAYA,SAAUC,EAAAA,aAAaM,OAAkB,QAAVP,EAAAA,EAAM9B,YAAN8B,IAAAA,GAAkB,QAAlBA,EAAAA,EAAYK,cAAZL,IAAAA,OAAAA,EAAAA,EAAoBM,QAAS,EAAG,C,IACpDN,EAAAA,EAAzB,MAAM4lC,EAAuC,QAApB5lC,EAAAA,EAAM9B,KAAKmC,OAAO,UAAlBL,IAAAA,GAA4B,QAA5BA,EAAAA,EAAsB3B,cAAtB2B,IAAAA,OAAAA,EAAAA,EAA8B1B,KAAMC,GAAiB,gBAAXA,EAAEf,MAEjEooC,GAAoBA,EAAiBlqC,OAAO,IAC9CgqC,EAAyBhlC,SAAS,SAC7BglC,EAAyB1lC,OAAK,CACjC8X,WAAY,GAAG8tB,EAAiBlqC,OAAO,OAG7C,KAIJgqC,EAAyBhlC,SAAS,SAC7BglC,EAAyB1lC,OAAK,CACjC8X,WAAY9Y,IAEhB,CAEc2mC,uBAAAA,G,qBACZ,MAAM,yBAAED,GAA6BtmC,KAAKY,MAC1C,IAAK0lC,EACH,OAGF,MAAMz/B,QAAa4/B,EAAAA,EAAAA,IAAqBH,GACpCz/B,GACF7G,KAAKsB,SAAS,CAAEolC,kBAAmB7/B,GAEvC,E,6KAAA,W,MAxJA,YAAmBjG,G,IAEHA,EACAA,EACFA,EAHZkC,MAAM,IACJ4oB,WAA4B,QAAhB9qB,EAAAA,EAAM8qB,kBAAN9qB,IAAAA,EAAAA,EAAoB,IAAIgsB,EAAAA,GAAe,CAAC,GACpDqM,WAA4B,QAAhBr4B,EAAAA,EAAMq4B,kBAANr4B,IAAAA,EAAAA,EAAoB+lC,GAAe/lC,GAC/C4Z,SAAwB,QAAd5Z,EAAAA,EAAM4Z,gBAAN5Z,IAAAA,EAAAA,EAAkB,CAAC,IAAI64B,EAAAA,GAAgB,CAAC,GAAI,IAAIC,EAAAA,GAAmB,CAAC,IAC9Eh4B,KAAM,IAAIklC,GAAsB,CAAC,GACjCC,YAAa,IAAI1J,EAAiB,CAAC,GACnCS,cAAe,IAAIP,GAChBz8B,IAVP,QAAUyqB,WAAW,IAAIQ,EAAAA,GAAyB7rB,KAAM,CAAEklB,KAAM,CAAC,UAAW,aA0E5E,QAAOqE,yBAA0Bnb,IAC/B,MAAM3J,EAAWzE,KAAK6I,oBACjBuF,GAAU3J,EAASE,aAAeyJ,GAIvC3J,EAASG,cAAcwJ,OAAQyJ,GAAW,KAnE1C7X,KAAKkF,qBAAqBlF,KAAKyI,YAAYK,KAAK9I,MAClD,EA8IA,GA7JW45B,GA6JJj0B,YAAY,EAAGC,YACpB,MAAM,KAAElE,GAASkE,EAAM5K,WACjBgB,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OAAO,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAO8qC,eAAe,IAAEplC,GAAQ,kBAACA,EAAKiE,UAAS,CAACC,MAAOlE,IAAS,OAIpF,MAAMklC,WAA8BloC,EAAAA,IACzC,GADWkoC,GACJjhC,YAAY,EAAGC,YACpB,MAAMse,GAAmBlhB,EAAAA,EAAAA,IAAyB4C,IAC5C,SACJ4U,EAAQ,SACRwrB,EAAQ,YACRa,EAAW,QACXjnC,EAAO,cACPg+B,EAAa,kBACb8I,EAAiB,yBACjBJ,EAAwB,SACxBlf,GACElD,EAAiBlpB,YACf,SAAE+iC,IAAaH,aAAAA,EAAAA,EAAe5iC,aAAc,CAChD+iC,UAAU,GAEN/hC,GAASC,EAAAA,EAAAA,YAAWC,IAe1B,OACE,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAOsB,UAAWiwB,GAAG,qBAClCwQ,GAAYH,GAAiB,kBAACI,EAAoBA,CAACC,SAAUL,IAC7DxW,EAAW,kBAAC2f,GAAAA,CAAenhC,MAAOA,IAAY,kBAACohC,GAAAA,CAAuBxsB,SAAUA,EAAU5U,MAAOA,IAClG,kBAACxI,MAAAA,CAAIC,UAAWrB,EAAO0F,MAAOskC,GAAY,kBAACA,EAASrgC,UAAS,CAACC,MAAOogC,KACrE,kBAAClB,EAAWA,CACVzD,SAAUwF,KAAiBjnC,EAC3Bu/B,QAAS,IAAMjb,EAAiBmiB,cAChCtmC,MAAO,cAAcH,IACrBwnB,SAAUA,EACV2d,cAAe3d,EACf4d,oBACEsB,GACAI,GACE,kBAAC7jB,EAAAA,OAAMA,CAAC3nB,QAAQ,YAAYqF,KAAK,KAAKpF,KAAK,cAAcC,QA3BhC23B,KAC7B2T,aAAAA,EAAAA,EAAmBtrC,UACrBsrC,EAAkBtrC,QAAQ23B,IAG5BluB,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAekiC,yCAGrCxrC,WAAW,IAAMyoB,EAAiBmiB,cAAe,OAkBtCa,EAAAA,KAKNL,GAAe,kBAACA,EAAYlhC,UAAS,CAACC,MAAOihC,QAOjD,MAAMM,GAAkBvhC,IAC7B,MAAOyE,EAAa+8B,GAAkBtG,IAAAA,WAChC5c,GAAmBlhB,EAAAA,EAAAA,IAAyB4C,GAC5Cub,GAAkBhQ,EAAAA,EAAAA,IAAmB+S,GAErCmjB,EAA6Bv4B,I,IAG7Bw4B,EAFJ,MAAMA,EAAoBx4B,EAAQ5P,KAAMC,GAAgB,0BAAVA,EAAE0I,KAChD,MAAuC,OAAhCy/B,aAAAA,EAAAA,EAAmBt1B,WAAoD,QAAhCs1B,aAAAA,EAAAA,EAAmBt1B,UAC7Ds1B,SAAwB,QAAxBA,EAAAA,EAAmBxrC,aAAnBwrC,IAAAA,OAAAA,EAAAA,EAA0Bp1B,QAAQ,KAAM,SACxC2F,GAeN,OAZA/E,EAAAA,EAAAA,WAAU,KACRs0B,EAAeC,EAA0BlmB,EAAgBvgB,MAAMkO,UAE/D,MAAM0vB,EAAMrd,EAAgBzb,iBAAkBiD,IAC5Cy+B,EAAeC,EAA0B1+B,EAASmG,YAGpD,MAAO,KACL0vB,EAAIxoB,gBAEL,CAACmL,IAEG9W,GAGH08B,GAAiB,EAAGnhC,Y,IASDse,EARvB,MAAMqjB,GAAsBC,EAAAA,EAAAA,uBACtBxrC,GAASC,EAAAA,EAAAA,YAAWC,IAAW,GAC/BgoB,GAAmBlhB,EAAAA,EAAAA,IAAyB4C,IAC5C,uBAAE6hC,GAA2BvjB,EAAiBlpB,WAC9CmmB,GAAkBhQ,EAAAA,EAAAA,IAAmB+S,GACrC4C,GAAwB9D,EAAAA,EAAAA,IAAyBkB,GACjDwjB,EAAmBxjB,EAAiBtjB,MAAM4Z,SAAStb,KAAM44B,GAAYA,aAAmB2B,EAAAA,IAExFkO,EAAkD,QAAjCzjB,EAAAA,EAAiBtjB,MAAM8qB,kBAAvBxH,IAAAA,OAAAA,EAAAA,EAAmClpB,WACpD4sC,EAAuBzmB,EAAgBnmB,WACvC6sC,EAAsB3jB,EAAiBrb,oBAAoB7N,YAC1D8sC,EAAgBC,GAAqBjH,IAAAA,SAAe,KAAMtlC,EAAAA,EAAAA,IAAqB0oB,IAStF,OANA4C,SAAAA,EAAuBliB,cAAcojC,EAAAA,GAAqB,GAAGlsC,QAE7DgX,EAAAA,EAAAA,WAAU,KACRi1B,GAAkBvsC,EAAAA,EAAAA,IAAqB0oB,KACtC,CAACyjB,EAAgBC,EAAsBC,EAAqB3jB,IAG7D,kBAAC9mB,MAAAA,CAAIC,UAAWrB,EAAOisC,iBACrB,kBAAC14B,EAAAA,MAAKA,CAACnJ,IAAK,EAAGE,WAAY,SAAU8F,KAAM,OAAQ/F,eAAe,iBAChE,kBAACygB,EAAsBnhB,UAAS,CAACC,MAAOkhB,IACvC3F,GACC,kBAAC/jB,MAAAA,KACC,kBAAC+jB,EAAgBxb,UAAS,CAACC,MAAOub,KAGtC,kBAAC5R,EAAAA,MAAKA,CAACnJ,IAAK,EAAGE,WAAY,UACzB,kBAACoJ,EAAAA,WAAUA,CACTtP,KAAM0nC,EACN5sC,QAAQ,YACRC,KAAK,cACLC,QAAS,KACPmsC,EAAoBE,GAA0B,aAC9C5iC,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBskB,OAAQpkB,EAAAA,GAAoBokB,OAAO8e,0BAE7E,oBAGAR,GAAoB,kBAACA,EAAiB/hC,UAAS,CAACC,MAAO8hC,QAY5DV,GAAyB,EAAGxsB,WAAU5U,YAC1C,MAAM5J,GAASC,EAAAA,EAAAA,YAAWC,KACnBisC,EAAaC,GAAkBtH,IAAAA,UAAe,GAC/Cz2B,EAAc88B,GAAevhC,GAC7Bse,GAAmBlhB,EAAAA,EAAAA,IAAyB4C,GAE5C4xB,EAAap0B,EAAAA,GAAW62B,eAAeb,EAAAA,GAAgBlV,GACvD/C,GAAkBhQ,EAAAA,EAAAA,IAAmB+S,GACrC4C,GAAwB9D,EAAAA,EAAAA,IAAyBkB,GAEvD,SAASmkB,IACP,MAAMrsC,GAASC,EAAAA,EAAAA,YAAWC,IAE1B,OACE,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAOssC,YACrB,kBAACC,KAAAA,KAAG,6BApUIzC,SAqUR,kBAAC1oC,MAAAA,CAAIC,UAAWrB,EAAOwsC,oBAAoB,gBAAczC,IAG/D,CAEA,MAAM0C,EACJ,kBAACC,EAAAA,KAAIA,CAAC1iC,OAAQ,kBAACqiC,EAAAA,OACb,kBAACjrC,MAAAA,CAAIC,UAAWrB,EAAOysC,MACpBjoC,EAAAA,OAAOmoC,sBACN,kBAACD,EAAAA,KAAKE,KAAI,CACRlsC,MAAM,gBACNmsC,UAAU,gBACV1tC,KAAM,sBACNwjB,IAAI,4DACJre,OAAO,SACPlF,QAAS,KACPyJ,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBskB,OAAQpkB,EAAAA,GAAoBokB,OAAO0f,4BAIhF,kBAACJ,EAAAA,KAAKE,KAAI,CACRlsC,MAAM,gBACNmsC,UAAU,gBACV1tC,KAAM,oBACNwjB,IAAI,+EACJre,OAAO,SACPlF,QAAS,KACPyJ,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBskB,OAAQpkB,EAAAA,GAAoBokB,OAAO2f,2BAOpF,OACE,kBAAC3rC,MAAAA,CAAIC,UAAWrB,EAAOisC,iBACrB,kBAAC14B,EAAAA,MAAKA,CAACnJ,IAAK,EAAGC,eAAgB,gBAAiB+F,KAAM,QACpD,kBAACmD,EAAAA,MAAKA,CAACnJ,IAAK,EAAGE,WAAY,SAAU8F,KAAM,QACxCorB,GACC,kBAACjoB,EAAAA,MAAKA,CAACnJ,IAAK,EAAGE,WAAY,UACzB,kBAAClJ,MAAAA,CAAIC,UAAWrB,EAAOgtC,iBAAiB,eACxC,kBAACxR,EAAW7xB,UAAS,CAACC,MAAO4xB,MAInC,kBAACp6B,MAAAA,CAAIC,UAAWrB,EAAOwe,UACrB,kBAAC4jB,EAAsBA,CAAC/zB,YAAaA,GAAe,GAAIzE,MAAOA,IAC/D,kBAACqjC,EAAAA,SAAQA,CAAC3E,QAASmE,EAAMS,gBAAiB,IAAMd,GAAgBD,IAC9D,kBAACtlB,EAAAA,OAAMA,CAAC3nB,QAAQ,YAAYC,KAAK,eAAc,YAE7C,kBAACgD,EAAAA,KAAIA,CAACd,UAAWrB,EAAOmtC,SAAU/qC,KAAM+pC,EAAc,WAAa,aAAc5nC,KAAK,SAGzFia,EAAS3c,IAAKi6B,GACb,kBAACA,EAAQnyB,UAAS,CAACkC,IAAKiwB,EAAQl3B,MAAMiH,IAAKjC,MAAOkyB,OAIxD,kBAACvoB,EAAAA,MAAKA,CAACnJ,IAAK,EAAGE,WAAY,SAAU8F,KAAM,QACzC,kBAACmD,EAAAA,MAAKA,CAACnJ,IAAK,EAAGE,WAAY,UACzB,kBAAClJ,MAAAA,CAAIC,UAAWrB,EAAOgtC,iBAAiB,WACvCliB,GAAyB,kBAACA,EAAsBnhB,UAAS,CAACC,MAAOkhB,KAEnE3F,GACC,kBAAC/jB,MAAAA,KACC,kBAAC+jB,EAAgBxb,UAAS,CAACC,MAAOub,QAY9C,SAASwlB,GAAe/lC,GACtB,OAAO,IAAIs4B,EAAAA,GAAiB,CAC1BzC,UAAW,CACT,IAAI0C,EAAAA,GAAmB,CACrB/6B,KAAMg7B,EAAAA,GACN18B,MAAO,cACPZ,MAAO8E,EAAMo4B,UACbK,SAAU,QACV+P,WAAYxoC,EAAMwmB,WAEpB,IAAImT,EAAAA,EAAsB,CACxBn8B,KAAM6lB,EAAAA,GACNmlB,WAAYxoC,EAAMwmB,WAEpB,IAAI8d,EAAyB,CAC3BnM,eAAgBn4B,EAAMm4B,eACtByM,aAAc5kC,EAAM4kC,aACpBpe,SAAUxmB,EAAMwmB,WAElB,IAAI+S,EAAAA,GAAe,CACjB/7B,KAAM4b,EAAAA,GACNorB,KAAMC,EAAAA,GAAagE,eAErB,IAAIlP,EAAAA,GAAe,CACjB/7B,KAAM87B,EAAAA,EACNoP,cAAc,EACdxtC,MAAO8E,EAAMsQ,iBAEf,IAAIipB,EAAAA,GAAe,CACjB/7B,KAAMg8B,EAAAA,GACNkP,cAAc,IAEhB,IAAInP,EAAAA,GAAe,CACjB/7B,KAAMyzB,EAAAA,GACNyX,cAAc,EACdlE,KAAMC,EAAAA,GAAagE,eAErB,IAAIlP,EAAAA,GAAe,CACjB/7B,KAAM0zB,EAAAA,GACNwX,cAAc,EACdlE,KAAMC,EAAAA,GAAagE,iBAI3B,CAEA,SAASntC,GAAUiK,EAAsBihB,GACvC,MAAO,CACL0f,eAAezoC,EAAAA,EAAAA,KAAI,CACjB3B,MAAO,gBACP2d,SAAU,EACV/b,QAAS,OACTgO,UAAW,OACX2D,cAAe,WAEjB3S,WAAWe,EAAAA,EAAAA,KAAI,CACb3B,MAAO,YACP2d,SAAU,EACV/b,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,GACnBiF,UAAW,OACX2D,cAAe,SACfjO,QAAS,KAAKmE,EAAMkB,QAAQ,MAAMlB,EAAMkB,QAAQ,MAAMlB,EAAMkB,QAAQ,KACpEL,SAAU,OACV0hB,UAAW,OACXxgB,SAAU,aAEZ+8B,cAAc5mC,EAAAA,EAAAA,KAAI,CAChBC,QAAS,OACT+H,eAAgB,gBAChBC,WAAY,SACZm+B,aAAc,aAAat+B,EAAMM,OAAO4B,OAAOC,OAC/CkkB,cAAermB,EAAMkB,QAAQ,GAC7BkB,aAAcpC,EAAMkB,QAAQ,GAE5BkiC,GAAI,CACFp5B,OAAQ,KAGZq5B,qBAAqBnrC,EAAAA,EAAAA,KAAI,CACvBC,QAAS,OACT+H,eAAgB,WAChBD,IAAKD,EAAMkB,QAAQ,OAErB3F,MAAMrD,EAAAA,EAAAA,KAAI,CACR3B,MAAO,OACP2d,SAAU,EACV/b,QAAS,OACT2R,cAAe,SACf7J,IAAKD,EAAMkB,QAAQ,KAErB4gC,iBAAiB5pC,EAAAA,EAAAA,KAAI,CACnB3B,MAAO,kBACPyL,gBAAiBif,EAAWjhB,EAAMM,OAAOD,WAAW4B,QAAUjC,EAAMM,OAAOD,WAAWijC,OACtFnrC,QAAS,OACT2R,cAAe,SACf/H,SAAU,SACVme,IAAK,EACLC,OAAQ,EACRtkB,QAAS,GAAGmE,EAAMkB,QAAQ,SAC1BjB,IAAKD,EAAMkB,QAAQ,KAErB2hC,iBAAiB3qC,EAAAA,EAAAA,KAAI,CACnB3B,MAAO,kBACP6J,SAAU,OACVvE,QAAS,KAAKmE,EAAMkB,QAAQ,KAC5BY,OAAQ,OACR3J,QAAS,OACTgI,WAAY,SACZD,eAAgB,aAChBomB,WAAYtmB,EAAMgB,WAAWuiC,iBAC7BxhC,SAAU,WACVyB,OAAQ,EACRnL,MAAO,SAETgc,UAAUnc,EAAAA,EAAAA,KAAI,CACZ3B,MAAO,WACP4B,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,GACnBif,OAAQ,EACRyP,SAAU,SAEZ0S,MAAMpqC,EAAAA,EAAAA,KAAI,CACR3B,MAAO,OACP,YAAa,CACXiK,MAAOR,EAAMM,OAAOG,KAAKC,QAG7ByhC,WAAYjqC,EAAAA,GAAG;iBACF8H,EAAMkB,QAAQ,GAAK;;MAGhCmhC,mBAAoBnqC,EAAAA,GAAG;eACZ8H,EAAMM,OAAOG,KAAKF;mBACdP,EAAMgB,WAAW0a,UAAUtb;MAE1CzL,SAASuD,EAAAA,EAAAA,KAAI,CACX3B,MAAO,UACP6J,SAAU,OACVojC,WAAY,OACZnrC,MAAO,QACP2yB,UAAW,WAEbgY,UAAU9qC,EAAAA,EAAAA,KAAI,CACZ3B,MAAO,WACP0T,WAAYjK,EAAMkB,QAAQ,KAE5ByH,SAASzQ,EAAAA,EAAAA,KAAI,CACX3B,MAAO,UACPwgC,UAAW/2B,EAAMkB,QAAQ,GACzB/I,QAAS,OACT8H,IAAKD,EAAMkB,QAAQ,KAGzB,C,6HC3mBO,MAAM3E,UAA0BhE,EAAAA,I,EACX,EAAGkH,YAC3B,MAAMO,GAAQN,EAAAA,EAAAA,aACR7J,GAASC,EAAAA,EAAAA,YAAWC,IACpB,UAAEyG,GAAciD,EAAM5K,WAE5B,OACE,kBAACoC,MAAAA,CAAIC,UAAWrB,EAAOsB,UAAWgmC,cAAasG,EAAAA,EAAQC,cACrD,kBAACC,EAAAA,EAAaA,CACZj6B,UAAW1J,EAAMM,OAAO2lB,UAAUjmB,EAAMM,OAAOD,WAAWE,WAC1DoJ,eAAgB3J,EAAMM,OAAO2lB,UAAUjmB,EAAMM,OAAOD,WAAWE,UAAW,IAC1EyS,aAAchT,EAAM6vB,MAAMC,OAAOC,SAEhCvzB,Q,EAZKgD,e,EADHjD,G,sFAoBb,MAAMqnC,GAASC,EAAAA,EAAAA,WAAU,CACvB,KAAM,CACJxF,QAAS,GAEX,OAAQ,CACNA,QAAS,KAIb,SAAStoC,IACP,MAAO,CACLoB,WAAWe,EAAAA,EAAAA,KAAI,CACb3B,MAAO,sBAEPutC,cAAeF,EACfG,eAAgB,QAChBC,wBAAyB,UACzBC,kBAAmB,QACnBC,kBAAmB,cAGzB,C,0FC1CO,MAAM5gB,EAAqB,EAChClB,cACAmB,WAAW,OAEX,MAAM1tB,GAASC,EAAAA,EAAAA,YAAWC,EAAWwtB,GAErC,OAAKnB,EAKH,kBAACuD,EAAAA,QAAOA,CAACxR,QAAS,aAChB,kBAACnc,EAAAA,KAAIA,CAACC,KAAM,cAAemC,KAAK,KAAKlD,UAAWrB,EAAOsuC,sBALlD,MAULpuC,EAAY,CAACiK,EAAsBujB,KAChC,CACL4gB,oBAAoBjsC,EAAAA,EAAAA,KAAI,CACtBG,MAAO,GAAGkrB,MACVzhB,OAAQ,GAAGyhB,MACXvhB,gBAAiBhC,EAAMM,OAAO8jC,QAAQ3jC,KACtC+I,KAAMxJ,EAAMM,OAAO8jC,QAAQ3jC,KAC3BuS,aAAc,MACd7a,QAAS,kB,+EC/BR,MAAMqY,EACXjK,GACkC,CAClC,CACE89B,MAAOC,EAAAA,UAAUC,YACjB14B,SAAU,IAAOpT,GACRA,EAAOC,MACZhB,EAAAA,EAAAA,KAAKiB,GACIA,EAAKjB,IAAK4B,IACf,GAAmB,aAAfA,EAAMrB,KAAqB,CAC7B,MAAMusC,EAAelrC,EAAMR,OAAOC,KAAM2X,GAAgC,YAAfA,EAAMzY,MAC3DusC,IAGFA,EAAanqC,OAAOie,MAAQ,CAC1B,CACE1e,MAAO,aACP4e,IAAK,kBACLvjB,QAAU+Z,I,IAEiBA,EAAAA,EAAAA,EADzBA,EAAM4d,EAAE6C,kBACR,MAAMgV,EAAiC,QAAdz1B,EAAAA,EAAM4d,EAAEzyB,cAAR6U,IAAAA,GAA6B,QAA7BA,EAAAA,EAAgB01B,qBAAhB11B,IAAAA,GAA4C,QAA5CA,EAAAA,EAA+B01B,qBAA/B11B,IAAAA,OAAAA,EAAAA,EAA8C/U,KACvE,IAAKwqC,IAAuD,IAAnCA,EAAiBhuC,QAAQ,KAChD,OAEF,MAAMgD,EAAUgrC,EAAiBltC,MAAM,KAAK,GACvCkC,GAAuB,KAAZA,IAGhB8M,SAAAA,EAAY9M,OAKtB,CAEA,OAAOH,QAQNsoB,EAAgC,IAAqC,CAChF,CACEyiB,MAAOC,EAAAA,UAAUC,YACjB14B,SAAU,IAAOpT,GACRA,EAAOC,MACZhB,EAAAA,EAAAA,KAAKiB,GACIA,EAAK8S,OAAQnS,GAAyB,aAAfA,EAAMrB,S,0LC5C9C,MAmBA,EAjByB,CAAC0sC,EAFW,MAGnC,MAAOC,EAAeC,IAAoBhwC,EAAAA,EAAAA,UAAwB,CAAE4C,EAAG,KAAMwiB,EAAG,OAahF,OAXAtN,EAAAA,EAAAA,WAAU,KACR,MAAMm4B,GAAsBC,EAAAA,EAAAA,UAAU/1B,IACpC61B,EAAiB,CAAEptC,EAAGuX,EAAM2qB,QAAS1f,EAAGjL,EAAMg2B,WAC7CL,GAGH,OAFAhhB,OAAOyW,iBAAiB,YAAa0K,GAE9B,KACLnhB,OAAOsW,oBAAoB,YAAa6K,KAEzC,CAACH,IAEGC,GCFIK,EAAe,EAAG5sC,QAAQ,OAAQyJ,SAAQojC,WAAU,MAC/D,MAAMllC,GAAQN,EAAAA,EAAAA,cACR,EAAEjI,EAAC,EAAEwiB,GAAMkrB,IACXtvC,GAASC,EAAAA,EAAAA,YAAWC,EAAW0B,EAAGwiB,EAAGirB,GAC3C,OAAO,kBAACE,EAAAA,EAAGA,CAACC,IAAKrlC,EAAMwoB,OAAS8c,EAAUC,EAAUruC,UAAWrB,EAAOq1B,IAAKppB,OAAQA,EAAQzJ,MAAOA,KAGpG4sC,EAAa3oB,YAAc,eAE3B,MAAMvmB,EAAY,CAACiK,EAAsBwlC,EAAqBC,EAAqBP,KACjF,MAAM,WAAEQ,EAAU,YAAEC,GAAgBhiB,OAC9BiiB,EAAcH,GAAQA,EAAOE,EAC7BE,EAAaL,GAAQA,EAAOE,EAC5BI,EAA2B,OAAhBF,EAAuBG,EAAqBH,GAxBtC,GACA,GAuByF,EAC1GI,EACW,OAAfH,EAAsBE,EAAqBF,GAxBnB,EACA,GAuB2E,EAErG,MAAO,CACL3a,KAAKhzB,EAAAA,EAAAA,KAAI,CACP,qCAAsC,CACpC6lC,UAAW,UAAU+H,oBAA2BE,MAChDC,gBAAiB,SACjBjI,WAAY,yBAEd,iBAAkB,CAChB7lC,QAAS+sC,EAAU,QAAU,YAU/Ba,EAAuB,CAACG,EAAe5N,EAAeE,IAC5C0N,GAAS1N,EAAMF,GAASA,E,cC3CjC,MAAMzuB,EAAa,EAAGpO,UAASE,gBAAe2mB,WAAUzmB,cAC7D,MAAMhG,GAASC,EAAAA,EAAAA,YAAWC,EAAW8F,GAErC,OACE,kBAAC5E,MAAAA,CAAIC,UAAWrB,EAAOsB,UAAWgmC,cAAasG,EAAAA,EAAQ0C,YACrD,kBAAC/8B,EAAAA,MAAKA,CAACtN,UAAU,SAASqE,WAAW,SAASF,IAAK,GACjD,kBAACglC,EAAYA,CAAC5sC,MAAOiqB,QAAAA,EAAY,MACb,iBAAZ7mB,GAAyB,kBAACwN,EAAAA,KAAIA,CAACC,cAAe,SAAUnU,QAAQ,MAAM0G,GAC1D,iBAAZA,GAAyBA,EAEhCE,GACC,kBAAC1E,MAAAA,CAAIC,UAAWrB,EAAOuwC,QACrB,kBAACh9B,EAAAA,MAAKA,CAACnJ,IAAK,GAAKE,WAAY,UAC3B,kBAACnI,EAAAA,KAAIA,CAACC,KAAK,gBACX,kBAACgR,EAAAA,KAAIA,CAACC,cAAe,SAAUnU,QAAQ,QACpC4G,QAYjB,SAAS5F,EAAUiK,EAAsBnE,GACvC,MAAO,CACL1E,WAAWe,EAAAA,EAAAA,KAAI,CACbG,MAAO,OACPF,QAAS,OACT+H,eAAgB,eAChB4J,cAAe,SACfjO,QAASA,GAAoB,IAE/BuqC,QAAQluC,EAAAA,EAAAA,KAAI,CACVkK,aAAcpC,EAAMkB,QAAQ,KAGlC,CAfA2I,EAAWyS,YAAc,Y,kECvClB,MAAMsL,EAAqB,sBAErBia,EAAuD,CAClE,CACEtrC,MAAO,aACPZ,MAAO,oBACP8V,OAAQ,CAAE/J,IAAK,kBAAmBmK,SAAU,IAAKlW,MAAO,KACxDmK,YAAa,sDAEf,CACEvJ,MAAO,YACPZ,MAAO,OACP8V,OAAQ,CAAE/J,IAAK,GAAImK,SAAU,GAAIlW,OAAO,GACxCmK,YAAa,+EAEf,CACEvJ,MAAO,eACPZ,MAAO,cACP8V,OAAQ,CAAE/J,IAAK,OAAQmK,SAAU,IAAKlW,MAAO,UAC7CmK,YAAa,8CAEf,CACEvJ,MAAO,iBACPZ,MAAO,gBACP8V,OAAQ,CAAE/J,IAAK,OAAQmK,SAAU,IAAKlW,MAAO,YAC7CmK,YAAa,uDAEf,CACEvJ,MAAO,iBACPZ,MAAO,GAAGiyB,QACVnc,OAAQ,CAAE/J,IAAKkmB,EAAoB/b,SAAU,KAAMlW,MAAO,MAC1DmK,YAAa,6DAIJquB,EAAmBzsB,GACvBmgC,EAAqB9oC,KAAMmT,GAAWA,EAAOvW,QAAU+L,E,ohCCnBzD,MAAMq/B,EAAiC,uBACxCsF,EAAmB,6CACnBC,EAAyC,yBACzCC,EAAuC,iBAStC,MAAMj0B,UAAkB/Z,EAAAA,GA8C7BiuC,OAAAA,CAAQC,GACF5sC,KAAKY,MAAMc,MACb1B,KAAKY,MAAMc,KAAKirC,QAAQC,EAE5B,CAEAC,QAAAA,CAASC,GACH9sC,KAAKY,MAAMc,MACb1B,KAAKY,MAAMc,KAAKmrC,SAASC,EAE7B,CAvDA,WAAAjqC,CAAYjC,GACVkC,MAAMlC,GACNZ,KAAKkF,qBAAqB,KACxB,MAAM4nC,EAAyB,CAC7B,CACElmC,KAAM,aACNxH,KAAM,SAER,CACEwH,KAAM,UACNmmC,cAAe,UACf3sC,KAAM4sC,EAAehtC,MACrB5E,QAAS,IAAM6xC,MAInBjtC,KAAKsB,SAAS,CACZI,KAAM,IAAIwrC,EAAAA,GAAa,CACrBJ,YAIJ,MAAM5oB,GAAmBlhB,EAAAA,EAAAA,IAAyBhD,MAC5C+7B,GAAQ74B,EAAAA,EAAAA,IAAcghB,GAEtBoiB,EAA2B,IAAI9K,EAAAA,EAAyB,CAC5Dz3B,MAAO/D,KAAKY,MAAMmD,MAClBg4B,UAGFuK,EAAyB9b,WACzBxqB,KAAKsB,SAAS,CAAEglC,6BAChBtmC,KAAKwF,MAAMC,IACT6gC,aAAAA,EAAAA,EAA0B5gC,iBAAiB,KAoFnD,IAA6C+iC,IAnFLzoC,K,cAoFtC,MAAMsmC,EAA2BmC,EAAK7nC,MAAM0lC,yBAC5C,GAAIA,EAA0B,C,IAEFmC,EAD1B,MAAM5hC,QAAa4/B,EAAqBH,G,IACdmC,EAA1B,MAAM0E,EAAgD,QAA5B1E,EAAe,QAAfA,EAAAA,EAAK7nC,MAAMc,YAAX+mC,IAAAA,OAAAA,EAAAA,EAAiB7nC,MAAMksC,aAAvBrE,IAAAA,EAAAA,EAAgC,GACpD2E,EAAiCD,EAAkBjuC,KACtD0tC,GAASA,EAAKhmC,OAASsgC,G,IA6BpBuB,EAxBFA,EAIAA,EAIAA,EAVA5hC,IACGumC,EAwBCA,IACa,QAAf3E,EAAAA,EAAK7nC,MAAMc,YAAX+mC,IAAAA,GAAAA,EAAiBoE,SACfM,EAAkBv7B,OACfg7B,IAK2B,IAJ1B,CACEH,EACAC,EACAxF,GACArqC,SAAS+vC,EAAKhmC,UA/BT,QAAf6hC,EAAAA,EAAK7nC,MAAMc,YAAX+mC,IAAAA,GAAAA,EAAiBkE,QAAQ,CACvB/lC,KAAM6lC,EACNrtC,KAAM,YAEO,QAAfqpC,EAAAA,EAAK7nC,MAAMc,YAAX+mC,IAAAA,GAAAA,EAAiBkE,QAAQ,CACvB/lC,KAAM8lC,EACNttC,KAAM,UAEO,QAAfqpC,EAAAA,EAAK7nC,MAAMc,YAAX+mC,IAAAA,GAAAA,EAAiBkE,QAAQ,CACvB/lC,KAAMsgC,EACN6F,cAAe,cACf3xC,QAAU23B,IACJlsB,EAAKzL,SACPyL,EAAKzL,QAAQ23B,IAGfluB,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAesoC,kCAmB/C,CACF,E,MAhIM/G,EAAyBhlC,SAAS,E,kUAAA,IAC7BglC,EAAyB1lC,OAAK,CACjC8X,WAAY1Y,KAAKY,MAAM8X,eAG7B,EAcA,EA1DWD,EA0DG9S,YAAY,EAAGC,YAC3B,MAAM,KAAElE,GAASkE,EAAM5K,WAEvB,OAAI0G,EACK,kBAACA,EAAKiE,UAAS,CAACC,MAAOlE,IAGzB,uCAIX,MAAMsrC,EAAkBpnC,IACtB,MAAMse,GAAmBlhB,EAAAA,EAAAA,IAAyB4C,GAC5C3C,GAAaC,EAAAA,EAAAA,IAAcghB,GAC3B/gB,EAAYC,EAAAA,GAAWC,aAAauC,GAAOhF,MAAM9E,MACjD4oB,GAAO8V,EAAAA,EAAAA,IAAe50B,GAEtBtC,EAAeC,KAAKC,UAAU,CAClC,iBAAoB,CAClBC,OAAOC,EAAAA,EAAAA,YAAWP,EAAUQ,KAC5BC,QAAS,CAAC,CAAEC,MAAO,IAAKZ,aAAYc,MAAO6B,EAAMhF,MAAMmD,MAAO2gB,Y,IAGnDlkB,EAAf,MAAM0D,EAAyB,QAAhB1D,EAAAA,EAAAA,OAAO2D,iBAAP3D,IAAAA,EAAAA,EAAoB,GAEnC,OADmB4D,EAAAA,QAAQC,UAAU,GAAGH,YAAkB,CAAEI,MAAOhB,EAAciB,cAAe,KAI5F0oC,EAAiB,MACrBpoC,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAeuoC,0BAG/E7G,EAA8B8G,GAAAA,EAAAA,YACzC,MAAMxT,EAAUwT,EAAoB3sC,MAAMm5B,QAG1C,QAAgCliB,IAA5B21B,EAAAA,wBAAuC,CAMzC,OALcA,EAAAA,EAAAA,yBAAwB,CACpChB,mBACAzS,YAGW0T,WAAW,EAC1B,CAGA,QAAiC51B,IAA7B61B,EAAAA,yBAAwC,CAQ1C,aAP2CC,EAAAA,EAAAA,iBACzCD,EAAAA,EAAAA,0BAAyB,CACvBlB,mBACAzS,cAIS,EACf,CAGF,EA1B2CwT,E,uDCtHpC,MAAM3pB,EAA4BnkB,I,IAOlB8iB,EANrB,MAAMA,EAAgB9iB,EAAMR,OAAOC,KAAMC,GAAiB,aAAXA,EAAEf,MAC3CokB,EAAiB/iB,EAAMR,OAAOC,KAAMC,GAAiB,cAAXA,EAAEf,MAElD,IAAI2lB,EAAgB,EAChB6pB,EAAqB,EAEzB,IAAK,IAAIjmC,EAAI,EAAGA,IAAK4a,SAAqB,QAArBA,EAAAA,EAAejmB,cAAfimB,IAAAA,OAAAA,EAAAA,EAAuBrhB,SAAU,GAAIyG,IAAK,CAC7D,MAAMkmC,IAAQrrB,aAAAA,EAAAA,EAAgBlmB,OAAOqL,KAAM,KAAM4a,aAAAA,EAAAA,EAAejmB,OAAOqL,KAAM,GACzE8B,KAAKqa,IAAI+pB,GAAQpkC,KAAKqa,IAAIC,GAAiB,KAC7CA,EAAgB8pB,EAChBD,EAAqBjmC,EAEzB,CAEA,MAAO,CAAEoc,gBAAe6pB,uBAGb9nB,EAAgC1X,IAC3C,GAAe,aAAXA,EAGJ,MAAO,CAAErK,MAAO,iBAAkB3E,KAAM,Q,6rBChB1C,MAAM0uC,EAActuC,IAClB,MAAMxD,GAASC,EAAAA,EAAAA,YAAWC,GAC1B,OAAO,kBAAC4mC,EAAAA,EAAW4F,KAAI,E,kUAAA,IAAKlpC,GAAAA,CAAOnC,UAAWrB,EAAO+xC,eAGhD,SAASC,GAAkB,YAAEC,IAClC,MACM9yC,EADS8yC,EAAYC,WACL,WAAa,aAEnC,OAAO,kBAAC/vC,EAAAA,KAAIA,CAACC,KAAMjD,EAAMoF,KADZ,MAEf,CAEA,MAAM4tC,EAAe,KACnB,MAAMnyC,GAASC,EAAAA,EAAAA,YAAWC,GAC1B,OACE,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAOoyC,SACrB,kBAACh/B,EAAAA,KAAIA,CAACi/B,OAAO,OAAOnzC,QAAQ,YAAYyL,MAAM,aAAY,oBAOzD,MAAM4zB,UAA8BJ,EAAAA,IACzC,EADWI,EACJ50B,YAAY,EAAGC,YACpB,MAAM5J,GAASC,EAAAA,EAAAA,YAAWC,IACpB,MAAEJ,EAAK,WAAEstC,GAAexjC,EAAM5K,YAGpC8X,EAAAA,EAAAA,WAAU,KACHhX,GACH8J,EAAMhB,cAAcwkC,EAAapB,EAAAA,GAAqB,GAAGlsC,MAASksC,EAAAA,GAAqB,GAAGlsC,SAI9F,MAAMwyC,EAAqBtG,EAAAA,GAAqBrrC,MAAM,EAAG,GACnD4xC,EAAgBvG,EAAAA,GAAqB9oC,KAAMmT,GAAWA,EAAOvW,QAAUA,GACzEyyC,IAAkBD,EAAmBv7B,KAAMV,GAAWA,EAAOT,OAAO/J,MAAQ0mC,EAAc38B,OAAO/J,MACnGymC,EAAmBvxC,KAAKwxC,GAE1B,MAAMC,EAAgBxG,EAAAA,GAAqBp2B,OACxCS,IAAYi8B,EAAmBv7B,KAAM5V,GAAMA,EAAErB,QAAUuW,EAAOvW,QAG3DC,EAAYwZ,KAChB1Q,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,eAClBC,EAAAA,GAAoBD,eAAe0pC,uBACnC,CACEC,eAAgBn5B,IAGpB3P,EAAMhB,cAAc2Q,OAAIsC,GAAW,IAGrC,OAAIuxB,EACK,qCAIP,oCACE,kBAACj2B,EAAAA,iBAAgBA,CACftX,QAASyyC,EACTxyC,MAAOA,EACPC,SAAUA,EACV45B,SAAUyT,EACV/rC,UAAWrB,EAAO2yC,cAEpB,kBAACnxC,EAAAA,OAAMA,CACL3B,QAAS,CAAC,CAAEa,MAAO,iBAAkBb,QAAS2yC,IAC9C1yC,MAAO,GACP6B,YAAY,GACZixC,cAAc,EACd5wC,aAAa,EACbQ,MAAO,EACPzC,SAAWwZ,GAAMxZ,EAASwZ,EAAEzZ,OAC5BuB,UAAWrB,EAAOsX,OAClBwvB,WAAY,CACV+L,mBAAoB,IAAM,KAC1BC,YAAa,IAAM,KACnBpG,KAAMoF,EACNE,oBACAG,qBAQZ,MAAMjyC,EAAaiK,IAA0B,CAC3CmN,OAAQjV,EAAAA,GAAG;;;;;;;;;;;;;;;;;;;;;IAsBXswC,YAAatwC,EAAAA,GAAG;;IAGhB0vC,WAAY1vC,EAAAA,GAAG;;;;;;;;;;IAWf+vC,SAAS/vC,EAAAA,EAAAA,KAAI,CACX2D,QAASmE,EAAMkB,QAAQ,EAAG,EAAG,IAAM,KACnC0nC,WAAY,wBACZtK,aAAc,aAAat+B,EAAMM,OAAO4B,OAAOC,U,0FCvInD,MAKazD,EAAuB,CAClCmqC,EACAv/B,EACAw/B,MAEAC,EAAAA,EAAAA,mBAV4B,EAACF,EAA0Bv/B,IAChD,GAAG0/B,EAAAA,GAAcj9B,QAAQ,KAAM,QAAQ88B,KAAQv/B,IASpC2/B,CAAsBJ,EAAMv/B,GAASw/B,IAG5CnqC,EAAoB,CAC/BC,eAAgB,iBAChBupB,KAAM,OACNlF,OAAQ,UASGpkB,EAAsB,CACjC,CAACF,EAAkBC,gBAAiB,CAClCwmB,oBAAqB,sBACrBnR,2BAA4B,6BAC5BT,iCAAkC,mCAClCqK,kCAAmC,oCACnCM,uCAAwC,yCACxCtQ,oBAAqB,sBACrBq7B,oBAAqB,sBACrBC,mBAAoB,qBACpBlS,WAAY,aACZkQ,wBAAyB,0BACzBD,6BAA8B,+BAC9BpG,wCAAyC,0CACzChiC,0BAA2B,4BAC3BsqC,wBAAyB,0BACzBd,uBAAwB,yBACxB7vB,0BAA2B,6BAE7B,CAAC9Z,EAAkBwpB,MAAO,CACxBkhB,qBAAsB,uBACtBjhB,kBAAmB,oBACnBqJ,uBAAwB,yBACxBC,2BAA4B,6BAC5BY,eAAgB,iBAChBlD,uBAAwB,0BAE1B,CAACzwB,EAAkBskB,QAAS,CAC1BC,eAAgB,iBAChBomB,0BAA2B,4BAC3BC,gBAAiB,kBACjB5G,yBAA0B,2BAC1Bxc,yBAA0B,2BAC1Byc,sBAAuB,wBACvBb,uBAAwB,0B,s8BC5DrB,MAAMzxB,UAAwBxI,EAAAA,GAM3B0hC,eAAAA,GACN,MAAMjrB,GAAOkrB,EAAAA,EAAAA,IAAoB5vC,KAAMA,KAAKY,MAAM8V,eAClD1W,KAAKsB,SAAS,CACZsC,QAAS5D,KAAKY,MAAMgD,QAAQ/F,IAAKkG,GACxB,OACFA,GAAAA,CACH2gB,YAKiBthB,EAAAA,GAAWC,aAAarD,MAChC0F,iBAAiB,CAACiD,EAAUC,KACzC,GAAID,EAAS7M,MAAM0Q,OAAS5D,EAAU9M,MAAM0Q,MAAQ7D,EAAS7M,MAAM2Q,KAAO7D,EAAU9M,MAAM2Q,GAAI,CAC5F,MAAMojC,GAAUD,EAAAA,EAAAA,IAAoB5vC,KAAMA,KAAKY,MAAM8V,eACrD1W,KAAKsB,SAAS,CACZsC,QAAS5D,KAAKY,MAAMgD,QAAQ/F,IAAKkG,GACxB,OACFA,GAAAA,CACH2gB,KAAMmrB,MAId,GAEJ,CA9BA,WAAAhtC,CAAYjC,GACVkC,MAAMlC,GACNZ,KAAKkF,qBAAqBlF,KAAK2vC,gBAAgB7mC,KAAK9I,MACtD,E,sECHK,MAAMuY,EAAkB,CAACnK,EAAwB0hC,KACtD,MAAMC,EAA4B,WAAX3hC,IAAuB,EAExC9L,EAAUJ,EAAAA,GAAcoS,aAC3BzH,UAAU,SAAU,CAAE0H,YAAY,IAClCI,qBAAqB,YAAaq7B,EAAAA,UAAUC,MAC5Ct7B,qBAAqB,WAAY,CAAEH,KAAM07B,EAAAA,aAAaC,SACtDx7B,qBAAqB,cAAe,IACpCA,qBAAqB,YAAa,GAClCA,qBAAqB,YAAa,GAClCA,qBAAqB,YAAa,QAClCtS,aAAc+tC,IACbA,EAAUC,2BAA2B,MAAMC,cAAc,CACvD97B,KAAM,QACN6T,WAAY0nB,EAAiB,gBAAkB,YAGlDljC,UAAU,UAAW,CAAE2H,KAAMC,EAAAA,mBAAmBC,QAMnD,YAJkBmD,IAAdi4B,GACFxtC,EAAQqS,qBAAqB,YAAam7B,GAGrCxtC,E,kYCbF,MAAMiuC,UAAkC7xC,EAAAA,GAC7C,aAAmB,UAAEqjB,IACnBjf,MAAM,CAAEif,cAGV,OAAOyuB,qBAAqB,KAC1B,MAAMttB,GAAiBnd,EAAAA,EAAAA,IAAuB/F,MAC9CkjB,EAAe5hB,SAAS,CAAEygB,UAAW/hB,KAAKY,MAAMmhB,aAC3C6Y,EAAAA,EAAAA,GAAoB1X,EAAetiB,MAAM6lB,aAC5CvD,EAAe7B,cAAc,eAG/Bxc,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAesqC,oBAAqB,CAC7GttB,UAAW/hB,KAAKY,MAAMmhB,UACtB3T,QAAQysB,EAAAA,EAAAA,IAAe76B,SAX3B,EA0CF,SAAS9D,EAAUiK,GACjB,MAAO,CACL47B,SAAS1jC,EAAAA,EAAAA,KAAI,CACXC,QAAS,OACT8H,IAAK,OACLE,WAAY,WAEd3I,aAAaU,EAAAA,EAAAA,KAAI,CACfsI,MAAOR,EAAMM,OAAOG,KAAKF,UACzBH,SAAUJ,EAAMgB,WAAW0a,UAAUtb,SACrCjI,QAAS,OACT8H,IAAKD,EAAMkB,QAAQklB,OAGzB,CAzCE,EAlBWgkB,EAkBG5qC,YAAY,EAAGC,YAC3B,MAAM,UAAEmc,IAAchc,EAAAA,EAAAA,IAAuBH,GAAO5K,WAC9CgB,GAASC,EAAAA,EAAAA,YAAWC,GAEpBu0C,EAAiC,UAApB1uB,aAAAA,EAAAA,EAAW3iB,MACxBtE,EAAU21C,EACZ,oGACA54B,EAEJ,OACE,kBAACza,MAAAA,CAAIC,UAAWrB,EAAO+lC,SACrB,kBAAClf,EAAAA,OAAMA,CACL3nB,QAAQ,YACRqF,KAAK,KACLoP,KAAK,QACLgmB,SAAU8a,EACVt1C,KAAM,OACNC,QAASwK,EAAM4qC,mBACf11C,QAASA,GAER21C,EAAa,0BAA4B,4B,wBCxB7C,SAASC,GAAuB,YAAErmC,EAAW,MAAEzE,IACpD,MAAM,UAAEmJ,EAAWpM,UAAWguC,IAAmCrS,EAAAA,EAAAA,oBAC/D,mDAEItiC,GAASC,EAAAA,EAAAA,YAAWC,GACpBinB,EAAiB/f,EAAAA,GAAWC,aAAauC,GAAO5K,WAEhDoT,GAASvF,EAAAA,EAAAA,IAAkBjD,GAAOhF,MAAM9E,MAC9C,IAAI80C,EAAwC,GAC7B,WAAXxiC,EACFwiC,EAAmB,CAAC,WAAY,WACZ,SAAXxiC,IACTwiC,EAAmB,CAAC,SAGtB,IAAIC,EAAoC,GAKxC,MAJe,aAAXziC,IACFyiC,EAA0B,CAAC,aAGzB9hC,GAAc4hC,GAAmCxtB,GAAmB9Y,EAKtE,kBAACsmC,EAAAA,CACCtmC,YAAaA,EACbo0B,MAAOtb,EAAe3W,KAAKkyB,UAC3BC,IAAKxb,EAAe1W,GAAGiyB,UACvBkS,iBAAkBA,EAClBC,wBAAyBA,EACzBn0C,MAAO,kBAACU,MAAAA,CAAIC,UAAWrB,EAAOU,OAAO,cAVhC,IAaX,CAEA,SAASR,EAAUiK,GACjB,MAAO,CACLzJ,OAAO2B,EAAAA,EAAAA,KAAI,CACTkI,SAAU,OACVI,MAAOR,EAAMM,OAAOG,KAAKF,UACzB0J,WAAY,OACZ8sB,UAAW,SAGjB,C,84BCxBO,MAAM5P,UAAiB5uB,EAAAA,GA+HpBoyC,UAAAA,GACN,MAA+C,cAAxCjoC,EAAAA,EAAAA,IAAkB7I,MAAMY,MAAM9E,KACvC,CAEQ2M,WAAAA,GACN,MAAM2F,GAASvF,EAAAA,EAAAA,IAAkB7I,MAAMY,MAAM9E,MAE7CkE,KAAKsB,SAAS,CACZ6D,MAAO,IAAIC,EAAAA,GAAqB,CAC9BD,MAAO,IAAIsR,EAAAA,EAAgB,CACzBC,cAAe1W,KAAK8wC,aAAe,GAAK,GACxC7tC,WAAYiL,EAAAA,GACZtK,QAAS,CAAC5D,KAAK8wC,cAAejpB,EAAAA,EAAAA,MAAwBzR,EAAAA,EAAAA,GAAqB,CAAEhI,SAAQ0Z,QAAQ,OAE/FziB,gBAAiBrF,KAAK8wC,aAClB,KAAI/oB,EAAAA,EAAAA,MACJ,KAAIpR,EAAAA,EAAAA,IAAyBhK,EAAAA,EAAAA,IAAa3M,UAEhDqB,MAAOrB,KAAKgoB,eAEhB,CAEQA,WAAAA,GACN,MAAM5Z,GAASvF,EAAAA,EAAAA,IAAkB7I,MAAMY,MAAM9E,M,IAET,EADpC,OAAIkE,KAAK8wC,cACAC,EAAAA,EAAAA,IAAqB/wC,KAAyB,QAAnB,EAAAA,KAAKY,MAAM8vB,gBAAX,QAAuB,IAGpD1wB,KAAKgxC,uBAAuB5iC,EACrC,CAEQ4iC,sBAAAA,CAAuB5xC,GAC7B,MAAMiC,GAAQkX,EAAAA,EAAAA,GAAgBnZ,EAAM,IAAIgD,gBAAe,GAAM+lB,eAAe,eAS5E,MARa,SAAT/oB,EACFiC,EAAMsT,qBAAqB,YAAa,UACtB,WAATvV,GACTiC,EAAMsT,qBAAqB,YAAa,WAAWyT,SAAS,CAC1DC,WAAY,gBACZ7T,KAAM,UAGH,IAAIjT,EAAAA,GAAgB,CACzBU,UAAW,MACXT,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAML,EAAMoB,YAIpB,CAEQwuC,wBAAAA,CAAyBrwC,G,IAKlBA,EAAAA,EACAA,EAAAA,EALb,KAAKg6B,EAAAA,EAAAA,GAAoBh6B,EAAM6lB,YAC7B,OAGF,MAAMyqB,EAAsB,QAAftwC,EAAAA,EAAMmhB,iBAANnhB,IAAAA,GAAoB,QAApBA,EAAAA,EAAiB+C,WAAjB/C,IAAAA,OAAAA,EAAAA,EAAsBhD,EAC7BuzC,EAAsB,QAAfvwC,EAAAA,EAAMmhB,iBAANnhB,IAAAA,GAAoB,QAApBA,EAAAA,EAAiB+C,WAAjB/C,IAAAA,OAAAA,EAAAA,EAAsBwf,EAE7B3gB,GAAQ2xC,EAAAA,EAAAA,kBAAiB,CAC7B,CACE90B,MAAM40B,aAAAA,EAAAA,EAAM1kC,OAAQ,EACpB6kC,MAAMH,aAAAA,EAAAA,EAAM1kC,OAAQ,EACpB8kC,MAAMJ,aAAAA,EAAAA,EAAMzkC,KAAM,EAClB8kC,SAASL,aAAAA,EAAAA,EAAMzkC,KAAM,EACrB+kC,KAAML,aAAAA,EAAAA,EAAM3kC,KACZilC,KAAMN,aAAAA,EAAAA,EAAM1kC,GACZilC,UAAU,EACV9wB,YAAa,IACbE,UAAW,EACX6wB,UAAW,QACXhrC,MAAOoe,EAAAA,GACPne,KAAM,0BAKV,OAFAnH,EAAMrB,KAAO,SAEN,CAACqB,EACV,CA5MA,WAAAoD,CAAYjC,GACVkC,MAAM,GACJ4tB,SAAU,GACV/X,QAAS,GACT4P,aAAa,GACV3nB,IAGLZ,KAAKkF,qBAAqB,KACxBlF,KAAKyI,cACL,MAAM3J,EAAOsE,EAAAA,GAAWmC,QAAQvF,MAC1BgK,GAASjE,EAAAA,EAAAA,IAAuB/F,MAChCmD,EAAYC,EAAAA,GAAWC,aAAarD,MAE1CA,KAAKwF,MAAMC,IACT3G,EAAK4G,iBAAkBksC,I,IACQA,EAEzBA,EAyEOA,EAzEX,GAFA5xC,KAAKsB,SAAS,CAAEinB,aAAyB,QAAZqpB,EAAAA,EAAQ9yC,YAAR8yC,IAAAA,OAAAA,EAAAA,EAAchxC,SAAUC,EAAAA,aAAaG,aAElD,QAAZ4wC,EAAAA,EAAQ9yC,YAAR8yC,IAAAA,OAAAA,EAAAA,EAAchxC,SAAUC,EAAAA,aAAaM,KACvC,GACiC,IAA/BywC,EAAQ9yC,KAAKmC,OAAOC,QACc,IAAlC0wC,EAAQ9yC,KAAKmC,OAAO,GAAGC,SACvBsnB,EAAAA,EAAAA,IAAoBopB,GAEpB5xC,KAAKsB,SAAS,CACZD,MAAO,IAAIE,EAAAA,GAAgB,CACzBC,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAIC,EAAAA,EAAgB,CACxBC,QAASC,EAAAA,GACT4mB,SAAU,iBAMf,CACL,IAAIiI,EAAiC,GACrC,GAAI1wB,KAAK8wC,aAAc,C,IACEhyC,EACO8yC,EAA9B,GADAlhB,EAAWC,GAA2B,QAAf7xB,EAAAA,EAAK8B,MAAM9B,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBmC,SAAU,IAC9C+I,EAAOpJ,MAAMmhB,YAAyB,QAAZ6vB,EAAAA,EAAQ9yC,YAAR8yC,IAAAA,OAAAA,EAAAA,EAAchxC,SAAUC,EAAAA,aAAaM,KAAM,C,IAInDrC,EAAAA,EAFpB,MAAMiZ,EAAc/X,KAAKixC,yBAAyBjnC,EAAOpJ,OAErDmX,KAA+B,QAAfjZ,EAAAA,EAAK8B,MAAM9B,YAAXA,IAAAA,GAA4B,QAA5BA,EAAAA,EAAiBiZ,mBAAjBjZ,IAAAA,OAAAA,EAAAA,EAA8BoC,SAChDpC,EAAKwC,SAAS,CACZxC,KAAM,OACDA,EAAK8B,MAAM9B,MAAI,CAClBiZ,YAAaA,KAIrB,CAEA,GAAI2Y,aAAAA,EAAAA,EAAUxvB,OAAQ,C,IAyBf8I,EAxBL,MAAM,YAAE4mB,EAAW,UAAEihB,GAAchhB,EAAuBH,GACpD3O,EAAiC,CAAE3iB,KAAM,SAE/Ci7B,EAAAA,EAAAA,IAA4Br6B,MAAM4E,cAAcgsB,IAChD0J,EAAAA,EAAAA,IAAmCt6B,MAAM4E,eACvCktC,EAAAA,EAAAA,IAAkBD,EAAY,EAAGnhB,EAAU,KAG7C3O,EAAUpU,SAAW,CAAEnB,KAAMokB,EAAankB,GAAI,IAC9CsV,EAAUpe,IAAM,CACd/F,EAAG,CACD4O,KAA0C,IAApCrJ,EAAUvC,MAAM9E,MAAM0Q,KAAK4W,OACjC3W,GAAsC,IAAlCtJ,EAAUvC,MAAM9E,MAAM2Q,GAAG2W,QAE/BhD,EAAG,CAAE5T,KAAMqlC,EAAY,GAAKplC,GAAIikB,EAASxvB,OAAS,KAGpDlB,KAAKsB,SAAS,CACZqX,QAAS,CACP,IAAI43B,EAA0B,CAC5BxuB,kBAIqB,QAAtB/X,EAAAA,EAAOpJ,MAAMmhB,iBAAb/X,IAAAA,OAAAA,EAAAA,EAAwB2D,WAA4C,SAAhC3D,EAAOpJ,MAAMmhB,UAAU3iB,MAC9D4K,EAAO1I,SAAS,CAAEygB,aAEtB,CACF,CAGA/hB,KAAKsB,SAAS,CACZovB,WACArvB,MAAOrB,KAAKgoB,eAEhB,MACqB,QAAZ4pB,EAAAA,EAAQ9yC,YAAR8yC,IAAAA,OAAAA,EAAAA,EAAchxC,SAAUC,EAAAA,aAAaC,SAC9Cd,KAAKsB,SAAS,CACZD,MAAO,IAAIE,EAAAA,GAAgB,CACzBU,UAAW,SACXT,SAAU,CACR,IAAIkB,EAAAA,EAAkB,CACpBC,UAAW,KAAMC,EAAAA,EAAAA,IAAkB,aASjD5C,KAAKwF,MAAMC,IACTuE,EAAOtE,iBAAiB,CAACiD,EAAUC,K,IAC7B9J,EAAJ,IAAmB,QAAfA,EAAAA,EAAK8B,MAAM9B,YAAXA,IAAAA,OAAAA,EAAAA,EAAiB8B,SAAUC,EAAAA,aAAaM,SACrC8hB,EAAAA,EAAAA,SAAQta,EAASoZ,UAAWnZ,EAAUmZ,YAAcpZ,EAAS8d,aAAe7d,EAAU6d,aACrFzmB,KAAK8wC,aAAc,CACrB,MAAM/4B,EAAc/X,KAAKixC,yBAAyBtoC,GAClD7J,EAAKwC,SAAS,CACZxC,KAAM,OACDA,EAAK8B,MAAM9B,MAAI,CAClBiZ,YAAaA,KAGnB,MAMZ,EAkFA,EA/MWuV,EA+MG3nB,YAAY,EAAGC,YAC3B,MAAM,MAAEvE,EAAK,QAAEsX,EAAO,YAAE4P,GAAgB3iB,EAAM5K,YACtCc,MAAOsS,IAAWvF,EAAAA,EAAAA,IAAkBjD,GAAO5K,WAC7CgB,GAASC,EAAAA,EAAAA,YAAWC,GACpBmO,GAAc88B,EAAAA,EAAAA,IAAevhC,GAEnC,IAAKvE,EACH,OAGF,MAsBM8qB,EAPG,aADC/d,EAEG,qDAEA,GAMb,OACE,kBAAChR,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACF,MAAAA,CAAIC,UAAWrB,EAAOisC,iBACrB,kBAAC7qC,MAAAA,CAAIC,UAAWrB,EAAO+1C,gBACrB,kBAAC30C,MAAAA,CAAIC,UAAWrB,EAAOg2C,mBACrB,kBAACxoB,EAAAA,gBAAeA,CACdprB,KAAM,UAAUgQ,IAChBvS,QAAS,CAAC,CAAEkE,MAAO,GAAIjE,MAAO,aAC9BA,MAAO,aAET,kBAACiM,OAAAA,KAlCM,MACf,OAAQqG,GACN,IAAK,SACH,MAAO,cACT,IAAK,OACH,MAAO,YACT,IAAK,WACH,MAAO,wBACT,QACE,MAAO,KAyBIuJ,KAERwU,GAAY,kBAAC/uB,MAAAA,CAAIC,UAAWrB,EAAOmwB,UAAWA,IAEjD,kBAAC/uB,MAAAA,CAAIC,UAAWrB,EAAO2c,SACpB4P,GAAe,kBAACkB,EAAAA,EAAkBA,CAAClB,aAAa,EAAMmB,SAAU,KAChE/Q,aAAAA,EAAAA,EAAS9a,IAAK4R,GAAW,kBAACA,EAAO9J,UAAS,CAACC,MAAO6J,EAAQ5H,IAAK4H,EAAO7O,MAAMiH,SAGjF,kBAACxG,EAAMsE,UAAS,CAACC,MAAOvE,IACxB,kBAACqvC,EAAsBA,CACrBrmC,YAAaA,GAAe,GAC5BzE,MAAOA,OAOV,MAAM+qB,EAAe1vB,GACnBA,EAAOpD,IAAK4M,GAAMsT,WAAWtT,EAAExL,OAAO,GAAGb,OAAOnB,KAAK,CAACC,EAAGC,IAAMD,EAAIC,GAG/D0zB,EAA0BH,IACrC,MAAMuhB,EAAiBxoC,KAAK0S,MAAMuU,EAASxvB,OAAS,GACpD,IAAI2wC,EAAYnhB,EAASxvB,OAAS+wC,EAAiB,EAKnD,OAJIJ,EAAY,IACdA,EAAY,GAGP,CACLjhB,aAAakhB,EAAAA,EAAAA,IAAkBD,EAAY,EAAGnhB,GAC9CmhB,cAIJ,SAAS31C,EAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbG,MAAO,OACPF,QAAS,OACT2R,cAAe,SACf5H,OAAQ,aAAalC,EAAMM,OAAO4B,OAAOC,OACzC6Q,aAAc,MACd3S,WAAYL,EAAMM,OAAOD,WAAW4B,QAEpC,iBAAkB,CAChB9J,QAAS,QAEX,yBAA0B,CACxBuqB,YAAa,eAEf,cAAe,CACbxgB,OAAQ,yBAGZ4/B,iBAAiB5pC,EAAAA,EAAAA,KAAI,CACnBG,MAAO,OACPF,QAAS,OACT2R,cAAe,MACfjO,QAAS,MACToE,IAAK,MACLC,eAAgB,gBAChBC,WAAY,aACZmmB,WAAYtmB,EAAMgB,WAAW+qC,iBAE/BH,gBAAgB1zC,EAAAA,EAAAA,KAAI,CAClBC,QAAS,OACT2R,cAAe,SACf7J,IAAK,QAEP4rC,mBAAmB3zC,EAAAA,EAAAA,KAAI,CACrBC,QAAS,OACTgI,WAAY,WAEdqS,SAASta,EAAAA,EAAAA,KAAI,CACXC,QAAS,OACT8H,IAAK,MACLE,WAAY,WAEd6lB,UAAU9tB,EAAAA,EAAAA,KAAI,CACZC,QAAS,OACTqI,MAAOR,EAAMM,OAAOG,KAAKF,UACzBH,SAAU,OACVkmB,WAAY,IAEZ,QAAS,CACPtc,OAAQ,WAIhB,C,mGCnYO,MAAMgiC,EAAkB,IAClBC,EAAa,IACbC,EAAa,IACbC,EAAW,KAIlBC,GAF8B9oC,KAAK+oC,MAAML,GAEuC,CACpF,CAAEM,KAAM,IAAKC,aAJQ,MAIeC,WAAY,IAChD,CAAEF,KAAM,IAAKC,aAAcJ,EAAUK,WAAY,IACjD,CAAEF,KAAM,IAAKC,aAAcL,EAAYM,WAAY,IACnD,CAAEF,KAAM,IAAKC,aAAcN,EAAYO,WAAY,KACnD,CAAEF,KAAM,KAAMC,aAAcP,EAAiBQ,WAAY,KACzD,CAAEF,KAAM,KAAMC,aAAc,EAAGC,WAAY,OAchC7iB,EAAkBniB,IAE7B,MAAOilC,EAAaC,IAAiBC,EAAAA,EAAAA,WACnCP,EACA,EAAGG,gBAAgBv+B,IAAUA,EAAQo+B,EAAWrxC,OAAS,GAAKwxC,EAAe/kC,GAG/E,GAA+B,MAA3BilC,EAAYD,WAEd,MAAO,IAAGI,EAAAA,EAAAA,OAAOplC,EAAWilC,EAAYF,aAAc,KAAKE,EAAYH,OAGzE,IAAIO,EAAevpC,KAAK0S,MAAMxO,EAAWilC,EAAYF,cACjDO,EAAiB,EAAYJ,EAAcH,aAAgBE,EAAYD,WAC3E,MAAMO,EAAwBzpC,KAAK0pC,MAAMF,GAGrCC,IAA0BN,EAAYD,YACxCK,GAAgB,EAChBC,EAAiB,GAEjBA,EAAiBC,EAGnB,MAAME,EAAoB,GAAGJ,IAAeJ,EAAYH,OAExD,GAAuB,IAAnBQ,EACF,OAAOG,EAIT,MAAO,GAAGA,KADkB,GAAGH,IAAiBJ,EAAcJ,UAUnD12B,EAAsB,CAACF,EAA0Bw3B,EAAa,KAClE5pC,KAAK0S,MAAMN,EAAmBw3B,IAAe,EAGzCzD,EAAsB,CAAC15B,EAAoBm9B,KACtD,MAAMlwB,EAAiB/f,EAAAA,GAAWC,aAAa6S,GACzC1J,EAAO2W,EAAeviB,MAAM9E,MAAM0Q,KAAK4W,OACvC3W,EAAK0W,EAAeviB,MAAM9E,MAAM2Q,GAAG2W,OAEnCkwB,GAAM3lC,EAAAA,EAAAA,UAASlB,EAAKD,EAAM,KAEhC,MAAO,GADmBuP,EAAoBu3B,EAAI7uB,YAAa4uB,M,oKCxE1D,MAAME,UAAwB70C,EAAAA,I,EACT,EAAGkH,YAC3B,MAAM,QAAEhE,GAAYgE,EAAM5K,WAC1B,OACE,kBAACkjC,EAAAA,MAAKA,CAACn+B,MAAO,cAAeo+B,SAAU,QAASmF,cAAasG,EAAAA,EAAQ4J,YAClE5xC,K,EAJO+D,e,EADH4tC,G,oGCCN,MAAME,EAAUj0C,IACrB,MAAMxD,GAASC,EAAAA,EAAAA,YAAWC,IACpB,YAAEw3C,EAAW,oBAAEC,GAAwBn0C,EAE7C,OACE,kBAACjC,EAAAA,MAAKA,CAACF,UAAWrB,EAAO43C,aACvB,kBAACC,EAAAA,MAAKA,CACJl2C,YAAY,SACZO,OAAQ,kBAACC,EAAAA,KAAIA,CAACC,KAAM,WACpBtC,MAAO43C,EACP33C,SAAU43C,EACVpmB,GAAG,uBAMX,SAASrxB,EAAUiK,GACjB,MAAO,CACLytC,aAAav1C,EAAAA,EAAAA,KAAI,CACfkK,aAAcpC,EAAMkB,QAAQ,KAGlC,C,w5BC/BO,SAASysC,EAAer0C,GAC7B,OAAO,OACFA,GAAAA,CACHR,OAAQQ,EAAMR,OAAOpB,IAAKgZ,GAAkB,OACvCA,GAAAA,CACHva,OAAQua,EAAMva,WAGpB,C,g4BC4BO,MAAM4a,UAAwBxY,EAAAA,GAmF3Bq1C,kBAAAA,CAAmBC,GACrBA,EAAS/yC,QAAU+yC,EAAS/yC,OAAOC,OAAS,EAC9ClB,KAAKi0C,cAAcD,GAEnBh0C,KAAKY,MAAMc,KAAKJ,SAAS,CACvBE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAIC,EAAAA,EAAgB,CACxBC,QAAS,0BACTI,QAAS,aAMrB,CAEQkyC,aAAAA,CAAcp1C,EAAiB0Y,GACrC,MAAM28B,EAAcr1C,EAAKmC,OAAO1E,OAC9B,CAACC,EAAKyE,K,IACQA,EAAAA,EAAZ,MAAM4G,EAA2D,QAArD5G,EAAAA,EAAOhC,OAAOC,KAAMC,GAAMA,EAAEC,OAASgO,EAAAA,UAAUM,eAA/CzM,IAAAA,GAA8D,QAA9DA,EAAAA,EAAwD+W,cAAxD/W,IAAAA,OAAAA,EAAAA,EAAiEuW,GAC7E,OAAK3P,GAGArL,EAAIqL,KACPrL,EAAIqL,GAAO,IAEbrL,EAAIqL,GAAK9K,KAAKkE,GACPzE,GANEA,GAQX,CAAC,GAGG43C,EAAY,GAClB,IAAK,MAAMvsC,KAAOssC,EAAa,CAC7B,MAAM1wB,EAAS0wB,EAAYtsC,GAAK5K,KAAK,CAACC,EAAGC,K,IAAMD,E,OAAM,QAANA,EAAAA,EAAEkB,YAAFlB,IAAAA,OAAAA,EAAAA,EAAQgb,cAAc/a,EAAEiB,QAAU,IAC3Ei2C,EAAYP,EAAerwB,EAAO,IACxCA,EAAO9mB,MAAM,EAAG8mB,EAAOviB,QAAQmU,QAAS5V,GAAU40C,EAAUp1C,OAAOlC,KAAK0C,EAAMR,OAAO,KACrFm1C,EAAUr3C,MAAKu3C,EAAAA,EAAAA,eAAcD,EAAW,GAC1C,CACA,OAAOD,CACT,CAEQH,aAAAA,CAAcn1C,GACpB,MAAMy1C,EAA+B,GACrC,IAAI9wB,EAAS3kB,EAAKmC,OAEdjB,KAAKY,MAAM4W,UACbiM,EAASzjB,KAAKk0C,cAAcp1C,GAAMwa,EAAAA,EAAAA,IAAmBtZ,MAAMsW,iBAG7D,IAAK,IAAIk+B,EAAa,EAAGA,EAAa/wB,EAAOviB,OAAQszC,IAAc,CAMjE,GAAY,IALS/wB,EAAO+wB,GAEHv1C,OACtB2S,OAAQzS,GAAMA,EAAEC,OAASgO,EAAAA,UAAUM,QACnCnR,OAAO,CAACk4C,EAAKt1C,IAAMs1C,EAAMt1C,EAAE7C,OAAOC,OAAO,CAACm4C,EAAMn/B,IAAMm/B,GAAQn/B,GAAK,GAAI,IAAM,EAAG,GAEjF,SAGF,MAAMo/B,EAAc30C,KAAKY,MAAM6W,eAAe3Y,EAAM2kB,EAAO+wB,GAAaA,GACxED,EAAYx3C,KAAK43C,EACnB,CAEA30C,KAAKY,MAAMc,KAAKJ,SAAS,CAAEE,SAAU+yC,GACvC,CApJA,YAAmB3zC,GACjBkC,MAAMlC,GAoER,OAAQ+yC,sBAAuBiB,IAC7B50C,KAAKsB,SAAS,CAAEoyC,YAAakB,EAAIC,cAAc/4C,UAGjD,OAAQg5C,gCAA+BC,EAAAA,EAAAA,UAAUrB,I,IAIrC50C,EAHV,MAAMA,EAAOsE,EAAAA,GAAWmC,QAAQvF,MAC1Bg0C,EAAW,OACZl1C,EAAK8B,MAAM9B,MAAI,CAClBmC,OAAuB,QAAfnC,EAAAA,EAAK8B,MAAM9B,YAAXA,IAAAA,OAAAA,EAAAA,EAAiBmC,OAAO2Q,OAAOojC,EAA8BtB,MAEvE1zC,KAAK+zC,mBAAmBC,IACvB,MA7EDh0C,KAAKkF,qBAAqB,KACxB,MAAMpG,EAAOsE,EAAAA,GAAWmC,QAAQvF,MAEhCA,KAAKwF,MAAMC,IACT3G,EAAK4G,iBAAkB5G,I,IACjBA,EAA0CA,EAqBnCA,EApB4BA,EAY1BA,EAbb,IAAa,QAATA,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaM,OAAiB,QAATrC,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaG,WAC9E,GAAgC,IAA5BlC,EAAKA,KAAKmC,OAAOC,SAAyB,QAATpC,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaG,UACrEhB,KAAKY,MAAMc,KAAKJ,SAAS,CACvBE,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAM,IAAIC,EAAAA,EAAgB,CACxBC,QAASC,EAAAA,GACTC,cAAeC,EAAAA,GACfC,QAAS,mBAKZ,IAAa,QAATlD,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAaM,KAAM,C,IAGvCrC,EAFV,MAAMk1C,EAAW,OACZl1C,EAAKA,MAAI,CACZmC,OAAiB,QAATnC,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAWmC,OAAO2Q,OAAOojC,EAA8Bh1C,KAAKY,MAAM8yC,gBAE5E1zC,KAAK+zC,mBAAmBC,GACxBh0C,KAAKC,aAAa,IAAIiV,EAAAA,GAA4B,CAAEjU,OAAQnC,EAAKA,KAAKmC,UAAW,EACnF,OACK,IAAa,QAATnC,EAAAA,EAAKA,YAALA,IAAAA,OAAAA,EAAAA,EAAW8B,SAAUC,EAAAA,aAAauI,MAAO,C,IAM/BtK,EAAAA,EAAAA,EALnBkB,KAAKY,MAAMc,KAAKJ,SAAS,CACvBE,SAAU,CACR,IAAI2V,EAAAA,GAAmB,CACrB3V,SAAU,CACR,IAAI+xC,EAAgB,CAClB3xC,QAAuC,QAA9B9C,EAAgB,QAAhBA,EAAAA,EAAKA,KAAKg/B,cAAVh/B,IAAAA,GAAqB,QAArBA,EAAAA,EAAmB,UAAnBA,IAAAA,OAAAA,EAAAA,EAAuB8C,eAAvB9C,IAAAA,EAAAA,EAAkC,wCAMvD,MACEkB,KAAKY,MAAMc,KAAKJ,SAAS,CACvBE,SAAU,CACR,IAAI2V,EAAAA,GAAmB,CACrB3V,SAAU,CACR,IAAIkB,EAAAA,EAAkB,CACpBC,UAAW,IAAMC,EAAkB,cAUnD5C,KAAK0F,iBAAiB,CAACiD,EAAUC,K,IAEKD,EADhCA,EAAS+qC,cAAgB9qC,EAAU8qC,aACrC1zC,KAAK80C,6BAAiD,QAApBnsC,EAAAA,EAAS+qC,mBAAT/qC,IAAAA,EAAAA,EAAwB,MAI1D7J,EAAK8B,MAAM9B,MACbkB,KAAKi0C,cAAcn1C,EAAK8B,MAAM9B,OAGpC,EAgGF,SAAS5C,IACP,MAAO,CACLoB,WAAWe,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACT2R,cAAe,SACfoK,SAAU,IAGhB,CArBE,EAvJWnD,EAuJGvR,YAAY,EAAGC,YAC3B,MAAM,KAAElE,EAAI,YAAEgyC,GAAgB9tC,EAAM5K,WAC9BgB,GAASC,EAAAA,EAAAA,YAAWC,GAE1B,OACE,kBAACkB,MAAAA,CAAIC,UAAWrB,EAAOsB,WACrB,kBAACm2C,EAAMA,CAACC,YAAaA,QAAAA,EAAe,GAAIC,oBAAqB/tC,EAAM+tC,sBACnE,kBAACjyC,EAAKiE,UAAS,CAACC,MAAOlE,OAgBxB,MAAMkB,EAAqBqyC,IAChC,MAAMj5C,GAASC,EAAAA,EAAAA,YAAWqL,GAE1B,OACE,kBAAClK,MAAAA,CAAIC,UAAWrB,EAAOsB,WACpB,IAAImK,MAAMwtC,IAASp3C,IAAI,CAAC6J,EAAGC,IAC1B,kBAACvK,MAAAA,CAAIC,UAAWrB,EAAOk5C,cAAertC,IAAKF,GACzC,kBAACvK,MAAAA,CAAIC,UAAWrB,EAAOgK,QACrB,kBAAC5I,MAAAA,CAAIC,UAAWrB,EAAO+D,OACrB,kBAACwH,EAAAA,EAAQA,CAACC,MAAO,KAEnB,kBAACpK,MAAAA,CAAIC,UAAWrB,EAAOyT,QACrB,kBAAClI,EAAAA,EAAQA,CAACC,MAAO,MAGrB,kBAACpK,MAAAA,CAAIC,UAAWrB,EAAOm5C,OACpB,IAAI1tC,MAAM,IAAI5J,IAAI,CAAC6J,EAAGC,IACrB,kBAACvK,MAAAA,CAAIC,UAAWrB,EAAOo5C,UAAWvtC,IAAKF,GACrC,kBAACJ,EAAAA,EAAQA,CAACC,MAAO,OAIvB,kBAACpK,MAAAA,CAAIC,UAAWrB,EAAOq5C,OACpB,IAAI5tC,MAAM,IAAI5J,IAAI,CAAC6J,EAAGC,IACrB,kBAACvK,MAAAA,CAAIC,UAAWrB,EAAOs5C,UAAWztC,IAAKF,GACrC,kBAACJ,EAAAA,EAAQA,CAACC,MAAO,WAUjC,SAASF,EAAkBnB,GACzB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACTi3C,oBAAqBl+B,EAAAA,GACrBm+B,aAAc,QACd1c,OAAQ3yB,EAAMkB,QAAQ,GACtBwxB,UAAW1yB,EAAMkB,QAAQ,KAE3B6tC,eAAe72C,EAAAA,EAAAA,KAAI,CACjB8J,gBAAiBhC,EAAMM,OAAOD,WAAW4B,QACzCC,OAAQ,aAAalC,EAAMM,OAAOD,WAAWE,YAC7C1E,QAAS,QAEXgE,QAAQ3H,EAAAA,EAAAA,KAAI,CACVC,QAAS,OACT+H,eAAgB,kBAElBtG,OAAO1B,EAAAA,EAAAA,KAAI,CACTG,MAAO,UAETiR,QAAQpR,EAAAA,EAAAA,KAAI,CACVG,MAAO,SAET22C,OAAO92C,EAAAA,EAAAA,KAAI,CACTC,QAAS,OACT2R,cAAe,SACf5J,eAAgB,eAChB62B,UAAW,SAEbkY,WAAW/2C,EAAAA,EAAAA,KAAI,CACbG,MAAO,OACPyJ,OAAQ,SAEVotC,OAAOh3C,EAAAA,EAAAA,KAAI,CACTC,QAAS,OACT+H,eAAgB,iBAElBivC,WAAWj3C,EAAAA,EAAAA,KAAI,CACbG,MAAO,SAGb,CAEO,MAAMw2C,EAAiCtB,GAA0B+B,IACtE,MAAMC,EAAUhC,aAAAA,EAAAA,EAAal3B,OAC7B,IAAKk5B,EACH,OAAO,EAGT,MAAMC,EAAQ,IAAIC,OAAOF,EAAS,KAElC,OAAOD,EAAUx2C,OAAO8T,KAAM5T,KAAQA,EAAE6Y,QAAiB3b,OAAOC,OAAO6C,EAAE6Y,QAAQ9Y,KAAMxC,GAAUi5C,EAAM1d,KAAKv7B,K,mGCtSvG,SAASq0C,EAAqB76B,EAAoBwa,GACvD,MAAM1mB,GAASjE,EAAAA,EAAAA,IAAuBmQ,GAChC7U,EAAQinB,IACXlmB,gBAAe,GAEfyK,UAAU,gBAAiB,MAC3BpK,QAwCH,OAvCApB,EAAMC,SAAS,CACbu0C,mBAAoB,CAAChhC,EAAUklB,KAG7BA,EAAQ+b,cAAiBC,I,IAUHC,EACFA,EAQeD,EACHA,EAnB9B,GAAoB,IAAhBA,EAAK70C,OAEP,YADA8I,EAAO1I,SAAS,CAAEygB,eAAWlK,IAG/B,MAAMm+B,EAAeD,EAAK,GAEpB5qB,EAAoC,CAAE/rB,KAAM,SAAUuE,IAAKqyC,GAQjE,GANA7qB,EAAahoB,UAAY,CACvBqJ,KAAM/C,KAAK0pC,QAAqB,QAAd6C,EAAAA,EAAap4C,SAAbo4C,IAAAA,OAAAA,EAAAA,EAAgBxpC,OAAQ,GAAK,KAC/CC,GAAIhD,KAAK0pC,QAAqB,QAAd6C,EAAAA,EAAap4C,SAAbo4C,IAAAA,OAAAA,EAAAA,EAAgBvpC,KAAM,GAAK,MAIzC0e,EAAahoB,UAAUqJ,OAAS2e,EAAahoB,UAAUsJ,GACzD,OAGF,MAAMwpC,EAAQnE,IAA4B,QAATiE,EAAAA,EAAK,GAAG31B,SAAR21B,IAAAA,OAAAA,EAAAA,EAAWvpC,OAAQ,GAAK,EAAGkkB,GACtDwlB,EAAMpE,GAA2B,QAATiE,EAAAA,EAAK,GAAG31B,SAAR21B,IAAAA,OAAAA,EAAAA,EAAWtpC,KAAM,EAAGikB,GAClDvF,EAAaxd,SAAW,CAAEnB,KAAMypC,EAAOxpC,GAAIypC,GAE3ClsC,EAAOohB,sBAAsBD,IACxByP,EAAAA,EAAAA,GAAoB5wB,EAAOpJ,MAAM6lB,aACpCzc,EAAOqX,cAAc,eAGvBxc,EAAAA,EAAAA,IAAqBC,EAAAA,GAAkBC,eAAgBC,EAAAA,GAAoBD,eAAesqC,oBAAqB,CAC7GttB,UAAWoJ,EACX/c,OAAQ,iBAKT,IAAI7M,EAAAA,GAAgB,CACzBU,UAAW,MACXT,SAAU,CACR,IAAIC,EAAAA,GAAc,CAChBC,KAAML,MAId,CAEO,MAAMinB,EAAuB,IAC3BpmB,EAAAA,GAAci0C,UAClBtpC,UAAU,SAAU,CAAEupC,MAAM,IAC5BvpC,UAAU,QAAS,CAClB4lC,KAAM,IACN4D,UAAW,aAEZxpC,UAAU,QAAS,CAClBypC,OAAQ,QACRC,MAAO,KAER1pC,UAAU,YAAa,CAAE/Q,MAAO,UAG9B,SAASg2C,EAAkB0E,EAAgBx6B,EAAoBy6B,GACpE,IAAKz6B,EACH,MAAO,GAET,GAAIw6B,EAAS,EACX,MAAO,IAGT,MAAME,EAAW16B,EAAQvS,KAAK0S,MAAMq6B,KAAYC,GAAc,GAC9D,OAAKC,GAAYj3B,MAAMi3B,GACd,GAELA,GAAY,EACP,GAAGA,EAAS7pB,QAAQ,MAEtB,IAAe,IAAX6pB,GAAiB7pB,QAAQ,MACtC,C,+CC3FO,MAAM+c,EAAU,CACrB0C,WAAY,0BACZkH,WAAY,0BACZ3J,aAAc,4B,qECMT,SAASzxB,GAAqB,OAAEhK,EAAM,WAAEiI,EAAU,aAAEgC,EAAY,OAAEyP,GAAS,IAEhF,IAAIhZ,EAAU,GAAGJ,EAAAA,KAEF,WAAXN,IACFU,GAAW,oBAGTuJ,IACFvJ,GAAW,OAAOuJ,KAGhBhC,GAAcA,IAAepD,EAAAA,KAC/BnE,GAAW,OAAOuH,YAIpB,IAAIsgC,EAAW,SACf,OAAQvoC,GACN,IAAK,SACHuoC,EAAW,SACX,MACF,IAAK,WACHA,EAAW,oCAKf,IAAIC,EAAe,GACfvgC,GAAcA,IAAepD,EAAAA,IAC/B2jC,EAAa75C,KAAKsZ,GAOpB,MAAO,IAAIvH,QAAc6nC,KAJTC,EAAa11C,OAAS,MAAM01C,EAAa94C,KAAK,SAAW,KAEvDgqB,EAAS,qBAAuB,IAGpD,CAEO,SAAS1R,EAAqBva,GACnC,MAAO,CACLgI,MAAO,IACPE,MAAOqU,EAAqBvc,GAC5BiI,UAAW,UACX6K,UAAW,QACXC,MAAO,IACPC,KAAM,GACNC,QAAS,GAEb,C,2mBC3CO,MAAM+nC,UAA+Bn4C,EAAAA,GASlC+J,WAAAA,GACN,MAAM,MAAEhJ,GAAUO,KAAKY,MACvBZ,KAAKsB,SAAS,MAAKsiB,EAAAA,EAAAA,GAAyBnkB,KAE5CO,KAAKwF,MAAMC,IACTzF,KAAK0F,iBAAiB,CAACiD,EAAUC,KAC/B,GAAID,EAASlJ,QAAUmJ,EAAUnJ,MAAO,CACtC,MAAM,MAAEA,GAAUkJ,EAClB3I,KAAKsB,SAAS,MAAKsiB,EAAAA,EAAAA,GAAyBnkB,IAC9C,IAGN,CAEQq3C,YAAAA,GACN,OAAO92C,KAAKY,MAAMnB,MAAMrB,IAC1B,CAEQuG,QAAAA,GACN,MAAMoyC,EAAa/2C,KAAKY,MAAMnB,MAAMR,OAAOC,KAAMC,GAAiB,UAAXA,EAAEf,MACzD,OAAO24C,aAAAA,EAAAA,EAAYz6C,OAAO0D,KAAKY,MAAMgtC,oBAAsB,EAC7D,CAEQoJ,cAAAA,GACN,MAAMvyC,GAAW0M,EAAAA,EAAAA,IAAmBnR,MAC9BiS,EAAYjS,KAAK82C,eACnB7kC,IACF2b,EAAAA,EAAAA,IAAanpB,EAAUwN,EAAWjS,KAAK2E,WAE3C,CArCA,WAAA9B,CAAYjC,GACVkC,MAAM,KACDlC,IAGLZ,KAAKkF,qBAAqB,IAAMlF,KAAKyI,cACvC,EAwEF,SAASvM,EAAUiK,GACjB,MAAO,CACL7I,WAAWe,EAAAA,EAAAA,KAAI,CACbC,QAAS,OACT2R,cAAe,SACfoK,SAAU,EACVpS,OAAQ,SAEVgvC,qBAAqB54C,EAAAA,EAAAA,KAAI,CACvBC,QAAS,OACT2R,cAAe,SACfoK,SAAU,EACVhS,OAAQ,aAAalC,EAAMM,OAAOC,UAAU2B,SAC5C7B,WAAYL,EAAMM,OAAOD,WAAW4B,QACpCpG,QAAS,MACTuG,aAAcpC,EAAMkB,QAAQ,GAC5Bd,SAAU,OACV0B,OAAQ,UAEVivC,iBAAiB74C,EAAAA,EAAAA,KAAI,CACnBkI,SAAU,OACVkmB,WAAY,OACZ0E,UAAW,WAEbr1B,OAAOuC,EAAAA,EAAAA,KAAI,CACT8yB,UAAW,SACXxqB,MAAOR,EAAMM,OAAOC,UAAUE,KAC9BuwC,SAAU,SACVC,WAAY,SACZpwC,SAAU,SACVC,aAAc,aAEhBlH,OAAO1B,EAAAA,EAAAA,KAAI,CACTouB,WAAY,MAGlB,CA3EE,EAxCWoqB,EAwCGlxC,YAAY,EAAGC,YAC3B,MAAM,cAAEme,EAAa,mBAAE6pB,EAAkB,MAAEvsC,GAAUuE,EAAM5K,WACrDgB,GAASC,EAAAA,EAAAA,YAAWC,GACpBJ,EAAQ8J,EAAMjB,W,IACRiB,EAAZ,MAAMiC,EAA4B,QAAtBjC,EAAAA,EAAMhF,MAAMnB,MAAMrB,YAAlBwH,IAAAA,EAAAA,EAA0B,GAChCyxC,GAAexpB,EAAAA,EAAAA,KAAmB1c,EAAAA,EAAAA,IAAmBvL,GAAQiC,EAAK/L,EAAMoW,QAAQ,KAAM,KAE5F,OACE,kBAAC9U,MAAAA,CAAIC,UAAWrB,EAAOsB,WACpB,kBAAC+D,EAAMsE,UAAS,CAACC,MAAOvE,IACzB,kBAACjE,MAAAA,CAAIC,UAAWrB,EAAOi7C,0BACFp/B,IAAlBkM,QAAsDlM,IAAvB+1B,GAC9B,oCACE,kBAACr+B,EAAAA,MAAKA,CAACnJ,IAAK,EAAGC,eAAgB,gBAAiBC,WAAY,UAC1D,kBAAClJ,MAAAA,CAAIC,UAAWrB,EAAO+D,OAAO,uBAC5Bs3C,GACA,kBAACx0B,EAAAA,OAAMA,CACLtiB,KAAK,KACLrF,QAAQ,UACRC,KAAM,cACNwU,KAAK,OACLvU,QAAS,IAAMwK,EAAMoxC,kBACtB,mBAKL,kBAAC55C,MAAAA,CAAIC,UAAWrB,EAAOk7C,kBACO,IAA1BztC,KAAKqa,IAAIC,IAAsB8I,QAA0B,IAAlB9I,EAAsB,EAAI,GAAG,KAExE,kBAAC3mB,MAAAA,CAAIC,UAAWrB,EAAOF,OAAQA,Q,84BC9EtC,MAAM6oB,EAAgB,YAChBI,EAAiB,UAEvB,SAASX,EACdjO,EACA/H,GAIA,OAAO,IAAI8I,EAAAA,GAAgB,CACzBxV,KAAM,IAAIyV,EAAAA,GAAmB,CAC3BC,gBAAiBC,EAAAA,GACjBC,SAAU,QACV9V,SAAU,KAEZiW,eAAgBA,EAR+B,CAAC,EAQT6/B,EAAcnhC,EAAW/H,IAEpE,CAEA,MAAMkpC,EAAgBv4C,GACbA,EAAGX,MAAQ,oBAGpB,SAASqZ,EACPlB,EACAoB,EACAxB,EACA/H,GAEA,MAAO,CAACtP,EAAiBW,KACvB,MAAMmY,EAAmBnY,EAAMrB,KAAOmY,EAAO9W,EAAMrB,WAAQyZ,EAErDC,EAAW,IAAI/K,EAAAA,GAAc,CACjCjO,KAAM,OACDA,GAAAA,CACHmC,OAAQ,CACN,KACKxB,QAMX,GAAImY,EAAkB,CACpB,MAAMlW,EAAOkW,EAAiBhX,MAAMc,KAGpC,OAFAA,EAAKJ,SAAS,CAAE7B,UAChBiC,EAAKd,MAAMS,MAAMC,SAAS,CAAE6D,MAAO2S,IAC5BF,CACT,CAEA,MAAMvW,EAAQshB,EAAevU,GAAQxB,SAAS+K,EAASlY,IAAQqN,QAAQgL,GAEjEa,EAAUxC,EAAU1W,GACtBkZ,GACFtX,EAAMuX,iBAAiBD,GAGzB,MAAME,EAAW,IAAIC,EAAAA,GAAiB,CACpCpX,KAAM,IAAIm1C,EAAuB,CAAEp3C,QAAO4B,MAAOA,EAAMoB,YAMzD,OAJIhD,EAAMrB,OACRmY,EAAO9W,EAAMrB,MAAQya,GAGhBA,EAEX,CAEO,SAAS8J,EAAevU,GAC7B,OAAOlM,EAAAA,GAAcq1C,WAClB1qC,UAAU,SAAU,CAAE0H,YAAY,IAClC1H,UAAU,UAAW,CAAE2H,KAAMC,EAAAA,GAAmBC,QAChD8iC,OAAO,GACPn1C,aAAc+tC,IACbA,EAAU7tC,oBAAoB,SAASC,0BAA0B,gBAAiBi1C,EAAAA,cAAcC,QAChGtH,EACG7tC,oBAAoB,YACpB+tC,cAAc,CACb97B,KAAM,QACN6T,WAAuB,aAAXja,EAAwBuW,EAAgB,oBAErDgzB,aAAa,eAChBvH,EACG7tC,oBAAoB,aACpB+tC,cAAc,CACb97B,KAAM,QACN6T,WAAuB,aAAXja,EAAwB2W,EAAiB,kBAEtD4yB,aAAa,gBAEtB,C", "sources": ["webpack://grafana-exploretraces-app/./components/Explore/actions/ShareExplorationAction.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Spans/SpanListColumnsSelector.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Spans/SpanListScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Spans/SpansScene.tsx", "webpack://grafana-exploretraces-app/./utils/trace-merge/utils.ts", "webpack://grafana-exploretraces-app/./utils/trace-merge/tree-node.ts", "webpack://grafana-exploretraces-app/./utils/trace-merge/merge.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Structure/StructureScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/GroupBySelector.tsx", "webpack://grafana-exploretraces-app/./components/Explore/LayoutSwitcher.tsx", "webpack://grafana-exploretraces-app/./components/Explore/panels/linesPanel.ts", "webpack://grafana-exploretraces-app/./components/Explore/behaviors/syncYaxis.tsx", "webpack://grafana-exploretraces-app/./components/Explore/layouts/attributeBreakdown.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Breakdown/AttributesDescription.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Breakdown/AttributesBreakdownScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Breakdown/BreakdownScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Exceptions/ExceptionUtils.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Exceptions/ExceptionsScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/queries/exceptions.ts", "webpack://grafana-exploretraces-app/./components/Explore/queries/comparisonQuery.ts", "webpack://grafana-exploretraces-app/./components/Explore/layouts/attributeComparison.ts", "webpack://grafana-exploretraces-app/./components/Explore/actions/InspectAttributeAction.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Comparison/AttributesComparisonScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/Comparison/ComparisonScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/Tabs/TabsBarScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/MiniREDPanel.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/TracesByServiceScene.tsx", "webpack://grafana-exploretraces-app/./components/states/EmptyState/EmptyStateScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/actions/AddToFiltersAction.tsx", "webpack://grafana-exploretraces-app/./components/Explore/queries/histogram.ts", "webpack://grafana-exploretraces-app/./components/Home/AttributePanelRow.tsx", "webpack://grafana-exploretraces-app/./components/Home/ErroredServicesRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/SlowestTracesRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/SlowestServicesRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanelRows.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanelScene.tsx", "webpack://grafana-exploretraces-app/./components/Home/AttributePanel.tsx", "webpack://grafana-exploretraces-app/./utils/rockets.tsx", "webpack://grafana-exploretraces-app/./pages/Home/bookmarks/utils.ts", "webpack://grafana-exploretraces-app/./pages/Home/bookmarks/BookmarkItem.tsx", "webpack://grafana-exploretraces-app/./pages/Home/bookmarks/Bookmarks.tsx", "webpack://grafana-exploretraces-app/./components/Home/HeaderScene.tsx", "webpack://grafana-exploretraces-app/./pages/Home/utils.ts", "webpack://grafana-exploretraces-app/./pages/Home/Home.tsx", "webpack://grafana-exploretraces-app/./utils/utils.ts", "webpack://grafana-exploretraces-app/../node_modules/moment/locale/ sync ^\\.\\/.*$", "webpack://grafana-exploretraces-app/./components/Explore/actions/AddToInvestigationButton.tsx", "webpack://grafana-exploretraces-app/./components/Explore/panels/TraceViewPanelScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/TraceDrawerScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TraceQLIssueDetector.tsx", "webpack://grafana-exploretraces-app/./addedComponents/EntityAssertionsWidget/EntityAssertionsWidget.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/Drawer.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/SmartDrawer.tsx", "webpack://grafana-exploretraces-app/./utils/filters-renderer.ts", "webpack://grafana-exploretraces-app/./pages/Explore/AttributeFiltersVariable.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/TraceExploration.tsx", "webpack://grafana-exploretraces-app/./components/states/LoadingState/LoadingStateScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/StreamingIndicator.tsx", "webpack://grafana-exploretraces-app/./utils/exemplars.ts", "webpack://grafana-exploretraces-app/./components/states/EmptyState/useMousePosition.ts", "webpack://grafana-exploretraces-app/./components/states/EmptyState/GrotNotFound.tsx", "webpack://grafana-exploretraces-app/./components/states/EmptyState/EmptyState.tsx", "webpack://grafana-exploretraces-app/./pages/Explore/primary-signals.ts", "webpack://grafana-exploretraces-app/./components/Explore/panels/PanelMenu.tsx", "webpack://grafana-exploretraces-app/./utils/comparison.ts", "webpack://grafana-exploretraces-app/./pages/Explore/PrimarySignalVariable.tsx", "webpack://grafana-exploretraces-app/./utils/analytics.ts", "webpack://grafana-exploretraces-app/./components/Explore/queries/StepQueryRunner.ts", "webpack://grafana-exploretraces-app/./components/Explore/panels/barsPanel.ts", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/DurationComparisonControl.tsx", "webpack://grafana-exploretraces-app/./addedComponents/InsightsTimelineWidget/InsightsTimelineWidget.tsx", "webpack://grafana-exploretraces-app/./components/Explore/TracesByService/REDPanel.tsx", "webpack://grafana-exploretraces-app/./utils/dates.ts", "webpack://grafana-exploretraces-app/./components/states/ErrorState/ErrorStateScene.tsx", "webpack://grafana-exploretraces-app/./components/Explore/Search.tsx", "webpack://grafana-exploretraces-app/./utils/frames.ts", "webpack://grafana-exploretraces-app/./components/Explore/ByFrameRepeater.tsx", "webpack://grafana-exploretraces-app/./components/Explore/panels/histogram.ts", "webpack://grafana-exploretraces-app/./utils/testIds.ts", "webpack://grafana-exploretraces-app/./components/Explore/queries/generateMetricsQuery.ts", "webpack://grafana-exploretraces-app/./components/Explore/layouts/HighestDifferencePanel.tsx", "webpack://grafana-exploretraces-app/./components/Explore/layouts/allComparison.ts"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useLocation } from 'react-use';\n\nimport { ToolbarButton } from '@grafana/ui';\n\nimport { TraceExploration } from '../../../pages/Explore';\nimport { getUrlForExploration } from '../../../utils/utils';\n\ninterface ShareExplorationActionState {\n  exploration: TraceExploration;\n}\n\nexport const ShareExplorationAction = ({ exploration }: ShareExplorationActionState) => {\n  const { origin } = useLocation();\n  const [tooltip, setTooltip] = useState('Copy url');\n\n  const onShare = () => {\n    if (navigator.clipboard) {\n      navigator.clipboard.writeText(origin + getUrlForExploration(exploration));\n      setTooltip('Copied!');\n      setTimeout(() => {\n        setTooltip('Copy url');\n      }, 2000);\n    }\n  };\n\n  return <ToolbarButton variant={'canvas'} icon={'share-alt'} tooltip={tooltip} onClick={onShare} />;\n};\n", "import React, { useMemo } from 'react';\n\nimport { SelectableValue } from '@grafana/data';\nimport { Icon, Select, Field, useStyles2 } from '@grafana/ui';\nimport { VariableValue } from '@grafana/scenes';\nimport { css } from '@emotion/css';\n\nconst RECOMMENDED_ATTRIBUTES = [\n  'span.http.method', \n  'span.http.request.method', \n  'span.http.route', \n  'span.http.path', \n  'span.http.status_code', \n  'span.http.response.status_code'\n]; \n\ntype Props = {\n  options: Array<SelectableValue<string>>;\n  onChange: (columns: string[]) => void;\n  value?: VariableValue;\n};\n\nconst labelOrder = ['Recommended', 'Resource', 'Span', 'Other'];\n\nexport function SpanListColumnsSelector({ options, value, onChange }: Props) {\n  const styles = useStyles2(getStyles);\n\n  const opt = useMemo(\n    () =>\n      Object.values(\n        options.reduce((acc, curr) => {\n          if (curr.label) {\n            const label = curr.label.slice(curr.label.indexOf('.') + 1);\n\n            // use text until first dot as key\n            if (RECOMMENDED_ATTRIBUTES.includes(curr.label)) {\n              const group = acc['recommended'] ?? { label: 'Recommended', options: [] };\n              group.options.push({ ...curr, label });\n              acc['recommended'] = group;\n            } else if (curr.label.startsWith('resource.')) {\n              const group = acc['resource'] ?? { label: 'Resource', options: [] };\n              group.options.push({ ...curr, label });\n              acc['resource'] = group;\n            } else {\n              if (curr.label.startsWith('span.')) {\n                const group = acc['span'] ?? { label: 'Span', options: [] };\n                group.options.push({ ...curr, label });\n                acc['span'] = group;\n              } else {\n                const group = acc['other'] ?? { label: 'Other', options: [] };\n                group.options.push(curr);\n                acc['other'] = group;\n              }\n            }\n          }\n          return acc;\n        }, {})\n      ).sort((a, b) => labelOrder.indexOf(a.label) - labelOrder.indexOf(b.label)),\n    [options]\n  );\n\n  return (\n    <div className={styles.container}>\n      <Field label=\"Add extra columns\">\n        <Select\n          value={value?.toString() !== '' ? (value?.toString()?.split(',') ?? '') : ''}\n          placeholder={'Select an attribute'}\n          options={opt}\n          onChange={(x) => onChange(x.map((x: SelectableValue) => x.value).join(','))}\n          isMulti={true}\n          isClearable\n          virtualized\n          prefix={<Icon name=\"columns\" />}\n        />\n      </Field>\n    </div>\n  );\n}\n\nconst getStyles = () => {\n  return {\n    container: css({\n      display: 'flex',\n      minWidth: '300px',\n\n      '& > div': {\n        width: '100%',\n      },\n    }),\n  };\n};\n", "import React from 'react';\n\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { DataFrame, GrafanaTheme2, LoadingState, PanelData, toURLRange, urlUtil, toOption } from '@grafana/data';\nimport { config } from '@grafana/runtime';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { css } from '@emotion/css';\nimport Skeleton from 'react-loading-skeleton';\nimport { Icon, Link, TableCellDisplayMode, TableCustomCellOptions, useStyles2, useTheme2 } from '@grafana/ui';\nimport { map, Observable } from 'rxjs';\nimport {\n  getDataSource,\n  getSpanListColumnsVariable,\n  getTraceByServiceScene,\n  getTraceExplorationScene,\n} from '../../../../../utils/utils';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n  EventTraceOpened,\n} from '../../../../../utils/shared';\nimport { SpanListColumnsSelector } from './SpanListColumnsSelector';\nimport { reportAppInteraction, USER_EVENTS_PAGES, USER_EVENTS_ACTIONS } from 'utils/analytics';\n\nexport interface SpanListSceneState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  dataState: 'empty' | 'loading' | 'done';\n}\n\nexport class SpanListScene extends SceneObjectBase<SpanListSceneState> {\n  constructor(state: Partial<SpanListSceneState>) {\n    super({\n      dataState: 'empty',\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      this.setState({\n        $data: new SceneDataTransformer({\n          transformations: this.setupTransformations(),\n        }),\n      });\n      const sceneData = sceneGraph.getData(this);\n\n      this.updatePanel(sceneData.state.data);\n      this._subs.add(\n        sceneData.subscribeToState((data) => {\n          this.updatePanel(data.data);\n        })\n      );\n    });\n  }\n\n  private setupTransformations() {\n    return [\n      () => (source: Observable<DataFrame[]>) => {\n        return source.pipe(\n          map((data: DataFrame[]) => {\n            return data.map((df) => {\n              const fields = df.fields;\n              const nameField = fields.find((f) => f.name === 'traceName');\n\n              const options: TableCustomCellOptions = {\n                type: TableCellDisplayMode.Custom,\n                cellComponent: (props) => {\n                  const data = props.frame;\n                  const traceIdField = data?.fields.find((f) => f.name === 'traceIdHidden');\n                  const spanIdField = data?.fields.find((f) => f.name === 'spanID');\n                  const traceId = traceIdField?.values[props.rowIndex];\n                  const spanId = spanIdField?.values[props.rowIndex];\n\n                  if (!traceId) {\n                    return props.value as string;\n                  }\n\n                  const name = props.value ? (props.value as string) : '<name not yet available>';\n                  return (\n                    <div className={'cell-link-wrapper'}>\n                      <div\n                        className={'cell-link'}\n                        title={name}\n                        onClick={() => {\n                          this.publishEvent(new EventTraceOpened({ traceId, spanId }), true);\n                        }}\n                      >\n                        {name}\n                      </div>\n                      <Link href={this.getLinkToExplore(traceId, spanId)} target={'_blank'} title={'Open in new tab'}>\n                        <Icon name={'external-link-alt'} size={'sm'} />\n                      </Link>\n                    </div>\n                  );\n                },\n              };\n              if (nameField?.config?.custom) {\n                nameField.config.custom.cellOptions = options;\n              }\n              return {\n                ...df,\n                fields,\n              };\n            });\n          })\n        );\n      },\n    ];\n  }\n\n  private getLinkToExplore = (traceId: string, spanId: string) => {\n    const traceExplorationScene = getTraceExplorationScene(this);\n    const datasource = getDataSource(traceExplorationScene);\n\n    const timeRange = sceneGraph.getTimeRange(this).state.value;\n    const exploreState = JSON.stringify({\n      ['explore-traces']: {\n        range: toURLRange(timeRange.raw),\n        queries: [{ refId: 'traceId', queryType: 'traceql', query: traceId, datasource }],\n        panelsState: {\n          trace: {\n            spanId,\n          },\n        },\n        datasource,\n      },\n    });\n    const subUrl = config.appSubUrl ?? '';\n    return urlUtil.renderUrl(`${subUrl}/explore`, { panes: exploreState, schemaVersion: 1 });\n  };\n\n  private updatePanel(data?: PanelData) {\n    if (\n      data?.state === LoadingState.Loading ||\n      data?.state === LoadingState.NotStarted ||\n      !data?.state ||\n      (data?.state === LoadingState.Streaming && !data.series?.[0]?.length)\n    ) {\n      if (this.state.dataState === 'loading') {\n        return;\n      }\n      this.setState({\n        dataState: 'loading',\n        panel: new SceneFlexLayout({\n          direction: 'row',\n          children: [\n            new LoadingStateScene({\n              component: SkeletonComponent,\n            }),\n          ],\n        }),\n      });\n      return;\n    }\n    if (data?.state === LoadingState.Done || data?.state === LoadingState.Streaming) {\n      if (data.series.length === 0 || data.series[0].length === 0) {\n        if (this.state.dataState === 'empty' && this.state.panel) {\n          return;\n        }\n        this.setState({\n          dataState: 'empty',\n          panel: new SceneFlexLayout({\n            children: [\n              new SceneFlexItem({\n                body: new EmptyStateScene({\n                  message: EMPTY_STATE_ERROR_MESSAGE,\n                  remedyMessage: EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n                  padding: '32px',\n                }),\n              }),\n            ],\n          }),\n        });\n      } else if (this.state.dataState !== 'done') {\n        this.setState({\n          dataState: 'done',\n          panel: new SceneFlexLayout({\n            direction: 'row',\n            children: [\n              new SceneFlexItem({\n                body: PanelBuilders.table()\n                  .setHoverHeader(true)\n                  .setOverrides((builder) => {\n                    return builder\n                      .matchFieldsWithName('spanID')\n                      .overrideCustomFieldConfig('hidden', true)\n                      .matchFieldsWithName('traceService')\n                      .overrideCustomFieldConfig('width', 350)\n                      .matchFieldsWithName('traceName')\n                      .overrideCustomFieldConfig('width', 350);\n                  })\n                  .build(),\n              }),\n            ],\n          }),\n        });\n      }\n    }\n  }\n\n  public onChange = (columns: string[]) => {\n    const variable = getSpanListColumnsVariable(this);\n    if (variable.getValue() !== columns) {\n      variable.changeValueTo(columns);\n\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.span_list_columns_changed,\n        {\n          columns,\n        }\n      );\n    }\n  };\n\n  public static Component = ({ model }: SceneComponentProps<SpanListScene>) => {\n    const { panel } = model.useState();\n    const styles = getStyles(useTheme2());\n    const variable = getSpanListColumnsVariable(model);\n    const { attributes } = getTraceByServiceScene(model).useState();\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.header}>\n          <div className={styles.description}>View a list of spans for the current set of filters.</div>\n          <SpanListColumnsSelector\n            options={attributes?.map((x) => toOption(x)) ?? []}\n            value={variable.getValue()}\n            onChange={model.onChange}\n          />\n        </div>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    container: css({\n      display: 'contents',\n\n      '[role=\"cell\"] > div': {\n        display: 'flex',\n        width: '100%',\n      },\n\n      '.cell-link-wrapper': {\n        display: 'flex',\n        gap: '4px',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        width: '100%',\n\n        a: {\n          padding: 4,\n          fontSize: 0,\n\n          ':hover': {\n            background: theme.colors.background.secondary,\n          },\n        },\n      },\n\n      '.cell-link': {\n        color: theme.colors.text.link,\n        cursor: 'pointer',\n        maxWidth: '300px',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n\n        ':hover': {\n          textDecoration: 'underline',\n        },\n      },\n    }),\n    description: css({\n      fontSize: theme.typography.h6.fontSize,\n      padding: `${theme.spacing(1)} 0 ${theme.spacing(2)} 0`,\n    }),\n    header: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start',\n      gap: '10px',\n    }),\n  };\n};\n\nconst SkeletonComponent = () => {\n  const styles = useStyles2(getSkeletonStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.title}>\n        <Skeleton count={1} width={80} />\n      </div>\n      {[...Array(3)].map((_, i) => (\n        <div className={styles.row} key={i}>\n          {[...Array(6)].map((_, j) => (\n            <span className={styles.rowItem} key={j}>\n              <Skeleton count={1} />\n            </span>\n          ))}\n        </div>\n      ))}\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      height: '100%',\n      width: '100%',\n      position: 'absolute',\n      backgroundColor: theme.colors.background.primary,\n      border: `1px solid ${theme.colors.border.weak}`,\n      padding: '5px',\n    }),\n    title: css({\n      marginBottom: '20px',\n    }),\n    row: css({\n      marginBottom: '5px',\n      display: 'flex',\n      justifyContent: 'space-around',\n    }),\n    rowItem: css({\n      width: '14%',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport { SceneComponentProps, SceneFlexItem, SceneObject, SceneObjectBase, SceneObjectState } from '@grafana/scenes';\nimport { SpanListScene } from 'components/Explore/TracesByService/Tabs/Spans/SpanListScene';\nimport { getMetricVariable, getTraceByServiceScene } from 'utils/utils';\n\nexport interface SpansSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class SpansScene extends SceneObjectBase<SpansSceneState> {\n  constructor(state: Partial<SpansSceneState>) {\n    super({ ...state });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    this._subs.add(\n      getTraceByServiceScene(this).state.$data?.subscribeToState(() => {\n        this.updateBody();\n      })\n    );\n\n    this._subs.add(\n      getTraceByServiceScene(this).subscribeToState((newState, prevState) => {\n        if (newState.$data?.state.key !== prevState.$data?.state.key) {\n          this.updateBody();\n        }\n      })\n    );\n\n    this._subs.add(\n      getMetricVariable(this).subscribeToState((newState, prevState) => {\n        if (newState.value !== prevState.value) {\n          this.updateBody();\n        }\n      })\n    );\n\n    this.updateBody();\n  }\n\n  private updateBody() {\n    this.setState({ body: new SpanListScene({}) });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<SpansScene>) => {\n    const { body } = model.useState();\n    return body && <body.Component model={body} />;\n  };\n}\n\nexport function buildSpansScene() {\n  return new SceneFlexItem({\n    body: new SpansScene({}),\n  });\n}\n", "import { Span } from '../../types';\n\nexport function nestedSetLeft(span: Span): number {\n  if (span.attributes) {\n    for (const a of span.attributes) {\n      if (a.key === 'nestedSetLeft') {\n        return parseInt(a.value.intValue || a.value.Value?.int_value || '0', 10);\n      }\n    }\n  }\n\n  throw new Error('nestedSetLeft not found!');\n}\n\nexport function nestedSetRight(span: Span): number {\n  if (span.attributes) {\n    for (const a of span.attributes) {\n      if (a.key === 'nestedSetRight') {\n        return parseInt(a.value.intValue || a.value.Value?.int_value || '0', 10);\n      }\n    }\n  }\n\n  throw new Error('nestedSetRight not found!');\n}\n", "import { Span } from '../../types';\nimport { nestedSetLeft, nestedSetRight } from './utils';\n\nexport class TreeNode {\n  name: string;\n  serviceName: string;\n  operationName: string;\n  spans: Span[];\n  left: number;\n  right: number;\n  children: TreeNode[];\n  parent: TreeNode | null;\n  traceID: string;\n\n  constructor({\n    name,\n    serviceName,\n    operationName,\n    spans,\n    left,\n    right,\n    traceID,\n  }: {\n    name: string;\n    serviceName: string;\n    operationName: string;\n    spans: Span[];\n    left: number;\n    right: number;\n    traceID: string;\n  }) {\n    this.name = name;\n    this.serviceName = serviceName;\n    this.operationName = operationName;\n    this.spans = spans;\n    this.left = left;\n    this.right = right;\n    this.children = [];\n    this.parent = null;\n    this.traceID = traceID;\n  }\n\n  addSpan(span: Span) {\n    // expand our left/right based on this span\n    this.left = Math.min(nestedSetLeft(span), this.left);\n    this.right = Math.max(nestedSetRight(span), this.right);\n    this.spans.push(span);\n  }\n\n  addChild(node: TreeNode) {\n    node.parent = this;\n    this.children.push(node);\n  }\n\n  isChild(span: Span): boolean {\n    return nestedSetLeft(span) > this.left && nestedSetRight(span) < this.right;\n  }\n\n  findMatchingChild(span: Span): TreeNode | null {\n    const name = nodeName(span);\n\n    for (const child of this.children) {\n      if (child.name === name) {\n        return child;\n      }\n    }\n\n    return null;\n  }\n}\n\nexport function createNode(s: Span): TreeNode {\n  const serviceNameAttr = s.attributes?.find((a) => a.key === 'service.name');\n  return new TreeNode({\n    left: nestedSetLeft(s),\n    right: nestedSetRight(s),\n    name: nodeName(s),\n    serviceName: serviceNameAttr?.value.stringValue ?? serviceNameAttr?.value?.Value?.string_value ?? '',\n    operationName: s.name ?? '',\n    spans: [s],\n    traceID: s.traceId ?? '',\n  });\n}\n\nfunction nodeName(s: Span): string {\n  let svcName = '';\n  for (const a of s.attributes || []) {\n    if (a.key === 'service.name' && a.value.stringValue) {\n      svcName = a.value.stringValue;\n    }\n  }\n\n  return `${svcName}:${s.name}`;\n}\n", "import { TraceSearchMetadata } from '../../types';\nimport { createNode, TreeNode } from './tree-node';\nimport { nestedSetLeft } from './utils';\n\nexport function mergeTraces(traces: TraceSearchMetadata[]): TreeNode {\n  const tree = new TreeNode({\n    name: 'root',\n    serviceName: '',\n    operationName: '',\n    left: Number.MIN_SAFE_INTEGER,\n    right: Number.MAX_SAFE_INTEGER,\n    spans: [],\n    traceID: '',\n  });\n\n  if (traces && traces.length > 0) {\n    for (const trace of traces) {\n      if (trace.spanSets?.length !== 1) {\n        throw new Error('there should be only 1 spanset!');\n      }\n\n      const traceStartTime = parseInt(trace.startTimeUnixNano || '0', 10);\n\n      const ss = trace.spanSets[0];\n      // sort by nestedSetLeft\n      ss.spans.sort((s1, s2) => nestedSetLeft(s1) - nestedSetLeft(s2));\n\n      // reset curNode to root each loop to re-overlay the next trace\n      let curNode: TreeNode = tree;\n      // left/right is only valid w/i a trace, so reset it each loop\n      resetLeftRight(tree);\n      for (const span of ss.spans) {\n        // force traceID to be the same for all spans in a trace\n        span.traceId = trace.traceID;\n        span.startTimeUnixNano = `${parseInt(span.startTimeUnixNano, 10) - traceStartTime}`;\n\n        // walk up the tree until we find a node that is a parent of this span\n        while (curNode.parent !== null) {\n          if (curNode.isChild(span)) {\n            break;\n          }\n          curNode = curNode.parent;\n        }\n\n        // is there an already existing child that matches the span?\n        const child = curNode.findMatchingChild(span);\n        if (child) {\n          child.addSpan(span);\n          // to the next span!\n          curNode = child;\n          continue;\n        }\n\n        // if not, create a new child node and make it the cur node\n        const newNode = createNode(span);\n        newNode.traceID = trace.traceID;\n        curNode.addChild(newNode);\n        curNode = newNode;\n      }\n    }\n  }\n\n  return tree;\n}\n\nexport function dumpTree(t: TreeNode, depth: number): string {\n  let result = '';\n  const space = ' '.repeat(depth * 2);\n\n  result += `${space}${t.name} ${t.spans.length}\\n`;\n\n  for (const c of t.children) {\n    result += dumpTree(c, depth + 1);\n  }\n  return result;\n}\n\nfunction resetLeftRight(t: TreeNode) {\n  t.left = Number.MAX_SAFE_INTEGER;\n  t.right = Number.MIN_SAFE_INTEGER;\n\n  for (const c of t.children) {\n    resetLeftRight(c);\n  }\n}\n", "import React from 'react';\n\nimport {\n  Panel<PERSON><PERSON>ers,\n  SceneComponentProps,\n  SceneDataNode,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  explorationDS,\n  filterStreamingProgressTransformations,\n  MetricFunction,\n  VAR_FILTERS_EXPR,\n  VAR_LATENCY_PARTIAL_THRESHOLD_EXPR,\n  VAR_LATENCY_THRESHOLD_EXPR,\n} from '../../../../../utils/shared';\nimport { TraceSearchMetadata } from '../../../../../types';\nimport { mergeTraces } from '../../../../../utils/trace-merge/merge';\nimport { createDataFrame, Field, FieldType, GrafanaTheme2, LinkModel, LoadingState } from '@grafana/data';\nimport { TreeNode } from '../../../../../utils/trace-merge/tree-node';\nimport { Icon, LinkButton, Stack, Text, useTheme2 } from '@grafana/ui';\nimport Skeleton from 'react-loading-skeleton';\nimport { EmptyState } from '../../../../states/EmptyState/EmptyState';\nimport { css } from '@emotion/css';\nimport { getOpenTrace, getTraceExplorationScene } from 'utils/utils';\nimport { structureDisplayName } from '../TabsBarScene';\n\nexport interface ServicesTabSceneState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  loading?: boolean;\n  tree?: TreeNode;\n  metric?: MetricFunction;\n}\n\nconst ROOT_SPAN_ID = '0000000000000000';\n\nexport class StructureTabScene extends SceneObjectBase<ServicesTabSceneState> {\n  constructor(state: Partial<ServicesTabSceneState>) {\n    super({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildQuery(state.metric as MetricFunction)],\n        }),\n        transformations: filterStreamingProgressTransformations,\n      }),\n      loading: true,\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  public _onActivate() {\n    this._subs.add(\n      this.state.$data?.subscribeToState((state) => {\n        if (state.data?.state === LoadingState.Loading || state.data?.state === LoadingState.Streaming) {\n          this.setState({ loading: true });\n          return;\n        }\n\n        if (state.data?.state === LoadingState.Done && state.data?.series.length) {\n          const frame = state.data?.series[0].fields[0].values[0];\n          if (frame) {\n            const response = JSON.parse(frame) as TraceSearchMetadata[];\n            const tree = mergeTraces(response);\n            tree.children.sort((a, b) => countSpans(b) - countSpans(a));\n\n            this.setState({\n              loading: false,\n              tree,\n              panel: new SceneFlexLayout({\n                height: '100%',\n                wrap: 'wrap',\n                children: this.getPanels(tree),\n              }),\n            });\n          }\n        }\n      })\n    );\n  }\n\n  private getPanels(tree: TreeNode) {\n    return tree.children.map((child) => {\n      return new SceneFlexItem({\n        height: 150,\n        width: '100%',\n        minHeight: '400px',\n        body: this.getPanel(child),\n      });\n    });\n  }\n\n  private getPanel(tree: TreeNode) {\n    const timeRange = sceneGraph.getTimeRange(this);\n    const from = timeRange.state.value.from;\n    const to = timeRange.state.value.to;\n\n    const openTrace = getOpenTrace(this);\n\n    return PanelBuilders.traces()\n      .setTitle(`Structure for ${tree.serviceName} [${countSpans(tree)} spans used]`)\n      .setOption('createFocusSpanLink' as any, (traceId: string, spanId: string): LinkModel<Field> => {\n        return {\n          title: 'Open trace',\n          href: '#',\n          onClick: () => openTrace(traceId, spanId),\n          origin: {} as Field,\n          target: '_self',\n        };\n      })\n      .setData(\n        new SceneDataNode({\n          data: {\n            state: LoadingState.Done,\n            timeRange: {\n              from,\n              to,\n              raw: { from, to },\n            },\n            series: [\n              {\n                ...this.buildData(tree),\n              },\n            ],\n          },\n        })\n      )\n      .build();\n  }\n\n  private buildData(tree: TreeNode) {\n    const trace = this.getTrace(tree, ROOT_SPAN_ID);\n    const traceName = trace[0].serviceName + ':' + trace[0].operationName;\n\n    return createDataFrame({\n      name: `Trace ${traceName}`,\n      refId: `trace_${traceName}`,\n      fields: [\n        {\n          name: 'references',\n          type: FieldType.other,\n          values: trace.map((x) => x.references),\n        },\n        {\n          name: 'traceID',\n          type: FieldType.string,\n          values: trace.map((x) => x.traceID),\n        },\n        {\n          name: 'spanID',\n          type: FieldType.string,\n          values: trace.map((x) => x.spanID),\n        },\n        {\n          name: 'parentSpanID',\n          type: FieldType.string,\n          values: trace.map((x) => x.parentSpanId),\n        },\n        {\n          name: 'serviceName',\n          type: FieldType.string,\n          values: trace.map((x) => x.serviceName),\n        },\n        {\n          name: 'operationName',\n          type: FieldType.string,\n          values: trace.map((x) => x.operationName),\n        },\n        {\n          name: 'duration',\n          type: FieldType.number,\n          values: trace.map((x) => x.duration),\n        },\n        {\n          name: 'startTime',\n          type: FieldType.number,\n          values: trace.map((x) => x.startTime),\n        },\n        {\n          name: 'statusCode',\n          type: FieldType.number,\n          values: trace.map((x) => x.statusCode),\n        },\n      ],\n    });\n  }\n\n  private getTrace(node: TreeNode, spanID: string) {\n    const erroredSpans = node.spans.reduce(\n      (acc, c) => (c.attributes?.find((a) => a.key === 'status')?.value.stringValue === 'error' ? acc + 1 : acc),\n      0\n    );\n\n    // start time needs to be different from zero otherwise for the root, otherwise the Trace View won't render it\n    let startTime = 0.0001;\n    if (spanID !== ROOT_SPAN_ID) {\n      startTime =\n        node.spans.reduce((acc, c) => acc + parseInt(c.startTimeUnixNano, 10), 0) / node.spans.length / 1000000;\n    }\n\n    const values = [\n      {\n        // Add last 5 spans of the list as external references\n        // refType = 'EXTERNAL' doesn't mean anything, it's just to be different from CHILD_OF and FOLLOW_FROM\n        references: node.spans.slice(-5).map((x) => ({\n          refType: 'EXTERNAL',\n          traceID: x.traceId,\n          spanID: x.spanID,\n        })),\n        traceID: node.traceID,\n        spanID: node.spans[0].spanID,\n        parentSpanId: spanID,\n        serviceName: node.serviceName,\n        operationName: node.operationName,\n        statusCode: erroredSpans > 0 ? 2 /*error*/ : 0 /*unset*/,\n        duration: node.spans.reduce((acc, c) => acc + parseInt(c.durationNanos, 10), 0) / node.spans.length / 1000000,\n        startTime,\n      },\n    ];\n\n    for (const child of node.children) {\n      values.push(...this.getTrace(child, node.spans[0].spanID));\n    }\n    return values;\n  }\n\n  public static Component = ({ model }: SceneComponentProps<StructureTabScene>) => {\n    const { tree, loading, panel, $data } = model.useState();\n    const styles = getStyles(useTheme2());\n    const theme = useTheme2();\n\n    const exploration = getTraceExplorationScene(model);\n    const { value } = exploration.getMetricVariable().useState();\n\n    const metric = value as MetricFunction;\n\n    let isLoading = loading || !tree?.children.length;\n    if ($data?.state.data?.state === LoadingState.Done) {\n      isLoading = false;\n    }\n\n    let description;\n    let emptyMsg = '';\n    switch (metric) {\n      case 'rate':\n        description = (\n          <>\n            <div>Analyse the service structure of the traces that match the current filters.</div>\n            <div>Each panel represents an aggregate view compiled using spans from multiple traces.</div>\n          </>\n        );\n        emptyMsg = 'server';\n        break;\n      case 'errors':\n        description = (\n          <>\n            <div>Analyse the errors structure of the traces that match the current filters.</div>\n            <div>Each panel represents an aggregate view compiled using spans from multiple traces.</div>\n          </>\n        );\n        emptyMsg = 'error';\n        break;\n      case 'duration':\n        description = (\n          <>\n            <div>Analyse the structure of slow spans from the traces that match the current filters.</div>\n            <div>Each panel represents an aggregate view compiled using spans from multiple traces.</div>\n          </>\n        );\n        emptyMsg = 'slow';\n        break;\n    }\n\n    const tabName = structureDisplayName(metric);\n\n    const noDataMessage = (\n      <>\n        <Text textAlignment={'center'} variant=\"h3\">\n          {EMPTY_STATE_ERROR_MESSAGE}\n        </Text>\n        <Text textAlignment={'center'} variant=\"body\">\n          <div className={styles.longText}>\n            The structure tab shows {emptyMsg} spans beneath what you are currently investigating. Currently, there are\n            no descendant {emptyMsg} spans beneath the spans you are investigating.\n          </div>\n        </Text>\n        <Stack gap={0.5} alignItems={'center'}>\n          <Icon name=\"info-circle\" />\n          <Text textAlignment={'center'} variant=\"body\">\n            The structure tab works best with full traces.\n          </Text>\n        </Stack>\n\n        <div className={styles.actionContainer}>\n          Read more about\n          <div className={styles.action}>\n            <LinkButton\n              icon=\"external-link-alt\"\n              fill=\"solid\"\n              size={'sm'}\n              target={'_blank'}\n              href={\n                'https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/concepts/#trace-structure'\n              }\n            >\n              {`${tabName.toLowerCase()}`}\n            </LinkButton>\n          </div>\n        </div>\n      </>\n    );\n\n    return (\n      <Stack direction={'column'} gap={1}>\n        <div className={styles.description}>{description}</div>\n        {isLoading && (\n          <Stack direction={'column'} gap={2}>\n            <Skeleton\n              count={4}\n              height={200}\n              baseColor={theme.colors.background.secondary}\n              highlightColor={theme.colors.background.primary}\n            />\n          </Stack>\n        )}\n\n        {!isLoading && tree && tree.children.length > 0 && (\n          <div className={styles.traceViewList}>{panel && <panel.Component model={panel} />}</div>\n        )}\n\n        {$data?.state.data?.state === LoadingState.Done && !tree?.children.length && (\n          <EmptyState message={noDataMessage} padding={'32px'} />\n        )}\n      </Stack>\n    );\n  };\n}\n\nfunction buildQuery(metric: MetricFunction) {\n  let metricQuery;\n  let selectionQuery = '';\n  switch (metric) {\n    case 'errors':\n      metricQuery = 'status = error';\n      selectionQuery = 'status = error';\n      break;\n    case 'duration':\n      metricQuery = `duration > ${VAR_LATENCY_PARTIAL_THRESHOLD_EXPR}`;\n      selectionQuery = `duration > ${VAR_LATENCY_THRESHOLD_EXPR}`;\n      break;\n    default:\n      metricQuery = 'kind = server';\n      break;\n  }\n\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR} ${\n      selectionQuery.length ? `&& ${selectionQuery}` : ''\n    }} &>> { ${metricQuery} } | select(status, resource.service.name, name, nestedSetParent, nestedSetLeft, nestedSetRight)`,\n    queryType: 'traceql',\n    tableType: 'raw',\n    limit: 200,\n    spss: 20,\n    filters: [],\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    description: css({\n      fontSize: theme.typography.h6.fontSize,\n      padding: `${theme.spacing(1)} 0`,\n    }),\n    traceViewList: css({\n      display: 'flex',\n      flexDirection: 'column',\n      gap: theme.spacing.x1,\n      // Hide the minimap and header components\n      'div[class*=\"panel-content\"] > div': {\n        overflow: 'auto',\n        '> :not([class*=\"TraceTimelineViewer\"])': {\n          display: 'none',\n        },\n      },\n      // Hide the Span and Resource accordions from span details\n      'div[data-testid=\"span-detail-component\"] > :nth-child(4) > :nth-child(1)': {\n        display: 'none',\n      },\n\n      // Hide span details row\n      '.span-detail-row': {\n        display: 'none',\n      },\n\n      // Remove cursor pointer as span details is hidden\n      'div[data-testid=\"TimelineRowCell\"]': {\n        'button[role=\"switch\"]': {\n          cursor: 'text',\n        },\n      },\n      'div[data-testid=\"span-view\"]': {\n        cursor: 'text !important',\n      },\n    }),\n    longText: css({\n      maxWidth: '800px',\n      margin: '0 auto',\n    }),\n    action: css({\n      marginLeft: theme.spacing(1),\n    }),\n    actionContainer: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n    }),\n  };\n};\n\nfunction countSpans(tree: TreeNode) {\n  let count = tree.spans.length;\n  for (const child of tree.children) {\n    count += countSpans(child);\n  }\n  return count;\n}\n\nexport function buildStructureScene(metric: MetricFunction) {\n  return new SceneFlexItem({\n    body: new StructureTabScene({ metric }),\n  });\n}\n", "import { css } from '@emotion/css';\nimport { useResizeObserver } from '@react-aria/utils';\nimport React, { useEffect, useMemo, useRef, useState } from 'react';\n\nimport { GrafanaTheme2, SelectableValue } from '@grafana/data';\nimport { Select, RadioButtonGroup, useStyles2, useTheme2, measureText, Field, InputActionMeta } from '@grafana/ui';\nimport { ALL, ignoredAttributes, maxOptions, MetricFunction, RESOURCE_ATTR, SPAN_ATTR } from 'utils/shared';\nimport { AttributesBreakdownScene } from './TracesByService/Tabs/Breakdown/AttributesBreakdownScene';\nimport { AttributesComparisonScene } from './TracesByService/Tabs/Comparison/AttributesComparisonScene';\nimport { getFiltersVariable, getMetricVariable, getTraceExplorationScene } from 'utils/utils';\n\ntype Props = {\n  options: Array<SelectableValue<string>>;\n  radioAttributes: string[];\n  value?: string;\n  onChange: (label: string, ignore?: boolean) => void;\n  showAll?: boolean;\n  model: AttributesBreakdownScene | AttributesComparisonScene;\n};\n\nconst additionalWidthPerItem = 40;\nconst widthOfOtherAttributes = 180;\n\nexport function GroupBySelector({ options, radioAttributes, value, onChange, showAll = false, model }: Props) {\n  const styles = useStyles2(getStyles);\n  const theme = useTheme2();\n  const { fontSize } = theme.typography;\n\n  const [selectQuery, setSelectQuery] = useState<string>('');\n  const [allowAutoUpdate, setAllowAutoUpdate] = useState<boolean>(true);\n\n  const [availableWidth, setAvailableWidth] = useState<number>(0);\n  const controlsContainer = useRef<HTMLDivElement>(null);\n\n  const { initialGroupBy } = getTraceExplorationScene(model).useState();\n  const { filters } = getFiltersVariable(model).useState();\n  const { value: metric } = getMetricVariable(model).useState();\n  const metricValue = metric as MetricFunction;\n\n  useResizeObserver({\n    ref: controlsContainer,\n    onResize: () => {\n      const element = controlsContainer.current;\n      if (element) {\n        setAvailableWidth(element.clientWidth);\n      }\n    },\n  });\n\n  const radioOptions = useMemo(() => {\n    let radioOptionsWidth = 0;\n    return radioAttributes\n      .filter((op) => {\n        // remove radio options that are in the dropdown\n        let checks = !!options.find((o) => o.value === op);\n\n        // remove radio options that are in the filters\n        if (filters.find((f) => f.key === op && (f.operator === '=' || f.operator === '!='))) {\n          return false;\n        }\n\n        // if filters (primary signal) has 'Full Traces' selected, then don't add rootName or rootServiceName to options\n        // as you would overwrite it in the query if it's selected\n        if (filters.find((f) => f.key === 'nestedSetParent')) {\n          checks = checks && op !== 'rootName' && op !== 'rootServiceName';\n        }\n\n        // if rate or error rate metric is selected, then don't add status to options\n        // as you would overwrite it in the query if it's selected\n        if (metricValue === 'rate' || metricValue === 'errors') {\n          checks = checks && op !== 'status';\n        }\n\n        return checks;\n      })\n      .map((attribute) => ({\n        label: attribute.replace(SPAN_ATTR, '').replace(RESOURCE_ATTR, ''),\n        text: attribute,\n        value: attribute,\n      }))\n      .filter((option) => {\n        const text = option.label || option.text || '';\n        const textWidth = measureText(text, fontSize).width;\n        if (radioOptionsWidth + textWidth + additionalWidthPerItem + widthOfOtherAttributes < availableWidth) {\n          radioOptionsWidth += textWidth + additionalWidthPerItem;\n          return true;\n        } else {\n          return false;\n        }\n      });\n  }, [radioAttributes, options, filters, metricValue, fontSize, availableWidth]);\n\n  const otherAttrOptions = useMemo(() => {\n    const ops = options.filter((op) => !radioOptions.find((ro) => ro.value === op.value?.toString()));\n    return filteredOptions(ops, selectQuery);\n  }, [selectQuery, options, radioOptions]);\n\n  const getModifiedSelectOptions = (options: Array<SelectableValue<string>>) => {\n    return options\n      .filter((op) => !ignoredAttributes.includes(op.value?.toString()!))\n      .map((op) => ({ label: op.label?.replace(SPAN_ATTR, '').replace(RESOURCE_ATTR, ''), value: op.value }));\n  };\n\n  const defaultValue = initialGroupBy ?? radioOptions[0]?.value ?? otherAttrOptions[0]?.value;\n\n  // Set default value as first value in options\n  useEffect(() => {\n    if (defaultValue && !showAll && allowAutoUpdate) {\n      onChange(defaultValue, true);\n      setAllowAutoUpdate(false);\n    }\n  }, [value, defaultValue, showAll, onChange, allowAutoUpdate]);\n\n  useEffect(() => {\n    if (radioAttributes.length > 0) {\n      setAllowAutoUpdate(true);\n    }\n  }, [radioAttributes]);\n\n  useEffect(() => {\n    if (filters.some((f) => f.key === value)) {\n      setAllowAutoUpdate(true);\n    }\n  }, [filters, value]);\n\n  const showAllOption = showAll ? [{ label: ALL, value: ALL }] : [];\n  const defaultOnChangeValue = showAll ? ALL : '';\n\n  return (\n    <Field label=\"Group by\">\n      <div ref={controlsContainer} className={styles.container}>\n        {radioOptions.length > 0 && (\n          <RadioButtonGroup options={[...showAllOption, ...radioOptions]} value={value} onChange={onChange} />\n        )}\n        <Select\n          value={value && getModifiedSelectOptions(otherAttrOptions).some((x) => x.value === value) ? value : null} // remove value from select when radio button clicked\n          placeholder={'Other attributes'}\n          options={getModifiedSelectOptions(otherAttrOptions)}\n          onChange={(selected) => {\n            const newSelected = selected?.value ?? defaultOnChangeValue;\n            onChange(newSelected);\n          }}\n          className={styles.select}\n          isClearable\n          onInputChange={(value: string, { action }: InputActionMeta) => {\n            if (action === 'input-change') {\n              setSelectQuery(value);\n            }\n          }}\n          onCloseMenu={() => setSelectQuery('')}\n          virtualized\n        />\n      </div>\n    </Field>\n  );\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    select: css({\n      maxWidth: theme.spacing(22),\n    }),\n    container: css({\n      display: 'flex',\n      gap: theme.spacing(1),\n    }),\n  };\n}\n\nexport const filteredOptions = (options: Array<SelectableValue<string>>, query: string) => {\n  if (options.length === 0) {\n    return [];\n  }\n\n  if (query.length === 0) {\n    return options.slice(0, maxOptions);\n  }\n\n  const queryLowerCase = query.toLowerCase();\n  return options\n    .filter((tag) => {\n      if (tag.value && tag.value.length > 0) {\n        return tag.value.toLowerCase().includes(queryLowerCase);\n      }\n      return false;\n    })\n    .slice(0, maxOptions);\n};\n", "import React from 'react';\n\nimport { SelectableValue } from '@grafana/data';\nimport { SceneComponentProps, SceneObject, SceneObjectBase, SceneObjectState } from '@grafana/scenes';\nimport { Field, RadioButtonGroup } from '@grafana/ui';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../utils/analytics';\n\nexport interface LayoutSwitcherState extends SceneObjectState {\n  active: LayoutType;\n  layouts: SceneObject[];\n  options: Array<SelectableValue<LayoutType>>;\n}\n\nexport type LayoutType = 'single' | 'grid' | 'rows';\n\nexport class LayoutSwitcher extends SceneObjectBase<LayoutSwitcherState> {\n  public Selector({ model }: { model: LayoutSwitcher }) {\n    const { active, options } = model.useState();\n\n    return (\n      <Field label=\"View\">\n        <RadioButtonGroup options={options} value={active} onChange={model.onLayoutChange} />\n      </Field>\n    );\n  }\n\n  public onLayoutChange = (active: LayoutType) => {\n    this.setState({ active });\n    reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.layout_type_changed, {\n      layout: active,\n    });\n  };\n\n  public static Component = ({ model }: SceneComponentProps<LayoutSwitcher>) => {\n    const { layouts, options, active } = model.useState();\n\n    const index = options.findIndex((o) => o.value === active);\n    if (index === -1) {\n      return null;\n    }\n\n    const layout = layouts[index];\n\n    return <layout.Component model={layout} />;\n  };\n}\n", "import { PanelBuilders } from '@grafana/scenes';\nimport { TooltipDisplayMode } from '@grafana/ui';\n\nexport const linesPanelConfig = () => {\n  return PanelBuilders.timeseries()\n    .setOption('legend', { showLegend: false })\n    .setOption('tooltip', { mode: TooltipDisplayMode.Multi })\n    .setCustomFieldConfig('fillOpacity', 15);\n};\n", "import { sceneGraph, SceneObject, SceneObjectState, VizPanel } from '@grafana/scenes';\nimport { cloneDeep, merge } from 'lodash';\nimport { EventTimeseriesDataReceived } from '../../../utils/shared';\n\nexport function syncYAxis() {\n  return (vizPanel: SceneObject<SceneObjectState>) => {\n    const maxima = new Map<string, number>();\n\n    const eventSub = vizPanel.subscribeToEvent(EventTimeseriesDataReceived, (event) => {\n      const series = event.payload.series;\n\n      series?.forEach((s) => {\n        s.fields.slice(1).forEach((f) => {\n          maxima.set(s.refId as string, Math.max(...f.values.filter((v) => v)));\n        })\n      });\n\n      updateTimeseriesAxis(vizPanel, Math.max(...maxima.values()));\n    });\n\n    return () => {\n      eventSub.unsubscribe();\n    };\n  };\n}\n\nfunction updateTimeseriesAxis(vizPanel: SceneObject, max: number) {\n  // findAllObjects searches down the full scene graph\n  const timeseries = sceneGraph.findAllObjects(vizPanel, (o) => o instanceof VizPanel) as VizPanel[];\n\n  for (const t of timeseries) {\n    t.clearFieldConfigCache(); // required\n\n    t.setState({\n      fieldConfig: merge(cloneDeep(t.state.fieldConfig), { defaults: { max } }),\n    });\n  }\n}\n", "import {\n  CustomVariable,\n  SceneCSSGridItem,\n  SceneCSSGridLayout,\n  SceneDataNode,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObject,\n  VizPanelState,\n} from '@grafana/scenes';\nimport { LayoutSwitcher } from '../LayoutSwitcher';\nimport { explorationDS, GRID_TEMPLATE_COLUMNS, MetricFunction } from '../../../utils/shared';\nimport { ByFrameRepeater } from '../ByFrameRepeater';\nimport { formatLabelValue, getLabelValue, getOpenTrace, getTraceExplorationScene } from '../../../utils/utils';\nimport { map, Observable } from 'rxjs';\nimport { DataFrame, PanelData, reduceField, ReducerID } from '@grafana/data';\nimport { generateMetricsQuery, getMetricsTempoQuery } from '../queries/generateMetricsQuery';\nimport { barsPanelConfig } from '../panels/barsPanel';\nimport { linesPanelConfig } from '../panels/linesPanel';\nimport { StepQueryRunner } from '../queries/StepQueryRunner';\nimport { syncYAxis } from '../behaviors/syncYaxis';\nimport { exemplarsTransformations } from '../../../utils/exemplars';\nimport { PanelMenu } from '../panels/PanelMenu';\n\nexport function buildNormalLayout(\n  scene: SceneObject,\n  variable: CustomVariable,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions']\n) {\n  const traceExploration = getTraceExplorationScene(scene);\n  const metric = traceExploration.getMetricVariable().getValue() as MetricFunction;\n  const query = getMetricsTempoQuery({ metric, groupByKey: variable.getValueText() });\n  const panels: Record<string, SceneCSSGridItem> = {};\n\n  return new LayoutSwitcher({\n    $behaviors: [syncYAxis()],\n    $data: new SceneDataTransformer({\n      $data: new StepQueryRunner({\n        maxDataPoints: 64,\n        datasource: explorationDS,\n        queries: [query],\n      }),\n      transformations: [\n        ...exemplarsTransformations(getOpenTrace(scene)),\n        () => (source: Observable<DataFrame[]>) => {\n          return source.pipe(\n            map((data: DataFrame[]) => {\n              data.forEach((a) => reduceField({ field: a.fields[1], reducers: [ReducerID.max] }));\n              return data.sort((a, b) => {\n                return (b.fields[1].state?.calcs?.max || 0) - (a.fields[1].state?.calcs?.max || 0);\n              });\n            })\n          );\n        },\n      ],\n    }),\n    options: [\n      { value: 'single', label: 'Single' },\n      { value: 'grid', label: 'Grid' },\n      { value: 'rows', label: 'Rows' },\n    ],\n    active: 'grid',\n    layouts: [\n      new SceneFlexLayout({\n        direction: 'column',\n        children: [\n          new SceneFlexItem({\n            minHeight: 300,\n            body: (metric === 'duration' ? linesPanelConfig().setUnit('s') : linesPanelConfig()).build(),\n          }),\n        ],\n      }),\n      new ByFrameRepeater({\n        body: new SceneCSSGridLayout({\n          templateColumns: GRID_TEMPLATE_COLUMNS,\n          autoRows: '200px',\n          isLazy: true,\n          children: [],\n        }),\n        groupBy: true,\n        getLayoutChild: getLayoutChild(panels, getLabelValue, variable, metric, actionsFn),\n      }),\n      new ByFrameRepeater({\n        body: new SceneCSSGridLayout({\n          templateColumns: '1fr',\n          autoRows: '200px',\n          isLazy: true,\n          children: [],\n        }),\n        groupBy: true,\n        getLayoutChild: getLayoutChild(panels, getLabelValue, variable, metric, actionsFn),\n      }),\n    ],\n  });\n}\n\nexport function getLayoutChild(\n  panels: Record<string, SceneCSSGridItem>,\n  getTitle: (df: DataFrame, labelName: string) => string,\n  variable: CustomVariable,\n  metric: MetricFunction,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions']\n) {\n  return (data: PanelData, frame: DataFrame) => {\n    const existingGridItem = frame.name ? panels[frame.name] : undefined;\n\n    const dataNode = new SceneDataNode({\n      data: {\n        ...data,\n        annotations: data.annotations?.filter((a) => a.refId === frame.refId),\n        series: [\n          {\n            ...frame,\n            fields: frame.fields.sort((a, b) => a.labels?.status?.localeCompare(b.labels?.status || '') || 0),\n          },\n        ],\n      },\n    });\n\n    if (existingGridItem) {\n      existingGridItem.state.body?.setState({ $data: dataNode });\n      return existingGridItem;\n    }\n\n    const query = sceneGraph.interpolate(\n      variable,\n      generateMetricsQuery({\n        metric,\n        extraFilters: `${variable.getValueText()}=${formatLabelValue(getLabelValue(frame))}`,\n      })\n    );\n\n    const panel = (metric === 'duration' ? linesPanelConfig().setUnit('s') : barsPanelConfig(metric))\n      .setTitle(getTitle(frame, variable.getValueText()))\n      .setMenu(new PanelMenu({ query, labelValue: getLabelValue(frame) }))\n      .setData(dataNode);\n\n    const actions = actionsFn(frame);\n    if (actions) {\n      panel.setHeaderActions(actions);\n    }\n\n    const gridItem = new SceneCSSGridItem({\n      body: panel.build(),\n    });\n    if (frame.name) {\n      panels[frame.name] = gridItem;\n    }\n\n    return gridItem;\n  };\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\ntype Tag = {\n  label: string;\n  color: string;\n};\n\ntype Props = {\n  description: string;\n  tags: Tag[];\n};\n\nexport function AttributesDescription({ description, tags }: Props) {\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n\n  return (\n    <div className={styles.infoFlex}>\n      <div className={styles.tagsFlex}>{description}</div>\n      {tags.length > 0 &&\n        tags.map((tag) => (\n          <div className={styles.tagsFlex} key={tag.label}>\n            <div className={styles.tag} style={{ backgroundColor: tag.color }} />\n            <div>{tag.label}</div>\n          </div>\n        ))}\n    </div>\n  );\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    infoFlex: css({\n      display: 'flex',\n      gap: theme.spacing(2),\n      alignItems: 'center',\n      padding: `${theme.spacing(1)} 0 ${theme.spacing(2)} 0`,\n    }),\n    tagsFlex: css({\n      display: 'flex',\n      gap: theme.spacing(1),\n      alignItems: 'center',\n    }),\n    tag: css({\n      display: 'inline-block',\n      width: theme.spacing(2),\n      height: theme.spacing(0.5),\n      borderRadius: theme.spacing(0.5),\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport React, { useEffect, useState } from 'react';\n\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport {\n  CustomVariable,\n  SceneComponentProps,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  VariableDependencyConfig,\n} from '@grafana/scenes';\nimport { Field, RadioButtonGroup, useStyles2 } from '@grafana/ui';\n\nimport { GroupBySelector } from '../../../GroupBySelector';\nimport {\n  MetricFunction,\n  RESOURCE,\n  RESOURCE_ATTR,\n  SPAN,\n  SPAN_ATTR,\n  VAR_FILTERS,\n  VAR_METRIC,\n  radioAttributesResource,\n  radioAttributesSpan,\n} from '../../../../../utils/shared';\n\nimport { LayoutSwitcher } from '../../../LayoutSwitcher';\nimport { AddToFiltersAction } from '../../../actions/AddToFiltersAction';\nimport { buildNormalLayout } from '../../../layouts/attributeBreakdown';\nimport {\n  getAttributesAsOptions,\n  getGroupByVariable,\n  getTraceByServiceScene,\n  getTraceExplorationScene,\n} from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../../../utils/analytics';\nimport { AttributesDescription } from './AttributesDescription';\n\nexport interface AttributesBreakdownSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class AttributesBreakdownScene extends SceneObjectBase<AttributesBreakdownSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_FILTERS, VAR_METRIC],\n    onReferencedVariableValueChanged: this.onReferencedVariableValueChanged.bind(this),\n  });\n\n  constructor(state: Partial<AttributesBreakdownSceneState>) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const variable = getGroupByVariable(this);\n\n    variable.subscribeToState(() => {\n      this.setBody(variable);\n    });\n\n    getTraceByServiceScene(this).subscribeToState(() => {\n      this.setBody(variable);\n    });\n\n    this.setBody(variable);\n  }\n\n  private onReferencedVariableValueChanged() {\n    const variable = getGroupByVariable(this);\n    variable.changeValueTo(radioAttributesResource[0]);\n    this.setBody(variable);\n  }\n\n  private onAddToFiltersClick(payload: any) {\n    reportAppInteraction(\n      USER_EVENTS_PAGES.analyse_traces,\n      USER_EVENTS_ACTIONS.analyse_traces.breakdown_add_to_filters_clicked,\n      payload\n    );\n  }\n\n  private setBody = (variable: CustomVariable) => {\n    this.setState({\n      body: buildNormalLayout(this, variable, (frame: DataFrame) => [\n        new AddToFiltersAction({ frame, labelKey: variable.getValueText(), onClick: this.onAddToFiltersClick }),\n      ]),\n    });\n  };\n\n  public onChange = (value: string, ignore?: boolean) => {\n    const variable = getGroupByVariable(this);\n    if (variable.getValueText() !== value) {\n      variable.changeValueTo(value, undefined, !ignore);\n\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.breakdown_group_by_changed,\n        {\n          groupBy: value,\n        }\n      );\n    }\n  };\n\n  public static Component = ({ model }: SceneComponentProps<AttributesBreakdownScene>) => {\n    const { value: groupByValue } = getGroupByVariable(model).useState();\n    const groupBy = groupByValue as string;\n    const defaultScope = groupBy.includes(SPAN_ATTR) || radioAttributesSpan.includes(groupBy) ? SPAN : RESOURCE;\n    const [scope, setScope] = useState(defaultScope);\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    const { attributes } = getTraceByServiceScene(model).useState();\n    const filterType = scope === RESOURCE ? RESOURCE_ATTR : SPAN_ATTR;\n    let filteredAttributes = attributes?.filter((attr) => attr.includes(filterType));\n    if (scope === SPAN) {\n      filteredAttributes = filteredAttributes?.concat(radioAttributesSpan);\n    }\n\n    const exploration = getTraceExplorationScene(model);\n    const { value: metric } = exploration.getMetricVariable().useState();\n    const getDescription = (metric: MetricFunction) => {\n      switch (metric) {\n        case 'rate':\n          return 'Attributes are ordered by their rate of requests per second.';\n        case 'errors':\n          return 'Attributes are ordered by their rate of errors per second.';\n        case 'duration':\n          return 'Attributes are ordered by their average duration.';\n        default:\n          throw new Error('Metric not supported');\n      }\n    };\n    const description = getDescription(metric as MetricFunction);\n\n    useEffect(() => {\n      if (scope !== defaultScope) {\n        setScope(defaultScope);\n      }\n    }, [groupBy]);\n\n    return (\n      <div className={styles.container}>\n        <AttributesDescription\n          description={description}\n          tags={\n            metric === 'duration'\n              ? []\n              : [\n                  { label: 'Rate', color: 'green' },\n                  { label: 'Error', color: 'red' },\n                ]\n          }\n        />\n\n        <div className={styles.controls}>\n          {filteredAttributes?.length && (\n            <div className={styles.controlsLeft}>\n              <div className={styles.scope}>\n                <Field label=\"Scope\">\n                  <RadioButtonGroup\n                    options={getAttributesAsOptions([RESOURCE, SPAN])}\n                    value={scope}\n                    onChange={setScope}\n                  />\n                </Field>\n              </div>\n\n              <div className={styles.groupBy}>\n                <GroupBySelector\n                  options={getAttributesAsOptions(filteredAttributes!)}\n                  radioAttributes={scope === RESOURCE ? radioAttributesResource : radioAttributesSpan}\n                  value={groupBy}\n                  onChange={model.onChange}\n                  model={model}\n                />\n              </div>\n            </div>\n          )}\n          {body instanceof LayoutSwitcher && (\n            <div className={styles.controlsRight}>\n              <body.Selector model={body} />\n            </div>\n          )}\n        </div>\n        <div className={styles.content}>{body && <body.Component model={body} />}</div>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      flexGrow: 1,\n      display: 'flex',\n      minHeight: '100%',\n      flexDirection: 'column',\n    }),\n    content: css({\n      flexGrow: 1,\n      display: 'flex',\n      paddingTop: theme.spacing(0),\n    }),\n    controls: css({\n      flexGrow: 0,\n      display: 'flex',\n      alignItems: 'top',\n      gap: theme.spacing(2),\n    }),\n    controlsRight: css({\n      flexGrow: 0,\n      display: 'flex',\n      justifyContent: 'flex-end',\n    }),\n    scope: css({\n      marginRight: theme.spacing(2),\n    }),\n    groupBy: css({\n      width: '100%',\n    }),\n    controlsLeft: css({\n      display: 'flex',\n      justifyContent: 'flex-left',\n      justifyItems: 'left',\n      width: '100%',\n      flexDirection: 'row',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneFlexItem,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  VariableDependencyConfig,\n} from '@grafana/scenes';\nimport { AttributesBreakdownScene } from './AttributesBreakdownScene';\nimport { VAR_METRIC } from '../../../../../utils/shared';\n\ninterface BreakdownSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class BreakdownScene extends SceneObjectBase<BreakdownSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_METRIC],\n  });\n\n  constructor(state: Partial<BreakdownSceneState>) {\n    super({ ...state });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    this.updateBody();\n  }\n\n  private updateBody() {\n    this.setState({ body: new AttributesBreakdownScene({}) });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<BreakdownScene>) => {\n    const { body } = model.useState();\n    return body && <body.Component model={body} />;\n  };\n}\n\nexport function buildBreakdownScene() {\n  return new SceneFlexItem({\n    body: new BreakdownScene({}),\n  });\n}\n", "import { Field } from \"@grafana/data\";\nimport { calculateBucketSize } from \"utils/dates\";\n\nexport function aggregateExceptions(messageField: Field<string>, typeField?: Field<string>, timeField?: Field<any>, serviceField?: Field<string>) {\n  const occurrences = new Map<string, number>();\n  const types = new Map<string, string>();\n  const lastSeenTimes = new Map<string, number>();\n  const services = new Map<string, string>();\n  const timeSeries = new Map<string, Array<{time: number, count: number}>>();\n  \n  // Collect timestamps for each message\n  const messageTimestamps = new Map<string, number[]>();\n  \n  for (let i = 0; i < messageField.values.length; i++) {\n    const message = messageField.values[i];\n    const type = typeField?.values[i];\n    const timestamp = timeField?.values[i];\n    const service = serviceField?.values[i];\n    \n    if (message) {\n      const normalizedMessage = normalizeExceptionMessage(message);\n      occurrences.set(normalizedMessage, (occurrences.get(normalizedMessage) || 0) + 1);\n      \n      if (!types.has(normalizedMessage) && type) {\n        types.set(normalizedMessage, type);\n      }\n\n      if (!services.has(normalizedMessage) && service) {\n        services.set(normalizedMessage, service);\n      }\n\n      if (timestamp) {\n        const timestampMs = typeof timestamp === 'string' ? parseFloat(timestamp) : timestamp;\n        if (!messageTimestamps.has(normalizedMessage)) {\n          messageTimestamps.set(normalizedMessage, []);\n        }\n        messageTimestamps.get(normalizedMessage)!.push(timestampMs);\n                    \n        const currentLastSeen = lastSeenTimes.get(normalizedMessage) || 0;\n        if (timestampMs > currentLastSeen) {\n          lastSeenTimes.set(normalizedMessage, timestampMs);\n        }\n      }\n    }\n  }\n\n  // Create time series data for each message\n  for (const [message, timestamps] of messageTimestamps.entries()) {\n    const timeSeriesData = createTimeSeries(timestamps);\n    timeSeries.set(message, timeSeriesData);\n  }\n\n  const sortedEntries = Array.from(occurrences.entries()).sort((a, b) => b[1] - a[1]);\n\n  return {\n    messages: sortedEntries.map(([message]) => message),\n    types: sortedEntries.map(([message]) => types.get(message) || ''),\n    occurrences: sortedEntries.map(([, count]) => count),\n    services: sortedEntries.map(([message]) => services.get(message) || ''),\n    timeSeries: sortedEntries.map(([message]) => timeSeries.get(message) || []),\n    lastSeenTimes: sortedEntries.map(([message]) => {\n      const lastSeenMs = lastSeenTimes.get(message);\n      \n      if (!lastSeenMs) {\n        return '';\n      }\n      \n      const now = Date.now();\n      const diffMs = now - lastSeenMs;\n      \n      if (diffMs < 60000) { // Less than 1 minute\n        return 'Just now';\n      } else if (diffMs < 3600000) { // Less than 1 hour\n        const minutes = Math.floor(diffMs / 60000);\n        return `${minutes}m ago`;\n      } else if (diffMs < 86400000) { // Less than 1 day\n        const hours = Math.floor(diffMs / 3600000);\n        return `${hours}h ago`;\n      } else { // More than 1 day\n        const days = Math.floor(diffMs / 86400000);\n        return `${days}d ago`;\n      }\n    }),\n  };\n}\n\nexport function createTimeSeries(timestamps: number[]): Array<{time: number, count: number}> {\n  if (!timestamps.length) {return [];}\n  \n  timestamps.sort((a, b) => a - b);\n  \n  const timeRangeMs = timestamps[timestamps.length - 1] - timestamps[0];\n  const timeRangeSeconds = timeRangeMs / 1000;\n  const bucketSizeSeconds = calculateBucketSize(timeRangeSeconds, 50);\n  const bucketSizeMs = bucketSizeSeconds * 1000; // Convert back to milliseconds\n  const buckets = new Map<number, number>();\n  \n  for (const timestamp of timestamps) {\n    const bucketKey = Math.floor(timestamp / bucketSizeMs) * bucketSizeMs;\n    buckets.set(bucketKey, (buckets.get(bucketKey) || 0) + 1);\n  }\n  \n  // Convert to array and sort by time\n  return Array.from(buckets.entries())\n    .map(([time, count]) => ({ time, count }))\n    .sort((a, b) => a.time - b.time);\n}\n\nexport function normalizeExceptionMessage(message: string): string {\n  if (!message) { return '' }\n  return message.replace(/\\s+/g, ' ').trim();\n}\n", "import React from 'react';\n\nimport {\n  <PERSON><PERSON><PERSON><PERSON>,\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport { DataFrame, GrafanaTheme2, LoadingState, PanelData, FieldType, DataLink } from '@grafana/data';\nimport { GraphDrawStyle, VisibilityMode, TableCellHeight } from '@grafana/schema';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { css } from '@emotion/css';\nimport Skeleton from 'react-loading-skeleton';\nimport { useStyles2, useTheme2, TableCellDisplayMode, TableCustomCellOptions, Sparkline } from '@grafana/ui';\nimport { map, Observable } from 'rxjs';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n  explorationDS,\n  filterStreamingProgressTransformations,\n} from '../../../../../utils/shared';\nimport { getTraceByServiceScene, getFiltersVariable } from '../../../../../utils/utils';\nimport { buildExceptionsQuery } from 'components/Explore/queries/exceptions';\nimport { aggregateExceptions } from './ExceptionUtils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\n\nexport interface ExceptionsSceneState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  dataState: 'empty' | 'loading' | 'done';\n  exceptionsCount?: number;\n}\n\nexport class ExceptionsScene extends SceneObjectBase<ExceptionsSceneState> {\n  constructor(state: Partial<ExceptionsSceneState>) {\n    super({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildExceptionsQuery()],\n        }),\n        transformations: [], // Will be set after construction\n      }),\n      dataState: 'empty',\n      ...state,\n    });\n\n    const dataTransformer = this.state.$data as SceneDataTransformer;\n    dataTransformer.setState({\n      transformations: [...filterStreamingProgressTransformations, this.createTransformation()],\n    });\n\n    this.addActivationHandler(() => {\n      const dataTransformer = this.state.$data as SceneDataTransformer;\n\n      this._subs.add(\n        dataTransformer.subscribeToState((newState, prevState) => {\n          if (newState.data !== prevState.data) {\n            this.updatePanel(newState.data);\n          }\n        })\n      );\n    });\n  }\n\n  private updatePanel(data?: PanelData) {\n    if (\n      data?.state === LoadingState.Loading ||\n      data?.state === LoadingState.NotStarted ||\n      !data?.state ||\n      (data?.state === LoadingState.Streaming && !data.series?.[0]?.length)\n    ) {\n      this.setState({\n        dataState: 'loading',\n        panel: new SceneFlexLayout({\n          direction: 'row',\n          children: [\n            new LoadingStateScene({\n              component: SkeletonComponent,\n            }),\n          ],\n        }),\n      });\n    } else if (\n      (data?.state === LoadingState.Done || data?.state === LoadingState.Streaming) &&\n      (data.series.length === 0 || !data.series?.[0]?.length)\n    ) {\n      this.setState({\n        dataState: 'empty',\n        exceptionsCount: 0,\n        panel: new SceneFlexLayout({\n          children: [\n            new SceneFlexItem({\n              body: new EmptyStateScene({\n                message: EMPTY_STATE_ERROR_MESSAGE,\n                remedyMessage: EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n                padding: '32px',\n              }),\n            }),\n          ],\n        }),\n      });\n    } else if (\n      (data?.state === LoadingState.Done || data?.state === LoadingState.Streaming) &&\n      data.series.length > 0\n    ) {\n      const exceptionsCount = this.calculateExceptionsCount(data);\n\n      this.setState({\n        dataState: 'done',\n        exceptionsCount,\n        panel: new SceneFlexLayout({\n          children: [\n            new SceneFlexItem({\n              body: PanelBuilders.table()\n                .setOption('cellHeight', TableCellHeight.Lg)\n                .setHoverHeader(true)\n                .setOverrides((builder) => {\n                  return builder\n                    .matchFieldsWithName('Service')\n                    .overrideCustomFieldConfig('width', 200)\n                    .matchFieldsWithName('Occurrences')\n                    .overrideCustomFieldConfig('width', 120)\n                    .matchFieldsWithName('Time Series')\n                    .overrideCustomFieldConfig('width', 220)\n                    .matchFieldsWithName('Last Seen')\n                    .overrideCustomFieldConfig('width', 120);\n                })\n                .build(),\n            }),\n          ],\n        }),\n      });\n    }\n  }\n\n  private createTransformation() {\n    return () => (source: Observable<DataFrame[]>) => {\n      return source.pipe(\n        map((data: DataFrame[]) => {\n          return data.map((df: DataFrame) => {\n            const messageField = df.fields.find((f) => f.name === 'exception.message');\n            const typeField = df.fields.find((f) => f.name === 'exception.type');\n            const serviceField = df.fields.find((f) => f.name === 'service.name');\n            const timeField = df.fields.find((f) => f.name === 'time');\n            const noData = !messageField || !messageField.values.length;\n\n            let messages: string[] = [];\n            let types: string[] = [];\n            let occurrences: number[] = [];\n            let lastSeenTimes: string[] = [];\n            let services: string[] = [];\n            let timeSeries: Array<Array<{ time: number; count: number }>> = [];\n\n            if (!noData) {\n              const aggregated = aggregateExceptions(messageField, typeField, timeField, serviceField);\n              messages = aggregated.messages;\n              types = aggregated.types;\n              occurrences = aggregated.occurrences;\n              lastSeenTimes = aggregated.lastSeenTimes;\n              services = aggregated.services;\n              timeSeries = aggregated.timeSeries;\n            }\n\n            const options: TableCustomCellOptions = {\n              type: TableCellDisplayMode.Custom,\n              cellComponent: (props) => {\n                const seriesData = props.value as Array<{ time: number; count: number }>;\n                return this.renderSparklineCell(seriesData);\n              },\n            };\n\n            return {\n              ...df,\n              length: messages.length,\n              fields: [\n                {\n                  name: 'Message',\n                  type: FieldType.string,\n                  values: messages,\n                  config: {\n                    links: messages.length > 0 ? [this.createDataLink()] : [],\n                  },\n                },\n                {\n                  name: 'Type',\n                  type: FieldType.string,\n                  values: types,\n                  config: {},\n                },\n                {\n                  name: 'Trace Service',\n                  type: FieldType.string,\n                  values: services,\n                  config: {},\n                },\n                {\n                  name: 'Occurrences',\n                  type: FieldType.number,\n                  values: occurrences,\n                  config: {},\n                },\n                {\n                  name: 'Frequency',\n                  type: FieldType.other,\n                  values: timeSeries,\n                  config: {\n                    custom: {\n                      cellOptions: options,\n                    },\n                  },\n                },\n                {\n                  name: 'Last Seen',\n                  type: FieldType.string,\n                  values: lastSeenTimes,\n                  config: {},\n                },\n              ],\n            };\n          });\n        })\n      );\n    };\n  }\n\n  private renderSparklineCell = (seriesData: Array<{ time: number; count: number }>) => {\n    const styles = useStyles2(getStyles);\n\n    const SparklineCell = () => {\n      const theme = useTheme2();\n\n      if (!seriesData || !seriesData.length) {\n        return <div className={styles.sparklineMessage}>No data</div>;\n      }\n\n      const countValues = seriesData.map((point) => point.count);\n      const timeValues = seriesData.map((point) => point.time);\n\n      const validCountValues = countValues.filter((v) => isFinite(v) && !isNaN(v));\n      const validTimeValues = timeValues.filter((v) => isFinite(v) && !isNaN(v));\n      if (validCountValues.length < 2 || validTimeValues.length < 2) {\n        return <div className={styles.sparklineMessage}>Not enough data</div>;\n      }\n\n      const minCount = Math.min(...validCountValues);\n      const maxCount = Math.max(...validCountValues);\n      const minTime = Math.min(...validTimeValues);\n      const maxTime = Math.max(...validTimeValues);\n\n      // Ensure valid ranges\n      const countDelta = maxCount - minCount;\n      const timeDelta = maxTime - minTime;\n\n      // Handle edge cases where all values are the same\n      const safeCountDelta = countDelta === 0 ? 1 : countDelta;\n      const safeTimeDelta = timeDelta === 0 ? 1 : timeDelta;\n\n      const sparklineData = {\n        y: {\n          name: 'count',\n          type: FieldType.number,\n          values: validCountValues,\n          config: {},\n          state: {\n            range: {\n              min: minCount,\n              max: maxCount,\n              delta: safeCountDelta,\n            },\n          },\n        },\n        x: {\n          name: 'time',\n          type: FieldType.time,\n          values: validTimeValues,\n          config: {},\n          state: {\n            range: {\n              min: minTime,\n              max: maxTime,\n              delta: safeTimeDelta,\n            },\n          },\n        },\n      };\n\n      return (\n        <div className={styles.sparklineContainer}>\n          <Sparkline\n            width={180}\n            height={20}\n            sparkline={sparklineData}\n            theme={theme}\n            config={{\n              custom: {\n                drawStyle: GraphDrawStyle.Line,\n                fillOpacity: 5,\n                fillColor: theme.colors.background.secondary,\n                lineWidth: 1,\n                showPoints: VisibilityMode.Never,\n              },\n            }}\n          />\n        </div>\n      );\n    };\n\n    return <SparklineCell />;\n  };\n\n  private createDataLink(): DataLink {\n    return {\n      title: 'View traces for this exception',\n      url: '',\n      onClick: (event: any) => {\n        const rowIndex = event.origin?.rowIndex;\n        if (rowIndex !== undefined) {\n          const message = event.origin?.field?.values?.[rowIndex];\n          if (message) {\n            reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.exception_message_clicked);\n            this.navigateToTracesWithFilter(message);\n          }\n        }\n      },\n    };\n  }\n\n  private navigateToTracesWithFilter = (exceptionMessage: string) => {\n    const filtersVariable = getFiltersVariable(this);\n    if (!filtersVariable) {\n      return;\n    }\n\n    const traceByServiceScene = getTraceByServiceScene(this);\n    traceByServiceScene?.setActionView('traceList');\n\n    const currentFilters = filtersVariable.state.filters || [];\n    const escapedMessage = this.escapeFilterValue(exceptionMessage);\n\n    const existingFilterIndex = currentFilters.findIndex((filter) => filter.key === 'event.exception.message');\n\n    const newFilter = {\n      key: 'event.exception.message',\n      operator: '=',\n      value: escapedMessage,\n    };\n\n    const newFilters =\n      existingFilterIndex >= 0\n        ? currentFilters.map((f, i) => (i === existingFilterIndex ? newFilter : f))\n        : [...currentFilters, newFilter];\n\n    filtersVariable.setState({ filters: newFilters });\n  };\n\n  private escapeFilterValue(value: string): string {\n    return value\n      .replace(/[\\n\\r\\t]/g, ' ')\n      .replace(/\\s+/g, ' ')\n      .replace(/\\\\/g, '\\\\\\\\')\n      .replace(/\"/g, '\\\"')\n      .trim();\n  }\n\n  private calculateExceptionsCount(data?: PanelData): number {\n    if (!data?.series?.[0]) {\n      return 0;\n    }\n\n    const occurrencesField = data.series[0].fields.find((field) => field.name === 'Occurrences');\n    if (!occurrencesField?.values) {\n      return 0;\n    }\n\n    return occurrencesField.values.reduce((total: number, value: number) => total + (value || 0), 0);\n  }\n\n  public getExceptionsCount(): number {\n    return this.state.exceptionsCount || 0;\n  }\n\n  public static Component = ({ model }: SceneComponentProps<ExceptionsScene>) => {\n    const styles = useStyles2(getStyles);\n    const theme = useTheme2();\n    const { panel, dataState } = model.useState();\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.description}>\n          View exception details from errored traces for the current set of filters.\n        </div>\n        {dataState === 'loading' && (\n          <div className={styles.loadingContainer}>\n            <Skeleton\n              count={10}\n              height={40}\n              baseColor={theme.colors.background.secondary}\n              highlightColor={theme.colors.background.primary}\n            />\n          </div>\n        )}\n        {panel && <panel.Component model={panel} />}\n      </div>\n    );\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      gap: theme.spacing(2),\n      height: '100%',\n    }),\n    description: css({\n      fontSize: theme.typography.h6.fontSize,\n      padding: `${theme.spacing(1)} 0`,\n    }),\n    loadingContainer: css({\n      padding: theme.spacing(2),\n    }),\n    sparklineContainer: css({\n      width: '200px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n    }),\n    sparklineMessage: css({\n      fontSize: theme.typography.bodySmall.fontSize,\n      color: theme.colors.text.secondary,\n      padding: theme.spacing(1),\n    }),\n  };\n};\n\nconst SkeletonComponent = () => {\n  const styles = useStyles2(getSkeletonStyles);\n  const theme = useTheme2();\n\n  return (\n    <div className={styles.container}>\n      <Skeleton\n        count={10}\n        height={40}\n        baseColor={theme.colors.background.secondary}\n        highlightColor={theme.colors.background.primary}\n      />\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: theme.spacing(2),\n    }),\n  };\n}\n\nexport function buildExceptionsScene() {\n  return new SceneFlexItem({\n    body: new ExceptionsScene({}),\n  });\n}\n", "import { VAR_FILTERS_EXPR } from 'utils/shared';\n\nexport function buildExceptionsQuery() {\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR} && status = error} | select(resource.service.name, event.exception.message,event.exception.stacktrace,event.exception.type) with(most_recent=true)`,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 400,\n    spss: 10,\n    filters: [],\n  };\n}\n", "import { ComparisonSelection } from '../../../utils/shared';\n\nexport function comparisonQuery(selection?: ComparisonSelection) {\n  let selector = '';\n\n  if (!selection) {\n    return '{}';\n  }\n\n  if (selection.query) {\n    selector += selection.query;\n  }\n\n  const duration = [];\n  if (selection.duration?.from.length) {\n    duration.push(`duration >= ${selection.duration.from}`);\n  }\n  if (selection.duration?.to.length) {\n    duration.push(`duration <= ${selection.duration.to}`);\n  }\n  if (duration.length) {\n    if (selector.length) {\n      selector += ' && ';\n    }\n    selector += duration.join(' && ');\n  }\n\n  const fromTimerange = selection.timeRange?.from;\n  const toTimerange = selection.timeRange?.to;\n  return `{${selector}}, 10${\n    fromTimerange && toTimerange ? `, ${fromTimerange * 1000000000}, ${toTimerange * 1000000000}` : ``\n  }`;\n}\n", "import {\n  CustomVariable,\n  SceneCSSGridItem,\n  SceneCSSGridLayout,\n  SceneDataNode,\n  SceneDataTransformer,\n  sceneGraph,\n  SceneObject,\n  VizPanelState,\n} from '@grafana/scenes';\nimport { ByFrameRepeater } from '../ByFrameRepeater';\nimport { map, Observable } from 'rxjs';\nimport { DataFrame, FieldType, LoadingState, PanelData, reduceField, ReducerID } from '@grafana/data';\nimport { getPanelConfig } from './allComparison';\nimport { GRID_TEMPLATE_COLUMNS, MetricFunction } from '../../../utils/shared';\n\nexport function buildAttributeComparison(\n  scene: SceneObject,\n  variable: CustomVariable,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  const timeRange = sceneGraph.getTimeRange(scene);\n  const data = sceneGraph.getData(scene);\n  const attribute = variable.getValueText();\n  const attributeSeries = data.state.data?.series.find((d) => d.name === attribute);\n  const splitFrames: DataFrame[] = [];\n  const nameField = attributeSeries?.fields.find((f) => f.name === 'Value');\n  const baselineField = attributeSeries?.fields.find((f) => f.name === 'Baseline');\n  const selectionField = attributeSeries?.fields.find((f) => f.name === 'Selection');\n\n  const panels: Record<string, SceneCSSGridItem> = {};\n\n  if (nameField && baselineField && selectionField) {\n    for (let i = 0; i < nameField.values.length; i++) {\n      if (!nameField.values[i] || (!baselineField.values[i] && !selectionField.values[i])) {\n        continue;\n      }\n\n      splitFrames.push({\n        name: nameField.values[i].replace(/\"/g, ''),\n        length: 1,\n        fields: [\n          {\n            name: 'Value',\n            type: FieldType.string,\n            values: ['Baseline', 'Comparison'],\n            config: {},\n          },\n          {\n            ...baselineField,\n            values: [baselineField.values[i]],\n            labels: {\n              [attribute]: nameField.values[i],\n            },\n            config: {\n              displayName: 'Baseline',\n            },\n          },\n          {\n            ...selectionField,\n            values: [selectionField.values[i]],\n          },\n        ],\n      });\n    }\n  }\n\n  return new ByFrameRepeater({\n    $data: new SceneDataTransformer({\n      $data: new SceneDataNode({\n        data: {\n          timeRange: timeRange.state.value,\n          state: LoadingState.Done,\n          series: splitFrames,\n        },\n      }),\n      transformations: [\n        () => (source: Observable<DataFrame[]>) => {\n          return source.pipe(\n            map((data: DataFrame[]) => {\n              data.forEach((a) => reduceField({ field: a.fields[2], reducers: [ReducerID.max] }));\n              return data.sort((a, b) => {\n                return (b.fields[2].state?.calcs?.max || 0) - (a.fields[2].state?.calcs?.max || 0);\n              });\n            })\n          );\n        },\n      ],\n    }),\n    body: new SceneCSSGridLayout({\n      templateColumns: GRID_TEMPLATE_COLUMNS,\n      autoRows: '200px',\n      isLazy: true,\n      children: [],\n    }),\n    getLayoutChild: getLayoutChild(panels, getLabel, actionsFn, metric),\n  });\n}\n\nconst getLabel = (df: DataFrame) => {\n  return df.name || 'No name available';\n};\n\nfunction getLayoutChild(\n  panels: Record<string, SceneCSSGridItem>,\n  getTitle: (df: DataFrame) => string,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  return (data: PanelData, frame: DataFrame) => {\n    const existingGridItem = frame.name ? panels[frame.name] : undefined;\n\n    const dataNode = new SceneDataNode({\n      data: {\n        ...data,\n        series: [\n          {\n            ...frame,\n          },\n        ],\n      },\n    });\n\n    if (existingGridItem) {\n      existingGridItem.state.body?.setState({ $data: dataNode });\n      return existingGridItem;\n    }\n\n    const panel = getPanelConfig(metric).setTitle(getTitle(frame)).setData(dataNode);\n\n    const actions = actionsFn(frame);\n    if (actions) {\n      panel.setHeaderActions(actions);\n    }\n\n    const gridItem = new SceneCSSGridItem({\n      body: panel.build(),\n    });\n    if (frame.name) {\n      panels[frame.name] = gridItem;\n    }\n\n    return gridItem;\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneObjectState,\n  SceneObjectBase,\n  SceneComponentProps,\n} from '@grafana/scenes';\nimport { Button } from '@grafana/ui';\n\ninterface InspectAttributeActionState extends SceneObjectState {\n  attribute?: string;\n  onClick: () => void;\n}\n\nexport class InspectAttributeAction extends SceneObjectBase<InspectAttributeActionState> {\n  public static Component = ({ model }: SceneComponentProps<InspectAttributeAction>) => {\n    if (!model.state.attribute) {\n      return null;\n    }\n\n    return (\n      <Button variant=\"secondary\" size=\"sm\" fill=\"solid\" onClick={() => model.state.onClick()}>\n        Inspect\n      </Button>\n    );\n  };\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { DataFrame, FieldType, GrafanaTheme2, Field } from '@grafana/data';\nimport {\n  CustomVariable,\n  SceneComponentProps,\n  SceneDataTransformer,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n  VariableDependencyConfig,\n  VariableValue,\n} from '@grafana/scenes';\nimport { getTheme, useStyles2 } from '@grafana/ui';\n\nimport { GroupBySelector } from '../../../GroupBySelector';\nimport { VAR_FILTERS, VAR_PRIMARY_SIGNAL, explorationDS, VAR_FILTERS_EXPR, ALL, radioAttributesSpan } from '../../../../../utils/shared';\n\nimport { LayoutSwitcher } from '../../../LayoutSwitcher';\nimport { AddToFiltersAction } from '../../../actions/AddToFiltersAction';\nimport { map, Observable } from 'rxjs';\nimport { BaselineColor, buildAllComparisonLayout, SelectionColor } from '../../../layouts/allComparison';\n// eslint-disable-next-line no-restricted-imports\nimport { duration } from 'moment';\nimport { comparisonQuery } from '../../../queries/comparisonQuery';\nimport { buildAttributeComparison } from '../../../layouts/attributeComparison';\nimport {\n  getAttributesAsOptions,\n  getGroupByVariable,\n  getPrimarySignalVariable,\n  getTraceByServiceScene,\n  getTraceExplorationScene,\n} from 'utils/utils';\nimport { InspectAttributeAction } from 'components/Explore/actions/InspectAttributeAction';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../../../utils/analytics';\nimport { computeHighestDifference } from '../../../../../utils/comparison';\nimport { AttributesDescription } from '../Breakdown/AttributesDescription';\nimport { isEqual } from 'lodash';\n\nexport interface AttributesComparisonSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class AttributesComparisonScene extends SceneObjectBase<AttributesComparisonSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_FILTERS, VAR_PRIMARY_SIGNAL],\n    onReferencedVariableValueChanged: this.onReferencedVariableValueChanged.bind(this),\n  });\n\n  constructor(state: Partial<AttributesComparisonSceneState>) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const variable = getGroupByVariable(this);\n\n    variable.changeValueTo(ALL);\n\n    this.updateData();\n\n    variable.subscribeToState((newState, prevState) => {\n      if (newState.value !== prevState.value) {\n        this.setBody(variable);\n      }\n    });\n\n    getPrimarySignalVariable(this).subscribeToState(() => {\n      this.updateData();\n      this.setBody(variable);\n    });\n\n    getTraceByServiceScene(this).subscribeToState((newState, prevState) => {\n      if (!isEqual(newState.selection, prevState.selection)) {\n        this.updateData();\n        this.setBody(variable);\n      }\n    });\n\n    sceneGraph.getTimeRange(this).subscribeToState(() => {\n      this.updateData();\n    });\n\n    this.setBody(variable);\n  }\n\n  private getFilteredAttributes = (primarySignal: VariableValue): string[] => {\n    return primarySignal === 'nestedSetParent<0' ? ['rootName', 'rootServiceName'] : [];\n  };\n\n  private updateData() {\n    const byServiceScene = getTraceByServiceScene(this);\n    const sceneTimeRange = sceneGraph.getTimeRange(this);\n    const from = sceneTimeRange.state.value.from.unix();\n    const to = sceneTimeRange.state.value.to.unix();\n    const primarySignal = getPrimarySignalVariable(this).state.value;\n    const filteredAttributes = this.getFilteredAttributes(primarySignal);\n\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildQuery(from, to, comparisonQuery(byServiceScene.state.selection))],\n        }),\n        transformations: [\n          () => (source: Observable<DataFrame[]>) => {\n            return source.pipe(\n              map((data: DataFrame[]) => {\n                const groupedFrames = groupFrameListByAttribute(data);\n                return Object.entries(groupedFrames)\n                  .filter(([attribute, _]) => !filteredAttributes.includes(attribute))\n                  .map(([attribute, frames]) => frameGroupToDataframe(attribute, frames))\n                  .sort((a, b) => {\n                    const aCompare = computeHighestDifference(a);\n                    const bCompare = computeHighestDifference(b);\n                    return Math.abs(bCompare.maxDifference) - Math.abs(aCompare.maxDifference);\n                  });\n              })\n            );\n          },\n        ],\n      }),\n    });\n  }\n\n  private onReferencedVariableValueChanged() {\n    const variable = getGroupByVariable(this);\n    variable.changeValueTo(ALL);\n    this.setBody(variable);\n  }\n\n  private onAddToFiltersClick(payload: any) {\n    reportAppInteraction(\n      USER_EVENTS_PAGES.analyse_traces,\n      USER_EVENTS_ACTIONS.analyse_traces.comparison_add_to_filters_clicked,\n      payload\n    );\n  }\n\n  private setBody = (variable: CustomVariable) => {\n    const traceExploration = getTraceExplorationScene(this);\n    this.setState({\n      body:\n        variable.hasAllValue() || variable.getValue() === ALL\n          ? buildAllComparisonLayout(\n              (frame) =>\n                new InspectAttributeAction({\n                  attribute: frame.name,\n                  onClick: () => this.onChange(frame.name || ''),\n                }),\n              traceExploration.getMetricFunction()\n            )\n          : buildAttributeComparison(\n              this,\n              variable,\n              (frame: DataFrame) => [\n                new AddToFiltersAction({\n                  frame,\n                  labelKey: variable.getValueText(),\n                  onClick: this.onAddToFiltersClick,\n                }),\n              ],\n              traceExploration.getMetricFunction()\n            ),\n    });\n  };\n\n  public onChange = (value: string, ignore?: boolean) => {\n    const variable = getGroupByVariable(this);\n    variable.changeValueTo(value, undefined, !ignore);\n\n    reportAppInteraction(\n      USER_EVENTS_PAGES.analyse_traces,\n      USER_EVENTS_ACTIONS.analyse_traces.select_attribute_in_comparison_clicked,\n      { value }\n    );\n  };\n\n  public static Component = ({ model }: SceneComponentProps<AttributesComparisonScene>) => {\n    const { body } = model.useState();\n    const variable = getGroupByVariable(model);\n    const traceExploration = getTraceExplorationScene(model);\n    const { attributes } = getTraceByServiceScene(model).useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <AttributesDescription\n          description=\"Attributes are ordered by the difference between the baseline and selection values for each value.\"\n          tags={[\n            {\n              label: 'Baseline',\n              color:\n                traceExploration.getMetricFunction() === 'duration'\n                  ? BaselineColor\n                  : getTheme().visualization.getColorByName('semi-dark-green'),\n            },\n            {\n              label: 'Selection',\n              color:\n                traceExploration.getMetricFunction() === 'duration'\n                  ? SelectionColor\n                  : getTheme().visualization.getColorByName('semi-dark-red'),\n            },\n          ]}\n        />\n\n        <div className={styles.controls}>\n          {attributes?.length && (\n            <div className={styles.controlsLeft}>\n              <GroupBySelector\n                options={getAttributesAsOptions(attributes)}\n                radioAttributes={radioAttributesSpan}\n                value={variable.getValueText()}\n                onChange={model.onChange}\n                showAll={true}\n                model={model}\n              />\n            </div>\n          )}\n          {body instanceof LayoutSwitcher && (\n            <div className={styles.controlsRight}>\n              <body.Selector model={body} />\n            </div>\n          )}\n        </div>\n        <div className={styles.content}>{body && <body.Component model={body} />}</div>\n      </div>\n    );\n  };\n}\n\nexport function buildQuery(from: number, to: number, compareQuery: string) {\n  const dur = duration(to - from, 's');\n  const durString = `${dur.asSeconds()}s`;\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR}} | compare(${compareQuery})`,\n    step: durString,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 100,\n    spss: 10,\n    filters: [],\n  };\n}\n\nconst groupFrameListByAttribute = (frames: DataFrame[]) => {\n  return frames.reduce((acc: Record<string, DataFrame[]>, series) => {\n    const numberField = series.fields.find((field) => field.type === 'number');\n    const nonInternalKey = Object.keys(numberField?.labels || {}).find((key) => !key.startsWith('__'));\n    if (nonInternalKey) {\n      acc[nonInternalKey] = [...(acc[nonInternalKey] || []), series];\n    }\n    return acc;\n  }, {});\n};\n\nconst frameGroupToDataframe = (attribute: string, frames: DataFrame[]): DataFrame => {\n  const newFrame: DataFrame = {\n    name: attribute,\n    refId: attribute,\n    fields: [],\n    length: 0,\n  };\n\n  const valueNameField: Field = {\n    name: 'Value',\n    type: FieldType.string,\n    values: [],\n    config: {},\n    labels: { [attribute]: attribute },\n  };\n  const baselineField: Field = {\n    name: 'Baseline',\n    type: FieldType.number,\n    values: [],\n    config: {},\n  };\n  const selectionField: Field = {\n    name: 'Selection',\n    type: FieldType.number,\n    values: [],\n    config: {},\n  };\n\n  const values = frames.reduce((acc: Record<string, Field[]>, frame) => {\n    const numberField = frame.fields.find((field) => field.type === 'number');\n    const val = numberField?.labels?.[attribute];\n    if (val) {\n      acc[val] = [...(acc[val] || []), numberField];\n    }\n    return acc;\n  }, {});\n\n  const baselineTotal = getTotalForMetaType(frames, 'baseline', values);\n  const selectionTotal = getTotalForMetaType(frames, 'selection', values);\n\n  newFrame.length = Object.keys(values).length;\n\n  Object.entries(values).forEach(([value, fields]) => {\n    valueNameField.values.push(value);\n    baselineField.values.push(\n      fields.find((field) => field.labels?.['__meta_type'] === '\"baseline\"')?.values[0] / baselineTotal\n    );\n    selectionField.values.push(\n      fields.find((field) => field.labels?.['__meta_type'] === '\"selection\"')?.values[0] / selectionTotal\n    );\n  });\n  newFrame.fields = [valueNameField, baselineField, selectionField];\n  return newFrame;\n};\n\nfunction getTotalForMetaType(frames: DataFrame[], metaType: string, values: Record<string, Field[]>) {\n  // calculate total from values so that we are properly normalizing the field values when dividing by the total\n  const calculatedTotal = Object.values(values).reduce((total, fields) => {\n    const field = fields.find((field) => field.labels?.['__meta_type'] === `\"${metaType}\"`);\n    return total + (field?.values[0] || 0);\n  }, 0);\n\n  let total = frames.reduce((currentValue, frame) => {\n    const field = frame.fields.find((f) => f.type === 'number');\n    if (field?.labels?.['__meta_type'] === `\"${metaType}_total\"`) {\n      return field.values[0];\n    }\n    return currentValue;\n  }, 1);\n\n  // if the baseline_total or selection_total field is found, but the total value is less than the calculated total\n  // we need to return the calculated total otherwise the values will be skewed\n  // e.g. calculatedTotal = 100, total = 80\n  // if we return the total, the field values will be normalized via 80/100 = 1.25 (incorrect)\n  // if we return the calculated total, the field values will be normalized via 100/100 = 1 (correct)\n  if (total < calculatedTotal) {\n    return calculatedTotal === 0 ? 1 : calculatedTotal; // fallback to 1 to avoid division by zero\n  }\n\n  // 1 if the baseline_total or selection_total field is not found\n  // 0 if the baseline_total or selection_total field is found, but the total value is 0\n  if (total === 1 || total === 0) {\n    return calculatedTotal === 0 ? 1 : calculatedTotal;\n  }\n\n  return total;\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      flexGrow: 1,\n      display: 'flex',\n      minHeight: '100%',\n      flexDirection: 'column',\n    }),\n    content: css({\n      flexGrow: 1,\n      display: 'flex',\n      paddingTop: theme.spacing(0),\n    }),\n    controls: css({\n      flexGrow: 0,\n      display: 'flex',\n      alignItems: 'top',\n      gap: theme.spacing(2),\n    }),\n    controlsRight: css({\n      flexGrow: 0,\n      display: 'flex',\n      justifyContent: 'flex-end',\n    }),\n    controlsLeft: css({\n      display: 'flex',\n      justifyContent: 'flex-left',\n      justifyItems: 'left',\n      width: '100%',\n      flexDirection: 'column',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneFlexItem,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  VariableDependencyConfig,\n} from '@grafana/scenes';\nimport { AttributesComparisonScene } from './AttributesComparisonScene';\nimport { MetricFunction, VAR_METRIC } from '../../../../../utils/shared';\nimport { getMetricVariable, getTraceByServiceScene } from '../../../../../utils/utils';\nimport { getDefaultSelectionForMetric } from '../../../../../utils/comparison';\n\ninterface ComparisonSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class ComparisonScene extends SceneObjectBase<ComparisonSceneState> {\n  protected _variableDependency = new VariableDependencyConfig(this, {\n    variableNames: [VAR_METRIC],\n  });\n\n  constructor(state: Partial<ComparisonSceneState>) {\n    super({ ...state });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const metricVar = getMetricVariable(this);\n    const metric = metricVar.getValue() as MetricFunction;\n\n    const tracesByService = getTraceByServiceScene(this);\n    if (!tracesByService.state.selection) {\n      const selection = getDefaultSelectionForMetric(metric);\n      if (selection) {\n        tracesByService.setState({ selection });\n      }\n    }\n\n    this.updateBody();\n  }\n\n  private updateBody() {\n    this.setState({ body: new AttributesComparisonScene({}) });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<ComparisonScene>) => {\n    const { body } = model.useState();\n    return body && <body.Component model={body} />;\n  };\n}\n\nexport function buildComparisonScene() {\n  return new SceneFlexItem({\n    body: new ComparisonScene({}),\n  });\n}\n", "import { css } from '@emotion/css';\nimport { SceneObjectBase, SceneComponentProps, SceneObject, sceneGraph, SceneObjectState } from '@grafana/scenes';\nimport { GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { useStyles2, Box, Stack, TabsBar, Tab } from '@grafana/ui';\nimport React, { useEffect, useState } from 'react';\nimport {\n  getTraceExplorationScene,\n  getTraceByServiceScene,\n  getExceptionsScene,\n  getFiltersVariable,\n  getPrimarySignalVariable,\n} from 'utils/utils';\nimport { ShareExplorationAction } from '../../actions/ShareExplorationAction';\nimport { buildSpansScene } from './Spans/SpansScene';\nimport { buildStructureScene } from './Structure/StructureScene';\nimport { buildBreakdownScene } from './Breakdown/BreakdownScene';\nimport { buildExceptionsScene } from './Exceptions/ExceptionsScene';\nimport { MetricFunction } from 'utils/shared';\nimport { buildComparisonScene } from './Comparison/ComparisonScene';\nimport { useMount } from 'react-use';\nimport { ActionViewType } from 'exposedComponents/types';\n\ninterface ActionViewDefinition {\n  displayName: (metric: MetricFunction) => string;\n  value: ActionViewType;\n  getScene: (metric: MetricFunction) => SceneObject;\n}\n\nexport const actionViewsDefinitions: ActionViewDefinition[] = [\n  { displayName: breakdownDisplayName, value: 'breakdown', getScene: buildBreakdownScene },\n  { displayName: structureDisplayName, value: 'structure', getScene: buildStructureScene },\n  { displayName: comparisonDisplayName, value: 'comparison', getScene: buildComparisonScene },\n  { displayName: exceptionsDisplayName, value: 'exceptions', getScene: buildExceptionsScene },\n  {\n    displayName: tracesDisplayName,\n    value: 'traceList',\n    getScene: buildSpansScene,\n  },\n];\n\nexport interface TabsBarSceneState extends SceneObjectState {}\n\nexport class TabsBarScene extends SceneObjectBase<TabsBarSceneState> {\n  public static Component = ({ model }: SceneComponentProps<TabsBarScene>) => {\n    const styles = useStyles2(getStyles);\n    const [exceptionsCount, setExceptionsCount] = useState(0);\n\n    const metricScene = getTraceByServiceScene(model);\n    const exploration = getTraceExplorationScene(model);\n\n    const { actionView } = metricScene.useState();\n    const { value: metric } = exploration.getMetricVariable().useState();\n    const { allowedActionViews } = exploration.useState();\n    const dataState = sceneGraph.getData(model).useState();\n    const tracesCount = dataState.data?.series?.[0]?.length;\n\n    const enabledViews = actionViewsDefinitions.filter((view) => {\n      if (view.value === 'exceptions' && metric !== 'errors') {\n        return false;\n      }\n      // If allowedActionViews is defined and has items, use it for filtering\n      // Otherwise, include all views (except exceptions when metric is not errors, handled above)\n      return !allowedActionViews?.length || allowedActionViews.includes(view.value);\n    });\n\n    // Get state variables that affect exceptions data\n    const filtersVariable = getFiltersVariable(model);\n    const primarySignalVariable = getPrimarySignalVariable(model);\n    const timeRange = sceneGraph.getTimeRange(model);\n    const { filters } = filtersVariable.useState();\n    const { value: primarySignal } = primarySignalVariable.useState();\n    const { value: timeRangeValue } = timeRange.useState();\n\n    useEffect(() => {\n      if (metric !== 'errors') {\n        setExceptionsCount(0);\n        return;\n      }\n\n      const exceptionsScene = getExceptionsScene(model);\n      if (!exceptionsScene) {\n        setExceptionsCount(0);\n        return;\n      }\n\n      setExceptionsCount(exceptionsScene.getExceptionsCount());\n      const subscription = exceptionsScene.subscribeToState((newState, prevState) => {\n        if (newState.exceptionsCount !== prevState.exceptionsCount) {\n          setExceptionsCount(newState.exceptionsCount || 0);\n        }\n      });\n\n      return () => {\n        subscription.unsubscribe();\n      };\n    }, [metric, model, actionView, filters, primarySignal, timeRangeValue]);\n\n    useEffect(() => {\n      if (metricScene.state.hasSetView) {\n        return;\n      }\n\n      // Set the view to traceList if the data is loaded and the traces count is greater than 20\n      if (\n        exploration.state.embedded &&\n        dataState.data?.state === LoadingState.Done &&\n        tracesCount !== undefined &&\n        tracesCount > 20\n      ) {\n        metricScene.setState({ hasSetView: true });\n        metricScene.setActionView('traceList');\n        return;\n      }\n    }, [dataState.data?.state, exploration.state.embedded, metricScene, tracesCount]);\n\n    useMount(() => {\n      if (enabledViews.length === 1) {\n        metricScene.setActionView(enabledViews[0].value);\n      }\n    });\n\n    if (enabledViews.length === 1) {\n      return null;\n    }\n\n    return (\n      <Box>\n        <div className={styles.actions}>\n          <Stack gap={1}>\n            <ShareExplorationAction exploration={exploration} />\n          </Stack>\n        </div>\n\n        <TabsBar>\n          {enabledViews.map((tab, index) => {\n            return (\n              <Tab\n                key={index}\n                label={tab.displayName(metric as MetricFunction)}\n                active={actionView === tab.value}\n                onChangeTab={() => metricScene.setActionView(tab.value)}\n                counter={\n                  tab.value === 'traceList' ? tracesCount : tab.value === 'exceptions' ? exceptionsCount : undefined\n                }\n              />\n            );\n          })}\n        </TabsBar>\n      </Box>\n    );\n  };\n}\n\nfunction breakdownDisplayName(_: MetricFunction) {\n  return 'Breakdown';\n}\n\nfunction comparisonDisplayName(_: MetricFunction) {\n  return 'Comparison';\n}\n\nexport function structureDisplayName(metric: MetricFunction) {\n  switch (metric) {\n    case 'rate':\n      return 'Service structure';\n    case 'errors':\n      return 'Root cause errors';\n    case 'duration':\n      return 'Root cause latency';\n  }\n}\n\nfunction tracesDisplayName(metric: MetricFunction) {\n  return metric === 'errors' ? 'Errored traces' : metric === 'duration' ? 'Slow traces' : 'Traces';\n}\n\nfunction exceptionsDisplayName(_: MetricFunction) {\n  return 'Exceptions';\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    actions: css({\n      [theme.breakpoints.up(theme.breakpoints.values.md)]: {\n        position: 'absolute',\n        right: 0,\n        top: 5,\n        zIndex: 2,\n      },\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { explorationDS, MetricFunction } from 'utils/shared';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { SkeletonComponent } from '../ByFrameRepeater';\nimport { barsPanelConfig } from '../panels/barsPanel';\nimport { getMetricsTempoQuery } from '../queries/generateMetricsQuery';\nimport { StepQueryRunner } from '../queries/StepQueryRunner';\nimport { RadioButtonList, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { fieldHasEmptyValues, getOpenTrace, getTraceExplorationScene } from '../../../utils/utils';\nimport { MINI_PANEL_HEIGHT } from './TracesByServiceScene';\nimport { buildHistogramQuery } from '../queries/histogram';\nimport { histogramPanelConfig } from '../panels/histogram';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { exemplarsTransformations, removeExemplarsTransformation } from '../../../utils/exemplars';\nimport { StreamingIndicator } from '../StreamingIndicator';\n\nexport interface MiniREDPanelState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  metric: MetricFunction;\n  isStreaming?: boolean;\n}\n\nexport class MiniREDPanel extends SceneObjectBase<MiniREDPanelState> {\n  constructor(state: MiniREDPanelState) {\n    super({\n      isStreaming: false,\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      this._onActivate();\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          this.setState({ isStreaming: data.data?.state === LoadingState.Streaming });\n\n          if (data.data?.state === LoadingState.Done) {\n            if (data.data.series.length === 0 || data.data.series[0].length === 0 || fieldHasEmptyValues(data)) {\n              this.setState({\n                panel: new SceneFlexLayout({\n                  children: [\n                    new SceneFlexItem({\n                      body: new EmptyStateScene({\n                        imgWidth: 110,\n                      }),\n                    }),\n                  ],\n                }),\n              });\n            } else {\n              this.setState({\n                panel: this.getVizPanel(this.state.metric),\n              });\n            }\n          } else if (data.data?.state === LoadingState.Loading) {\n            this.setState({\n              panel: new SceneFlexLayout({\n                direction: 'column',\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n                children: [\n                  new LoadingStateScene({\n                    component: () => SkeletonComponent(1),\n                  }),\n                ],\n              }),\n            });\n          }\n        })\n      );\n    });\n  }\n\n  private _onActivate() {\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new StepQueryRunner({\n          maxDataPoints: this.state.metric === 'duration' ? 24 : 64,\n          datasource: explorationDS,\n          queries: [this.state.metric === 'duration' ? buildHistogramQuery() : getMetricsTempoQuery({ metric: this.state.metric, sample: true })],\n        }),\n        transformations:\n          this.state.metric === 'duration'\n            ? [...removeExemplarsTransformation()]\n            : [...exemplarsTransformations(getOpenTrace(this))],\n      }),\n      panel: this.getVizPanel(this.state.metric),\n    });\n  }\n\n  private getVizPanel(metric: MetricFunction) {\n    return new SceneFlexLayout({\n      direction: 'row',\n      children: [\n        new SceneFlexItem({\n          body: metric === 'duration' ? this.getDurationVizPanel() : this.getRateOrErrorPanel(metric),\n        }),\n      ],\n    });\n  }\n\n  private getRateOrErrorPanel(metric: MetricFunction) {\n    const panel = barsPanelConfig(metric).setHoverHeader(true).setDisplayMode('transparent');\n    if (metric === 'rate') {\n      panel.setCustomFieldConfig('axisLabel', 'span/s');\n    } else if (metric === 'errors') {\n      panel.setTitle('Errors rate').setCustomFieldConfig('axisLabel', 'error/s').setColor({\n        fixedColor: 'semi-dark-red',\n        mode: 'fixed',\n      });\n    }\n\n    return panel.build();\n  }\n\n  private getDurationVizPanel() {\n    return histogramPanelConfig()\n      .setTitle('Histogram by duration')\n      .setHoverHeader(true)\n      .setDisplayMode('transparent')\n      .build();\n  }\n\n  public static Component = ({ model }: SceneComponentProps<MiniREDPanel>) => {\n    const { panel, isStreaming } = model.useState();\n    const styles = useStyles2(getStyles);\n    const traceExploration = getTraceExplorationScene(model);\n\n    const selectMetric = () => {\n      reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.metric_changed, {\n        metric: model.state.metric,\n        location: 'panel',\n      });\n      traceExploration.onChangeMetricFunction(model.state.metric);\n    };\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={css([styles.container, styles.clickable])} onClick={selectMetric}>\n        <div className={styles.headerWrapper}>\n          <RadioButtonList\n            className={styles.radioButton}\n            name={`metric-${model.state.metric}`}\n            options={[{ title: '', value: 'selected' }]}\n            onChange={() => selectMetric()}\n            value={'not-selected'}\n          />\n        </div>\n        {isStreaming && (\n          <div className={styles.indicatorWrapper}>\n            <StreamingIndicator isStreaming={true} iconSize={10} />\n          </div>\n        )}\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      flex: 1,\n      width: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      border: `1px solid ${theme.colors.border.weak}`,\n      borderRadius: '2px',\n      background: theme.colors.background.primary,\n      paddingTop: '8px',\n\n      'section, section:hover': {\n        borderColor: 'transparent',\n      },\n\n      '& .show-on-hover': {\n        display: 'none',\n      },\n    }),\n    headerWrapper: css({\n      display: 'flex',\n      alignItems: 'center',\n      position: 'absolute',\n      top: '4px',\n      left: '8px',\n      zIndex: 2,\n    }),\n    clickable: css({\n      cursor: 'pointer',\n      maxHeight: MINI_PANEL_HEIGHT,\n\n      ['[class*=\"loading-state-scene\"]']: {\n        height: MINI_PANEL_HEIGHT,\n        overflow: 'hidden',\n      },\n\n      ':hover': {\n        background: theme.colors.background.secondary,\n        input: {\n          backgroundColor: '#ffffff',\n          border: '5px solid #3D71D9',\n          cursor: 'pointer',\n        },\n      },\n    }),\n    radioButton: css({\n      display: 'block',\n    }),\n    indicatorWrapper: css({\n      position: 'absolute',\n      top: '4px',\n      right: '8px',\n      zIndex: 2,\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  Dashboard<PERSON>ursorSync,\n  GrafanaTheme2,\n  MetricFindValue,\n  dateTime,\n  DataFrame,\n  GetTagResponse,\n} from '@grafana/data';\nimport {\n  behaviors,\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneObjectUrlSyncConfig,\n  SceneObjectUrlValues,\n  SceneQueryRunner,\n  SceneTimeRange,\n} from '@grafana/scenes';\n\nimport { REDPanel } from './REDPanel';\nimport {\n  MakeOptional,\n  explorationDS,\n  VAR_FILTERS_EXPR,\n  VAR_DATASOURCE_EXPR,\n  MetricFunction,\n  ComparisonSelection,\n  ALL,\n  VAR_LATENCY_THRESHOLD_EXPR,\n  filterStreamingProgressTransformations,\n} from '../../../utils/shared';\nimport { getDataSourceSrv } from '@grafana/runtime';\nimport { TabsBarScene, actionViewsDefinitions } from './Tabs/TabsBarScene';\nimport { isEqual } from 'lodash';\nimport {\n  getDatasourceVariable,\n  getGroupByVariable,\n  getSpanListColumnsVariable,\n  getTraceExplorationScene,\n} from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { MiniREDPanel } from './MiniREDPanel';\nimport { Icon, LinkButton, Stack, Tooltip, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { getDefaultSelectionForMetric } from '../../../utils/comparison';\nimport { map, Observable } from 'rxjs';\nimport { ActionViewType } from 'exposedComponents/types';\nimport { ExceptionsScene } from './Tabs/Exceptions/ExceptionsScene';\n\nexport interface TraceSceneState extends SceneObjectState {\n  body: SceneFlexLayout;\n  actionView?: ActionViewType;\n\n  attributes?: string[];\n  selection?: ComparisonSelection;\n  hasSetView?: boolean;\n  exceptionsScene?: ExceptionsScene;\n}\n\nexport class TracesByServiceScene extends SceneObjectBase<TraceSceneState> {\n  protected _urlSync = new SceneObjectUrlSyncConfig(this, { keys: ['actionView', 'selection'] });\n\n  public constructor(state: MakeOptional<TraceSceneState, 'body'>) {\n    super({\n      body: state.body ?? new SceneFlexLayout({ children: [] }),\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    // Get the initial actionView from URL if it exists i.e. coming from a bookmark\n    const params = new URLSearchParams(window.location.search);\n    const urlActionView = params.get('actionView');\n    if (urlActionView && actionViewsDefinitions.find((v) => v.value === urlActionView)) {\n      this.setState({ actionView: urlActionView as ActionViewType });\n    }\n\n    this.updateBody();\n\n    const exploration = getTraceExplorationScene(this);\n    const metricVariable = exploration.getMetricVariable();\n    this._subs.add(\n      metricVariable.subscribeToState((newState, prevState) => {\n        if (newState.value !== prevState.value) {\n          const selection = getDefaultSelectionForMetric(newState.value as MetricFunction);\n          if (selection) {\n            this.setState({ selection });\n          }\n          this.updateQueryRunner(newState.value as MetricFunction);\n          this.updateExceptionsScene(newState.value as MetricFunction);\n          this.updateBody();\n        }\n      })\n    );\n\n    // Initialize exceptions scene for the current metric\n    this.updateExceptionsScene(metricVariable.getValue() as MetricFunction);\n\n    this._subs.add(\n      this.subscribeToState((newState, prevState) => {\n        const timeRange = sceneGraph.getTimeRange(this);\n        const selectionFrom = newState.selection?.timeRange?.from;\n        // clear selection if it's out of time range\n        if (selectionFrom && selectionFrom < timeRange.state.value.from.unix()) {\n          this.setState({ selection: undefined });\n        }\n\n        // Set group by to All when starting a comparison\n        if (!isEqual(newState.selection, prevState.selection)) {\n          const groupByVar = getGroupByVariable(this);\n          groupByVar.changeValueTo(ALL);\n          this.updateQueryRunner(metricVariable.getValue() as MetricFunction);\n        }\n      })\n    );\n\n    this._subs.add(\n      getDatasourceVariable(this).subscribeToState(() => {\n        this.updateAttributes();\n      })\n    );\n\n    this._subs.add(\n      getSpanListColumnsVariable(this).subscribeToState(() => {\n        this.updateQueryRunner(metricVariable.getValue() as MetricFunction);\n      })\n    );\n\n    this.updateQueryRunner(metricVariable.getValue() as MetricFunction);\n    this.updateAttributes();\n  }\n\n  updateBody() {\n    const traceExploration = getTraceExplorationScene(this);\n    const metric = traceExploration.getMetricVariable().getValue();\n    const actionViewDef = actionViewsDefinitions.find((v) => v.value === this.state.actionView);\n\n    this.setState({\n      body: buildGraphScene(\n        metric as MetricFunction,\n        actionViewDef ? [actionViewDef?.getScene(metric as MetricFunction)] : undefined\n      ),\n    });\n\n    if (this.state.actionView === undefined) {\n      this.setActionView('breakdown');\n    }\n  }\n\n    private updateExceptionsScene(metric: MetricFunction) {\n    if (metric === 'errors') {\n      if (!this.state.exceptionsScene) {\n        const exceptionsScene = new ExceptionsScene({});\n        this.setState({\n          exceptionsScene\n        });\n        \n        // Activate the scene after it's been set in state to ensure it starts fetching data\n        setTimeout(() => {\n          exceptionsScene.activate();\n        }, 0);\n      }\n    } else {\n      // Remove exceptions scene if metric is not errors\n      if (this.state.exceptionsScene) {\n        this.setState({\n          exceptionsScene: undefined\n        });\n      }\n    }\n  }\n\n  private async updateAttributes() {\n    const ds = await getDataSourceSrv().get(VAR_DATASOURCE_EXPR, { __sceneObject: { value: this } });\n\n    if (!ds) {\n      return;\n    }\n\n    const timeRange = sceneGraph.getTimeRange(this);\n    const options = {\n      timeRange: timeRange.state.value,\n      filters: []\n    };\n\n    ds.getTagKeys?.(options).then((tagKeys: GetTagResponse | MetricFindValue[]) => {\n      let keys: MetricFindValue[] = [];\n      if ('data' in tagKeys) {\n        keys = (tagKeys as GetTagResponse).data;\n      } else {\n        keys = tagKeys;\n      }\n      const attributes = keys.map((l) => l.text);\n      if (attributes !== this.state.attributes) {\n        this.setState({ attributes });\n      }\n    });\n  }\n\n  getUrlState() {\n    return {\n      actionView: this.state.actionView,\n      selection: this.state.selection ? JSON.stringify(this.state.selection) : undefined,\n    };\n  }\n\n  updateFromUrl(values: SceneObjectUrlValues) {\n    if (typeof values.actionView === 'string') {\n      if (this.state.actionView !== values.actionView) {\n        const actionViewDef = actionViewsDefinitions.find((v) => v.value === values.actionView);\n        if (actionViewDef) {\n          this.setActionView(actionViewDef.value);\n        }\n      }\n    } else if (values.actionView === null) {\n      this.setActionView('breakdown');\n    }\n\n    if (typeof values.selection === 'string') {\n      const newSelection = JSON.parse(values.selection);\n      if (!isEqual(newSelection, this.state.selection)) {\n        this.setState({ selection: newSelection });\n      }\n    }\n  }\n\n  onUserUpdateSelection(newSelection: ComparisonSelection) {\n    this._urlSync.performBrowserHistoryAction(() => {\n      this.setState({ selection: newSelection });\n    });\n  }\n\n  public setActionView(actionView?: ActionViewType) {\n    const { body } = this.state;\n    const actionViewDef = actionViewsDefinitions.find((v) => v.value === actionView);\n    const traceExploration = getTraceExplorationScene(this);\n    const metric = traceExploration.getMetricVariable().getValue();\n\n    if (body.state.children.length > 1) {\n      if (actionViewDef) {\n        let scene: SceneObject;\n        if (actionView === 'exceptions' && this.state.exceptionsScene) {\n          // Use the persistent exceptions scene to maintain data subscription\n          scene = new SceneFlexItem({\n            body: this.state.exceptionsScene,\n          });\n        } else {\n          scene = actionViewDef.getScene(metric as MetricFunction);\n        }\n        \n        body.setState({\n          children: [...body.state.children.slice(0, 2), scene],\n        });\n        reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.action_view_changed, {\n          oldAction: this.state.actionView,\n          newAction: actionView,\n        });\n        this.setState({ actionView: actionViewDef.value });\n      }\n    }\n  }\n\n  private updateQueryRunner(metric: MetricFunction) {\n    const selection = this.state.selection;\n    const columns = getSpanListColumnsVariable(this).getValue()?.toString() ?? '';\n\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new SceneQueryRunner({\n          datasource: explorationDS,\n          queries: [buildQuery(metric, columns, selection)],\n          $timeRange: timeRangeFromSelection(selection),\n        }),\n        transformations: [...filterStreamingProgressTransformations, ...spanListTransformations],\n      }),\n    });\n  }\n\n  static Component = ({ model }: SceneComponentProps<TracesByServiceScene>) => {\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <>\n        <div className={styles.title}>\n          <Tooltip content={<MetricTypeTooltip />} placement={'right-start'} interactive>\n            <span className={styles.hand}>\n              Select metric type <Icon name={'info-circle'} />\n            </span>\n          </Tooltip>\n        </div>\n        <body.Component model={body} />\n      </>\n    );\n  };\n}\n\nconst MetricTypeTooltip = () => {\n  const styles = useStyles2(getStyles);\n\n  return (\n    <Stack direction={'column'} gap={1}>\n      <div className={styles.tooltip.title}>RED metrics for traces</div>\n      <span className={styles.tooltip.subtitle}>\n        Explore rate, errors, and duration (RED) metrics generated from traces by Tempo.\n      </span>\n      <div className={styles.tooltip.text}>\n        <div>\n          <span className={styles.tooltip.emphasize}>Rate</span> - Spans per second that match your filter, useful to\n          find unusual spikes in activity\n        </div>\n        <div>\n          <span className={styles.tooltip.emphasize}>Errors</span> -Spans that are failing, overall issues in tracing\n          ecosystem\n        </div>\n        <div>\n          <span className={styles.tooltip.emphasize}>Duration</span> - Amount of time those spans take, represented as a\n          heat map (responds time, latency)\n        </div>\n      </div>\n\n      <div className={styles.tooltip.button}>\n        <LinkButton\n          icon=\"external-link-alt\"\n          fill=\"solid\"\n          size={'sm'}\n          target={'_blank'}\n          href={\n            'https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces/concepts/#rate-error-and-duration-metrics'\n          }\n          onClick={() =>\n            reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.metric_docs_link_clicked)\n          }\n        >\n          Read documentation\n        </LinkButton>\n      </div>\n    </Stack>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    title: css({\n      label: 'title',\n      display: 'flex',\n      gap: theme.spacing.x0_5,\n      fontSize: theme.typography.bodySmall.fontSize,\n      paddingBottom: theme.spacing.x0_5,\n      alignItems: 'center',\n    }),\n    hand: css({\n      label: 'hand',\n      cursor: 'pointer',\n    }),\n    tooltip: {\n      label: 'tooltip',\n      title: css({\n        fontSize: '14px',\n        fontWeight: 500,\n      }),\n      subtitle: css({\n        marginBottom: theme.spacing.x1,\n      }),\n      text: css({\n        label: 'text',\n        color: theme.colors.text.secondary,\n\n        div: {\n          marginBottom: theme.spacing.x0_5,\n        },\n      }),\n      emphasize: css({\n        label: 'emphasize',\n        color: theme.colors.text.primary,\n      }),\n      button: css({\n        marginBottom: theme.spacing.x0_5,\n      }),\n    },\n  };\n}\n\nconst MAIN_PANEL_HEIGHT = 240;\nexport const MINI_PANEL_HEIGHT = (MAIN_PANEL_HEIGHT - 8) / 2;\n\nexport function buildQuery(type: MetricFunction, columns: string, selection?: ComparisonSelection) {\n  const selectQuery = columns !== '' ? ` | select(${columns})` : '';\n  let typeQuery = '';\n  switch (type) {\n    case 'errors':\n      typeQuery = ' && status = error';\n      break;\n    case 'duration':\n      if (selection) {\n        const duration = [];\n        if (selection.duration?.from.length) {\n          duration.push(`duration >= ${selection.duration.from}`);\n        }\n        if (selection.duration?.to.length) {\n          duration.push(`duration <= ${selection.duration.to}`);\n        }\n        if (duration.length) {\n          typeQuery += '&& ' + duration.join(' && ');\n        }\n      }\n      if (!typeQuery.length) {\n        typeQuery = `&& duration > ${VAR_LATENCY_THRESHOLD_EXPR}`;\n      }\n      break;\n  }\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR}${typeQuery}}${selectQuery}`,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 200,\n    spss: 10,\n    filters: [],\n  };\n}\n\nfunction timeRangeFromSelection(selection?: ComparisonSelection) {\n  const fromTimerange = (selection?.timeRange?.from || 0) * 1000;\n  const toTimerange = (selection?.timeRange?.to || 0) * 1000;\n  return fromTimerange && toTimerange\n    ? new SceneTimeRange({\n        from: fromTimerange.toFixed(0),\n        to: toTimerange.toFixed(0),\n        value: {\n          from: dateTime(fromTimerange),\n          to: dateTime(toTimerange),\n          raw: { from: dateTime(fromTimerange), to: dateTime(toTimerange) },\n        },\n      })\n    : undefined;\n}\n\nfunction buildGraphScene(metric: MetricFunction, children?: SceneObject[]) {\n  const secondaryPanel =\n    metric === 'rate'\n      ? new MiniREDPanel({ metric: 'errors' })\n      : new MiniREDPanel({\n          metric: 'rate',\n        });\n\n  const tertiaryPanel =\n    metric === 'duration'\n      ? new MiniREDPanel({\n          metric: 'errors',\n        })\n      : new MiniREDPanel({ metric: 'duration' });\n\n  return new SceneFlexLayout({\n    direction: 'column',\n    $behaviors: [\n      new behaviors.CursorSync({\n        key: 'metricCrosshairSync',\n        sync: DashboardCursorSync.Crosshair,\n      }),\n    ],\n    children: [\n      new SceneFlexLayout({\n        direction: 'row',\n        ySizing: 'content',\n        children: [\n          new SceneFlexItem({\n            minHeight: MAIN_PANEL_HEIGHT,\n            maxHeight: MAIN_PANEL_HEIGHT,\n            width: '60%',\n            body: new REDPanel({}),\n          }),\n          new SceneFlexLayout({\n            direction: 'column',\n            minHeight: MAIN_PANEL_HEIGHT,\n            maxHeight: MAIN_PANEL_HEIGHT,\n            children: [\n              new SceneFlexItem({\n                minHeight: MINI_PANEL_HEIGHT,\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n\n                body: secondaryPanel,\n              }),\n              new SceneFlexItem({\n                minHeight: MINI_PANEL_HEIGHT,\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n\n                ySizing: 'fill',\n\n                body: tertiaryPanel,\n              }),\n            ],\n          }),\n        ],\n      }),\n      new SceneFlexItem({\n        ySizing: 'content',\n        body: new TabsBarScene({}),\n      }),\n      ...(children || []),\n    ],\n  });\n}\n\nconst spanListTransformations = [\n  () => (source: Observable<DataFrame[]>) => {\n    return source.pipe(\n      map((data: DataFrame[]) => {\n        return data.map((df) => ({\n          ...df,\n          fields: df.fields.filter((f) => !f.name.startsWith('nestedSet')),\n        }));\n      })\n    );\n  },\n  {\n    id: 'sortBy',\n    options: {\n      fields: {},\n      sort: [\n        {\n          field: 'Duration',\n          desc: true,\n        },\n      ],\n    },\n  },\n  {\n    id: 'organize',\n    options: {\n      indexByName: {\n        'Start time': 0,\n        status: 1,\n        'Trace Service': 2,\n        'Trace Name': 3,\n        Duration: 4,\n        'Span ID': 5,\n        'span.http.method': 6,\n        'span.http.request.method': 7,\n        'span.http.path': 8,\n        'span.http.route': 9,\n        'span.http.status_code': 10,\n        'span.http.response.status_code': 11,\n      },\n    },\n  },\n];\n", "import { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport React from 'react';\nimport { EmptyState } from './EmptyState';\n\ninterface EmptyStateSceneState extends SceneObjectState {\n  message?: string;\n  remedyMessage?: string;\n  imgWidth?: number;\n  padding?: string;\n}\n\nexport class EmptyStateScene extends SceneObjectBase<EmptyStateSceneState> {\n  public static Component = ({ model }: SceneComponentProps<EmptyStateScene>) => {\n    const { message, remedyMessage, imgWidth, padding } = model.useState();\n    return <EmptyState message={message} remedyMessage={remedyMessage} imgWidth={imgWidth} padding={padding} />;\n  };\n}\n", "import React from 'react';\n\nimport { DataFrame } from '@grafana/data';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps, AdHocFiltersVariable } from '@grafana/scenes';\nimport { Button } from '@grafana/ui';\nimport { getFiltersVariable, getLabelValue } from '../../../utils/utils';\nimport { DATABASE_CALLS_KEY } from 'pages/Explore/primary-signals';\n\nexport interface AddToFiltersActionState extends SceneObjectState {\n  frame: DataFrame;\n  onClick: (payload: any) => void;\n  labelKey?: string;\n}\n\nexport class AddToFiltersAction extends SceneObjectBase<AddToFiltersActionState> {\n  public onClick = () => {\n    const variable = getFiltersVariable(this);\n\n    const labels = this.state.frame.fields.find((f) => f.labels)?.labels ?? {};\n    if (this.state.labelKey) {\n      if (!labels[this.state.labelKey]) {\n        return;\n      }\n    } else {\n      if (Object.keys(labels).length !== 1) {\n        return;\n      }\n    }\n\n    const labelName = this.state.labelKey ?? Object.keys(labels)[0];\n    const value = getLabelValue(this.state.frame, this.state.labelKey);\n\n    addToFilters(variable, labelName, value);\n\n    this.state.onClick({ labelName });\n  };\n\n  public static Component = ({ model }: SceneComponentProps<AddToFiltersAction>) => {\n    const key = model.state?.labelKey ?? '';\n    const field = model.state?.frame.fields.filter((x) => x.type !== 'time');\n    const value = field?.[0]?.labels?.[key] ?? '';\n    const filterExists = filterExistsForKey(getFiltersVariable(model), key, value.replace(/\"/g, ''));\n\n    if (!filterExists) {\n      return (\n        <Button variant=\"primary\" size=\"sm\" fill=\"text\" onClick={model.onClick} icon={'search-plus'}>\n          Add to filters\n        </Button>\n      );\n    }\n    return <></>;\n  };\n}\n\nexport const addToFilters = (variable: AdHocFiltersVariable, label: string, value: string) => {\n  // ensure we set the new filter with latest value\n  // and remove any existing filter for the same key\n  // and also keep span.db.system.name as it is a primary filter\n  const filtersWithoutNew = variable.state.filters.filter((f) => f.key === DATABASE_CALLS_KEY || f.key !== label);\n\n  // TODO: Replace it with new API introduced in https://github.com/grafana/scenes/issues/1103\n  // At the moment AdHocFiltersVariable doesn't support pushing new history entry on change\n  history.pushState(null, '');\n\n  variable.setState({\n    filters: [\n      ...filtersWithoutNew,\n      {\n        key: label,\n        operator: '=',\n        value: value,\n      },\n    ],\n  });\n};\n\nexport const filterExistsForKey = (model: AdHocFiltersVariable, key: string, value: string) => {\n  const variable = getFiltersVariable(model);\n  return variable.state.filters.find((f) => f.key === key && f.value === value);\n};\n", "import { VAR_FILTERS_EXPR } from '../../../utils/shared';\n\nexport function buildHistogramQuery() {\n  return {\n    refId: 'A',\n    query: `{${VAR_FILTERS_EXPR}} | histogram_over_time(duration) with(sample=true)`,\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 1000,\n    spss: 10,\n    filters: [],\n  };\n}\n", "import { css } from '@emotion/css';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { locationService } from '@grafana/runtime';\nimport { Icon, useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { HomepagePanelType } from './AttributePanel';\n\ntype Props = {\n  index: number;\n  type: HomepagePanelType;\n  label: string;\n  labelTitle: string;\n  value: string;\n  valueTitle: string;\n  url: string;\n};\n\nexport const AttributePanelRow = (props: Props) => {\n  const { index, type, label, labelTitle, value, valueTitle, url } = props;\n  const styles = useStyles2(getStyles);\n\n  return (\n    <div key={index}>\n      {index === 0 && (\n        <div className={styles.rowHeader}>\n          <span>{labelTitle}</span>\n          <span className={styles.valueTitle}>{valueTitle}</span>\n        </div>\n      )}\n\n      <div\n        className={styles.row}\n        key={index}\n        onClick={() => {\n          reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.panel_row_clicked, {\n            type,\n            index,\n            value,\n          });\n          locationService.push(url);\n        }}\n      >\n        <div className={'rowLabel'}>{label}</div>\n\n        <div className={styles.action}>\n          <span className={styles.actionText}>{value}</span>\n          <Icon className={styles.actionIcon} name=\"arrow-right\" size=\"xl\" />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    rowHeader: css({\n      color: theme.colors.text.secondary,\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      padding: `0 ${theme.spacing(2)} ${theme.spacing(1)} ${theme.spacing(2)}`,\n    }),\n    valueTitle: css({\n      margin: '0 45px 0 0',\n    }),\n    row: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      gap: theme.spacing(2),\n      padding: `${theme.spacing(0.75)} ${theme.spacing(2)}`,\n\n      '&:hover': {\n        backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n        cursor: 'pointer',\n        '.rowLabel': {\n          textDecoration: 'underline',\n        },\n      },\n    }),\n    action: css({\n      display: 'flex',\n      alignItems: 'center',\n    }),\n    actionText: css({\n      color: '#d5983c',\n      padding: `0 ${theme.spacing(1)}`,\n      width: 'max-content',\n    }),\n    actionIcon: css({\n      cursor: 'pointer',\n      margin: `0 ${theme.spacing(0.5)} 0 ${theme.spacing(1)}`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2, urlUtil } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { EXPLORATIONS_ROUTE } from 'utils/shared';\nimport { AttributePanelRow } from './AttributePanelRow';\nimport { HomepagePanelType } from './AttributePanel';\n\ntype Props = {\n  series: DataFrame[];\n  type: HomepagePanelType;\n};\n\nexport const ErroredServicesRows = (props: Props) => {\n  const { series, type } = props;\n  const styles = useStyles2(getStyles);\n\n  const getLabel = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return valuesField?.labels?.['resource.service.name'].replace(/\"/g, '') ?? 'Service name not found';\n  };\n\n  const getUrl = (df: DataFrame) => {\n    const serviceName = getLabel(df);\n    const params = {\n      'var-filters': `resource.service.name|=|${serviceName}`,\n      'var-metric': 'errors',\n    };\n    return urlUtil.renderUrl(EXPLORATIONS_ROUTE, params);\n  };\n\n  const getTotalErrs = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return (\n      valuesField?.values?.reduce((x, acc) => {\n        if (typeof x === 'number' && !isNaN(x)) {\n          return x + acc;\n        }\n        return acc;\n      }, 0) ?? 1\n    );\n  };\n\n  return (\n    <div className={styles.container}>\n      {series\n        .sort((a, b) => getTotalErrs(b) - getTotalErrs(a))\n        .slice(0, 10)\n        ?.map((df, index) => (\n          <span key={index}>\n            <AttributePanelRow\n              type={type}\n              index={index}\n              label={getLabel(df)}\n              labelTitle=\"Service\"\n              value={getTotalErrs(df)}\n              valueTitle=\"Total errors\"\n              url={getUrl(df)}\n            />\n          </span>\n        ))}\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, Field, GrafanaTheme2, urlUtil } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { EXPLORATIONS_ROUTE, ROUTES } from 'utils/shared';\nimport { AttributePanelRow } from './AttributePanelRow';\nimport { HomepagePanelType } from './AttributePanel';\nimport { formatDuration } from '../../utils/dates';\n\ntype Props = {\n  series: DataFrame[];\n  type: HomepagePanelType;\n};\n\nexport const SlowestTracesRows = (props: Props) => {\n  const { series, type } = props;\n  const styles = useStyles2(getStyles);\n\n  const durField = series[0].fields.find((f) => f.name === 'duration');\n  if (durField && durField.values) {\n    const sortedByDuration = durField?.values\n      .map((_, i) => i)\n      ?.sort((a, b) => durField?.values[b] - durField?.values[a]);\n    const sortedFields = series[0].fields.map((f) => {\n      return {\n        ...f,\n        values: sortedByDuration?.map((i) => f.values[i]),\n      };\n    });\n\n    const getLabel = (traceServiceField: Field | undefined, traceNameField: Field | undefined, index: number) => {\n      let label = '';\n      if (traceServiceField?.values[index]) {\n        label = traceServiceField.values[index];\n      }\n      if (traceNameField?.values[index]) {\n        label = label.length === 0 ? traceNameField.values[index] : `${label}: ${traceNameField.values[index]}`;\n      }\n      return label.length === 0 ? 'Trace service & name not found' : label;\n    };\n\n    const getUrl = (\n      traceId: string,\n      spanIdField: Field | undefined,\n      traceServiceField: Field | undefined,\n      index: number\n    ) => {\n      if (!spanIdField || !spanIdField.values[index] || !traceServiceField || !traceServiceField.values[index]) {\n        console.error('SpanId or traceService not found');\n        return ROUTES.Explore;\n      }\n\n      const params = {\n        traceId,\n        spanId: spanIdField.values[index],\n        'var-filters': `resource.service.name|=|${traceServiceField.values[index]}`,\n        'var-metric': 'duration',\n      };\n\n      return urlUtil.renderUrl(EXPLORATIONS_ROUTE, params);\n    };\n\n    const getDuration = (durationField: Field | undefined, index: number) => {\n      if (!durationField || !durationField.values) {\n        return 'Duration not found';\n      }\n\n      return formatDuration(durationField.values[index] / 1000);\n    };\n\n    const traceIdField = sortedFields.find((f) => f.name === 'traceIdHidden');\n    const spanIdField = sortedFields.find((f) => f.name === 'spanID');\n    const traceNameField = sortedFields.find((f) => f.name === 'traceName');\n    const traceServiceField = sortedFields.find((f) => f.name === 'traceService');\n    const durationField = sortedFields.find((f) => f.name === 'duration');\n\n    return (\n      <div className={styles.container}>\n        {traceIdField?.values?.map((traceId, index) => (\n          <span key={index}>\n            <AttributePanelRow\n              type={type}\n              index={index}\n              label={getLabel(traceServiceField, traceNameField, index)}\n              labelTitle=\"Trace\"\n              value={getDuration(durationField, index)}\n              valueTitle=\"Duration\"\n              url={getUrl(traceId, spanIdField, traceServiceField, index)}\n            />\n          </span>\n        ))}\n      </div>\n    );\n  }\n  return null;\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2, urlUtil } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { EXPLORATIONS_ROUTE } from 'utils/shared';\nimport { AttributePanelRow } from './AttributePanelRow';\nimport { HomepagePanelType } from './AttributePanel';\nimport { formatDuration } from '../../utils/dates';\n\ntype Props = {\n  series: DataFrame[];\n  type: HomepagePanelType;\n};\n\nexport const SlowestServicesRows = (props: Props) => {\n  const { series, type } = props;\n  const styles = useStyles2(getStyles);\n\n  const getLabel = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return valuesField?.labels?.['resource.service.name'].replace(/\"/g, '') ?? 'Service name not found';\n  };\n\n  const getUrl = (df: DataFrame) => {\n    const serviceName = getLabel(df);\n    const params = {\n      'var-filters': `resource.service.name|=|${serviceName}`,\n      'var-metric': 'duration',\n    };\n    return urlUtil.renderUrl(EXPLORATIONS_ROUTE, params);\n  };\n\n  const getDuration = (df: DataFrame) => {\n    const valuesField = df.fields.find((f) => f.name !== 'time');\n    return (\n      valuesField?.values?.reduce((x, acc) => {\n        if (typeof x === 'number' && !isNaN(x)) {\n          return x + acc;\n        }\n        return acc;\n      }, 0) ?? 1\n    );\n  };\n\n  return (\n    <div className={styles.container}>\n      {series\n        .sort((a, b) => getDuration(b) - getDuration(a))\n        .slice(0, 10)\n        ?.map((df, index) => (\n          <span key={index}>\n            <AttributePanelRow\n              type={type}\n              index={index}\n              label={getLabel(df)}\n              labelTitle=\"Service\"\n              value={formatDuration(getDuration(df) * 1000000 /*s to μs*/)}\n              valueTitle=\"p90\"\n              url={getUrl(df)}\n            />\n          </span>\n        ))}\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport { Icon, useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { HomepagePanelType } from './AttributePanel';\nimport { ErroredServicesRows } from './ErroredServicesRows';\nimport { SlowestTracesRows } from './SlowestTracesRows';\nimport { SlowestServicesRows } from './SlowestServicesRows';\n\ntype Props = {\n  series?: DataFrame[];\n  type: HomepagePanelType;\n  message?: string;\n};\n\nexport const AttributePanelRows = (props: Props) => {\n  const { series, type, message } = props;\n  const styles = useStyles2(getStyles);\n\n  if (message) {\n    return (\n      <div className={styles.container}>\n        <div className={styles.message}>\n          <Icon className={styles.icon} name=\"exclamation-circle\" size=\"xl\" />\n          {message}\n        </div>\n      </div>\n    );\n  }\n\n  if (series && series.length > 0) {\n    switch (type) {\n      case 'slowest-traces':\n        return <SlowestTracesRows series={series} type={type} />;\n      case 'errored-services':\n        return <ErroredServicesRows series={series} type={type} />;\n      case 'slowest-services':\n        return <SlowestServicesRows series={series} type={type} />;\n    }\n  }\n  return <div className={styles.container}>No series data</div>;\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      padding: `${theme.spacing(2)} 0`,\n    }),\n    icon: css({\n      margin: `0 ${theme.spacing(0.5)} 0 ${theme.spacing(1)}`,\n    }),\n    message: css({\n      display: 'flex',\n      gap: theme.spacing(1.5),\n      margin: `${theme.spacing(2)} auto`,\n      width: '60%',\n    }),\n  };\n}\n", "import { css } from '@emotion/css';\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport { Icon, useStyles2 } from '@grafana/ui';\nimport React from 'react';\nimport { AttributePanelRows } from './AttributePanelRows';\nimport { HomepagePanelType } from './AttributePanel';\n\ninterface AttributePanelSceneState extends SceneObjectState {\n  series?: DataFrame[];\n  title: string;\n  type: HomepagePanelType;\n  message?: string;\n}\n\nexport class AttributePanelScene extends SceneObjectBase<AttributePanelSceneState> {\n  public static Component = ({ model }: SceneComponentProps<AttributePanelScene>) => {\n    const { series, title, type, message } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.title}>\n          <Icon name={getIcon(type)} size=\"lg\" />\n          <span className={styles.titleText}>{title}</span>\n        </div>\n        <AttributePanelRows series={series} type={type} message={message} />\n      </div>\n    );\n  };\n}\n\nfunction getIcon(type: HomepagePanelType) {\n  switch (type) {\n    case 'slowest-services':\n      return 'clock-nine';\n    case 'slowest-traces':\n      return 'crosshair';\n    case 'errored-services':\n      return 'exclamation-triangle';\n    default:\n      return 'exclamation-triangle';\n  }\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      border: `1px solid ${theme.isDark ? theme.colors.border.medium : theme.colors.border.weak}`,\n      borderRadius: theme.spacing(0.5),\n      marginBottom: theme.spacing(4),\n      width: '100%',\n    }),\n    title: css({\n      color: theme.isDark ? theme.colors.text.secondary : theme.colors.text.primary,\n      backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n      borderTopLeftRadius: theme.spacing(0.5),\n      borderTopRightRadius: theme.spacing(0.5),\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      fontSize: '1.3rem',\n      padding: `${theme.spacing(1.5)} ${theme.spacing(2)}`,\n    }),\n    titleText: css({\n      marginLeft: theme.spacing(1),\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport { GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { explorationDS } from 'utils/shared';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { MINI_PANEL_HEIGHT } from 'components/Explore/TracesByService/TracesByServiceScene';\nimport { AttributePanelScene } from './AttributePanelScene';\nimport Skeleton from 'react-loading-skeleton';\nimport { getErrorMessage, getNoDataMessage } from 'utils/utils';\nimport { getMinimumsForDuration, getYBuckets } from 'components/Explore/TracesByService/REDPanel';\n\nexport type HomepagePanelType = 'errored-services' | 'slowest-services' | 'slowest-traces';\n\nexport interface AttributePanelState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  query: {\n    query: string;\n    step?: string;\n  };\n  title: string;\n  type: HomepagePanelType;\n  renderDurationPanel?: boolean;\n  filter?: string;\n}\n\nexport class AttributePanel extends SceneObjectBase<AttributePanelState> {\n  constructor(state: AttributePanelState) {\n    super({\n      $data: new SceneQueryRunner({\n        datasource: explorationDS,\n        queries: [{ refId: 'A', queryType: 'traceql', tableType: 'spans', limit: 10, ...state.query, exemplars: 0 }],\n      }),\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          if (data.data?.state === LoadingState.Done || data.data?.state === LoadingState.Streaming) {\n            if (\n              data.data?.state === LoadingState.Done &&\n              (data.data.series.length === 0 || data.data.series[0].length === 0)\n            ) {\n              this.setState({\n                panel: new SceneFlexLayout({\n                  children: [\n                    new AttributePanelScene({\n                      message: getNoDataMessage(state.title.toLowerCase()),\n                      title: state.title,\n                      type: state.type,\n                    }),\n                  ],\n                }),\n              });\n            } else if (data.data.series.length > 0) {\n              if (state.type !== 'slowest-traces' || state.renderDurationPanel) {\n                this.setState({\n                  panel: new SceneFlexLayout({\n                    children: [\n                      new AttributePanelScene({\n                        series: data.data.series,\n                        title: state.title,\n                        type: state.type,\n                      }),\n                    ],\n                  }),\n                });\n              } else if (data.data?.state === LoadingState.Done) {\n                let yBuckets = getYBuckets(data.data?.series ?? []);\n                if (yBuckets?.length) {\n                  const { minDuration } = getMinimumsForDuration(yBuckets);\n\n                  this.setState({\n                    panel: new SceneFlexLayout({\n                      children: [\n                        new AttributePanel({\n                          query: {\n                            query: `{nestedSetParent<0 && duration > ${minDuration} ${state.filter ?? ''}}`,\n                          },\n                          title: state.title,\n                          type: state.type,\n                          renderDurationPanel: true,\n                        }),\n                      ],\n                    }),\n                  });\n                }\n              }\n            }\n          } else if (data.data?.state === LoadingState.Error) {\n            this.setState({\n              panel: new SceneFlexLayout({\n                children: [\n                  new AttributePanelScene({\n                    message: getErrorMessage(data),\n                    title: state.title,\n                    type: state.type,\n                  }),\n                ],\n              }),\n            });\n          } else {\n            this.setState({\n              panel: new SceneFlexLayout({\n                direction: 'column',\n                maxHeight: MINI_PANEL_HEIGHT,\n                height: MINI_PANEL_HEIGHT,\n                children: [\n                  new LoadingStateScene({\n                    component: () => SkeletonComponent(),\n                  }),\n                ],\n              }),\n            });\n          }\n        })\n      );\n    });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<AttributePanel>) => {\n    const { panel } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={styles.container}>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nfunction getStyles() {\n  return {\n    container: css({\n      minWidth: '350px',\n      width: '-webkit-fill-available',\n    }),\n  };\n}\n\nexport const SkeletonComponent = () => {\n  const styles = useStyles2(getSkeletonStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.title}>\n        <Skeleton count={1} width={200} />\n      </div>\n      <div className={styles.tracesContainer}>\n        {[...Array(11)].map((_, i) => (\n          <div className={styles.row} key={i}>\n            <div className={styles.rowLeft}>\n              <Skeleton count={1} />\n            </div>\n            <div className={styles.rowRight}>\n              <Skeleton count={1} />\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      border: `1px solid ${theme.isDark ? theme.colors.border.medium : theme.colors.border.weak}`,\n      borderRadius: theme.spacing(0.5),\n      marginBottom: theme.spacing(4),\n      width: '100%',\n    }),\n    title: css({\n      color: theme.colors.text.secondary,\n      backgroundColor: theme.colors.background.secondary,\n      fontSize: '1.3rem',\n      padding: `${theme.spacing(1.5)} ${theme.spacing(2)}`,\n      textAlign: 'center',\n    }),\n    tracesContainer: css({\n      padding: `13px ${theme.spacing(2)}`,\n    }),\n    row: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n    }),\n    rowLeft: css({\n      margin: '7px 0',\n      width: '150px',\n    }),\n    rowRight: css({\n      width: '50px',\n    }),\n  };\n}\n", "import React from \"react\";\n\nexport const LightModeRocket = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"73\" height=\"72\" viewBox=\"0 0 73 72\" fill=\"none\">\n    <path\n      d=\"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z\"\n      fill=\"#24292E\"\n      fillOpacity=\"0.75\"\n    />\n  </svg>\n);\n\nexport const DarkModeRocket = () => (\n  <svg xmlns=\"http://www.w3.org/2000/svg\" width=\"73\" height=\"72\" viewBox=\"0 0 73 72\" fill=\"none\">\n    <path\n      d=\"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z\"\n      fill=\"#CCCCDC\"\n      fillOpacity=\"0.65\"\n    />\n  </svg>\n);\n", "import { ACTION_VIEW, PRIMARY_SIGNAL, VAR_FILTERS, FILTER_SEPARATOR, BOOKMARKS_LS_KEY, EXPLORATIONS_ROUTE, VAR_LATENCY_PARTIAL_THRESHOLD, VAR_LATENCY_THRESHOLD, SELECTION, VAR_METRIC } from \"utils/shared\";\nimport { Bookmark } from \"./Bookmarks\";\nimport { urlUtil } from \"@grafana/data\";\nimport { locationService, usePluginUserStorage } from '@grafana/runtime';\nimport { USER_EVENTS_ACTIONS, USER_EVENTS_PAGES, reportAppInteraction } from \"utils/analytics\";\n\ntype PluginStorage = ReturnType<typeof usePluginUserStorage>;\n\nconst cleanupParams = (params: URLSearchParams) => {\n  // Remove selection, latency threshold, and latency partial threshold because\n  // selection keeps changing as time moves on, so it's not a good match for bookmarking\n  params.delete(SELECTION);\n  params.delete(`var-${VAR_LATENCY_THRESHOLD}`);\n  params.delete(`var-${VAR_LATENCY_PARTIAL_THRESHOLD}`);\n  return params;\n}\n\nexport const useBookmarksStorage = () => {\n  const storage = usePluginUserStorage();\n  \n  return {\n    getBookmarks: () => getBookmarks(storage),\n    removeBookmark: (bookmark: Bookmark) => removeBookmark(storage, bookmark),\n    bookmarkExists: (bookmark: Bookmark) => bookmarkExists(storage, bookmark),\n    toggleBookmark: () => toggleBookmark(storage),\n  };\n};\n\nexport const getBookmarkParams = (bookmark: Bookmark) => {\n  if (!bookmark || !bookmark.params) {\n    return { actionView: '', primarySignal: '', filters: '', metric: '' };\n  }\n  \n  const params = new URLSearchParams(bookmark.params);\n  const actionView = params.get(ACTION_VIEW) ?? '';\n  const primarySignal = params.get(PRIMARY_SIGNAL) ?? '';\n  const filters = params.getAll(`var-${VAR_FILTERS}`).join(FILTER_SEPARATOR);\n  const metric = params.get(`var-${VAR_METRIC}`) ?? '';\n  return { actionView, primarySignal, filters, metric };\n}\n\nexport const getBookmarkFromURL = (): Bookmark => {\n  const params = cleanupParams(new URLSearchParams(window.location.search));\n  return { params: params.toString() };\n}\n\nexport const getBookmarkForUrl = (bookmark: Bookmark): string => {\n  if (!bookmark || !bookmark.params) {\n    return EXPLORATIONS_ROUTE;\n  }\n  \n  const params = new URLSearchParams(bookmark.params);\n  const urlQueryMap = Object.fromEntries(params.entries());\n  \n  const filters = params.getAll(`var-${VAR_FILTERS}`); \n  \n  const url = urlUtil.renderUrl(EXPLORATIONS_ROUTE, {\n    ...urlQueryMap,\n    [`var-${VAR_FILTERS}`]: filters // Filters need to be added as separate params in the url as there are multiple filters with the same key\n  });\n  \n  return url;\n}\n\nconst setBookmarks = async (storage: PluginStorage, bookmarks: Bookmark[]): Promise<void> => {\n  try {\n    await storage.setItem(BOOKMARKS_LS_KEY, JSON.stringify(bookmarks));\n  } catch (e) {\n    console.error(\"Failed to save bookmarks to storage:\", e);\n  }\n};\n\nexport const getBookmarks = async (storage: PluginStorage): Promise<Bookmark[]> => {\n  try {\n    const value = await storage.getItem(BOOKMARKS_LS_KEY);\n    if (value) {\n      return JSON.parse(value);\n    }\n    return [];\n  } catch (e) {\n    console.error(\"Failed to get bookmarks from storage:\", e);\n    return [];\n  }\n};\n\nexport const toggleBookmark = async (storage: PluginStorage): Promise<boolean> => {\n  const bookmark = getBookmarkFromURL();\n  const exists = await bookmarkExists(storage, bookmark);\n  \n  if (exists) {\n    await removeBookmark(storage, bookmark);\n    return false;\n  } else {\n    await addBookmark(storage, bookmark);\n    return true;\n  }\n};\n\nconst addBookmark = async (storage: PluginStorage, bookmark: Bookmark): Promise<void> => {\n  const bookmarks = await getBookmarks(storage);\n  bookmarks.push(bookmark);\n  await setBookmarks(storage, bookmarks);\n};\n\nexport const removeBookmark = async (storage: PluginStorage, bookmark: Bookmark): Promise<void> => {\n  const storedBookmarks = await getBookmarks(storage);\n  const filteredBookmarks = storedBookmarks.filter((storedBookmark) => !areBookmarksEqual(bookmark, storedBookmark));\n  await setBookmarks(storage, filteredBookmarks);\n};\n\nexport const bookmarkExists = async (storage: PluginStorage, bookmark: Bookmark): Promise<boolean> => {\n  const bookmarks = await getBookmarks(storage);\n  return bookmarks.some((b) => areBookmarksEqual(bookmark, b));\n};\n\nexport const areBookmarksEqual = (bookmark: Bookmark, storedBookmark: Bookmark) => {\n  const bookmarkParams = cleanupParams(new URLSearchParams(bookmark.params));\n  const storedBookmarkParams = cleanupParams(new URLSearchParams(storedBookmark.params));\n\n  const filterKey = `var-${VAR_FILTERS}`;\n  const bookmarkKeys = Array.from(bookmarkParams.keys()).filter(k => k !== filterKey);\n  const storedKeys = Array.from(storedBookmarkParams.keys()).filter(k => k !== filterKey);\n\n  // If they have different number of keys (excluding filters), they can't be equal\n  if (bookmarkKeys.length !== storedKeys.length) {\n    return false;\n  }\n  \n  // Check if every key in bookmarkParams exists in storedBookmarkParams with the same value\n  const allKeysMatch = bookmarkKeys.every(key => \n    storedBookmarkParams.has(key) && bookmarkParams.get(key) === storedBookmarkParams.get(key)\n  );  \n  if (!allKeysMatch) {\n    return false;\n  }\n  \n  // Compare filters (which can have multiple values with the same key)\n  const bookmarkFilters = bookmarkParams.getAll(filterKey);\n  const storedFilters = storedBookmarkParams.getAll(filterKey);  \n  if (bookmarkFilters.length !== storedFilters.length) {\n    return false;\n  }\n  \n  // Check if every filter in bookmarkFilters exists in storedFilters\n  // This handles cases where order might be different\n  return bookmarkFilters.every(filter => storedFilters.includes(filter));\n}\n\nexport const goToBookmark = (bookmark: Bookmark) => {\n  reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.go_to_bookmark_clicked);\n  const url = getBookmarkForUrl(bookmark);\n  locationService.push(url);\n}\n", "import { EVENT_ATTR, FILTER_SEPARATOR, RESOURCE_ATTR, SPAN_ATTR } from \"utils/shared\";\nimport React from \"react\";\nimport { capitalizeFirstChar } from \"utils/utils\";\nimport { css } from \"@emotion/css\";\nimport { useStyles2 } from \"@grafana/ui\";\nimport { Bookmark } from \"./Bookmarks\";\nimport { getBookmarkParams } from \"./utils\";\nimport { getSignalForKey } from \"pages/Explore/primary-signals\";\n\nexport const BookmarkItem = ({ bookmark }: { bookmark: Bookmark }) => {\n  let { actionView, primarySignal, metric, filters } = getBookmarkParams(bookmark);\n  const styles = useStyles2(getStyles);\n\n  const getPrimarySignalFilter = (primarySignal: string): string => {\n    const signalData = getSignalForKey(primarySignal);\n    if (!signalData || !signalData.filter) {\n      return '';\n    }\n    const filter = signalData.filter;\n\n    if (filter.key && filter.operator && filter.value !== undefined) {\n      return `${filter.key}|${filter.operator}|${filter.value}`;\n    }\n    return '';\n  }\n  \n  // Don't render the primary signal filter as the primary signal already represents this information\n  const getFiltersWithoutPrimarySignal = (filters: string, primarySignal: string): string => {\n    const primarySignalFilter = getPrimarySignalFilter(primarySignal);\n    let filtersArray = filters.split(FILTER_SEPARATOR);\n    filtersArray = filtersArray.filter(f => f !== primarySignalFilter);\n    return filtersArray.join(FILTER_SEPARATOR);\n  }\n\n  filters = getFiltersWithoutPrimarySignal(filters, primarySignal);\n  filters = filters.replace(/\\|=\\|/g, ' = ');\n  filters = filters.replace(RESOURCE_ATTR, '').replace(SPAN_ATTR, '').replace(EVENT_ATTR, '');\n\n  return (\n    <div title={filters}>\n      <div>\n        <b>{capitalizeFirstChar(metric)}</b> of <b>{primarySignal.replace('_', ' ')}</b> ({actionView})\n      </div>\n      <div className={styles.filters}>\n        {filters}\n      </div>\n    </div>\n  );\n}\n\nfunction getStyles() {\n  return {\n    filters: css({\n      textOverflow: 'ellipsis', \n      overflow: 'hidden',\n      WebkitLineClamp: 2, \n      display: '-webkit-box', \n      WebkitBoxOrient: 'vertical'\n    }),\n  }\n}\n", "import { css } from \"@emotion/css\";\nimport { GrafanaTheme2 } from \"@grafana/data\";\nimport { Button, useStyles2, LoadingPlaceholder } from \"@grafana/ui\";\nimport React, { useEffect, useState } from \"react\";\nimport { BookmarkItem } from \"./BookmarkItem\";\nimport { useBookmarksStorage, goToBookmark } from \"./utils\";\n\nexport type Bookmark = {\n  params: string;\n}\n\nexport const Bookmarks = () => {\n  const styles = useStyles2(getStyles);\n  const { getBookmarks, removeBookmark } = useBookmarksStorage();\n  const [bookmarks, setBookmarks] = useState<Bookmark[]>([]);\n  const [isLoading, setIsLoading] = useState<boolean>(true);\n  const [isRemoving, setIsRemoving] = useState<boolean>(false);\n\n  useEffect(() => {\n    const fetchBookmarks = async () => {\n      setIsLoading(true);\n      try {\n        const loadedBookmarks = await getBookmarks();\n        setBookmarks(loadedBookmarks);\n      } catch (error) {\n        console.error('Error loading bookmarks:', error);\n        setBookmarks([]);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    \n    fetchBookmarks();\n  }, []);\n\n  const removeBookmarkClicked = async (bookmark: Bookmark, event: React.MouseEvent) => {\n    event.stopPropagation();\n    setIsRemoving(true);\n    \n    try {\n      await removeBookmark(bookmark);\n      const updatedBookmarks = await getBookmarks();\n      setBookmarks(updatedBookmarks);\n    } catch (error) {\n      console.error('Error removing bookmark:', error);\n    } finally {\n      setIsRemoving(false);\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <div>\n        <div className={styles.header}>\n          <h4>Or view bookmarks</h4>\n        </div>\n        <div className={styles.loading}>\n          <LoadingPlaceholder text=\"Loading bookmarks...\" />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className={styles.header}>\n        <h4>Or view bookmarks</h4>\n      </div>\n      {bookmarks.length === 0 ? (\n        <p className={styles.noBookmarks}>Bookmark your favorite queries to view them here.</p>\n      ) : (\n        <div className={styles.bookmarks}>\n          {bookmarks.map((bookmark: Bookmark, i: number) => (\n            <div \n              className={styles.bookmark} \n              key={i} \n              onClick={() => goToBookmark(bookmark)}\n            >\n              <div className={styles.bookmarkItem}>\n                <BookmarkItem bookmark={bookmark} />\n              </div>\n              <div className={styles.remove}>\n                <Button \n                  variant='secondary' \n                  fill='text' \n                  icon='trash-alt'\n                  disabled={isRemoving}\n                  onClick={(e) => removeBookmarkClicked(bookmark, e)}\n                />\n              </div>\n            </div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    header: css({\n      textAlign: 'center',\n      'h4': {\n        margin: 0,\n      }\n    }),\n    bookmarks: css({\n      display: 'flex',\n      flexWrap: 'wrap',\n      gap: theme.spacing(2),\n      margin: `${theme.spacing(4)} 0 ${theme.spacing(2)} 0`,\n      justifyContent: 'center',\n    }),\n    bookmark: css({\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'space-between',\n      cursor: 'pointer',\n      width: '318px',\n      border: `1px solid ${theme.colors.border.medium}`,\n      borderRadius: theme.shape.radius.default,\n\n      '&:hover': {\n        backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n      }\n    }),\n    bookmarkItem: css({\n      padding: `${theme.spacing(1.5)} ${theme.spacing(1.5)} 0 ${theme.spacing(1.5)}`,\n      overflow: 'hidden'\n    }),\n    filters: css({\n      textOverflow: 'ellipsis', \n      overflow: 'hidden',\n      WebkitLineClamp: 2, \n      display: '-webkit-box', \n      WebkitBoxOrient: 'vertical'\n    }),\n    remove: css({\n      display: 'flex',\n      justifyContent: 'flex-end',\n    }),\n    noBookmarks: css({\n      margin: `${theme.spacing(4)} 0 ${theme.spacing(2)} 0`,\n      textAlign: 'center',\n    }),\n    loading: css({\n      display: 'flex',\n      justifyContent: 'center',\n      margin: `${theme.spacing(4)} 0`,\n    }),\n  }\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport {\n  SceneComponentProps,\n  SceneObjectBase,\n} from '@grafana/scenes';\nimport { Button, Icon, LinkButton, Stack, useStyles2, useTheme2 } from '@grafana/ui';\n\nimport {\n  EXPLORATIONS_ROUTE,\n} from '../../utils/shared';\nimport { getDatasourceVariable, getHomeFilterVariable, getHomeScene } from '../../utils/utils';\nimport { DarkModeRocket, LightModeRocket } from '../../utils/rockets';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { Home } from 'pages/Home/Home';\nimport { useNavigate } from 'react-router-dom';\nimport { Bookmarks } from 'pages/Home/bookmarks/Bookmarks';\n\nexport class HeaderScene extends SceneObjectBase {\n  public static Component = ({ model }: SceneComponentProps<Home>) => {\n    const home = getHomeScene(model);\n    const navigate = useNavigate();\n    const { controls } = home.useState();\n    const styles = useStyles2(getStyles);\n    const theme = useTheme2();\n\n    const dsVariable = getDatasourceVariable(home);\n    const filterVariable = getHomeFilterVariable(home);\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.header}>\n          <div className={styles.headerTitleContainer}>\n            {theme.isDark ? <DarkModeRocket /> : <LightModeRocket />}\n            <h2 className={styles.title}>Start your traces exploration!</h2>\n          </div>\n          <div>\n            <p>Drilldown and visualize your trace data without writing a query.</p>\n            <div className={styles.headerActions}>\n              <Button variant='primary' onClick={() => {\n                  reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.explore_traces_clicked);\n                  navigate(EXPLORATIONS_ROUTE);\n                }}>\n                Let’s start\n                <Icon name='arrow-right' size='lg' />\n              </Button>\n              <LinkButton\n                icon=\"external-link-alt\"\n                fill=\"text\"\n                size={'md'}\n                target={'_blank'}\n                href={\n                  'https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces'\n                }\n                className={styles.documentationLink}\n                onClick={() => reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.read_documentation_clicked)}\n              >\n                Read documentation\n              </LinkButton>\n            </div>\n          </div>\n        </div>\n\n        <Bookmarks />\n\n        <div className={styles.subHeader}>\n          <h4>Or quick-start into your tracing data</h4>\n        </div>\n\n        <Stack gap={2}>\n          <div className={styles.variablesAndControls}>\n            <div className={styles.variables}>\n              {dsVariable && (\n                <Stack gap={1} alignItems={'center'}>\n                  <div className={styles.label}>Data source</div>\n                  <dsVariable.Component model={dsVariable} />\n                </Stack>\n              )}\n              {filterVariable && (\n                <Stack gap={1} alignItems={'center'}>\n                  <div className={styles.label}>Filter</div>\n                  <filterVariable.Component model={filterVariable} />\n                </Stack>\n              )}\n            </div>\n\n            <div className={styles.controls}>\n              {controls?.map((control) => (\n                <control.Component key={control.state.key} model={control} />\n              ))}\n            </div>\n          </div>\n        </Stack>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      display: 'flex',\n      gap: theme.spacing(7),\n      flexDirection: 'column',\n      margin: `0 0 ${theme.spacing(4)} 0`,\n      justifyContent: 'center',\n    }),\n    header: css({\n      display: 'flex',\n      alignItems: 'center',\n      backgroundColor: theme.isDark ? theme.colors.background.secondary : theme.colors.background.primary,\n      borderRadius: theme.spacing(0.5),\n      flexWrap: 'wrap',\n      justifyContent: 'center',\n      padding: theme.spacing(3),\n      gap: theme.spacing(4),\n    }),\n    headerTitleContainer: css({\n      display: 'flex',\n      alignItems: 'center',\n    }),\n    title: css({\n      margin: `0 0 0 ${theme.spacing(2)}`,\n    }),\n\n    headerActions: css({\n      alignItems: 'center',\n      justifyContent: 'flex-start',\n      display: 'flex',\n      gap: theme.spacing(2),\n    }),\n    documentationLink: css({\n      textDecoration: 'underline',\n      '&:hover': {\n        textDecoration: 'underline',\n      },\n    }),\n\n    subHeader: css({\n      textAlign: 'center',\n      'h4': {\n        margin: `0 0 -${theme.spacing(2)} 0`,\n      }\n    }),\n\n    label: css({\n      fontSize: '12px',\n    }),\n    variablesAndControls: css({\n      alignItems: 'center',\n      gap: theme.spacing(2),\n      display: 'flex',\n      justifyContent: 'space-between',\n      width: '100%',\n    }),\n    variables: css({\n      display: 'flex',\n      gap: theme.spacing(2),\n    }),\n    controls: css({\n      display: 'flex',\n      gap: theme.spacing(1),\n    }),\n  };\n}\n", "import { AdHocVariableFilter, MetricFindValue } from \"@grafana/data\";\nimport { getDataSourceSrv, DataSourceWithBackend } from \"@grafana/runtime\";\nimport { AdHocFiltersVariable, sceneGraph } from \"@grafana/scenes\";\nimport { EVENT_ATTR, EVENT_INTRINSIC, FILTER_SEPARATOR, ignoredAttributes, ignoredAttributesHomeFilter, RESOURCE_ATTR, SPAN_ATTR, VAR_DATASOURCE_EXPR } from \"utils/shared\";\nimport { isNumber } from \"utils/utils\";\n\nexport async function getTagKeysProvider(variable: AdHocFiltersVariable): Promise<{replace?: boolean, values: MetricFindValue[]}> {\n  const dsVar = sceneGraph.interpolate(variable, VAR_DATASOURCE_EXPR);\n  const datasource_ = await getDataSourceSrv().get(dsVar);\n  if (!(datasource_ instanceof DataSourceWithBackend)) {\n    console.error(new Error('getTagKeysProvider: invalid datasource!'));\n    throw new Error('getTagKeysProvider: invalid datasource!');\n  }\n  \n  const datasource = datasource_ as DataSourceWithBackend;\n  if (datasource && datasource.getTagKeys) {\n    const tagKeys = await datasource.getTagKeys();\n\n    if (Array.isArray(tagKeys)) {\n      const filteredKeys = filterKeys(tagKeys);\n      return { replace: true, values: filteredKeys };\n    } else {\n      console.error(new Error('getTagKeysProvider: invalid tagKeys!'));\n      return { values: [] };\n    }\n  } else {\n    console.error(new Error('getTagKeysProvider: missing or invalid datasource!'));\n    return { values: [] };\n  }\n}\n\nexport function filterKeys(keys: MetricFindValue[]): MetricFindValue[] {\n  const resourceAttributes = keys.filter((k) => k.text?.includes(RESOURCE_ATTR));\n  const spanAttributes = keys.filter((k) => k.text?.includes(SPAN_ATTR));\n  const otherAttributes = keys.filter((k) => {\n    return !k.text?.includes(RESOURCE_ATTR) && !k.text?.includes(SPAN_ATTR)\n      && !k.text?.includes(EVENT_ATTR) && !k.text?.includes(EVENT_INTRINSIC)\n      && ignoredAttributes.concat(ignoredAttributesHomeFilter).indexOf(k.text!) === -1;\n  })\n  return [...resourceAttributes, ...spanAttributes, ...otherAttributes];\n}\n\nexport function renderTraceQLLabelFilters(filters: AdHocVariableFilter[]) {\n  const expr = filters\n    .filter((f) => f.key && f.operator && f.value)\n    .map((filter) => renderFilter(filter))\n    .join(FILTER_SEPARATOR);\n  return expr.length ? `&& ${expr}` : '';\n}\n\nconst renderFilter = (filter: AdHocVariableFilter) => {\n  if (!filter) {\n    return '';\n  } \n  \n  let val = filter.value;\n  if (val === undefined || val === null || val === '') {\n    return '';\n  }\n\n  if (!isNumber.test(val) && !['kind'].includes(filter.key)) {\n    if (typeof val === 'string' && !val.startsWith('\"') && !val.endsWith('\"')) {\n      val = `\"${val}\"`;\n    }\n  }\n\n  return `${filter.key}${filter.operator}${val}`;\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\n// eslint-disable-next-line no-restricted-imports\nimport { duration } from 'moment';\n\nimport { AdHocVariableFilter, GrafanaTheme2 } from '@grafana/data';\nimport {\n  AdHocFiltersVariable,\n  DataSourceVariable,\n  SceneComponentProps,\n  SceneCSSGridItem,\n  SceneCSSGridLayout,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneRefreshPicker,\n  SceneTimePicker,\n  SceneTimeRange,\n  SceneTimeRangeLike,\n  SceneVariableSet,\n} from '@grafana/scenes';\nimport { useStyles2 } from '@grafana/ui';\n\nimport {\n  DATASOURCE_LS_KEY,\n  explorationDS,\n  HOMEPAGE_FILTERS_LS_KEY,\n  VAR_DATASOURCE,\n  VAR_HOME_FILTER,\n} from '../../utils/shared';\nimport { AttributePanel } from 'components/Home/AttributePanel';\nimport { HeaderScene } from 'components/Home/HeaderScene';\nimport { getDatasourceVariable, getHomeFilterVariable } from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_PAGES, USER_EVENTS_ACTIONS } from 'utils/analytics';\nimport { getTagKeysProvider, renderTraceQLLabelFilters } from './utils';\n\nexport interface HomeState extends SceneObjectState {\n  controls?: SceneObject[];\n  initialDS?: string;\n  initialFilters: AdHocVariableFilter[];\n  body?: SceneCSSGridLayout;\n}\n\nexport class Home extends SceneObjectBase<HomeState> {\n  public constructor(state: HomeState) {\n    super({\n      $timeRange: state.$timeRange ?? new SceneTimeRange({}),\n      $variables: state.$variables ?? getVariableSet(state.initialFilters, state.initialDS),\n      controls: state.controls ?? [new SceneTimePicker({}), new SceneRefreshPicker({})],\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    const sceneTimeRange = sceneGraph.getTimeRange(this);\n    const filterVariable = getHomeFilterVariable(this);\n    filterVariable.setState({\n      getTagKeysProvider: getTagKeysProvider,\n    });\n\n    getDatasourceVariable(this).subscribeToState((newState) => {\n      if (newState.value) {\n        localStorage.setItem(DATASOURCE_LS_KEY, newState.value.toString());\n      }\n    });\n\n    getHomeFilterVariable(this).subscribeToState((newState, prevState) => {\n      if (newState.filters !== prevState.filters) {\n        this.buildPanels(sceneTimeRange, newState.filters);\n\n        // save the filters to local storage\n        localStorage.setItem(HOMEPAGE_FILTERS_LS_KEY, JSON.stringify(newState.filters));\n\n        const newFilters = newState.filters.filter((f) => !prevState.filters.find((pf) => pf.key === f.key));\n        if (newFilters.length > 0) {\n          reportAppInteraction(USER_EVENTS_PAGES.home, USER_EVENTS_ACTIONS.home.filter_changed, {\n            key: newFilters[0].key,\n          });\n        }\n      }\n    });\n\n    sceneTimeRange.subscribeToState((newState, prevState) => {\n      if (newState.value.from !== prevState.value.from || newState.value.to !== prevState.value.to) {\n        this.buildPanels(sceneTimeRange, filterVariable.state.filters);\n      }\n    });\n    this.buildPanels(sceneTimeRange, filterVariable.state.filters);\n  }\n\n  buildPanels(sceneTimeRange: SceneTimeRangeLike, filters: AdHocVariableFilter[]) {\n    const from = sceneTimeRange.state.value.from.unix();\n    const to = sceneTimeRange.state.value.to.unix();\n    const dur = duration(to - from, 's');\n    const durString = `${dur.asSeconds()}s`;\n    const renderedFilters = renderTraceQLLabelFilters(filters);\n\n    this.setState({\n      body: new SceneCSSGridLayout({\n        children: [\n          new SceneCSSGridLayout({\n            autoRows: 'min-content',\n            columnGap: 2,\n            rowGap: 2,\n            children: [\n              new SceneCSSGridItem({\n                body: new AttributePanel({\n                  query: {\n                    query: `{nestedSetParent < 0 && status = error ${renderedFilters}} | count_over_time() by (resource.service.name)`,\n                    step: durString,\n                  },\n                  title: 'Errored services',\n                  type: 'errored-services',\n                }),\n              }),\n              new SceneCSSGridItem({\n                body: new AttributePanel({\n                  query: {\n                    query: `{nestedSetParent < 0 ${renderedFilters}} | quantile_over_time(duration, 0.9) by (resource.service.name)`,\n                    step: durString,\n                  },\n                  title: 'Slow services',\n                  type: 'slowest-services',\n                }),\n              }),\n              new SceneCSSGridItem({\n                body: new AttributePanel({\n                  query: {\n                    query: `{nestedSetParent<0 ${renderedFilters}} | histogram_over_time(duration)`,\n                  },\n                  title: 'Slow traces',\n                  type: 'slowest-traces',\n                  filter: renderedFilters,\n                }),\n              }),\n            ],\n          }),\n        ],\n      }),\n    });\n  }\n\n  static Component = ({ model }: SceneComponentProps<Home>) => {\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <HeaderScene.Component model={model} />\n        {body && <body.Component model={body} />}\n      </div>\n    );\n  };\n}\n\nfunction getVariableSet(initialFilters: AdHocVariableFilter[], initialDS?: string) {\n  return new SceneVariableSet({\n    variables: [\n      new DataSourceVariable({\n        name: VAR_DATASOURCE,\n        label: 'Data source',\n        value: initialDS,\n        pluginId: 'tempo',\n      }),\n      new AdHocFiltersVariable({\n        name: VAR_HOME_FILTER,\n        datasource: explorationDS,\n        layout: 'combobox',\n        filters: initialFilters,\n        allowCustomValue: true,\n      }),\n    ],\n  });\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      margin: `${theme.spacing(4)} auto`,\n      width: '75%',\n\n      '@media (max-width: 900px)': {\n        width: '95%',\n      },\n    }),\n  };\n}\n", "import { AdHocVariableFilter, DataFrame, urlUtil } from '@grafana/data';\nimport {\n  AdHocFiltersVariable,\n  CustomVariable,\n  DataSourceVariable,\n  SceneDataQuery,\n  SceneDataState,\n  sceneGraph,\n  SceneObject,\n  SceneObjectUrlValues,\n  SceneTimeRange,\n  sceneUtils,\n} from '@grafana/scenes';\n\nimport { TraceExploration } from '../pages/Explore';\nimport {\n  EventTraceOpened,\n  EXPLORATIONS_ROUTE,\n  VAR_DATASOURCE,\n  VAR_DATASOURCE_EXPR,\n  VAR_FILTERS,\n  VAR_GROUPBY,\n  VAR_HOME_FILTER,\n  VAR_LATENCY_PARTIAL_THRESHOLD,\n  VAR_LATENCY_THRESHOLD,\n  VAR_METRIC,\n  VAR_PRIMARY_SIGNAL,\n  VAR_SPAN_LIST_COLUMNS,\n} from './shared';\nimport { TracesByServiceScene } from 'components/Explore/TracesByService/TracesByServiceScene';\nimport { Home } from 'pages/Home/Home';\nimport { PrimarySignalVariable } from 'pages/Explore/PrimarySignalVariable';\nimport { ActionViewType } from 'exposedComponents/types';\nimport { ExceptionsScene } from 'components/Explore/TracesByService/Tabs/Exceptions/ExceptionsScene';\n\nexport function getTraceExplorationScene(model: SceneObject): TraceExploration {\n  return sceneGraph.getAncestor(model, TraceExploration);\n}\n\nexport function getHomeScene(model: SceneObject): Home {\n  return sceneGraph.getAncestor(model, Home);\n}\n\nexport function getTraceByServiceScene(model: SceneObject): TracesByServiceScene {\n  return sceneGraph.getAncestor(model, TracesByServiceScene);\n}\n\nexport function getExceptionsScene(model: SceneObject): ExceptionsScene | undefined {\n  const tracesByServiceScene = getTraceByServiceScene(model);\n  return tracesByServiceScene?.state.exceptionsScene;\n}\n\nexport function newTracesExploration(initialDS?: string, initialFilters?: AdHocVariableFilter[]): TraceExploration {\n  return new TraceExploration({\n    initialDS,\n    initialFilters: initialFilters ?? [],\n    $timeRange: new SceneTimeRange({ from: 'now-30m', to: 'now' }),\n  });\n}\n\nexport function newHome(initialFilters: AdHocVariableFilter[], initialDS?: string): Home {\n  return new Home({\n    initialDS,\n    initialFilters,\n    $timeRange: new SceneTimeRange({ from: 'now-30m', to: 'now' }),\n  });\n}\n\nexport function getErrorMessage(data: SceneDataState) {\n  return data?.data?.error?.message ?? 'There are no Tempo data sources';\n}\n\nexport function getNoDataMessage(context: string) {\n  return `No data for selected data source and filter. Select another to see ${context}.`;\n}\n\nexport function getUrlForExploration(exploration: TraceExploration) {\n  const params = sceneUtils.getUrlState(exploration);\n  return getUrlForValues(params);\n}\n\nexport function getUrlForValues(values: SceneObjectUrlValues) {\n  return urlUtil.renderUrl(EXPLORATIONS_ROUTE, values);\n}\n\nexport function getDataSource(exploration: TraceExploration) {\n  return sceneGraph.interpolate(exploration, VAR_DATASOURCE_EXPR);\n}\n\nexport const getFilterSignature = (filter: AdHocVariableFilter) => {\n  return `${filter.key}${filter.operator}${filter.value}`;\n};\n\nexport function getAttributesAsOptions(attributes: string[]) {\n  return attributes.map((attribute) => ({ label: attribute, value: attribute }));\n}\n\nexport function getLabelKey(frame: DataFrame) {\n  const labels = frame.fields.find((f) => f.type === 'number')?.labels;\n\n  if (!labels) {\n    return 'No labels';\n  }\n\n  const keys = Object.keys(labels);\n  if (keys.length === 0) {\n    return 'No labels';\n  }\n\n  return keys[0].replace(/\"/g, '');\n}\n\nexport function getLabelValue(frame: DataFrame, labelName?: string) {\n  const labels = frame.fields.find((f) => f.type === 'number')?.labels;\n\n  if (!labels) {\n    return 'No labels';\n  }\n\n  const keys = Object.keys(labels).filter((k) => k !== 'p'); // remove the percentile label\n  if (keys.length === 0) {\n    return 'No labels';\n  }\n\n  return labels[labelName || keys[0]].replace(/\"/g, '');\n}\n\nexport function getGroupByVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_GROUPBY, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Group by variable not found');\n  }\n  return variable;\n}\n\nexport function getSpanListColumnsVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_SPAN_LIST_COLUMNS, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Span list columns variable not found');\n  }\n  return variable;\n}\n\nexport function getLatencyThresholdVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_LATENCY_THRESHOLD, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Latency threshold variable not found');\n  }\n  return variable;\n}\n\nexport function getLatencyPartialThresholdVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_LATENCY_PARTIAL_THRESHOLD, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Partial latency threshold variable not found');\n  }\n  return variable;\n}\n\nexport function getMetricVariable(scene: SceneObject): CustomVariable {\n  const variable = sceneGraph.lookupVariable(VAR_METRIC, scene);\n  if (!(variable instanceof CustomVariable)) {\n    throw new Error('Metric variable not found');\n  }\n  return variable;\n}\n\nexport function getFiltersVariable(scene: SceneObject): AdHocFiltersVariable {\n  const variable = sceneGraph.lookupVariable(VAR_FILTERS, scene);\n  if (!(variable instanceof AdHocFiltersVariable)) {\n    throw new Error('Filters variable not found');\n  }\n  return variable;\n}\n\nexport function getPrimarySignalVariable(scene: SceneObject): PrimarySignalVariable {\n  const variable = sceneGraph.lookupVariable(VAR_PRIMARY_SIGNAL, scene);\n  if (!(variable instanceof PrimarySignalVariable)) {\n    throw new Error('Primary signal variable not found');\n  }\n  return variable;\n}\n\nexport function getHomeFilterVariable(scene: SceneObject): AdHocFiltersVariable {\n  const variable = sceneGraph.lookupVariable(VAR_HOME_FILTER, scene);\n  if (!(variable instanceof AdHocFiltersVariable)) {\n    throw new Error('Home filter variable not found');\n  }\n  return variable;\n}\n\nexport function getDatasourceVariable(scene: SceneObject): DataSourceVariable {\n  const variable = sceneGraph.lookupVariable(VAR_DATASOURCE, scene);\n  if (!(variable instanceof DataSourceVariable)) {\n    throw new Error('Datasource variable not found');\n  }\n  return variable;\n}\n\nexport function getCurrentStep(scene: SceneObject): number | undefined {\n  const data = sceneGraph.getData(scene).state.data;\n  const targetQuery = data?.request?.targets[0];\n  return targetQuery ? (targetQuery as SceneDataQuery).step : undefined;\n}\n\nexport function shouldShowSelection(tab?: ActionViewType): boolean {\n  return tab === 'comparison' || tab === 'traceList';\n}\n\nexport function getMetricValue(scene: SceneObject) {\n  return getMetricVariable(scene).useState().value;\n}\n\nexport function fieldHasEmptyValues(data: SceneDataState) {\n  return data?.data?.series[0].fields?.some((v) => v.values.every((e) => e === undefined)) ?? false;\n}\n\nexport const isNumber = /^-?\\d+\\.?\\d*$/;\n\nexport const formatLabelValue = (value: string) => {\n  if (!isNumber.test(value) && typeof value === 'string' && !value.startsWith('\"') && !value.endsWith('\"')) {\n    return `\"${value}\"`;\n  }\n  return value;\n};\n\nexport const capitalizeFirstChar = (str: string) => str?.[0]?.toUpperCase() + str?.slice(1) || '';\n\nexport const getOpenTrace = (scene: SceneObject) => {\n  return (traceId: string, spanId?: string) => {\n    scene.publishEvent(new EventTraceOpened({ traceId, spanId }), true);\n  };\n};\n", "var map = {\n\t\"./af\": 9805,\n\t\"./af.js\": 9805,\n\t\"./ar\": 4449,\n\t\"./ar-dz\": 4468,\n\t\"./ar-dz.js\": 4468,\n\t\"./ar-kw\": 3480,\n\t\"./ar-kw.js\": 3480,\n\t\"./ar-ly\": 4197,\n\t\"./ar-ly.js\": 4197,\n\t\"./ar-ma\": 2180,\n\t\"./ar-ma.js\": 2180,\n\t\"./ar-ps\": 9343,\n\t\"./ar-ps.js\": 9343,\n\t\"./ar-sa\": 230,\n\t\"./ar-sa.js\": 230,\n\t\"./ar-tn\": 2808,\n\t\"./ar-tn.js\": 2808,\n\t\"./ar.js\": 4449,\n\t\"./az\": 5865,\n\t\"./az.js\": 5865,\n\t\"./be\": 6627,\n\t\"./be.js\": 6627,\n\t\"./bg\": 901,\n\t\"./bg.js\": 901,\n\t\"./bm\": 3179,\n\t\"./bm.js\": 3179,\n\t\"./bn\": 1966,\n\t\"./bn-bd\": 969,\n\t\"./bn-bd.js\": 969,\n\t\"./bn.js\": 1966,\n\t\"./bo\": 6317,\n\t\"./bo.js\": 6317,\n\t\"./br\": 6474,\n\t\"./br.js\": 6474,\n\t\"./bs\": 5961,\n\t\"./bs.js\": 5961,\n\t\"./ca\": 7270,\n\t\"./ca.js\": 7270,\n\t\"./cs\": 1564,\n\t\"./cs.js\": 1564,\n\t\"./cv\": 3239,\n\t\"./cv.js\": 3239,\n\t\"./cy\": 2366,\n\t\"./cy.js\": 2366,\n\t\"./da\": 2453,\n\t\"./da.js\": 2453,\n\t\"./de\": 6601,\n\t\"./de-at\": 5027,\n\t\"./de-at.js\": 5027,\n\t\"./de-ch\": 8101,\n\t\"./de-ch.js\": 8101,\n\t\"./de.js\": 6601,\n\t\"./dv\": 6080,\n\t\"./dv.js\": 6080,\n\t\"./el\": 2655,\n\t\"./el.js\": 2655,\n\t\"./en-au\": 6836,\n\t\"./en-au.js\": 6836,\n\t\"./en-ca\": 2086,\n\t\"./en-ca.js\": 2086,\n\t\"./en-gb\": 2103,\n\t\"./en-gb.js\": 2103,\n\t\"./en-ie\": 5964,\n\t\"./en-ie.js\": 5964,\n\t\"./en-il\": 4379,\n\t\"./en-il.js\": 4379,\n\t\"./en-in\": 765,\n\t\"./en-in.js\": 765,\n\t\"./en-nz\": 1502,\n\t\"./en-nz.js\": 1502,\n\t\"./en-sg\": 1152,\n\t\"./en-sg.js\": 1152,\n\t\"./eo\": 50,\n\t\"./eo.js\": 50,\n\t\"./es\": 3350,\n\t\"./es-do\": 9338,\n\t\"./es-do.js\": 9338,\n\t\"./es-mx\": 1326,\n\t\"./es-mx.js\": 1326,\n\t\"./es-us\": 9947,\n\t\"./es-us.js\": 9947,\n\t\"./es.js\": 3350,\n\t\"./et\": 8231,\n\t\"./et.js\": 8231,\n\t\"./eu\": 8512,\n\t\"./eu.js\": 8512,\n\t\"./fa\": 9083,\n\t\"./fa.js\": 9083,\n\t\"./fi\": 5059,\n\t\"./fi.js\": 5059,\n\t\"./fil\": 2607,\n\t\"./fil.js\": 2607,\n\t\"./fo\": 3369,\n\t\"./fo.js\": 3369,\n\t\"./fr\": 7390,\n\t\"./fr-ca\": 6711,\n\t\"./fr-ca.js\": 6711,\n\t\"./fr-ch\": 6152,\n\t\"./fr-ch.js\": 6152,\n\t\"./fr.js\": 7390,\n\t\"./fy\": 2419,\n\t\"./fy.js\": 2419,\n\t\"./ga\": 3002,\n\t\"./ga.js\": 3002,\n\t\"./gd\": 4914,\n\t\"./gd.js\": 4914,\n\t\"./gl\": 6557,\n\t\"./gl.js\": 6557,\n\t\"./gom-deva\": 8944,\n\t\"./gom-deva.js\": 8944,\n\t\"./gom-latn\": 5387,\n\t\"./gom-latn.js\": 5387,\n\t\"./gu\": 7462,\n\t\"./gu.js\": 7462,\n\t\"./he\": 9237,\n\t\"./he.js\": 9237,\n\t\"./hi\": 9617,\n\t\"./hi.js\": 9617,\n\t\"./hr\": 6544,\n\t\"./hr.js\": 6544,\n\t\"./hu\": 341,\n\t\"./hu.js\": 341,\n\t\"./hy-am\": 1388,\n\t\"./hy-am.js\": 1388,\n\t\"./id\": 5251,\n\t\"./id.js\": 5251,\n\t\"./is\": 1146,\n\t\"./is.js\": 1146,\n\t\"./it\": 7891,\n\t\"./it-ch\": 7,\n\t\"./it-ch.js\": 7,\n\t\"./it.js\": 7891,\n\t\"./ja\": 3727,\n\t\"./ja.js\": 3727,\n\t\"./jv\": 5198,\n\t\"./jv.js\": 5198,\n\t\"./ka\": 8974,\n\t\"./ka.js\": 8974,\n\t\"./kk\": 7308,\n\t\"./kk.js\": 7308,\n\t\"./km\": 7786,\n\t\"./km.js\": 7786,\n\t\"./kn\": 4807,\n\t\"./kn.js\": 4807,\n\t\"./ko\": 1584,\n\t\"./ko.js\": 1584,\n\t\"./ku\": 1906,\n\t\"./ku-kmr\": 5305,\n\t\"./ku-kmr.js\": 5305,\n\t\"./ku.js\": 1906,\n\t\"./ky\": 9190,\n\t\"./ky.js\": 9190,\n\t\"./lb\": 7396,\n\t\"./lb.js\": 7396,\n\t\"./lo\": 8503,\n\t\"./lo.js\": 8503,\n\t\"./lt\": 3010,\n\t\"./lt.js\": 3010,\n\t\"./lv\": 5192,\n\t\"./lv.js\": 5192,\n\t\"./me\": 1944,\n\t\"./me.js\": 1944,\n\t\"./mi\": 6492,\n\t\"./mi.js\": 6492,\n\t\"./mk\": 2934,\n\t\"./mk.js\": 2934,\n\t\"./ml\": 1463,\n\t\"./ml.js\": 1463,\n\t\"./mn\": 8377,\n\t\"./mn.js\": 8377,\n\t\"./mr\": 8733,\n\t\"./mr.js\": 8733,\n\t\"./ms\": 8030,\n\t\"./ms-my\": 9445,\n\t\"./ms-my.js\": 9445,\n\t\"./ms.js\": 8030,\n\t\"./mt\": 5887,\n\t\"./mt.js\": 5887,\n\t\"./my\": 7228,\n\t\"./my.js\": 7228,\n\t\"./nb\": 8294,\n\t\"./nb.js\": 8294,\n\t\"./ne\": 9559,\n\t\"./ne.js\": 9559,\n\t\"./nl\": 600,\n\t\"./nl-be\": 8796,\n\t\"./nl-be.js\": 8796,\n\t\"./nl.js\": 600,\n\t\"./nn\": 9570,\n\t\"./nn.js\": 9570,\n\t\"./oc-lnc\": 5662,\n\t\"./oc-lnc.js\": 5662,\n\t\"./pa-in\": 7101,\n\t\"./pa-in.js\": 7101,\n\t\"./pl\": 6118,\n\t\"./pl.js\": 6118,\n\t\"./pt\": 9198,\n\t\"./pt-br\": 7203,\n\t\"./pt-br.js\": 7203,\n\t\"./pt.js\": 9198,\n\t\"./ro\": 5565,\n\t\"./ro.js\": 5565,\n\t\"./ru\": 3315,\n\t\"./ru.js\": 3315,\n\t\"./sd\": 8473,\n\t\"./sd.js\": 8473,\n\t\"./se\": 1258,\n\t\"./se.js\": 1258,\n\t\"./si\": 8798,\n\t\"./si.js\": 8798,\n\t\"./sk\": 6404,\n\t\"./sk.js\": 6404,\n\t\"./sl\": 7057,\n\t\"./sl.js\": 7057,\n\t\"./sq\": 5718,\n\t\"./sq.js\": 5718,\n\t\"./sr\": 5363,\n\t\"./sr-cyrl\": 478,\n\t\"./sr-cyrl.js\": 478,\n\t\"./sr.js\": 5363,\n\t\"./ss\": 7260,\n\t\"./ss.js\": 7260,\n\t\"./sv\": 2231,\n\t\"./sv.js\": 2231,\n\t\"./sw\": 7104,\n\t\"./sw.js\": 7104,\n\t\"./ta\": 7493,\n\t\"./ta.js\": 7493,\n\t\"./te\": 7705,\n\t\"./te.js\": 7705,\n\t\"./tet\": 4457,\n\t\"./tet.js\": 4457,\n\t\"./tg\": 2727,\n\t\"./tg.js\": 2727,\n\t\"./th\": 2206,\n\t\"./th.js\": 2206,\n\t\"./tk\": 3419,\n\t\"./tk.js\": 3419,\n\t\"./tl-ph\": 7243,\n\t\"./tl-ph.js\": 7243,\n\t\"./tlh\": 16,\n\t\"./tlh.js\": 16,\n\t\"./tr\": 7020,\n\t\"./tr.js\": 7020,\n\t\"./tzl\": 8026,\n\t\"./tzl.js\": 8026,\n\t\"./tzm\": 8537,\n\t\"./tzm-latn\": 7899,\n\t\"./tzm-latn.js\": 7899,\n\t\"./tzm.js\": 8537,\n\t\"./ug-cn\": 818,\n\t\"./ug-cn.js\": 818,\n\t\"./uk\": 8478,\n\t\"./uk.js\": 8478,\n\t\"./ur\": 7893,\n\t\"./ur.js\": 7893,\n\t\"./uz\": 9133,\n\t\"./uz-latn\": 311,\n\t\"./uz-latn.js\": 311,\n\t\"./uz.js\": 9133,\n\t\"./vi\": 2179,\n\t\"./vi.js\": 2179,\n\t\"./x-pseudo\": 2455,\n\t\"./x-pseudo.js\": 2455,\n\t\"./yo\": 3310,\n\t\"./yo.js\": 3310,\n\t\"./zh-cn\": 7244,\n\t\"./zh-cn.js\": 7244,\n\t\"./zh-hk\": 76,\n\t\"./zh-hk.js\": 76,\n\t\"./zh-mo\": 2305,\n\t\"./zh-mo.js\": 2305,\n\t\"./zh-tw\": 8588,\n\t\"./zh-tw.js\": 8588\n};\n\n\nfunction webpackContext(req) {\n\tvar id = webpackContextResolve(req);\n\treturn __webpack_require__(id);\n}\nfunction webpackContextResolve(req) {\n\tif(!__webpack_require__.o(map, req)) {\n\t\tvar e = new Error(\"Cannot find module '\" + req + \"'\");\n\t\te.code = 'MODULE_NOT_FOUND';\n\t\tthrow e;\n\t}\n\treturn map[req];\n}\nwebpackContext.keys = function webpackContextKeys() {\n\treturn Object.keys(map);\n};\nwebpackContext.resolve = webpackContextResolve;\nmodule.exports = webpackContext;\nwebpackContext.id = 1738;", "import { TimeRange } from '@grafana/data';\nimport { sceneGraph, SceneObject, SceneObjectBase, SceneObjectState, SceneQueryRunner } from '@grafana/scenes';\nimport { DataQuery, DataSourceRef } from '@grafana/schema';\n\nimport Logo from '../../../../src/img/logo.svg';\n\nexport interface AddToInvestigationButtonState extends SceneObjectState {\n  dsUid?: string;\n  query?: string;\n  labelValue?: string;\n  type?: string;\n  context?: ExtensionContext;\n  queries: DataQuery[];\n}\n\ninterface ExtensionContext {\n  timeRange: TimeRange;\n  queries: DataQuery[];\n  datasource: DataSourceRef;\n  origin: string;\n  url: string;\n  type: string;\n  title: string;\n  id: string;\n  logoPath: string;\n}\n\nexport class AddToInvestigationButton extends SceneObjectBase<AddToInvestigationButtonState> {\n  constructor(state: Omit<AddToInvestigationButtonState, 'queries'>) {\n    super({ ...state, queries: [] });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate = () => {\n    this._subs.add(\n      this.subscribeToState(() => {\n        this.getQueries();\n        this.getContext();\n      })\n    );\n  };\n\n  private readonly getQueries = () => {\n    const data = sceneGraph.getData(this);\n    const queryRunner = sceneGraph.findObject(data, isQueryRunner);\n\n    if (isQueryRunner(queryRunner)) {\n      const queries = queryRunner.state.queries.map((q) => ({\n        ...q,\n        query: this.state.query,\n      }));\n\n      if (JSON.stringify(queries) !== JSON.stringify(this.state.queries)) {\n        this.setState({ queries });\n      }\n    }\n  };\n\n  private readonly getContext = () => {\n    const { queries, dsUid, labelValue, type = 'traceMetrics' } = this.state;\n    const timeRange = sceneGraph.getTimeRange(this);\n\n    if (!timeRange || !queries || !dsUid) {\n      return;\n    }\n    const ctx = {\n      origin: 'Explore Traces',\n      type,\n      queries,\n      timeRange: { ...timeRange.state.value },\n      datasource: { uid: dsUid },\n      url: window.location.href,\n      id: `${JSON.stringify(queries)}`,\n      title: `${labelValue}`,\n      logoPath: Logo,\n    };\n    if (JSON.stringify(ctx) !== JSON.stringify(this.state.context)) {\n      this.setState({ context: ctx });\n    }\n  };\n}\n\nfunction isQueryRunner(o: SceneObject<SceneObjectState> | null): o is SceneQueryRunner {\n  return o instanceof SceneQueryRunner;\n}\n", "import React from 'react';\n\nimport {\n  SceneObjectState,\n  SceneObjectBase,\n  SceneComponentProps,\n  PanelBuilders,\n  SceneQueryRunner,\n  sceneGraph,\n  SceneObject,\n} from '@grafana/scenes';\nimport { LoadingState, GrafanaTheme2 } from '@grafana/data';\nimport { explorationDS } from 'utils/shared';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { css } from '@emotion/css';\nimport Skeleton from 'react-loading-skeleton';\nimport { useStyles2 } from '@grafana/ui';\n\nexport interface TracePanelState extends SceneObjectState {\n  panel?: SceneObject;\n  traceId: string;\n  spanId?: string;\n}\n\nexport class TraceViewPanelScene extends SceneObjectBase<TracePanelState> {\n  constructor(state: TracePanelState) {\n    super({\n      $data: new SceneQueryRunner({\n        datasource: explorationDS,\n        queries: [{ refId: 'A', query: state.traceId, queryType: 'traceql' }],\n      }),\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          if (data.data?.state === LoadingState.Done) {\n            this.setState({\n              panel: this.getVizPanel().build(),\n            });\n          } else if (data.data?.state === LoadingState.Loading) {\n            this.setState({\n              panel: new LoadingStateScene({\n                component: SkeletonComponent,\n              }),\n            });\n          }\n        })\n      );\n    });\n  }\n\n  private getVizPanel() {\n    const panel = PanelBuilders.traces().setHoverHeader(true);\n    if (this.state.spanId) {\n      panel.setOption('focusedSpanId' as any, this.state.spanId as any);\n    }\n    return panel;\n  }\n\n  public static Component = ({ model }: SceneComponentProps<TraceViewPanelScene>) => {\n    const { panel } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    if (!panel) {\n      return;\n    }\n\n    return (\n      <div className={styles.panelContainer}>\n        <panel.Component model={panel} />\n      </div>\n    );\n  };\n}\n\nconst SkeletonComponent = () => {\n  const styles = useStyles2(getStyles);\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.header}>\n        <Skeleton count={1} width={60} />\n        <Skeleton count={1} width={60} />\n      </div>\n      <Skeleton count={2} width={'80%'} />\n      <div className={styles.map}>\n        <Skeleton count={1} />\n        <Skeleton count={1} height={70} />\n      </div>\n\n      <div className={styles.span}>\n        <span className={styles.service1}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar1}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service2}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar2}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service3}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar3}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service4}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar4}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service5}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar5}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n      <div className={styles.span}>\n        <span className={styles.service6}>\n          <Skeleton count={1} />\n        </span>\n        <span className={styles.bar6}>\n          <Skeleton count={1} />\n        </span>\n      </div>\n    </div>\n  );\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    panelContainer: css({\n      display: 'flex',\n      height: '100%',\n\n      '& [data-testid=\"data-testid panel content\"] > div': {\n        overflow: 'auto',\n      },\n\n      '& .show-on-hover': {\n        display: 'none',\n      },\n    }),\n    container: css({\n      height: 'calc(100% - 32px)',\n      width: 'calc(100% - 32px)',\n      position: 'absolute',\n      backgroundColor: theme.colors.background.primary,\n      border: `1px solid ${theme.colors.border.weak}`,\n      padding: '5px',\n    }),\n    header: css({\n      marginBottom: '20px',\n      display: 'flex',\n      justifyContent: 'space-between',\n    }),\n    map: css({\n      marginTop: '20px',\n      marginBottom: '20px',\n    }),\n    span: css({\n      display: 'flex',\n    }),\n    service1: css({\n      width: '25%',\n    }),\n    bar1: css({\n      marginLeft: '5%',\n      width: '70%',\n    }),\n    service2: css({\n      width: '25%',\n    }),\n    bar2: css({\n      marginLeft: '10%',\n      width: '15%',\n    }),\n    service3: css({\n      width: '20%',\n      marginLeft: '5%',\n    }),\n    bar3: css({\n      marginLeft: '10%',\n      width: '65%',\n    }),\n    service4: css({\n      width: '20%',\n      marginLeft: '5%',\n    }),\n    bar4: css({\n      marginLeft: '15%',\n      width: '60%',\n    }),\n    service5: css({\n      width: '15%',\n      marginLeft: '10%',\n    }),\n    bar5: css({\n      marginLeft: '20%',\n      width: '35%',\n    }),\n    service6: css({\n      width: '15%',\n      marginLeft: '10%',\n    }),\n    bar6: css({\n      marginLeft: '30%',\n      width: '15%',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps, SceneObject } from '@grafana/scenes';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { TraceViewPanelScene } from '../panels/TraceViewPanelScene';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { getTraceExplorationScene } from '../../../utils/utils';\n\nexport interface DetailsSceneState extends SceneObjectState {\n  body?: SceneObject;\n}\n\nexport class TraceDrawerScene extends SceneObjectBase<DetailsSceneState> {\n  constructor(state: Partial<DetailsSceneState>) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {\n    this.updateBody();\n\n    const traceExploration = getTraceExplorationScene(this);\n\n    traceExploration.subscribeToState((newState, prevState) => {\n      if (newState.traceId !== prevState.traceId || newState.spanId !== prevState.spanId) {\n        this.updateBody();\n        reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.open_trace, {\n          traceId: newState.traceId,\n          spanId: newState.spanId,\n        });\n      }\n    });\n  }\n\n  private updateBody() {\n    const traceExploration = getTraceExplorationScene(this);\n\n    if (traceExploration.state.traceId) {\n      this.setState({\n        body: new TraceViewPanelScene({\n          traceId: traceExploration.state.traceId,\n          spanId: traceExploration.state.spanId,\n        }),\n      });\n    } else {\n      this.setState({\n        body: new EmptyStateScene({\n          message: 'No trace selected',\n        }),\n      });\n    }\n  }\n\n  public static Component = ({ model }: SceneComponentProps<TraceDrawerScene>) => {\n    const { body } = model.useState();   \n    return body && <body.Component model={body} />;\n  };\n}\n", "import { LoadingState, dateTime } from '@grafana/data';\nimport {\n  SceneObjectBase,\n  SceneObjectState,\n  SceneTimeRange,\n  SceneQueryRunner,\n} from '@grafana/scenes';\nimport { getDatasourceVariable } from '../../utils/utils';\nimport { Alert, LinkButton } from '@grafana/ui';\nimport React from 'react';\n\nexport interface TraceQLIssueDetectorState extends SceneObjectState {\n  hasIssue: boolean;\n}\n\nexport class TraceQLIssueDetector extends SceneObjectBase<TraceQLIssueDetectorState> {\n  constructor() {\n    super({\n      hasIssue: false,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  private _onActivate() {    \n    this.runIssueDetectionQuery();\n\n    const datasourceVar = getDatasourceVariable(this);\n    this._subs.add(\n      datasourceVar.subscribeToState((newState, prevState) => {\n        if (newState.value !== prevState.value) {\n          this.resetIssues();\n          this.runIssueDetectionQuery();\n        }\n      })\n    );\n  }\n\n  private runIssueDetectionQuery() {\n    const datasourceVar = getDatasourceVariable(this);\n    \n    // Create a minimal time range to reduce resource usage\n    const now = dateTime();\n    const from = dateTime(now).subtract(1, 'minute');\n    const minimalTimeRange = new SceneTimeRange({\n      from: from.toISOString(),\n      to: now.toISOString(),\n    });\n    \n    const issueDetector = new SceneQueryRunner({\n      maxDataPoints: 1,\n      datasource: { uid: String(datasourceVar.state.value) },\n      $timeRange: minimalTimeRange,\n      queries: [{\n        refId: 'issueDetectorQuery',\n        query: '{} | rate()',\n        queryType: 'traceql',\n        tableType: 'spans',\n        limit: 1,\n        spss: 1,\n        filters: [],\n      }],\n    });\n    \n    this._subs.add(\n      issueDetector.subscribeToState((state) => {\n        if (state.data?.state === LoadingState.Error) {\n          const message = state.data?.errors?.[0]?.message || '';\n          // This is the error message when the datasource is not configured for TraceQL metrics\n          // https://grafana.com/docs/tempo/latest/operations/traceql-metrics/#activate-and-configure-the-local-blocks-processor\n          if (message.includes('localblocks processor not found')) {\n            this.setState({ hasIssue: true });\n          }\n        }\n      })\n    );\n    \n    issueDetector.activate();\n  }\n\n  public resetIssues() {\n    this.setState({\n      hasIssue: false,\n    });\n  }\n} \n\nconst TraceQLWarningTitle = 'TraceQL metrics not configured';\nconst TraceQLWarningMessage = 'We found an error running a TraceQL metrics query: \"localblocks processor not found\". This typically means the \"local-blocks\" processor is not configured in Tempo, which is required for Grafana Traces Drilldown to work.';\n\nexport const TraceQLConfigWarning: React.FC<{ detector: TraceQLIssueDetector }> = ({ detector }) => {\n  const { hasIssue } = detector.useState();\n\n  if (!hasIssue) {\n    return null;\n  }\n\n  return (\n    <Alert\n      severity=\"warning\"\n      title={TraceQLWarningTitle}\n    >\n      <p>\n        {TraceQLWarningMessage}\n        <LinkButton\n          icon=\"external-link-alt\"\n          fill=\"text\"\n          size=\"sm\"\n          target=\"_blank\"\n          href=\"https://grafana.com/docs/tempo/latest/operations/traceql-metrics\"\n        >\n          Read documentation\n        </LinkButton>\n      </p>\n    </Alert>\n  );\n};\n", "import React, { ReactElement, useEffect, useState } from 'react';\n\nimport { TimeRange } from '@grafana/data';\nimport { ComponentSize } from '@grafana/ui';\nimport { usePluginComponent } from '@grafana/runtime';\nimport { sceneGraph, SceneObject } from '@grafana/scenes';\n\ninterface EntityAssertionsWidgetProps {\n  query: {\n    entityName?: string;\n    entityType?: string;\n    start: number;\n    end: number;\n  };\n  size: ComponentSize;\n  source?: string;\n  returnToPrevious?: boolean;\n}\n\nexport type EntityAssertionsWidgetExternal = (props: EntityAssertionsWidgetProps) => ReactElement | null;\n\ninterface Props {\n  serviceName: string;\n  model: SceneObject;\n}\n\nexport function EntityAssertionsWidget({ serviceName, model }: Props) {\n  const { isLoading, component: EntityAssertionsWidgetExternal } = usePluginComponent<EntityAssertionsWidgetProps>(\n    'grafana-asserts-app/entity-assertions-widget/v1'\n  );\n  const [timeRange, setTimeRange] = useState<TimeRange>();\n\n  useEffect(() => {\n    const sceneTimeRange = sceneGraph.getTimeRange(model);\n    setTimeRange(sceneTimeRange.state.value);\n\n    const sub = sceneTimeRange.subscribeToState((state) => {\n      setTimeRange(state.value);\n    });\n\n    return () => {\n      sub.unsubscribe();\n    };\n  }, [model]);\n\n  if (isLoading || !EntityAssertionsWidgetExternal || !timeRange) {\n    return null;\n  }\n\n  return (\n    <EntityAssertionsWidgetExternal\n      size='md'\n      source='Traces Drilldown'\n      query={{\n        start: timeRange.from.valueOf(),\n        end: timeRange.to.valueOf(),\n        entityName: serviceName,\n        entityType: 'Service',\n      }}\n      returnToPrevious={true}\n    />\n  );\n}\n", "import { css, cx } from '@emotion/css';\nimport { useDialog } from '@react-aria/dialog';\nimport { FocusScope } from '@react-aria/focus';\nimport { useOverlay } from '@react-aria/overlays';\nimport RcDrawer from 'rc-drawer';\nimport { ReactNode, useCallback, useEffect, useState } from 'react';\nimport * as React from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { selectors } from '@grafana/e2e-selectors';\nimport { t } from '@grafana/i18n';\n\nimport 'rc-drawer/assets/index.css';\nimport { useStyles2, getDragStyles, IconButton, ScrollContainer, Text } from '@grafana/ui';\n\nexport interface Props {\n  children: ReactNode;\n  /** Title shown at the top of the drawer */\n  title?: ReactNode;\n  /** Subtitle shown below the title */\n  subtitle?: ReactNode;\n  /** Should the Drawer be closable by clicking on the mask, defaults to true */\n  closeOnMaskClick?: boolean;\n  /**\n   * Specifies the width and min-width.\n   * sm = width 25% & min-width 384px\n   * md = width 50% & min-width 568px\n   * lg = width 75% & min-width 744px\n   **/\n  size?: 'sm' | 'md' | 'lg';\n  /** Tabs */\n  tabs?: React.ReactNode;\n  /**\n   * Whether the content should be wrapped in a ScrollContainer\n   * Only change this if you intend to manage scroll behaviour yourself\n   * (e.g. having a split pane with independent scrolling)\n   **/\n  scrollableContent?: boolean;\n  /** Callback for closing the drawer */\n  onClose: () => void;\n}\n\nconst drawerSizes = {\n  sm: { width: '25%', minWidth: 384 },\n  md: { width: '50%', minWidth: 568 },\n  lg: { width: '75%', minWidth: 744 },\n};\n\nexport function Drawer({\n  children,\n  onClose,\n  closeOnMaskClick = true,\n  scrollableContent = true,\n  title,\n  subtitle,\n  size = 'md',\n  tabs,\n}: Props) {\n  const [drawerWidth, onMouseDown, onTouchStart] = useResizebleDrawer();\n\n  const styles = useStyles2(getStyles);\n  const wrapperStyles = useStyles2(getWrapperStyles, size);\n  const dragStyles = useStyles2(getDragStyles);\n\n  const overlayRef = React.useRef(null);\n  const { dialogProps, titleProps } = useDialog({}, overlayRef);\n  const { overlayProps } = useOverlay(\n    {\n      isDismissable: false,\n      isOpen: true,\n      onClose,\n    },\n    overlayRef\n  );\n\n  // Adds body class while open so the toolbar nav can hide some actions while drawer is open\n  useBodyClassWhileOpen();\n\n  const content = <div className={styles.content}>{children}</div>;\n  const overrideWidth = drawerWidth ?? drawerSizes[size].width;\n  const minWidth = drawerSizes[size].minWidth;\n\n  return (\n    <RcDrawer\n      open={true}\n      onClose={onClose}\n      placement=\"right\"\n      getContainer={'#trace-exploration'}\n      className={styles.drawerContent}\n      rootClassName={styles.drawer}\n      classNames={{\n        wrapper: wrapperStyles,\n      }}\n      styles={{\n        wrapper: {\n          width: overrideWidth,\n          minWidth,\n        },\n      }}\n      width={''}\n      motion={{\n        motionAppear: true,\n        motionName: styles.drawerMotion,\n      }}\n      maskClassName={styles.mask}\n      maskClosable={closeOnMaskClick}\n      maskMotion={{\n        motionAppear: true,\n        motionName: styles.maskMotion,\n      }}\n    >\n      <FocusScope restoreFocus contain autoFocus>\n        <div\n          aria-label={\n            typeof title === 'string'\n              ? selectors.components.Drawer.General.title(title)\n              : selectors.components.Drawer.General.title('no title')\n          }\n          className={styles.container}\n          {...overlayProps}\n          {...dialogProps}\n          ref={overlayRef}\n        >\n          <div\n            className={cx(dragStyles.dragHandleVertical, styles.resizer)}\n            onMouseDown={onMouseDown}\n            onTouchStart={onTouchStart}\n          />\n          <div className={cx(styles.header, Boolean(tabs) && styles.headerWithTabs)}>\n            <div className={styles.actions}>\n              <IconButton\n                name=\"times\"\n                variant=\"secondary\"\n                onClick={onClose}\n                data-testid={selectors.components.Drawer.General.close}\n                tooltip={t(`grafana-ui.drawer.close`, 'Close')}\n              />\n            </div>\n            {typeof title === 'string' ? (\n              <div className={styles.titleWrapper}>\n                <Text element=\"h3\" {...titleProps}>\n                  {title}\n                </Text>\n                {subtitle && (\n                  <div className={styles.subtitle} data-testid={selectors.components.Drawer.General.subtitle}>\n                    {subtitle}\n                  </div>\n                )}\n              </div>\n            ) : (\n              title\n            )}\n            {tabs && <div className={styles.tabsWrapper}>{tabs}</div>}\n          </div>\n          {!scrollableContent ? content : <ScrollContainer showScrollIndicators>{content}</ScrollContainer>}\n        </div>\n      </FocusScope>\n    </RcDrawer>\n  );\n}\n\nfunction useResizebleDrawer(): [\n  string | undefined,\n  React.EventHandler<React.MouseEvent>,\n  React.EventHandler<React.TouchEvent>,\n] {\n  const [drawerWidth, setDrawerWidth] = useState<string | undefined>(undefined);\n\n  const onMouseMove = useCallback((e: MouseEvent) => {\n    setDrawerWidth(getCustomDrawerWidth(e.clientX));\n  }, []);\n\n  const onTouchMove = useCallback((e: TouchEvent) => {\n    const touch = e.touches[0];\n    setDrawerWidth(getCustomDrawerWidth(touch.clientX));\n  }, []);\n\n  const onMouseUp = useCallback(\n    (e: MouseEvent) => {\n      document.removeEventListener('mousemove', onMouseMove);\n      document.removeEventListener('mouseup', onMouseUp);\n    },\n    [onMouseMove]\n  );\n\n  const onTouchEnd = useCallback(\n    (e: TouchEvent) => {\n      document.removeEventListener('touchmove', onTouchMove);\n      document.removeEventListener('touchend', onTouchEnd);\n    },\n    [onTouchMove]\n  );\n\n  function onMouseDown(e: React.MouseEvent<HTMLDivElement>) {\n    e.stopPropagation();\n    e.preventDefault();\n    // we will only add listeners when needed, and remove them afterward\n    document.addEventListener('mousemove', onMouseMove);\n    document.addEventListener('mouseup', onMouseUp);\n  }\n\n  function onTouchStart(e: React.TouchEvent<HTMLDivElement>) {\n    e.stopPropagation();\n    e.preventDefault();\n    // we will only add listeners when needed, and remove them afterward\n    document.addEventListener('touchmove', onTouchMove);\n    document.addEventListener('touchend', onTouchEnd);\n  }\n\n  return [drawerWidth, onMouseDown, onTouchStart];\n}\n\nfunction getCustomDrawerWidth(clientX: number) {\n  let offsetRight = document.body.offsetWidth - (clientX - document.body.offsetLeft);\n  let widthPercent = Math.min((offsetRight / document.body.clientWidth) * 100, 98).toFixed(2);\n  return `${widthPercent}vw`;\n}\n\nfunction useBodyClassWhileOpen() {\n  useEffect(() => {\n    if (!document.body) {\n      return;\n    }\n\n    document.body.classList.add('body-drawer-open');\n\n    return () => {\n      document.body.classList.remove('body-drawer-open');\n    };\n  }, []);\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100%',\n      flex: '1 1 0',\n      minHeight: '100%',\n      position: 'relative',\n    }),\n    drawer: css({\n      top: 0,\n      position: 'absolute !important' as 'absolute',\n\n      '.rc-drawer-content-wrapper': {\n        boxShadow: theme.shadows.z3,\n      },\n    }),\n    drawerContent: css({\n      backgroundColor: `${theme.colors.background.primary} !important`,\n      display: 'flex',\n      overflow: 'unset !important',\n      flexDirection: 'column',\n    }),\n    drawerMotion: css({\n      '&-appear': {\n        transform: 'translateX(100%)',\n        transition: 'none !important',\n\n        '&-active': {\n          transition: `${theme.transitions.create('transform')} !important`,\n          transform: 'translateX(0)',\n        },\n      },\n    }),\n    // we want the mask itself to span the whole page including the top bar\n    // this ensures trying to click something in the top bar will close the drawer correctly\n    // but we don't want the backdrop styling to apply over the top bar as it looks weird\n    // instead have a child pseudo element to apply the backdrop styling below the top bar\n    mask: css({\n      // The !important here is to override the default .rc-drawer-mask styles\n      backgroundColor: 'transparent !important',\n      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions\n      position: 'absolute !important' as 'absolute',\n\n      '&:before': {\n        backgroundColor: `${theme.components.overlay.background} !important`,\n        bottom: 0,\n        content: '\"\"',\n        left: 0,\n        position: 'absolute',\n        right: 0,\n        top: 0,\n      },\n    }),\n    maskMotion: css({\n      '&-appear': {\n        opacity: 0,\n\n        '&-active': {\n          opacity: 1,\n          transition: theme.transitions.create('opacity'),\n        },\n      },\n    }),\n    header: css({\n      label: 'drawer-header',\n      flexGrow: 0,\n      padding: theme.spacing(2, 2, 3),\n      borderBottom: `1px solid ${theme.colors.border.weak}`,\n    }),\n    headerWithTabs: css({\n      borderBottom: 'none',\n    }),\n    actions: css({\n      position: 'absolute',\n      right: theme.spacing(1),\n      top: theme.spacing(1),\n    }),\n    titleWrapper: css({\n      label: 'drawer-title',\n      overflowWrap: 'break-word',\n    }),\n    subtitle: css({\n      label: 'drawer-subtitle',\n      color: theme.colors.text.secondary,\n      paddingTop: theme.spacing(1),\n    }),\n    content: css({\n      padding: theme.spacing(theme.components.drawer?.padding ?? 2),\n      height: '100%',\n      flexGrow: 1,\n      minHeight: 0,\n    }),\n    tabsWrapper: css({\n      label: 'drawer-tabs',\n      paddingLeft: theme.spacing(2),\n      margin: theme.spacing(1, -1, -3, -3),\n    }),\n    resizer: css({\n      top: 0,\n      left: theme.spacing(-1),\n      bottom: 0,\n      position: 'absolute',\n      zIndex: theme.zIndex.modal,\n    }),\n  };\n};\n\nfunction getWrapperStyles(theme: GrafanaTheme2, size: 'sm' | 'md' | 'lg') {\n  return css({\n    label: `drawer-content-wrapper-${size}`,\n    overflow: 'unset !important',\n\n    [theme.breakpoints.down('md')]: {\n      width: `calc(100% - ${theme.spacing(2)}) !important`,\n      minWidth: '0 !important',\n    },\n  });\n}\n", "import { css } from '@emotion/css';\nimport React from 'react';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Button, useStyles2 } from '@grafana/ui';\nimport { Drawer } from './Drawer';\n\ninterface SmartDrawerProps {\n  children: React.ReactNode;\n  title?: string;\n  isOpen: boolean;\n  onClose: () => void;\n  embedded?: boolean;\n  forceNoDrawer?: boolean;\n  investigationButton?: React.ReactNode;\n}\n\nexport const SmartDrawer = ({\n  children,\n  title,\n  isOpen,\n  onClose,\n  embedded = false,\n  forceNoDrawer = false,\n  investigationButton,\n}: SmartDrawerProps) => {\n  const styles = useStyles2(getStyles);\n\n  const shouldUseDrawer = !forceNoDrawer && !embedded;\n\n  if (!isOpen) {\n    return null;\n  }\n\n  if (shouldUseDrawer) {\n    return (\n      <Drawer size=\"lg\" title={title} onClose={onClose}>\n        {children}\n      </Drawer>\n    );\n  }\n\n  return (\n    <div className={styles.container}>\n      <div className={styles.drawerHeader}>\n        <Button variant=\"primary\" fill=\"text\" size=\"md\" icon={'arrow-left'} onClick={onClose}>\n          Back to all traces\n        </Button>\n        {embedded && investigationButton}\n      </div>\n      {children}\n    </div>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  container: css({\n    height: '100%',\n    width: '100%',\n    background: theme.colors.background.primary,\n    padding: theme.spacing(2),\n    display: 'flex',\n    flexDirection: 'column',\n    position: 'absolute',\n    top: 0,\n    left: 0,\n    zIndex: 3,\n  }),\n  drawerHeader: css({\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    paddingBottom: theme.spacing(2),\n\n    h4: {\n      margin: 0,\n    },\n  }),\n});\n", "import { AdHocVariableFilter } from '@grafana/data';\n\nexport function renderTraceQLLabelFilters(filters: AdHocVariableFilter[]) {\n  const expr = filters\n    .filter((f) => f.key && f.operator && f.value)\n    .map((filter) => renderFilter(filter))\n    .join('&&');\n  // Return 'true' if there are no filters to help with cases where we want to concatenate additional filters in the expression\n  // and avoid invalid queries like '{ && key=value }'\n  return expr.length ? expr : 'true';\n}\n\nfunction renderFilter(filter: AdHocVariableFilter) {\n  let val = filter.value;\n  if (\n    ['span.messaging.destination.partition.id', 'span.network.protocol.version'].includes(filter.key) ||\n    (!isNumber(val) &&\n      ![\n        'status',\n        'kind',\n        'span:status',\n        'span:kind',\n        'duration',\n        'span:duration',\n        'trace:duration',\n        'event:timeSinceStart',\n      ].includes(filter.key) &&\n      !['true', 'false'].includes(val)) &&\n      !isQuotedNumericString(val)\n  ) {\n    if (typeof val === 'string') {\n      // Escape \" and \\ to \\\" and \\\\ respectively\n      val = val.replace(/[\"\\\\]/g, (s) => `\\\\${s}`);\n      val = `\"${val}\"`;\n    }\n  }\n\n  return `${filter.key}${filter.operator}${val}`;\n}\n\nfunction isNumber(value?: string | number): boolean {\n  return value != null && value !== '' && !isNaN(Number(value.toString().trim()));\n}\n\nfunction isQuotedNumericString(value: string): boolean {\n  return typeof value === 'string' && value.length >= 2 && isNumber(value.slice(1, -1)) && ((value.startsWith('\"') && value.endsWith('\"')) || (value.startsWith(\"'\") && value.endsWith(\"'\")));\n}\n", "import { AdHocFiltersVariable } from '@grafana/scenes';\nimport { AdHocVariableFilter, VariableHide } from '@grafana/data';\nimport { VAR_FILTERS, explorationDS } from 'utils/shared';\nimport { renderTraceQLLabelFilters } from 'utils/filters-renderer';\n\nexport interface AttributeFiltersVariableProps {\n  initialFilters?: AdHocVariableFilter[];\n  embedderName?: string;\n  embedded?: boolean;\n}\n\nexport class AttributeFiltersVariable extends AdHocFiltersVariable {\n  private initialFilters?: AdHocVariableFilter[];\n  private embedderName?: string;\n  private embedded?: boolean;\n\n  constructor(props: Partial<AttributeFiltersVariableProps>) {\n    super({\n      addFilterButtonText: 'Add filter',\n      name: VAR_FILTERS,\n      datasource: explorationDS,\n      hide: VariableHide.hideLabel,\n      layout: 'combobox',\n      filters: (props.initialFilters ?? []).map((f) => ({\n        ...f,\n        readOnly: props.embedded,\n        origin: props.embedderName,\n      })),\n      allowCustomValue: true,\n      expressionBuilder: renderTraceQLLabelFilters,\n    });\n\n    this.initialFilters = props.initialFilters;\n    this.embedderName = props.embedderName;\n    this.embedded = props.embedded;\n\n    // Subscribe to state changes to update readOnly and origin for matching filters\n    this.subscribeToState((newState) => {\n      if (newState.filters && this.embedded) {\n        let hasChanges = false;\n        const updatedFilters = newState.filters.map((filter) => {\n          // Check if this filter matches any of the initial filters\n          const matchingInitialFilter = this.initialFilters?.find(\n            (initialFilter) =>\n              initialFilter.key === filter.key &&\n              initialFilter.operator === filter.operator &&\n              initialFilter.value === filter.value\n          );\n\n          if (matchingInitialFilter && !filter.readOnly && filter.origin !== this.embedderName) {\n            hasChanges = true;\n            return {\n              ...filter,\n              readOnly: true,\n              origin: this.embedderName,\n            };\n          }\n\n          return filter;\n        });\n\n        // Only update if there are actual changes\n        if (hasChanges) {\n          this.setState({ filters: updatedFilters });\n        }\n      }\n    });\n  }\n}\n", "import { css } from '@emotion/css';\nimport React, { useEffect } from 'react';\n\nimport { GrafanaTheme2, LoadingState, PluginExtensionLink, AdHocVariableFilter } from '@grafana/data';\nimport {\n  CustomVariable,\n  DataSourceVariable,\n  SceneComponentProps,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n  SceneObjectUrlSyncConfig,\n  SceneObjectUrlValues,\n  SceneQueryRunner,\n  SceneRefreshPicker,\n  SceneTimePicker,\n  SceneTimeRange,\n  SceneVariableSet,\n} from '@grafana/scenes';\nimport { config, useReturnToPrevious } from '@grafana/runtime';\nimport { Button, Dropdown, Icon, Menu, Stack, useStyles2, <PERSON>Button } from '@grafana/ui';\n\nimport {\n  DATASOURCE_LS_KEY,\n  EventTraceOpened,\n  MetricFunction,\n  VAR_DATASOURCE,\n  VAR_GROUPBY,\n  VAR_LATENCY_PARTIAL_THRESHOLD,\n  VAR_LATENCY_THRESHOLD,\n  VAR_METRIC,\n  VAR_PRIMARY_SIGNAL,\n  VAR_SPAN_LIST_COLUMNS,\n} from '../../utils/shared';\nimport {\n  getTraceExplorationScene,\n  getFiltersVariable,\n  getPrimarySignalVariable,\n  getDataSource,\n  getUrlForExploration,\n} from '../../utils/utils';\nimport { TraceDrawerScene } from '../../components/Explore/TracesByService/TraceDrawerScene';\nimport { VariableHide } from '@grafana/schema';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { PrimarySignalVariable } from './PrimarySignalVariable';\nimport { primarySignalOptions } from './primary-signals';\nimport { TraceQLIssueDetector, TraceQLConfigWarning } from '../../components/Explore/TraceQLIssueDetector';\nimport { AddToInvestigationButton } from 'components/Explore/actions/AddToInvestigationButton';\nimport { ADD_TO_INVESTIGATION_MENU_TEXT, getInvestigationLink } from 'components/Explore/panels/PanelMenu';\nimport { TracesByServiceScene } from 'components/Explore/TracesByService/TracesByServiceScene';\nimport { SharedExplorationState } from 'exposedComponents/types';\nimport { EntityAssertionsWidget } from '../../addedComponents/EntityAssertionsWidget/EntityAssertionsWidget';\nimport { SmartDrawer } from './SmartDrawer';\nimport { AttributeFiltersVariable } from './AttributeFiltersVariable';\n\nexport interface TraceExplorationState extends SharedExplorationState, SceneObjectState {\n  topScene?: SceneObject;\n  controls: SceneObject[];\n\n  body: SceneObject;\n\n  drawerScene?: TraceDrawerScene;\n\n  // details scene\n  traceId?: string;\n  spanId?: string;\n\n  issueDetector?: TraceQLIssueDetector;\n\n  investigationLink?: PluginExtensionLink;\n  addToInvestigationButton?: AddToInvestigationButton;\n}\n\nconst version = process.env.VERSION;\nconst buildTime = process.env.BUILD_TIME;\nconst commitSha = process.env.COMMIT_SHA;\nconst compositeVersion = `${buildTime?.split('T')[0]} (${commitSha})`;\n\nexport class TraceExploration extends SceneObjectBase<TraceExplorationState> {\n  protected _urlSync = new SceneObjectUrlSyncConfig(this, { keys: ['traceId', 'spanId'] });\n\n  public constructor(state: Partial<TraceExplorationState>) {\n    super({\n      $timeRange: state.$timeRange ?? new SceneTimeRange({}),\n      $variables: state.$variables ?? getVariableSet(state as TraceExplorationState),\n      controls: state.controls ?? [new SceneTimePicker({}), new SceneRefreshPicker({})],\n      body: new TraceExplorationScene({}),\n      drawerScene: new TraceDrawerScene({}),\n      issueDetector: new TraceQLIssueDetector(),\n      ...state,\n    });\n\n    this.addActivationHandler(this._onActivate.bind(this));\n  }\n\n  public _onActivate() {\n    if (!this.state.topScene) {\n      this.setState({ topScene: getTopScene() });\n    }\n\n    this._subs.add(\n      this.subscribeToEvent(EventTraceOpened, (event) => {\n        this.setupInvestigationButton(event.payload.traceId);\n        this.setState({ traceId: event.payload.traceId, spanId: event.payload.spanId });\n      })\n    );\n\n    if (this.state.traceId) {\n      this.setupInvestigationButton(this.state.traceId);\n    }\n\n    const datasourceVar = sceneGraph.lookupVariable(VAR_DATASOURCE, this) as DataSourceVariable;\n    datasourceVar.subscribeToState((newState) => {\n      if (newState.value) {\n        localStorage.setItem(DATASOURCE_LS_KEY, newState.value.toString());\n      }\n    });\n\n    if (this.state.issueDetector) {\n      if (!this.state.issueDetector.isActive) {\n        this.state.issueDetector.activate();\n      }\n    }\n  }\n\n  getUrlState() {\n    return { traceId: this.state.traceId, spanId: this.state.spanId };\n  }\n\n  updateFromUrl(values: SceneObjectUrlValues) {\n    const stateUpdate: Partial<TraceExplorationState> = {};\n\n    if (values.traceId || values.spanId) {\n      stateUpdate.traceId = values.traceId ? (values.traceId as string) : undefined;\n      stateUpdate.spanId = values.spanId ? (values.spanId as string) : undefined;\n    }\n\n    this.setState(stateUpdate);\n  }\n\n  public getMetricVariable() {\n    const variable = sceneGraph.lookupVariable(VAR_METRIC, this);\n    if (!(variable instanceof CustomVariable)) {\n      throw new Error('Metric variable not found');\n    }\n\n    if (!variable.getValue()) {\n      variable.changeValueTo(this.state.initialMetric ?? 'rate');\n    }\n\n    return variable;\n  }\n\n  public onChangeMetricFunction = (metric: string) => {\n    const variable = this.getMetricVariable();\n    if (!metric || variable.getValue() === metric) {\n      return;\n    }\n\n    variable.changeValueTo(metric, undefined, true);\n  };\n\n  public getMetricFunction() {\n    return this.getMetricVariable().getValue() as MetricFunction;\n  }\n\n  public closeDrawer() {\n    this.setState({ traceId: undefined, spanId: undefined });\n  }\n\n  private setupInvestigationButton(traceId: string) {\n    const traceExploration = getTraceExplorationScene(this);\n    const dsUid = getDataSource(traceExploration);\n\n    const queryRunner = new SceneQueryRunner({\n      datasource: { uid: dsUid },\n      queries: [\n        {\n          refId: 'A',\n          query: traceId,\n          queryType: 'traceql',\n        },\n      ],\n    });\n\n    const addToInvestigationButton = new AddToInvestigationButton({\n      query: traceId,\n      type: 'trace',\n      dsUid,\n      $data: queryRunner,\n    });\n\n    addToInvestigationButton.activate();\n    this.setState({ addToInvestigationButton });\n    this._subs.add(\n      addToInvestigationButton.subscribeToState(() => {\n        this.updateInvestigationLink();\n      })\n    );\n\n    queryRunner.activate();\n\n    this._subs.add(\n      queryRunner.subscribeToState((state) => {\n        if (state.data?.state === LoadingState.Done && state.data?.series?.length > 0) {\n          const serviceNameField = state.data.series[0]?.fields?.find((f) => f.name === 'serviceName');\n\n          if (serviceNameField && serviceNameField.values[0]) {\n            addToInvestigationButton.setState({\n              ...addToInvestigationButton.state,\n              labelValue: `${serviceNameField.values[0]}`,\n            });\n          }\n        }\n      })\n    );\n\n    addToInvestigationButton.setState({\n      ...addToInvestigationButton.state,\n      labelValue: traceId,\n    });\n  }\n\n  private async updateInvestigationLink() {\n    const { addToInvestigationButton } = this.state;\n    if (!addToInvestigationButton) {\n      return;\n    }\n\n    const link = await getInvestigationLink(addToInvestigationButton);\n    if (link) {\n      this.setState({ investigationLink: link });\n    }\n  }\n\n  static Component = ({ model }: SceneComponentProps<TraceExploration>) => {\n    const { body } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return <div className={styles.bodyContainer}> {body && <body.Component model={body} />} </div>;\n  };\n}\n\nexport class TraceExplorationScene extends SceneObjectBase {\n  static Component = ({ model }: SceneComponentProps<TraceExplorationScene>) => {\n    const traceExploration = getTraceExplorationScene(model);\n    const {\n      controls,\n      topScene,\n      drawerScene,\n      traceId,\n      issueDetector,\n      investigationLink,\n      addToInvestigationButton,\n      embedded,\n    } = traceExploration.useState();\n    const { hasIssue } = issueDetector?.useState() || {\n      hasIssue: false,\n    };\n    const styles = useStyles2(getStyles);\n\n    const addToInvestigationClicked = (e: React.MouseEvent) => {\n      if (investigationLink?.onClick) {\n        investigationLink.onClick(e);\n      }\n\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.add_to_investigation_trace_view_clicked\n      );\n\n      setTimeout(() => traceExploration.closeDrawer(), 100);\n    };\n\n    return (\n      <div className={styles.container} id=\"trace-exploration\">\n        {hasIssue && issueDetector && <TraceQLConfigWarning detector={issueDetector} />}\n        {embedded ? <EmbeddedHeader model={model} /> : <TraceExplorationHeader controls={controls} model={model} />}\n        <div className={styles.body}>{topScene && <topScene.Component model={topScene} />}</div>\n        <SmartDrawer\n          isOpen={!!drawerScene && !!traceId}\n          onClose={() => traceExploration.closeDrawer()}\n          title={`View trace ${traceId}`}\n          embedded={embedded}\n          forceNoDrawer={embedded}\n          investigationButton={\n            addToInvestigationButton &&\n            investigationLink && (\n              <Button variant=\"secondary\" size=\"sm\" icon=\"plus-square\" onClick={addToInvestigationClicked}>\n                {ADD_TO_INVESTIGATION_MENU_TEXT}\n              </Button>\n            )\n          }\n        >\n          {drawerScene && <drawerScene.Component model={drawerScene} />}\n        </SmartDrawer>\n      </div>\n    );\n  };\n}\n\nexport const useServiceName = (model: SceneObject) => {\n  const [serviceName, setServiceName] = React.useState<string>();\n  const traceExploration = getTraceExplorationScene(model);\n  const filtersVariable = getFiltersVariable(traceExploration);\n\n  const getServiceNameFromFilters = (filters: AdHocVariableFilter[]) => {\n    const serviceNameFilter = filters.find((f) => f.key === 'resource.service.name');\n    return serviceNameFilter?.operator === '=' || serviceNameFilter?.operator === '=~'\n      ? serviceNameFilter?.value?.replace(/\"/g, '')\n      : undefined;\n  };\n\n  useEffect(() => {\n    setServiceName(getServiceNameFromFilters(filtersVariable.state.filters));\n\n    const sub = filtersVariable.subscribeToState((newState) => {\n      setServiceName(getServiceNameFromFilters(newState.filters));\n    });\n\n    return () => {\n      sub.unsubscribe();\n    };\n  }, [filtersVariable]);\n\n  return serviceName;\n};\n\nconst EmbeddedHeader = ({ model }: SceneComponentProps<TraceExplorationScene>) => {\n  const setReturnToPrevious = useReturnToPrevious();\n  const styles = useStyles2(getStyles, true);\n  const traceExploration = getTraceExplorationScene(model);\n  const { returnToPreviousSource } = traceExploration.useState();\n  const filtersVariable = getFiltersVariable(traceExploration);\n  const primarySignalVariable = getPrimarySignalVariable(traceExploration);\n  const timeRangeControl = traceExploration.state.controls.find((control) => control instanceof SceneTimePicker);\n\n  const timeRangeState = traceExploration.state.$timeRange?.useState();\n  const filtersVariableState = filtersVariable.useState();\n  const metricVariableState = traceExploration.getMetricVariable().useState();\n  const [explorationUrl, setExplorationUrl] = React.useState(() => getUrlForExploration(traceExploration));\n  \n  // Force the primary signal to be 'All Spans'\n  primarySignalVariable?.changeValueTo(primarySignalOptions[1].value!);\n\n  useEffect(() => {\n    setExplorationUrl(getUrlForExploration(traceExploration));\n  }, [timeRangeState, filtersVariableState, metricVariableState, traceExploration]);\n\n  return (\n    <div className={styles.headerContainer}>\n      <Stack gap={1} alignItems={'center'} wrap={'wrap'} justifyContent=\"space-between\">\n        <primarySignalVariable.Component model={primarySignalVariable} />\n        {filtersVariable && (\n          <div>\n            <filtersVariable.Component model={filtersVariable} />\n          </div>\n        )}\n        <Stack gap={1} alignItems={'center'}>\n          <LinkButton\n            href={explorationUrl}\n            variant=\"secondary\"\n            icon=\"arrow-right\"\n            onClick={() => {\n              setReturnToPrevious(returnToPreviousSource || 'previous');\n              reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.go_to_full_app_clicked);\n            }}\n          >\n            Traces Drilldown\n          </LinkButton>\n          {timeRangeControl && <timeRangeControl.Component model={timeRangeControl} />}\n        </Stack>\n      </Stack>\n    </div>\n  );\n};\n\ninterface TraceExplorationHeaderProps {\n  controls: SceneObject[];\n  model: SceneObject;\n}\n\nconst TraceExplorationHeader = ({ controls, model }: TraceExplorationHeaderProps) => {\n  const styles = useStyles2(getStyles);\n  const [menuVisible, setMenuVisible] = React.useState(false);\n  const serviceName = useServiceName(model);\n  const traceExploration = getTraceExplorationScene(model);\n\n  const dsVariable = sceneGraph.lookupVariable(VAR_DATASOURCE, traceExploration);\n  const filtersVariable = getFiltersVariable(traceExploration);\n  const primarySignalVariable = getPrimarySignalVariable(traceExploration);\n\n  function VersionHeader() {\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.menuHeader}>\n        <h5>Grafana Traces Drilldown v{version}</h5>\n        <div className={styles.menuHeaderSubtitle}>Last update: {compositeVersion}</div>\n      </div>\n    );\n  }\n\n  const menu = (\n    <Menu header={<VersionHeader />}>\n      <div className={styles.menu}>\n        {config.feedbackLinksEnabled && (\n          <Menu.Item\n            label=\"Give feedback\"\n            ariaLabel=\"Give feedback\"\n            icon={'comment-alt-message'}\n            url=\"https://grafana.qualtrics.com/jfe/form/SV_9LUZ21zl3x4vUcS\"\n            target=\"_blank\"\n            onClick={() =>\n              reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.global_docs_link_clicked)\n            }\n          />\n        )}\n        <Menu.Item\n          label=\"Documentation\"\n          ariaLabel=\"Documentation\"\n          icon={'external-link-alt'}\n          url=\"https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/\"\n          target=\"_blank\"\n          onClick={() =>\n            reportAppInteraction(USER_EVENTS_PAGES.common, USER_EVENTS_ACTIONS.common.feedback_link_clicked)\n          }\n        />\n      </div>\n    </Menu>\n  );\n\n  return (\n    <div className={styles.headerContainer}>\n      <Stack gap={1} justifyContent={'space-between'} wrap={'wrap'}>\n        <Stack gap={1} alignItems={'center'} wrap={'wrap'}>\n          {dsVariable && (\n            <Stack gap={0} alignItems={'center'}>\n              <div className={styles.datasourceLabel}>Data source</div>\n              <dsVariable.Component model={dsVariable} />\n            </Stack>\n          )}\n        </Stack>\n        <div className={styles.controls}>\n          <EntityAssertionsWidget serviceName={serviceName || ''} model={model} />\n          <Dropdown overlay={menu} onVisibleChange={() => setMenuVisible(!menuVisible)}>\n            <Button variant=\"secondary\" icon=\"info-circle\">\n              Need help\n              <Icon className={styles.helpIcon} name={menuVisible ? 'angle-up' : 'angle-down'} size=\"lg\" />\n            </Button>\n          </Dropdown>\n          {controls.map((control) => (\n            <control.Component key={control.state.key} model={control} />\n          ))}\n        </div>\n      </Stack>\n      <Stack gap={1} alignItems={'center'} wrap={'wrap'}>\n        <Stack gap={0} alignItems={'center'}>\n          <div className={styles.datasourceLabel}>Filters</div>\n          {primarySignalVariable && <primarySignalVariable.Component model={primarySignalVariable} />}\n        </Stack>\n        {filtersVariable && (\n          <div>\n            <filtersVariable.Component model={filtersVariable} />\n          </div>\n        )}\n      </Stack>\n    </div>\n  );\n};\n\nfunction getTopScene() {\n  return new TracesByServiceScene({});\n}\n\nfunction getVariableSet(state: TraceExplorationState) {\n  return new SceneVariableSet({\n    variables: [\n      new DataSourceVariable({\n        name: VAR_DATASOURCE,\n        label: 'Data source',\n        value: state.initialDS,\n        pluginId: 'tempo',\n        isReadOnly: state.embedded,\n      }),\n      new PrimarySignalVariable({\n        name: VAR_PRIMARY_SIGNAL,\n        isReadOnly: state.embedded,\n      }),\n      new AttributeFiltersVariable({\n        initialFilters: state.initialFilters,\n        embedderName: state.embedderName,\n        embedded: state.embedded,\n      }),\n      new CustomVariable({\n        name: VAR_METRIC,\n        hide: VariableHide.hideVariable,\n      }),\n      new CustomVariable({\n        name: VAR_GROUPBY,\n        defaultToAll: false,\n        value: state.initialGroupBy,\n      }),\n      new CustomVariable({\n        name: VAR_SPAN_LIST_COLUMNS,\n        defaultToAll: false,\n      }),\n      new CustomVariable({\n        name: VAR_LATENCY_THRESHOLD,\n        defaultToAll: false,\n        hide: VariableHide.hideVariable,\n      }),\n      new CustomVariable({\n        name: VAR_LATENCY_PARTIAL_THRESHOLD,\n        defaultToAll: false,\n        hide: VariableHide.hideVariable,\n      }),\n    ],\n  });\n}\n\nfunction getStyles(theme: GrafanaTheme2, embedded?: boolean) {\n  return {\n    bodyContainer: css({\n      label: 'bodyContainer',\n      flexGrow: 1,\n      display: 'flex',\n      minHeight: '100%',\n      flexDirection: 'column',\n    }),\n    container: css({\n      label: 'container',\n      flexGrow: 1,\n      display: 'flex',\n      gap: theme.spacing(1),\n      minHeight: '100%',\n      flexDirection: 'column',\n      padding: `0 ${theme.spacing(2)} ${theme.spacing(2)} ${theme.spacing(2)}`,\n      overflow: 'auto' /* Needed for sticky positioning */,\n      maxHeight: '100%' /* Needed for sticky positioning */,\n      position: 'relative', // Needed for the drawer to be positioned correctly\n    }),\n    drawerHeader: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n      alignItems: 'center',\n      borderBottom: `1px solid ${theme.colors.border.weak}`,\n      paddingBottom: theme.spacing(2),\n      marginBottom: theme.spacing(2),\n\n      h3: {\n        margin: 0,\n      },\n    }),\n    drawerHeaderButtons: css({\n      display: 'flex',\n      justifyContent: 'flex-end',\n      gap: theme.spacing(1.5),\n    }),\n    body: css({\n      label: 'body',\n      flexGrow: 1,\n      display: 'flex',\n      flexDirection: 'column',\n      gap: theme.spacing(1),\n    }),\n    headerContainer: css({\n      label: 'headerContainer',\n      backgroundColor: embedded ? theme.colors.background.primary : theme.colors.background.canvas,\n      display: 'flex',\n      flexDirection: 'column',\n      position: 'sticky',\n      top: 0,\n      zIndex: 3,\n      padding: `${theme.spacing(1.5)} 0`,\n      gap: theme.spacing(1),\n    }),\n    datasourceLabel: css({\n      label: 'datasourceLabel',\n      fontSize: '12px',\n      padding: `0 ${theme.spacing(1)}`,\n      height: '32px',\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'flex-start',\n      fontWeight: theme.typography.fontWeightMedium,\n      position: 'relative',\n      right: -1,\n      width: '90px',\n    }),\n    controls: css({\n      label: 'controls',\n      display: 'flex',\n      gap: theme.spacing(1),\n      zIndex: 3,\n      flexWrap: 'wrap',\n    }),\n    menu: css({\n      label: 'menu',\n      'svg, span': {\n        color: theme.colors.text.link,\n      },\n    }),\n    menuHeader: css`\n      padding: ${theme.spacing(0.5, 1)};\n      white-space: nowrap;\n    `,\n    menuHeaderSubtitle: css`\n      color: ${theme.colors.text.secondary};\n      font-size: ${theme.typography.bodySmall.fontSize};\n    `,\n    tooltip: css({\n      label: 'tooltip',\n      fontSize: '14px',\n      lineHeight: '22px',\n      width: '180px',\n      textAlign: 'center',\n    }),\n    helpIcon: css({\n      label: 'helpIcon',\n      marginLeft: theme.spacing(1),\n    }),\n    filters: css({\n      label: 'filters',\n      marginTop: theme.spacing(1),\n      display: 'flex',\n      gap: theme.spacing(1),\n    }),\n  };\n}\n", "import { css, keyframes } from '@emotion/css';\nimport { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport { useStyles2, useTheme2 } from '@grafana/ui';\nimport React from 'react';\nimport { SkeletonTheme } from 'react-loading-skeleton';\nimport { testIds } from 'utils/testIds';\n\ninterface LoadingStateSceneState extends SceneObjectState {\n  component: () => React.JSX.Element;\n}\n\nexport class LoadingStateScene extends SceneObjectBase<LoadingStateSceneState> {\n  public static Component = ({ model }: SceneComponentProps<LoadingStateScene>) => {\n    const theme = useTheme2();\n    const styles = useStyles2(getStyles);\n    const { component } = model.useState();\n\n    return (\n      <div className={styles.container} data-testid={testIds.loadingState}>\n        <SkeletonTheme\n          baseColor={theme.colors.emphasize(theme.colors.background.secondary)}\n          highlightColor={theme.colors.emphasize(theme.colors.background.secondary, 0.1)}\n          borderRadius={theme.shape.radius.default}\n        >\n          {component()}\n        </SkeletonTheme>\n      </div>\n    );\n  };\n}\n\nconst fadeIn = keyframes({\n  '0%': {\n    opacity: 0,\n  },\n  '100%': {\n    opacity: 1,\n  },\n});\n\nfunction getStyles() {\n  return {\n    container: css({\n      label: 'loading-state-scene',\n      // animation prevents flickering when loading\n      animationName: fadeIn,\n      animationDelay: '100ms',\n      animationTimingFunction: 'ease-in',\n      animationDuration: '100ms',\n      animationFillMode: 'backwards',\n    }),\n  };\n}\n", "import React from 'react';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Icon, Tooltip, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\n\ninterface StreamingIndicatorProps {\n  isStreaming: boolean;\n  iconSize?: number;\n}\n\nexport const StreamingIndicator = ({ \n  isStreaming, \n  iconSize = 14,\n}: StreamingIndicatorProps) => {\n  const styles = useStyles2(getStyles, iconSize);\n\n  if (!isStreaming) {\n    return null;\n  }\n\n  return (\n    <Tooltip content={'Streaming'}>\n      <Icon name={'circle-mono'} size=\"md\" className={styles.streamingIndicator} />\n    </Tooltip>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2, iconSize: number) => {\n  return {\n    streamingIndicator: css({\n      width: `${iconSize}px`,\n      height: `${iconSize}px`,\n      backgroundColor: theme.colors.success.text,\n      fill: theme.colors.success.text,\n      borderRadius: '50%',\n      display: 'inline-block',\n    }),\n  };\n}; \n", "import { map, Observable } from 'rxjs';\nimport { DataFrame, DataTopic, Field } from '@grafana/data';\nimport { CustomTransformerDefinition } from '@grafana/scenes';\n\nexport const exemplarsTransformations = (\n  openTrace?: (traceId: string, spanId?: string) => void\n): CustomTransformerDefinition[] => [\n  {\n    topic: DataTopic.Annotations,\n    operator: () => (source: Observable<DataFrame[]>) => {\n      return source.pipe(\n        map((data: DataFrame[]) => {\n          return data.map((frame) => {\n            if (frame.name === 'exemplar') {\n              const traceIDField = frame.fields.find((field: Field) => field.name === 'traceId');\n              if (traceIDField) {\n                // The traceID will be interpolated in the url\n                // Then, onClick we retrieve the traceId from the url and navigate to the trace exploration scene by setting the state\n                traceIDField.config.links = [\n                  {\n                    title: 'View trace',\n                    url: '#${__value.raw}',\n                    onClick: (event) => {\n                      event.e.stopPropagation(); // Prevent the click event from propagating to the parent anchor\n                      const parentAnchorHref = event.e.target?.parentElement?.parentElement?.href;\n                      if (!parentAnchorHref || parentAnchorHref.indexOf('#') === -1) {\n                        return;\n                      }\n                      const traceId = parentAnchorHref.split('#')[1];\n                      if (!traceId || traceId === '') {\n                        return;\n                      }\n                      openTrace?.(traceId);\n                    },\n                  },\n                ];\n              }\n            }\n\n            return frame;\n          });\n        })\n      );\n    },\n  },\n];\n\nexport const removeExemplarsTransformation = (): CustomTransformerDefinition[] => [\n  {\n    topic: DataTopic.Annotations,\n    operator: () => (source: Observable<DataFrame[]>) => {\n      return source.pipe(\n        map((data: DataFrame[]) => {\n          return data.filter((frame) => frame.name !== 'exemplar');\n        })\n      );\n    },\n  },\n];\n\n", "import { throttle } from 'lodash';\nimport { useState, useEffect } from 'react';\n\ninterface MousePosition {\n  x: number | null;\n  y: number | null;\n}\n\n// For performance reasons, we throttle the mouse position updates\nconst DEFAULT_THROTTLE_INTERVAL_MS = 50;\n\nconst useMousePosition = (throttleInterval = DEFAULT_THROTTLE_INTERVAL_MS) => {\n  const [mousePosition, setMousePosition] = useState<MousePosition>({ x: null, y: null });\n\n  useEffect(() => {\n    const updateMousePosition = throttle((event: MouseEvent) => {\n      setMousePosition({ x: event.clientX, y: event.clientY });\n    }, throttleInterval);\n    window.addEventListener('mousemove', updateMousePosition);\n\n    return () => {\n      window.removeEventListener('mousemove', updateMousePosition);\n    };\n  }, [throttleInterval]);\n\n  return mousePosition;\n};\n\nexport default useMousePosition;\n", "import { css } from '@emotion/css';\nimport React, { SVGProps } from 'react';\nimport SVG from 'react-inlinesvg';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useStyles2, useTheme2 } from '@grafana/ui';\n\nimport dark404 from './img/grot-404-dark.svg';\nimport light404 from './img/grot-404-light.svg';\n\nimport useMousePosition from './useMousePosition';\n\nconst MIN_ARM_ROTATION = -20;\nconst MAX_ARM_ROTATION = 5;\nconst MIN_ARM_TRANSLATION = -5;\nconst MAX_ARM_TRANSLATION = 5;\n\nexport interface Props {\n  width?: SVGProps<SVGElement>['width'];\n  height?: SVGProps<SVGElement>['height'];\n  show404?: boolean;\n}\n\nexport const GrotNotFound = ({ width = 'auto', height, show404 = false }: Props) => {\n  const theme = useTheme2();\n  const { x, y } = useMousePosition();\n  const styles = useStyles2(getStyles, x, y, show404);\n  return <SVG src={theme.isDark ? dark404 : light404} className={styles.svg} height={height} width={width} />;\n};\n\nGrotNotFound.displayName = 'GrotNotFound';\n\nconst getStyles = (theme: GrafanaTheme2, xPos: number | null, yPos: number | null, show404: boolean) => {\n  const { innerWidth, innerHeight } = window;\n  const heightRatio = yPos && yPos / innerHeight;\n  const widthRatio = xPos && xPos / innerWidth;\n  const rotation = heightRatio !== null ? getIntermediateValue(heightRatio, MIN_ARM_ROTATION, MAX_ARM_ROTATION) : 0;\n  const translation =\n    widthRatio !== null ? getIntermediateValue(widthRatio, MIN_ARM_TRANSLATION, MAX_ARM_TRANSLATION) : 0;\n\n  return {\n    svg: css({\n      '#grot-404-arm, #grot-404-magnifier': {\n        transform: `rotate(${rotation}deg) translateX(${translation}%)`,\n        transformOrigin: 'center',\n        transition: 'transform 50ms linear',\n      },\n      '#grot-404-text': {\n        display: show404 ? 'block' : 'none',\n      },\n    }),\n  };\n};\n\n/**\n * Given a start value, end value, and a ratio, return the intermediate value\n * Works with negative and inverted start/end values\n */\nconst getIntermediateValue = (ratio: number, start: number, end: number) => {\n  const value = ratio * (end - start) + start;\n  return value;\n};\n", "import React from 'react';\n\nimport { Icon, Stack, Text, useStyles2 } from '@grafana/ui';\nimport { GrafanaTheme2 } from '@grafana/data';\n\nimport { GrotNotFound } from './GrotNotFound';\nimport { css } from '@emotion/css';\nimport { testIds } from 'utils/testIds';\n\nexport interface Props {\n  message?: string | React.ReactNode;\n  remedyMessage?: string;\n  imgWidth?: number;\n  padding?: string;\n}\n\nexport const EmptyState = ({ message, remedyMessage, imgWidth, padding }: Props) => {\n  const styles = useStyles2(getStyles, padding);\n\n  return (\n    <div className={styles.container} data-testid={testIds.emptyState}>\n      <Stack direction=\"column\" alignItems=\"center\" gap={3}>\n        <GrotNotFound width={imgWidth ?? 300} />\n        {typeof message === 'string' &&  <Text textAlignment={'center'} variant=\"h5\">{message}</Text>}\n        {typeof message !== 'string' &&  message}\n\n        {remedyMessage && (\n          <div className={styles.remedy}>\n            <Stack gap={0.5} alignItems={'center'}>\n              <Icon name=\"info-circle\" />\n              <Text textAlignment={'center'} variant=\"body\">\n                {remedyMessage}\n              </Text>\n            </Stack>\n          </div>\n        )}\n      </Stack>\n    </div>\n  );\n};\n\nEmptyState.displayName = 'EmptyState';\n\nfunction getStyles(theme: GrafanaTheme2, padding?: string) {\n  return {\n    container: css({\n      width: '100%',\n      display: 'flex',\n      justifyContent: 'space-evenly',\n      flexDirection: 'column',\n      padding: padding ? padding : 0,\n    }),\n    remedy: css({\n      marginBottom: theme.spacing(4),\n    })\n  };\n}\n", "import { SelectableValue } from '@grafana/data';\n\nexport const DATABASE_CALLS_KEY = 'span.db.system.name';\n\nexport const primarySignalOptions: Array<SelectableValue<string>> = [\n  {\n    label: 'Root spans',\n    value: 'nestedSetParent<0',\n    filter: { key: 'nestedSetParent', operator: '<', value: '0' },\n    description: 'Focus your analysis on the root span of each trace',\n  },\n  {\n    label: 'All spans',\n    value: 'true',\n    filter: { key: '', operator: '', value: true },\n    description: 'View and analyse raw span data. This option may result in long query times.',\n  },\n  {\n    label: 'Server spans',\n    value: 'kind=server',\n    filter: { key: 'kind', operator: '=', value: 'server' },\n    description: 'Explore server-specific segments of traces',\n  },\n  {\n    label: 'Consumer spans',\n    value: 'kind=consumer',\n    filter: { key: 'kind', operator: '=', value: 'consumer' },\n    description: 'Analyze interactions initiated by consumer services',\n  },\n  {\n    label: 'Database calls',\n    value: `${DATABASE_CALLS_KEY}!=\"\"`,\n    filter: { key: DATABASE_CALLS_KEY, operator: '!=', value: '\"\"' },\n    description: 'Evaluate the performance issues in database interactions',\n  },\n];\n\nexport const getSignalForKey = (key?: string) => {\n  return primarySignalOptions.find((option) => option.value === key);\n};\n", "import { PanelMenuItem, PluginExtensionLink, toURLRange, urlUtil } from '@grafana/data';\nimport {\n  SceneObjectBase,\n  VizPanelMenu,\n  SceneObject,\n  SceneComponentProps,\n  sceneGraph,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport React from 'react';\nimport { AddToInvestigationButton } from '../actions/AddToInvestigationButton';\n// Certain imports are not available in the dependant package, but can be if the plugin is running in a different Grafana version.\n// We need both imports to support Grafana v11 and v12.\n// @ts-expect-error\nimport { config, getPluginLinkExtensions, getObservablePluginLinks } from '@grafana/runtime';\nimport { reportAppInteraction, USER_EVENTS_PAGES, USER_EVENTS_ACTIONS } from 'utils/analytics';\nimport { getCurrentStep, getDataSource, getTraceExplorationScene } from 'utils/utils';\nimport { firstValueFrom } from 'rxjs';\n\nexport const ADD_TO_INVESTIGATION_MENU_TEXT = 'Add to investigation';\nconst extensionPointId = 'grafana-exploretraces-app/investigation/v1';\nconst ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT = 'investigations_divider'; // Text won't be visible\nconst ADD_TO_INVESTIGATION_MENU_GROUP_TEXT = 'Investigations';\n\ninterface PanelMenuState extends SceneObjectState {\n  body?: VizPanelMenu;\n  query?: string;\n  labelValue?: string;\n  addToInvestigationButton?: AddToInvestigationButton;\n}\n\nexport class PanelMenu extends SceneObjectBase<PanelMenuState> implements VizPanelMenu, SceneObject {\n  constructor(state: Partial<PanelMenuState>) {\n    super(state);\n    this.addActivationHandler(() => {\n      const items: PanelMenuItem[] = [\n        {\n          text: 'Navigation',\n          type: 'group',\n        },\n        {\n          text: 'Explore',\n          iconClassName: 'compass',\n          href: getExploreHref(this),\n          onClick: () => onExploreClick(),\n        },\n      ];\n\n      this.setState({\n        body: new VizPanelMenu({\n          items,\n        }),\n      });\n\n      const traceExploration = getTraceExplorationScene(this);\n      const dsUid = getDataSource(traceExploration);\n\n      const addToInvestigationButton = new AddToInvestigationButton({\n        query: this.state.query,\n        dsUid,\n      });\n\n      addToInvestigationButton.activate();\n      this.setState({ addToInvestigationButton });\n      this._subs.add(\n        addToInvestigationButton?.subscribeToState(() => {\n          subscribeToAddToInvestigation(this);\n        })\n      );\n    \n      addToInvestigationButton.setState({\n        ...addToInvestigationButton.state,\n        labelValue: this.state.labelValue,\n      });\n    });\n  }\n\n  addItem(item: PanelMenuItem): void {\n    if (this.state.body) {\n      this.state.body.addItem(item);\n    }\n  }\n\n  setItems(items: PanelMenuItem[]): void {\n    if (this.state.body) {\n      this.state.body.setItems(items);\n    }\n  }\n\n  public static Component = ({ model }: SceneComponentProps<PanelMenu>) => {\n    const { body } = model.useState();\n\n    if (body) {\n      return <body.Component model={body} />;\n    }\n\n    return <></>;\n  };\n}\n\nconst getExploreHref = (model: SceneObject<PanelMenuState>) => {\n  const traceExploration = getTraceExplorationScene(model);\n  const datasource = getDataSource(traceExploration);\n  const timeRange = sceneGraph.getTimeRange(model).state.value;\n  const step = getCurrentStep(model);\n\n  const exploreState = JSON.stringify({\n    ['traces-explore']: {\n      range: toURLRange(timeRange.raw),\n      queries: [{ refId: 'A', datasource, query: model.state.query, step }],\n    },\n  });\n  const subUrl = config.appSubUrl ?? '';\n  const exploreUrl = urlUtil.renderUrl(`${subUrl}/explore`, { panes: exploreState, schemaVersion: 1 });\n  return exploreUrl;\n};\n\nconst onExploreClick = () => {\n  reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.open_in_explore_clicked);\n};\n\nexport const getInvestigationLink = async (addToInvestigations: AddToInvestigationButton) => {\n  const context = addToInvestigations.state.context;\n\n  // `getPluginLinkExtensions` is removed in Grafana v12\n  if (getPluginLinkExtensions !== undefined) {\n    const links = getPluginLinkExtensions({\n      extensionPointId,\n      context,\n    });\n\n    return links.extensions[0];\n  }\n\n  // `getObservablePluginLinks` is introduced in Grafana v12\n  if (getObservablePluginLinks !== undefined) {\n    const links: PluginExtensionLink[] = await firstValueFrom(\n      getObservablePluginLinks({\n        extensionPointId,\n        context,\n      })\n    );\n\n    return links[0];\n  }\n\n  return undefined;\n};\n\nasync function subscribeToAddToInvestigation(menu: PanelMenu) {\n  const addToInvestigationButton = menu.state.addToInvestigationButton;\n  if (addToInvestigationButton) {\n    const link = await getInvestigationLink(addToInvestigationButton);\n    const existingMenuItems = menu.state.body?.state.items ?? [];\n    const existingAddToInvestigationLink = existingMenuItems.find(\n      (item) => item.text === ADD_TO_INVESTIGATION_MENU_TEXT\n    );\n\n    if (link) {\n      if (!existingAddToInvestigationLink) {\n        menu.state.body?.addItem({\n          text: ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT,\n          type: 'divider',\n        });\n        menu.state.body?.addItem({\n          text: ADD_TO_INVESTIGATION_MENU_GROUP_TEXT,\n          type: 'group',\n        });\n        menu.state.body?.addItem({\n          text: ADD_TO_INVESTIGATION_MENU_TEXT,\n          iconClassName: 'plus-square',\n          onClick: (e) => {\n            if (link.onClick) {\n              link.onClick(e);\n            }\n\n            reportAppInteraction(\n              USER_EVENTS_PAGES.analyse_traces,\n              USER_EVENTS_ACTIONS.analyse_traces.add_to_investigation_clicked\n            );\n          },\n        });\n      } else {\n        if (existingAddToInvestigationLink) {\n          menu.state.body?.setItems(\n            existingMenuItems.filter(\n              (item) =>\n                [\n                  ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT,\n                  ADD_TO_INVESTIGATION_MENU_GROUP_TEXT,\n                  ADD_TO_INVESTIGATION_MENU_TEXT,\n                ].includes(item.text) === false\n            )\n          );\n        }\n      }\n    }\n  }\n}\n", "import { DataFrame } from '@grafana/data';\nimport { ComparisonSelection, MetricFunction } from './shared';\n\nexport const computeHighestDifference = (frame: DataFrame) => {\n  const baselineField = frame.fields.find((f) => f.name === 'Baseline');\n  const selectionField = frame.fields.find((f) => f.name === 'Selection');\n\n  let maxDifference = 0;\n  let maxDifferenceIndex = 0;\n\n  for (let i = 0; i < (baselineField?.values?.length || 0); i++) {\n    const diff = (selectionField?.values[i] || 0) - (baselineField?.values[i] || 0);\n    if (Math.abs(diff) > Math.abs(maxDifference || 0)) {\n      maxDifference = diff;\n      maxDifferenceIndex = i;\n    }\n  }\n\n  return { maxDifference, maxDifferenceIndex };\n};\n\nexport const getDefaultSelectionForMetric = (metric: MetricFunction): ComparisonSelection | undefined => {\n  if (metric === 'duration') {\n    return undefined;\n  }\n  return { query: 'status = error', type: 'auto' };\n};\n", "import React, { useEffect } from 'react';\nimport { CustomVariable, MultiValueVariable, MultiValueVariableState, SceneComponentProps } from '@grafana/scenes';\nimport { primarySignalOptions } from './primary-signals';\nimport { Icon, RadioButtonGroup, Select, useStyles2, Text } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { components, DropdownIndicatorProps } from 'react-select';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'utils/analytics';\nimport { GrafanaTheme2 } from '@grafana/data';\n\nconst CustomMenu = (props: any) => {\n  const styles = useStyles2(getStyles);\n  return <components.Menu {...props} className={styles.customMenu} />;\n};\n\nexport function DropdownIndicator({ selectProps }: DropdownIndicatorProps) {\n  const isOpen = selectProps.menuIsOpen;\n  const icon = isOpen ? 'angle-up' : 'angle-down';\n  const size = 'md';\n  return <Icon name={icon} size={size} />;\n}\n\nconst GroupHeading = () => {\n  const styles = useStyles2(getStyles);\n  return (\n    <div className={styles.heading}>\n      <Text weight=\"bold\" variant=\"bodySmall\" color=\"secondary\">\n        Primary signal\n      </Text>\n    </div>\n  );\n};\n\nexport class PrimarySignalVariable extends CustomVariable {\n  static Component = ({ model }: SceneComponentProps<MultiValueVariable<MultiValueVariableState>>) => {\n    const styles = useStyles2(getStyles);\n    const { value, isReadOnly } = model.useState();\n\n    // ensure the variable is set to the default value\n    useEffect(() => {\n      if (!value) {\n        model.changeValueTo(isReadOnly ? primarySignalOptions[1].value! : primarySignalOptions[0].value!);\n      }\n    });\n\n    const buttonGroupOptions = primarySignalOptions.slice(0, 2);\n    const currentSignal = primarySignalOptions.find((option) => option.value === value);\n    if (currentSignal && !buttonGroupOptions.some((option) => option.filter.key === currentSignal.filter.key)) {\n      buttonGroupOptions.push(currentSignal);\n    }\n    const selectOptions = primarySignalOptions.filter(\n      (option) => !buttonGroupOptions.some((b) => b.value === option.value)\n    );\n\n    const onChange = (v: string) => {\n      reportAppInteraction(\n        USER_EVENTS_PAGES.analyse_traces,\n        USER_EVENTS_ACTIONS.analyse_traces.primary_signal_changed,\n        {\n          primary_signal: v,\n        }\n      );\n      model.changeValueTo(v!, undefined, true);\n    };\n\n    if (isReadOnly) {\n      return <></>;\n    }\n\n    return (\n      <>\n        <RadioButtonGroup\n          options={buttonGroupOptions}\n          value={value as string}\n          onChange={onChange}\n          disabled={isReadOnly}\n          className={styles.buttonGroup}\n        />\n        <Select\n          options={[{ label: 'Primary signal', options: selectOptions }]}\n          value={''}\n          placeholder=\"\"\n          isSearchable={false}\n          isClearable={false}\n          width={4}\n          onChange={(v) => onChange(v.value!)}\n          className={styles.select}\n          components={{\n            IndicatorSeparator: () => null,\n            SingleValue: () => null,\n            Menu: CustomMenu,\n            DropdownIndicator,\n            GroupHeading,\n          }}\n        />\n      </>\n    );\n  };\n}\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  select: css`\n    [class$='input-suffix'] {\n      position: absolute;\n      z-index: 2;\n    }\n\n    :focus-within {\n      outline: none;\n      box-shadow: none;\n    }\n\n    > div {\n      padding: 0;\n    }\n\n    input {\n      opacity: 0 !important;\n    }\n\n    border-radius: 0 2px 2px 0;\n    border-left: none;\n  `,\n  buttonGroup: css`\n    border-radius: 2px 0 0 2px;\n  `,\n  customMenu: css`\n    width: 230px;\n\n    [class$='grafana-select-option-grafana-select-option-focused'] {\n      background: transparent;\n\n      ::before {\n        display: none;\n      }\n    }\n  `,\n  heading: css({\n    padding: theme.spacing(1, 1, 0.75, 0.75),\n    borderLeft: '2px solid transparent',\n    borderBottom: `1px solid ${theme.colors.border.weak}`,\n  }),\n});\n", "import { reportInteraction } from '@grafana/runtime';\nimport pluginJson from '../plugin.json';\n\n// Helper function to create a unique interaction name for analytics\nconst createInteractionName = (page: UserEventPagesType, action: string) => {\n  return `${pluginJson.id.replace(/-/g, '_')}_${page}_${action}`;\n};\n\n// Runs reportInteraction with a standardized interaction name\nexport const reportAppInteraction = (\n  page: UserEventPagesType,\n  action: UserEventActionType,\n  properties?: Record<string, unknown>\n) => {\n  reportInteraction(createInteractionName(page, action), properties);\n};\n\nexport const USER_EVENTS_PAGES = {\n  analyse_traces: 'analyse_traces',\n  home: 'home',\n  common: 'common',\n} as const;\n\nexport type UserEventPagesType = keyof typeof USER_EVENTS_PAGES;\ntype UserEventActionType =\n  | keyof (typeof USER_EVENTS_ACTIONS)['analyse_traces']\n  | keyof (typeof USER_EVENTS_ACTIONS)['home']\n  | keyof (typeof USER_EVENTS_ACTIONS)['common'];\n\nexport const USER_EVENTS_ACTIONS = {\n  [USER_EVENTS_PAGES.analyse_traces]: {\n    action_view_changed: 'action_view_changed',\n    breakdown_group_by_changed: 'breakdown_group_by_changed',\n    breakdown_add_to_filters_clicked: 'breakdown_add_to_filters_clicked',\n    comparison_add_to_filters_clicked: 'comparison_add_to_filters_clicked',\n    select_attribute_in_comparison_clicked: 'select_attribute_in_comparison_clicked',\n    layout_type_changed: 'layout_type_changed',\n    start_investigation: 'start_investigation',\n    stop_investigation: 'stop_investigation',\n    open_trace: 'open_trace',\n    open_in_explore_clicked: 'open_in_explore_clicked',\n    add_to_investigation_clicked: 'add_to_investigation_clicked',\n    add_to_investigation_trace_view_clicked: 'add_to_investigation_trace_view_clicked',\n    span_list_columns_changed: 'span_list_columns_changed',\n    toggle_bookmark_clicked: 'toggle_bookmark_clicked',\n    primary_signal_changed: 'primary_signal_changed',\n    exception_message_clicked: 'exception_message_clicked',\n  },\n  [USER_EVENTS_PAGES.home]: {\n    homepage_initialized: 'homepage_initialized',\n    panel_row_clicked: 'panel_row_clicked',\n    explore_traces_clicked: 'explore_traces_clicked',\n    read_documentation_clicked: 'read_documentation_clicked',\n    filter_changed: 'filter_changed',\n    go_to_bookmark_clicked: 'go_to_bookmark_clicked',\n  },\n  [USER_EVENTS_PAGES.common]: {\n    metric_changed: 'metric_changed',\n    new_filter_added_manually: 'new_filter_added_manually',\n    app_initialized: 'app_initialized',\n    global_docs_link_clicked: 'global_docs_link_clicked',\n    metric_docs_link_clicked: 'metric_docs_link_clicked',\n    feedback_link_clicked: 'feedback_link_clicked',\n    go_to_full_app_clicked: 'go_to_full_app_clicked',\n  },\n} as const;\n", "import { QueryRunnerState, sceneGraph, SceneQueryRunner } from '@grafana/scenes';\nimport { getStepForTimeRange } from '../../../utils/dates';\n\nexport class StepQueryRunner extends SceneQueryRunner {\n  constructor(state: QueryRunnerState) {\n    super(state);\n    this.addActivationHandler(this._onActivateStep.bind(this));\n  }\n\n  private _onActivateStep() {\n    const step = getStepForTimeRange(this, this.state.maxDataPoints);\n    this.setState({\n      queries: this.state.queries.map((query) => {\n        return {\n          ...query,\n          step,\n        };\n      }),\n    });\n\n    const sceneTimeRange = sceneGraph.getTimeRange(this);\n    sceneTimeRange.subscribeToState((newState, prevState) => {\n      if (newState.value.from !== prevState.value.from || newState.value.to !== prevState.value.to) {\n        const newStep = getStepForTimeRange(this, this.state.maxDataPoints);\n        this.setState({\n          queries: this.state.queries.map((query) => {\n            return {\n              ...query,\n              step: newStep,\n            };\n          }),\n        });\n      }\n    });\n  }\n}\n", "import { PanelBuilders } from '@grafana/scenes';\nimport { DrawStyle, StackingMode, TooltipDisplayMode } from '@grafana/ui';\nimport { MetricFunction } from 'utils/shared';\n\nexport const barsPanelConfig = (metric: MetricFunction, axisWidth?: number) => {\n  const isErrorsMetric = metric === 'errors' || false;\n  \n  const builder = PanelBuilders.timeseries()\n    .setOption('legend', { showLegend: false })\n    .setCustomFieldConfig('drawStyle', DrawStyle.Bars)\n    .setCustomFieldConfig('stacking', { mode: StackingMode.Normal })\n    .setCustomFieldConfig('fillOpacity', 75)\n    .setCustomFieldConfig('lineWidth', 0)\n    .setCustomFieldConfig('pointSize', 0)\n    .setCustomFieldConfig('axisLabel', 'Rate')\n    .setOverrides((overrides) => {\n      overrides.matchFieldsWithNameByRegex('.*').overrideColor({\n        mode: 'fixed',\n        fixedColor: isErrorsMetric ? 'semi-dark-red' : 'green',\n      });\n    })\n    .setOption('tooltip', { mode: TooltipDisplayMode.Multi });\n\n  if (axisWidth !== undefined) {\n    builder.setCustomFieldConfig('axisWidth', axisWidth);\n  }\n\n  return builder;\n};\n", "import React from 'react';\n\nimport { SceneObjectBase, SceneComponentProps, SceneObjectState } from '@grafana/scenes';\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Button, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport { getMetricValue, getTraceByServiceScene, shouldShowSelection } from 'utils/utils';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { ComparisonSelection } from '../../../utils/shared';\n\nexport interface ComparisonControlState extends SceneObjectState {\n  selection?: ComparisonSelection;\n}\n\nexport class DurationComparisonControl extends SceneObjectBase<ComparisonControlState> {\n  public constructor({ selection }: ComparisonControlState) {\n    super({ selection });\n  }\n\n  public startInvestigation = () => {\n    const byServiceScene = getTraceByServiceScene(this);\n    byServiceScene.setState({ selection: this.state.selection });\n    if (!shouldShowSelection(byServiceScene.state.actionView)) {\n      byServiceScene.setActionView('comparison');\n    }\n\n    reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.start_investigation, {\n      selection: this.state.selection,\n      metric: getMetricValue(this),\n    });\n  };\n\n  public static Component = ({ model }: SceneComponentProps<DurationComparisonControl>) => {\n    const { selection } = getTraceByServiceScene(model).useState();\n    const styles = useStyles2(getStyles);\n\n    const isDisabled = selection?.type === 'auto';\n    const tooltip = isDisabled\n      ? 'Slowest traces are selected, navigate to the Comparison or Slow Traces tab for more details.'\n      : undefined;\n\n    return (\n      <div className={styles.wrapper}>\n        <Button\n          variant=\"secondary\"\n          size=\"sm\"\n          fill=\"solid\"\n          disabled={isDisabled}\n          icon={'bolt'}\n          onClick={model.startInvestigation}\n          tooltip={tooltip}\n        >\n          {isDisabled ? 'Slowest traces selected' : 'Select slowest traces'}\n        </Button>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    wrapper: css({\n      display: 'flex',\n      gap: '16px',\n      alignItems: 'center',\n    }),\n    placeholder: css({\n      color: theme.colors.text.secondary,\n      fontSize: theme.typography.bodySmall.fontSize,\n      display: 'flex',\n      gap: theme.spacing.x0_5,\n    }),\n  };\n}\n", "import React, { ReactElement } from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { usePluginComponent } from '@grafana/runtime';\nimport { sceneGraph, SceneObject } from '@grafana/scenes';\nimport { css } from '@emotion/css';\nimport { useStyles2 } from '@grafana/ui';\nimport { getMetricVariable } from 'utils/utils';\nimport { MetricFunction } from 'utils/shared';\n\nexport type AssertionSeverity = 'warning' | 'critical' | 'info';\n\ninterface InsightsTimelineWidgetProps {\n  serviceName: string;\n  start: string | number;\n  end: string | number;\n  filterBySeverity?: AssertionSeverity[];\n  filterBySummaryKeywords?: string[];\n  label?: ReactElement;\n}\n\nexport type InsightsTimelineWidgetExternal = (props: InsightsTimelineWidgetProps) => ReactElement | null;\n\ninterface Props {\n  serviceName: string;\n  model: SceneObject;\n}\n\nexport function InsightsTimelineWidget({ serviceName, model }: Props) {\n  const { isLoading, component: InsightsTimelineWidgetExternal } = usePluginComponent<InsightsTimelineWidgetProps>(\n    'grafana-asserts-app/insights-timeline-widget/v1'\n  );\n  const styles = useStyles2(getStyles);\n  const sceneTimeRange = sceneGraph.getTimeRange(model).useState();\n\n  const metric = getMetricVariable(model).state.value as MetricFunction;\n  let filterBySeverity: AssertionSeverity[] = [];\n  if (metric === 'errors') {\n    filterBySeverity = ['critical', 'warning'];\n  } else if (metric === 'rate') {\n    filterBySeverity = ['info'];\n  }\n\n  let filterBySummaryKeywords: string[] = [];\n  if (metric === 'duration') {\n    filterBySummaryKeywords = ['latency'];\n  }\n\n  if (isLoading || !InsightsTimelineWidgetExternal || !sceneTimeRange || !serviceName) {\n    return null;\n  }\n\n  return (\n    <InsightsTimelineWidgetExternal\n      serviceName={serviceName}\n      start={sceneTimeRange.from.valueOf()}\n      end={sceneTimeRange.to.valueOf()}\n      filterBySeverity={filterBySeverity}\n      filterBySummaryKeywords={filterBySummaryKeywords}\n      label={<div className={styles.label}>Insights</div>}\n    />\n  );\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    label: css({\n      fontSize: '12px',\n      color: theme.colors.text.secondary,\n      marginLeft: '35px', // we are also passing an axisWidth of 70 to barsPanelConfig()\n      marginTop: '-3px',\n    }),\n  };\n}\n", "import React from 'react';\n\nimport {\n  SceneComponentProps,\n  SceneDataTransformer,\n  SceneFlexItem,\n  SceneFlexLayout,\n  sceneGraph,\n  SceneObject,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { arrayToDataFrame, DataFrame, GrafanaTheme2, LoadingState } from '@grafana/data';\nimport { ComparisonSelection, EMPTY_STATE_ERROR_MESSAGE, explorationDS, MetricFunction } from 'utils/shared';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { SkeletonComponent } from '../ByFrameRepeater';\nimport { barsPanelConfig } from '../panels/barsPanel';\nimport { getMetricsTempoQuery } from '../queries/generateMetricsQuery';\nimport { StepQueryRunner } from '../queries/StepQueryRunner';\nimport { css } from '@emotion/css';\nimport { RadioButtonList, useStyles2 } from '@grafana/ui';\nimport { StreamingIndicator } from '../StreamingIndicator';\nimport {\n  fieldHasEmptyValues,\n  getLatencyPartialThresholdVariable,\n  getLatencyThresholdVariable,\n  getMetricVariable,\n  getOpenTrace,\n  getTraceByServiceScene,\n  shouldShowSelection,\n} from '../../../utils/utils';\nimport { getHistogramVizPanel, yBucketToDuration } from '../panels/histogram';\nimport { TraceSceneState } from './TracesByServiceScene';\nimport { SelectionColor } from '../layouts/allComparison';\nimport { buildHistogramQuery } from '../queries/histogram';\nimport { isEqual } from 'lodash';\nimport { DurationComparisonControl } from './DurationComparisonControl';\nimport { exemplarsTransformations, removeExemplarsTransformation } from '../../../utils/exemplars';\nimport { InsightsTimelineWidget } from 'addedComponents/InsightsTimelineWidget/InsightsTimelineWidget';\nimport { useServiceName } from 'pages/Explore/TraceExploration';\n\nexport interface RateMetricsPanelState extends SceneObjectState {\n  panel?: SceneFlexLayout;\n  actions?: SceneObject[];\n  yBuckets?: number[];\n  isStreaming?: boolean;\n}\n\nexport class REDPanel extends SceneObjectBase<RateMetricsPanelState> {\n  constructor(state: RateMetricsPanelState) {\n    super({\n      yBuckets: [],\n      actions: [],\n      isStreaming: false,\n      ...state,\n    });\n\n    this.addActivationHandler(() => {\n      this._onActivate();\n      const data = sceneGraph.getData(this);\n      const parent = getTraceByServiceScene(this);\n      const timeRange = sceneGraph.getTimeRange(this);\n\n      this._subs.add(\n        data.subscribeToState((newData) => {\n          this.setState({ isStreaming: newData.data?.state === LoadingState.Streaming });\n\n          if (newData.data?.state === LoadingState.Done) {\n            if (\n              newData.data.series.length === 0 ||\n              newData.data.series[0].length === 0 ||\n              fieldHasEmptyValues(newData)\n            ) {\n              this.setState({\n                panel: new SceneFlexLayout({\n                  children: [\n                    new SceneFlexItem({\n                      body: new EmptyStateScene({\n                        message: EMPTY_STATE_ERROR_MESSAGE,\n                        imgWidth: 150,\n                      }),\n                    }),\n                  ],\n                }),\n              });\n            } else {\n              let yBuckets: number[] | undefined = [];\n              if (this.isDuration()) {\n                yBuckets = getYBuckets(data.state.data?.series || []);\n                if (parent.state.selection && newData.data?.state === LoadingState.Done) {\n                  // set selection annotation if it exists\n                  const annotations = this.buildSelectionAnnotation(parent.state);\n\n                  if (annotations && !data.state.data?.annotations?.length) {\n                    data.setState({\n                      data: {\n                        ...data.state.data!,\n                        annotations: annotations,\n                      },\n                    });\n                  }\n                }\n\n                if (yBuckets?.length) {\n                  const { minDuration, minBucket } = getMinimumsForDuration(yBuckets);\n                  const selection: ComparisonSelection = { type: 'auto' };\n\n                  getLatencyThresholdVariable(this).changeValueTo(minDuration);\n                  getLatencyPartialThresholdVariable(this).changeValueTo(\n                    yBucketToDuration(minBucket - 1, yBuckets, 0.3)\n                  );\n\n                  selection.duration = { from: minDuration, to: '' };\n                  selection.raw = {\n                    x: {\n                      from: timeRange.state.value.from.unix() * 1000,\n                      to: timeRange.state.value.to.unix() * 1000,\n                    },\n                    y: { from: minBucket - 0.5, to: yBuckets.length - 0.5 },\n                  };\n\n                  this.setState({\n                    actions: [\n                      new DurationComparisonControl({\n                        selection,\n                      }),\n                    ],\n                  });\n                  if (!parent.state.selection?.duration || parent.state.selection.type === 'auto') {\n                    parent.setState({ selection });\n                  }\n                }\n              }\n\n              // update panel\n              this.setState({\n                yBuckets,\n                panel: this.getVizPanel(),\n              });\n            }\n          } else if (newData.data?.state === LoadingState.Loading) {\n            this.setState({\n              panel: new SceneFlexLayout({\n                direction: 'column',\n                children: [\n                  new LoadingStateScene({\n                    component: () => SkeletonComponent(1),\n                  }),\n                ],\n              }),\n            });\n          }\n        })\n      );\n\n      this._subs.add(\n        parent.subscribeToState((newState, prevState) => {\n          if (data.state.data?.state === LoadingState.Done) {\n            if (!isEqual(newState.selection, prevState.selection) || newState.actionView !== prevState.actionView) {\n              if (this.isDuration()) {\n                const annotations = this.buildSelectionAnnotation(newState);\n                data.setState({\n                  data: {\n                    ...data.state.data!,\n                    annotations: annotations,\n                  },\n                });\n              }\n            }\n          }\n        })\n      );\n    });\n  }\n\n  private isDuration() {\n    return getMetricVariable(this).state.value === 'duration';\n  }\n\n  private _onActivate() {\n    const metric = getMetricVariable(this).state.value as MetricFunction;\n\n    this.setState({\n      $data: new SceneDataTransformer({\n        $data: new StepQueryRunner({\n          maxDataPoints: this.isDuration() ? 24 : 64,\n          datasource: explorationDS,\n          queries: [this.isDuration() ? buildHistogramQuery() : getMetricsTempoQuery({ metric, sample: true })],\n        }),\n        transformations: this.isDuration()\n          ? [...removeExemplarsTransformation()]\n          : [...exemplarsTransformations(getOpenTrace(this))],\n      }),\n      panel: this.getVizPanel(),\n    });\n  }\n\n  private getVizPanel() {\n    const metric = getMetricVariable(this).state.value as MetricFunction;\n    if (this.isDuration()) {\n      return getHistogramVizPanel(this, this.state.yBuckets ?? []);\n    }\n\n    return this.getRateOrErrorVizPanel(metric);\n  }\n\n  private getRateOrErrorVizPanel(type: MetricFunction) {\n    const panel = barsPanelConfig(type, 70).setHoverHeader(true).setDisplayMode('transparent');\n    if (type === 'rate') {\n      panel.setCustomFieldConfig('axisLabel', 'span/s');\n    } else if (type === 'errors') {\n      panel.setCustomFieldConfig('axisLabel', 'error/s').setColor({\n        fixedColor: 'semi-dark-red',\n        mode: 'fixed',\n      });\n    }\n    return new SceneFlexLayout({\n      direction: 'row',\n      children: [\n        new SceneFlexItem({\n          body: panel.build(),\n        }),\n      ],\n    });\n  }\n\n  private buildSelectionAnnotation(state: TraceSceneState) {\n    if (!shouldShowSelection(state.actionView)) {\n      return undefined;\n    }\n\n    const xSel = state.selection?.raw?.x;\n    const ySel = state.selection?.raw?.y;\n\n    const frame = arrayToDataFrame([\n      {\n        time: xSel?.from || 0,\n        xMin: xSel?.from || 0,\n        xMax: xSel?.to || 0,\n        timeEnd: xSel?.to || 0,\n        yMin: ySel?.from,\n        yMax: ySel?.to,\n        isRegion: true,\n        fillOpacity: 0.15,\n        lineWidth: 1,\n        lineStyle: 'solid',\n        color: SelectionColor,\n        text: 'Comparison selection',\n      },\n    ]);\n    frame.name = 'xymark';\n\n    return [frame];\n  }\n\n  public static Component = ({ model }: SceneComponentProps<REDPanel>) => {\n    const { panel, actions, isStreaming } = model.useState();\n    const { value: metric } = getMetricVariable(model).useState();\n    const styles = useStyles2(getStyles);\n    const serviceName = useServiceName(model);\n\n    if (!panel) {\n      return;\n    }\n\n    const getTitle = () => {\n      switch (metric) {\n        case 'errors':\n          return 'Errors rate';\n        case 'rate':\n          return 'Span rate';\n        case 'duration':\n          return 'Histogram by duration';\n        default:\n          return '';\n      }\n    };\n\n    const getSubtitle = () => {\n      switch (metric) {\n        case 'duration':\n          return 'Click and drag to compare selection with baseline.';\n        default:\n          return '';\n      }\n    };\n\n    const subtitle = getSubtitle();\n\n    return (\n      <div className={styles.container}>\n        <div className={styles.headerContainer}>\n          <div className={styles.titleContainer}>\n            <div className={styles.titleRadioWrapper}>\n              <RadioButtonList\n                name={`metric-${metric}`}\n                options={[{ title: '', value: 'selected' }]}\n                value={'selected'}\n              />\n              <span>{getTitle()}</span>\n            </div>\n            {subtitle && <div className={styles.subtitle}>{subtitle}</div>}\n          </div>\n          <div className={styles.actions}>\n            {isStreaming && <StreamingIndicator isStreaming={true} iconSize={10} />}\n            {actions?.map((action) => <action.Component model={action} key={action.state.key} />)}\n          </div>\n        </div>\n        <panel.Component model={panel} />\n        <InsightsTimelineWidget\n          serviceName={serviceName || ''}\n          model={model}\n        />\n      </div>\n    );\n  };\n}\n\nexport const getYBuckets = (series: DataFrame[]) => {\n  return series.map((s) => parseFloat(s.fields[1].name)).sort((a, b) => a - b);\n};\n\nexport const getMinimumsForDuration = (yBuckets: number[]) => {\n  const slowestBuckets = Math.floor(yBuckets.length / 4);\n  let minBucket = yBuckets.length - slowestBuckets - 1;\n  if (minBucket < 0) {\n    minBucket = 0;\n  }\n\n  return {\n    minDuration: yBucketToDuration(minBucket - 1, yBuckets),\n    minBucket,\n  };\n};\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      width: '100%',\n      display: 'flex',\n      flexDirection: 'column',\n      border: `1px solid ${theme.colors.border.weak}`,\n      borderRadius: '2px',\n      background: theme.colors.background.primary,\n\n      '.show-on-hover': {\n        display: 'none',\n      },\n      'section, section:hover': {\n        borderColor: 'transparent',\n      },\n      '& .u-select': {\n        border: '1px solid #ffffff75',\n      },\n    }),\n    headerContainer: css({\n      width: '100%',\n      display: 'flex',\n      flexDirection: 'row',\n      padding: '8px',\n      gap: '8px',\n      justifyContent: 'space-between',\n      alignItems: 'flex-start',\n      fontWeight: theme.typography.fontWeightBold,\n    }),\n    titleContainer: css({\n      display: 'flex',\n      flexDirection: 'column',\n      gap: '4px',\n    }),\n    titleRadioWrapper: css({\n      display: 'flex',\n      alignItems: 'center',\n    }),\n    actions: css({\n      display: 'flex',\n      gap: '8px',\n      alignItems: 'center',\n    }),\n    subtitle: css({\n      display: 'flex',\n      color: theme.colors.text.secondary,\n      fontSize: '12px',\n      fontWeight: 400,\n\n      '& svg': {\n        margin: '0 2px',\n      },\n    }),\n  };\n}\n", "import { dropWhile as _dropWhile, round as _round } from 'lodash';\nimport { sceneGraph, SceneObject } from '@grafana/scenes';\nimport { duration } from 'moment/moment';\n\nexport const ONE_MILLISECOND = 1000;\nexport const ONE_SECOND = 1000 * ONE_MILLISECOND;\nexport const ONE_MINUTE = 60 * ONE_SECOND;\nexport const ONE_HOUR = 60 * ONE_MINUTE;\nexport const ONE_DAY = 24 * ONE_HOUR;\nexport const DEFAULT_MS_PRECISION = Math.log10(ONE_MILLISECOND);\n\nconst UNIT_STEPS: Array<{ unit: string; microseconds: number; ofPrevious: number }> = [\n  { unit: 'd', microseconds: ONE_DAY, ofPrevious: 24 },\n  { unit: 'h', microseconds: ONE_HOUR, ofPrevious: 60 },\n  { unit: 'm', microseconds: ONE_MINUTE, ofPrevious: 60 },\n  { unit: 's', microseconds: ONE_SECOND, ofPrevious: 1000 },\n  { unit: 'ms', microseconds: ONE_MILLISECOND, ofPrevious: 1000 },\n  { unit: 'μs', microseconds: 1, ofPrevious: 1000 },\n];\n\n/**\n * Humanizes the duration for display.\n *\n * Example:\n * 5000ms => 5s\n * 1000μs => 1ms\n * 183840s => 2d 3h\n *\n * @param {number} duration (in microseconds)\n * @return {string} formatted duration\n */\nexport const formatDuration = (duration: number): string => {\n  // Drop all units that are too large except the last one\n  const [primaryUnit, secondaryUnit] = _dropWhile(\n    UNIT_STEPS,\n    ({ microseconds }, index) => index < UNIT_STEPS.length - 1 && microseconds > duration\n  );\n\n  if (primaryUnit.ofPrevious === 1000) {\n    // If the unit is decimal based, display as a decimal\n    return `${_round(duration / primaryUnit.microseconds, 2)}${primaryUnit.unit}`;\n  }\n\n  let primaryValue = Math.floor(duration / primaryUnit.microseconds);\n  let secondaryValue = (duration / secondaryUnit.microseconds) % primaryUnit.ofPrevious;\n  const secondaryValueRounded = Math.round(secondaryValue);\n\n  // Handle rollover case before rounding (e.g., 60s should become 1m, not 0m 60s)\n  if (secondaryValueRounded === primaryUnit.ofPrevious) {\n    primaryValue += 1;\n    secondaryValue = 0;\n  } else {\n    secondaryValue = secondaryValueRounded;\n  }\n\n  const primaryUnitString = `${primaryValue}${primaryUnit.unit}`;\n\n  if (secondaryValue === 0) {\n    return primaryUnitString;\n  }\n\n  const secondaryUnitString = `${secondaryValue}${secondaryUnit.unit}`;\n  return `${primaryUnitString} ${secondaryUnitString}`;\n}\n\n/**\n * Calculate bucket size based on time range and desired number of data points\n * @param timeRangeSeconds - The time range in seconds\n * @param dataPoints - Desired number of data points (default: 50)\n * @returns Bucket size in seconds\n */\nexport const calculateBucketSize = (timeRangeSeconds: number, dataPoints = 50): number => {\n  return Math.floor(timeRangeSeconds / dataPoints) || 1;\n};\n\nexport const getStepForTimeRange = (scene: SceneObject, dataPoints?: number) => {\n  const sceneTimeRange = sceneGraph.getTimeRange(scene);\n  const from = sceneTimeRange.state.value.from.unix();\n  const to = sceneTimeRange.state.value.to.unix();\n\n  const dur = duration(to - from, 's');\n  const bucketSizeSeconds = calculateBucketSize(dur.asSeconds(), dataPoints);\n  return `${bucketSizeSeconds}s`;\n}\n", "import { SceneObjectState, SceneObjectBase, SceneComponentProps } from '@grafana/scenes';\nimport React from 'react';\nimport { Alert } from '@grafana/ui';\nimport { testIds } from 'utils/testIds';\n\ninterface ErrorStateSceneState extends SceneObjectState {\n  message: string;\n}\n\nexport class ErrorStateScene extends SceneObjectBase<ErrorStateSceneState> {\n  public static Component = ({ model }: SceneComponentProps<ErrorStateScene>) => {\n    const { message } = model.useState();\n    return (\n      <Alert title={'Query error'} severity={'error'} data-testid={testIds.errorState}>\n        {message}\n      </Alert>\n    );\n  };\n}\n", "import { Field, Input, Icon, useStyles2 } from \"@grafana/ui\"\nimport React from \"react\"\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { css } from \"@emotion/css\";\n\ntype Props = {\n  searchQuery: string;\n  onSearchQueryChange: (event: React.ChangeEvent<HTMLInputElement>) => void;\n}\n\nexport const Search = (props: Props) => {\n  const styles = useStyles2(getStyles);\n  const { searchQuery, onSearchQueryChange } = props;\n\n  return (\n    <Field className={styles.searchField}>\n      <Input\n        placeholder='Search'\n        prefix={<Icon name={'search'} />}\n        value={searchQuery}\n        onChange={onSearchQueryChange}\n        id='searchFieldInput'\n      />\n    </Field>\n  )\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    searchField: css({\n      marginBottom: theme.spacing(1),\n    }),\n  };\n}\n", "import { DataQueryResponseData, Field } from '@grafana/data';\n\nexport function cloneDataFrame(frame: DataQueryResponseData): DataQueryResponseData {\n  return {\n    ...frame,\n    fields: frame.fields.map((field: Field) => ({\n      ...field,\n      values: field.values,\n    })),\n  };\n}\n", "import React from 'react';\n\nimport { DataFrame, FieldType, GrafanaTheme2, LoadingState, PanelData, sortDataFrame } from '@grafana/data';\nimport {\n  SceneComponentProps,\n  SceneCSSGridLayout,\n  SceneFlexItem,\n  sceneGraph,\n  SceneLayout,\n  SceneObjectBase,\n  SceneObjectState,\n} from '@grafana/scenes';\nimport { EmptyStateScene } from 'components/states/EmptyState/EmptyStateScene';\nimport { css } from '@emotion/css';\nimport { useStyles2 } from '@grafana/ui';\nimport Skeleton from 'react-loading-skeleton';\nimport { LoadingStateScene } from 'components/states/LoadingState/LoadingStateScene';\nimport { ErrorStateScene } from 'components/states/ErrorState/ErrorStateScene';\nimport { debounce } from 'lodash';\nimport { Search } from './Search';\nimport { getGroupByVariable } from 'utils/utils';\nimport {\n  EMPTY_STATE_ERROR_MESSAGE,\n  EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n  EventTimeseriesDataReceived,\n  GRID_TEMPLATE_COLUMNS,\n} from '../../utils/shared';\nimport { cloneDataFrame } from '../../utils/frames';\n\ninterface ByFrameRepeaterState extends SceneObjectState {\n  body: SceneLayout;\n  groupBy?: boolean;\n\n  getLayoutChild(data: PanelData, frame: DataFrame, frameIndex: number): SceneFlexItem;\n\n  searchQuery?: string;\n}\n\nexport class ByFrameRepeater extends SceneObjectBase<ByFrameRepeaterState> {\n  public constructor(state: ByFrameRepeaterState) {\n    super(state);\n\n    this.addActivationHandler(() => {\n      const data = sceneGraph.getData(this);\n\n      this._subs.add(\n        data.subscribeToState((data) => {\n          if (data.data?.state === LoadingState.Done || data.data?.state === LoadingState.Streaming) {\n            if (data.data.series.length === 0 && data.data?.state !== LoadingState.Streaming) {\n              this.state.body.setState({\n                children: [\n                  new SceneFlexItem({\n                    body: new EmptyStateScene({\n                      message: EMPTY_STATE_ERROR_MESSAGE,\n                      remedyMessage: EMPTY_STATE_ERROR_REMEDY_MESSAGE,\n                      padding: '32px',\n                    }),\n                  }),\n                ],\n              });\n            } else if (data.data?.state === LoadingState.Done) {\n              const filtered = {\n                ...data.data,\n                series: data.data?.series.filter(doesQueryMatchDataFrameLabels(this.state.searchQuery)),\n              };\n              this.renderFilteredData(filtered as PanelData);\n              this.publishEvent(new EventTimeseriesDataReceived({ series: data.data.series }), true);\n            }\n          } else if (data.data?.state === LoadingState.Error) {\n            this.state.body.setState({\n              children: [\n                new SceneCSSGridLayout({\n                  children: [\n                    new ErrorStateScene({\n                      message: data.data.errors?.[0]?.message ?? 'An error occurred in the query',\n                    }),\n                  ],\n                }),\n              ],\n            });\n          } else {\n            this.state.body.setState({\n              children: [\n                new SceneCSSGridLayout({\n                  children: [\n                    new LoadingStateScene({\n                      component: () => SkeletonComponent(8),\n                    }),\n                  ],\n                }),\n              ],\n            });\n          }\n        })\n      );\n\n      this.subscribeToState((newState, prevState) => {\n        if (newState.searchQuery !== prevState.searchQuery) {\n          this.onSearchQueryChangeDebounced(newState.searchQuery ?? '');\n        }\n      });\n\n      if (data.state.data) {\n        this.performRepeat(data.state.data);\n      }\n    });\n  }\n\n  private onSearchQueryChange = (evt: React.SyntheticEvent<HTMLInputElement>) => {\n    this.setState({ searchQuery: evt.currentTarget.value });\n  };\n\n  private onSearchQueryChangeDebounced = debounce((searchQuery: string) => {\n    const data = sceneGraph.getData(this);\n    const filtered = {\n      ...data.state.data,\n      series: data.state.data?.series.filter(doesQueryMatchDataFrameLabels(searchQuery)),\n    };\n    this.renderFilteredData(filtered as PanelData);\n  }, 250);\n\n  private renderFilteredData(filtered: PanelData) {\n    if (filtered.series && filtered.series.length > 0) {\n      this.performRepeat(filtered as PanelData);\n    } else {\n      this.state.body.setState({\n        children: [\n          new SceneFlexItem({\n            body: new EmptyStateScene({\n              message: 'No data for search term',\n              padding: '32px',\n            }),\n          }),\n        ],\n      });\n    }\n  }\n\n  private groupSeriesBy(data: PanelData, groupBy: string) {\n    const groupedData = data.series.reduce(\n      (acc, series) => {\n        const key = series.fields.find((f) => f.type === FieldType.number)?.labels?.[groupBy];\n        if (!key) {\n          return acc;\n        }\n        if (!acc[key]) {\n          acc[key] = [];\n        }\n        acc[key].push(series);\n        return acc;\n      },\n      {} as Record<string, DataFrame[]>\n    );\n\n    const newSeries = [];\n    for (const key in groupedData) {\n      const frames = groupedData[key].sort((a, b) => a.name?.localeCompare(b.name!) || 0);\n      const mainFrame = cloneDataFrame(frames[0]);\n      frames.slice(1, frames.length).forEach((frame) => mainFrame.fields.push(frame.fields[1]));\n      newSeries.push(sortDataFrame(mainFrame, 0));\n    }\n    return newSeries;\n  }\n\n  private performRepeat(data: PanelData) {\n    const newChildren: SceneFlexItem[] = [];\n    let frames = data.series;\n\n    if (this.state.groupBy) {\n      frames = this.groupSeriesBy(data, getGroupByVariable(this).getValueText());\n    }\n\n    for (let frameIndex = 0; frameIndex < frames.length; frameIndex++) {\n      const currentFrame = frames[frameIndex];\n      // Skip frames with no data\n      const sum = currentFrame.fields\n        .filter((f) => f.type === FieldType.number)\n        .reduce((sum, f) => sum + f.values.reduce((vSum, v) => vSum + (v || 0), 0) || 0, 0);\n      if (sum === 0) {\n        continue;\n      }\n      // Build the layout child\n      const layoutChild = this.state.getLayoutChild(data, frames[frameIndex], frameIndex);\n      newChildren.push(layoutChild);\n    }\n\n    this.state.body.setState({ children: newChildren });\n  }\n\n  public static Component = ({ model }: SceneComponentProps<ByFrameRepeater>) => {\n    const { body, searchQuery } = model.useState();\n    const styles = useStyles2(getStyles);\n\n    return (\n      <div className={styles.container}>\n        <Search searchQuery={searchQuery ?? ''} onSearchQueryChange={model.onSearchQueryChange} />\n        <body.Component model={body} />\n      </div>\n    );\n  };\n}\n\nfunction getStyles() {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      flexGrow: 1,\n    }),\n  };\n}\n\nexport const SkeletonComponent = (repeat: number) => {\n  const styles = useStyles2(getSkeletonStyles);\n\n  return (\n    <div className={styles.container}>\n      {[...Array(repeat)].map((_, i) => (\n        <div className={styles.itemContainer} key={i}>\n          <div className={styles.header}>\n            <div className={styles.title}>\n              <Skeleton count={1} />\n            </div>\n            <div className={styles.action}>\n              <Skeleton count={1} />\n            </div>\n          </div>\n          <div className={styles.yAxis}>\n            {[...Array(2)].map((_, i) => (\n              <div className={styles.yAxisItem} key={i}>\n                <Skeleton count={1} />\n              </div>\n            ))}\n          </div>\n          <div className={styles.xAxis}>\n            {[...Array(2)].map((_, i) => (\n              <div className={styles.xAxisItem} key={i}>\n                <Skeleton count={1} />\n              </div>\n            ))}\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\nfunction getSkeletonStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      display: 'grid',\n      gridTemplateColumns: GRID_TEMPLATE_COLUMNS,\n      gridAutoRows: '200px',\n      rowGap: theme.spacing(1),\n      columnGap: theme.spacing(1),\n    }),\n    itemContainer: css({\n      backgroundColor: theme.colors.background.primary,\n      border: `1px solid ${theme.colors.background.secondary}`,\n      padding: '5px',\n    }),\n    header: css({\n      display: 'flex',\n      justifyContent: 'space-between',\n    }),\n    title: css({\n      width: '100px',\n    }),\n    action: css({\n      width: '60px',\n    }),\n    yAxis: css({\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'space-around',\n      marginTop: '35px',\n    }),\n    yAxisItem: css({\n      width: '60px',\n      height: '55px',\n    }),\n    xAxis: css({\n      display: 'flex',\n      justifyContent: 'space-evenly',\n    }),\n    xAxisItem: css({\n      width: '55px',\n    }),\n  };\n}\n\nexport const doesQueryMatchDataFrameLabels = (searchQuery?: string) => (dataFrame: DataFrame) => {\n  const pattern = searchQuery?.trim();\n  if (!pattern) {\n    return true;\n  }\n\n  const regex = new RegExp(pattern, 'i');\n\n  return dataFrame.fields.some((f) => (!f.labels ? false : Object.values(f.labels).find((label) => regex.test(label))));\n};\n", "import { getTraceByServiceScene, shouldShowSelection } from '../../../utils/utils';\nimport { ComparisonSelection } from '../../../utils/shared';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../../utils/analytics';\nimport { PanelBuilders, SceneFlexItem, SceneFlexLayout, SceneObject } from '@grafana/scenes';\n\nexport function getHistogramVizPanel(scene: SceneObject, yBuckets: number[]) {\n  const parent = getTraceByServiceScene(scene);\n  const panel = histogramPanelConfig()\n    .setHoverHeader(true)\n    // @ts-ignore\n    .setOption('selectionMode', 'xy')\n    .build();\n  panel.setState({\n    extendPanelContext: (vizPanel, context) => {\n      // TODO remove when we the Grafana version with #88107 is released\n      // @ts-ignore\n      context.onSelectRange = (args) => {\n        if (args.length === 0) {\n          parent.setState({ selection: undefined });\n          return;\n        }\n        const rawSelection = args[0];\n        // @ts-ignore\n        const newSelection: ComparisonSelection = { type: 'manual', raw: rawSelection };\n\n        newSelection.timeRange = {\n          from: Math.round((rawSelection.x?.from || 0) / 1000),\n          to: Math.round((rawSelection.x?.to || 0) / 1000),\n        };\n\n        // Ignore selection and return if the selection is invalid\n        if (newSelection.timeRange.from === newSelection.timeRange.to) {\n          return;\n        }\n\n        const yFrom = yBucketToDuration((args[0].y?.from || 0) - 1, yBuckets);\n        const yTo = yBucketToDuration(args[0].y?.to || 0, yBuckets);\n        newSelection.duration = { from: yFrom, to: yTo };\n\n        parent.onUserUpdateSelection(newSelection);\n        if (!shouldShowSelection(parent.state.actionView)) {\n          parent.setActionView('comparison');\n        }\n\n        reportAppInteraction(USER_EVENTS_PAGES.analyse_traces, USER_EVENTS_ACTIONS.analyse_traces.start_investigation, {\n          selection: newSelection,\n          metric: 'duration',\n        });\n      };\n    },\n  });\n  return new SceneFlexLayout({\n    direction: 'row',\n    children: [\n      new SceneFlexItem({\n        body: panel,\n      }),\n    ],\n  });\n}\n\nexport const histogramPanelConfig = () => {\n  return PanelBuilders.heatmap()\n    .setOption('legend', { show: false })\n    .setOption('yAxis', {\n      unit: 's',\n      axisLabel: 'duration',\n    })\n    .setOption('color', {\n      scheme: 'Blues',\n      steps: 16,\n    })\n    .setOption('rowsFrame', { value: 'Spans' });\n};\n\nexport function yBucketToDuration(yValue: number, buckets?: number[], multiplier?: number) {\n  if (!buckets) {\n    return '';\n  }\n  if (yValue < 0) {\n    return '0';\n  }\n\n  const rawValue = buckets[Math.floor(yValue)] * (multiplier || 1);\n  if (!rawValue || isNaN(rawValue)) {\n    return '';\n  }\n  if (rawValue >= 1) {\n    return `${rawValue.toFixed(0)}s`;\n  }\n  return `${(rawValue * 1000).toFixed(0)}ms`;\n}\n", "export const testIds = {\n  emptyState: 'data-testid empty-state',\n  errorState: 'data-testid error-state',\n  loadingState: 'data-testid loading-state',\n};\n", "import { ALL, MetricFunction, VAR_FILTERS_EXPR } from '../../../utils/shared';\n\ninterface QueryOptions {\n  metric: MetricFunction;\n  extraFilters?: string;\n  groupByKey?: string;\n  sample?: boolean;\n}\n\nexport function generateMetricsQuery({ metric, groupByKey, extraFilters, sample = false }: QueryOptions) {\n  // Generate span set filters\n  let filters = `${VAR_FILTERS_EXPR}`;\n\n  if (metric === 'errors') {\n    filters += ' && status=error';\n  }\n\n  if (extraFilters) {\n    filters += ` && ${extraFilters}`;\n  }\n\n  if (groupByKey && groupByKey !== ALL) {\n    filters += ` && ${groupByKey} != nil`;\n  }\n\n  // Generate metrics function\n  let metricFn = 'rate()';\n  switch (metric) {\n    case 'errors':\n      metricFn = 'rate()';\n      break;\n    case 'duration':\n      metricFn = 'quantile_over_time(duration, 0.9)';\n      break;\n  }\n\n  // Generate group by section\n  let groupByAttrs = [];\n  if (groupByKey && groupByKey !== ALL) {\n    groupByAttrs.push(groupByKey);\n  }\n\n  const groupBy = groupByAttrs.length ? `by(${groupByAttrs.join(', ')})` : '';\n\n  const sampleStr = sample ? ' with(sample=true)' : '';\n\n  return `{${filters}} | ${metricFn} ${groupBy}${sampleStr}`;\n}\n\nexport function getMetricsTempoQuery(options: QueryOptions) {\n  return {\n    refId: 'A',\n    query: generateMetricsQuery(options),\n    queryType: 'traceql',\n    tableType: 'spans',\n    limit: 100,\n    spss: 10,\n    filters: [],\n  };\n}\n", "import { SceneComponentProps, SceneObjectBase, SceneObjectState, VizPanel } from '@grafana/scenes';\nimport { DataFrame, GrafanaTheme2 } from '@grafana/data';\nimport { But<PERSON>, Stack, useStyles2 } from '@grafana/ui';\nimport { css } from '@emotion/css';\nimport React from 'react';\nimport { getFiltersVariable } from '../../../utils/utils';\nimport { addToFilters, filterExistsForKey } from '../actions/AddToFiltersAction';\nimport { computeHighestDifference } from '../../../utils/comparison';\n\nexport interface HighestDifferencePanelState extends SceneObjectState {\n  frame: DataFrame;\n  panel: VizPanel;\n  maxDifference?: number;\n  maxDifferenceIndex?: number;\n}\n\nexport class HighestDifferencePanel extends SceneObjectBase<HighestDifferencePanelState> {\n  constructor(state: HighestDifferencePanelState) {\n    super({\n      ...state,\n    });\n\n    this.addActivationHandler(() => this._onActivate());\n  }\n\n  private _onActivate() {\n    const { frame } = this.state;\n    this.setState({ ...computeHighestDifference(frame) });\n\n    this._subs.add(\n      this.subscribeToState((newState, prevState) => {\n        if (newState.frame !== prevState.frame) {\n          const { frame } = newState;\n          this.setState({ ...computeHighestDifference(frame) });\n        }\n      })\n    );\n  }\n\n  private getAttribute() {\n    return this.state.frame.name;\n  }\n\n  private getValue() {\n    const valueField = this.state.frame.fields.find((f) => f.name === 'Value');\n    return valueField?.values[this.state.maxDifferenceIndex || 0];\n  }\n\n  private onAddToFilters() {\n    const variable = getFiltersVariable(this);\n    const attribute = this.getAttribute();\n    if (attribute) {\n      addToFilters(variable, attribute, this.getValue());\n    }\n  }\n\n  public static Component = ({ model }: SceneComponentProps<HighestDifferencePanel>) => {\n    const { maxDifference, maxDifferenceIndex, panel } = model.useState();\n    const styles = useStyles2(getStyles);\n    const value = model.getValue();\n    const key = model.state.frame.name ?? '';\n    const filterExists = filterExistsForKey(getFiltersVariable(model), key, value.replace(/\"/g, ''));\n\n    return (\n      <div className={styles.container}>\n        {<panel.Component model={panel} />}\n        <div className={styles.differenceContainer}>\n          {maxDifference !== undefined && maxDifferenceIndex !== undefined && (\n            <>\n              <Stack gap={1} justifyContent={'space-between'} alignItems={'center'}>\n                <div className={styles.title}>Highest difference</div>\n                {!filterExists && (\n                  <Button\n                    size=\"sm\"\n                    variant=\"primary\"\n                    icon={'search-plus'}\n                    fill=\"text\"\n                    onClick={() => model.onAddToFilters()}\n                  >\n                    Add to filters\n                  </Button>\n                )}\n              </Stack>\n              <div className={styles.differenceValue}>\n                {(Math.abs(maxDifference) * 100).toFixed(maxDifference === 0 ? 0 : 2)}%\n              </div>\n              <div className={styles.value}>{value}</div>\n            </>\n          )}\n        </div>\n      </div>\n    );\n  };\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    container: css({\n      display: 'flex',\n      flexDirection: 'column',\n      flexGrow: 1,\n      height: '100%',\n    }),\n    differenceContainer: css({\n      display: 'flex',\n      flexDirection: 'column',\n      flexGrow: 1,\n      border: `1px solid ${theme.colors.secondary.border}`,\n      background: theme.colors.background.primary,\n      padding: '8px',\n      marginBottom: theme.spacing(2),\n      fontSize: '12px',\n      height: '116px',\n    }),\n    differenceValue: css({\n      fontSize: '36px',\n      fontWeight: 'bold',\n      textAlign: 'center',\n    }),\n    value: css({\n      textAlign: 'center',\n      color: theme.colors.secondary.text,\n      textWrap: 'nowrap',\n      whiteSpace: 'nowrap',\n      overflow: 'hidden',\n      textOverflow: 'ellipsis',\n    }),\n    title: css({\n      fontWeight: 500,\n    }),\n  };\n}\n", "import { PanelBuilders, SceneCSSGridItem, SceneCSSGridLayout, SceneDataNode, VizPanelState } from '@grafana/scenes';\nimport { ByFrameRepeater } from '../ByFrameRepeater';\nimport { DataFrame, PanelData } from '@grafana/data';\nimport { AxisPlacement } from '@grafana/ui';\nimport { TooltipDisplayMode } from '@grafana/schema';\nimport { HighestDifferencePanel } from './HighestDifferencePanel';\nimport { GRID_TEMPLATE_COLUMNS, MetricFunction } from '../../../utils/shared';\n\nexport const BaselineColor = '#5794F299';\nexport const SelectionColor = '#FF9930';\n\nexport function buildAllComparisonLayout(\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  const panels: Record<string, SceneCSSGridItem> = {};\n\n  return new ByFrameRepeater({\n    body: new SceneCSSGridLayout({\n      templateColumns: GRID_TEMPLATE_COLUMNS,\n      autoRows: '320px',\n      children: [],\n    }),\n    getLayoutChild: getLayoutChild(panels, getFrameName, actionsFn, metric),\n  });\n}\n\nconst getFrameName = (df: DataFrame) => {\n  return df.name || 'No name available';\n};\n\nfunction getLayoutChild(\n  panels: Record<string, SceneCSSGridItem>,\n  getTitle: (df: DataFrame) => string,\n  actionsFn: (df: DataFrame) => VizPanelState['headerActions'],\n  metric: MetricFunction\n) {\n  return (data: PanelData, frame: DataFrame) => {\n    const existingGridItem = frame.name ? panels[frame.name] : undefined;\n\n    const dataNode = new SceneDataNode({\n      data: {\n        ...data,\n        series: [\n          {\n            ...frame,\n          },\n        ],\n      },\n    });\n\n    if (existingGridItem) {\n      const body = existingGridItem.state.body as HighestDifferencePanel;\n      body.setState({ frame });\n      body.state.panel.setState({ $data: dataNode });\n      return existingGridItem;\n    }\n\n    const panel = getPanelConfig(metric).setTitle(getTitle(frame)).setData(dataNode);\n\n    const actions = actionsFn(frame);\n    if (actions) {\n      panel.setHeaderActions(actions);\n    }\n\n    const gridItem = new SceneCSSGridItem({\n      body: new HighestDifferencePanel({ frame, panel: panel.build() }),\n    });\n    if (frame.name) {\n      panels[frame.name] = gridItem;\n    }\n\n    return gridItem;\n  };\n}\n\nexport function getPanelConfig(metric: MetricFunction) {\n  return PanelBuilders.barchart()\n    .setOption('legend', { showLegend: false })\n    .setOption('tooltip', { mode: TooltipDisplayMode.Multi })\n    .setMax(1)\n    .setOverrides((overrides) => {\n      overrides.matchFieldsWithName('Value').overrideCustomFieldConfig('axisPlacement', AxisPlacement.Hidden);\n      overrides\n        .matchFieldsWithName('Baseline')\n        .overrideColor({\n          mode: 'fixed',\n          fixedColor: metric === 'duration' ? BaselineColor : 'semi-dark-green',\n        })\n        .overrideUnit('percentunit');\n      overrides\n        .matchFieldsWithName('Selection')\n        .overrideColor({\n          mode: 'fixed',\n          fixedColor: metric === 'duration' ? SelectionColor : 'semi-dark-red',\n        })\n        .overrideUnit('percentunit');\n    });\n}\n"], "names": ["ShareExplorationAction", "exploration", "origin", "useLocation", "tooltip", "setTooltip", "useState", "<PERSON><PERSON>barButton", "variant", "icon", "onClick", "navigator", "clipboard", "writeText", "getUrlForExploration", "setTimeout", "RECOMMENDED_ATTRIBUTES", "labelOrder", "SpanListColumnsSelector", "options", "value", "onChange", "styles", "useStyles2", "getStyles", "opt", "useMemo", "Object", "values", "reduce", "acc", "curr", "label", "slice", "indexOf", "includes", "group", "push", "startsWith", "sort", "a", "b", "div", "className", "container", "Field", "Select", "toString", "split", "placeholder", "x", "map", "join", "is<PERSON><PERSON><PERSON>", "isClearable", "virtualized", "prefix", "Icon", "name", "css", "display", "min<PERSON><PERSON><PERSON>", "width", "SpanListScene", "SceneObjectBase", "setupTransformations", "source", "pipe", "data", "df", "nameField", "fields", "find", "f", "type", "TableCellDisplayMode", "Custom", "cellComponent", "props", "frame", "traceIdField", "spanIdField", "traceId", "rowIndex", "spanId", "title", "this", "publishEvent", "EventTraceOpened", "Link", "href", "getLinkToExplore", "target", "size", "config", "custom", "cellOptions", "updatePanel", "state", "LoadingState", "Loading", "NotStarted", "Streaming", "series", "length", "Done", "dataState", "panel", "setState", "SceneFlexLayout", "children", "SceneFlexItem", "body", "EmptyStateScene", "message", "EMPTY_STATE_ERROR_MESSAGE", "remedyMessage", "EMPTY_STATE_ERROR_REMEDY_MESSAGE", "padding", "direction", "PanelBuilders", "table", "setHoverHeader", "setOverrides", "builder", "matchFieldsWithName", "overrideCustomFieldConfig", "build", "LoadingStateScene", "component", "SkeletonComponent", "constructor", "super", "traceExplorationScene", "getTraceExplorationScene", "datasource", "getDataSource", "timeRange", "sceneGraph", "getTimeRange", "exploreState", "JSON", "stringify", "range", "toURLRange", "raw", "queries", "refId", "queryType", "query", "panelsState", "trace", "subUrl", "appSubUrl", "url<PERSON><PERSON>", "renderUrl", "panes", "schemaVersion", "columns", "variable", "getSpanListColumnsVariable", "getValue", "changeValueTo", "reportAppInteraction", "USER_EVENTS_PAGES", "analyse_traces", "USER_EVENTS_ACTIONS", "span_list_columns_changed", "addActivationHandler", "$data", "SceneDataTransformer", "transformations", "sceneData", "getData", "_subs", "add", "subscribeToState", "Component", "model", "useTheme2", "attributes", "getTraceByServiceScene", "header", "description", "toOption", "theme", "gap", "justifyContent", "alignItems", "fontSize", "background", "colors", "secondary", "color", "text", "link", "cursor", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "textDecoration", "typography", "h6", "spacing", "getSkeletonStyles", "Skeleton", "count", "Array", "_", "i", "row", "key", "j", "span", "rowItem", "height", "position", "backgroundColor", "primary", "border", "weak", "marginBottom", "SpansScene", "_onActivate", "updateBody", "newState", "prevState", "getMetricVariable", "bind", "nestedSetLeft", "parseInt", "intValue", "Value", "int_value", "Error", "nestedSetRight", "TreeNode", "addSpan", "left", "Math", "min", "right", "max", "spans", "<PERSON><PERSON><PERSON><PERSON>", "node", "parent", "<PERSON><PERSON><PERSON><PERSON>", "findMatchingChild", "nodeName", "child", "serviceName", "operationName", "traceID", "createNode", "s", "serviceNameAttr", "stringValue", "string_value", "svcName", "resetLeftRight", "t", "Number", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "c", "ROOT_SPAN_ID", "StructureTabScene", "tree", "traces", "spanSets", "traceStartTime", "startTimeUnixNano", "ss", "s1", "s2", "curNode", "newNode", "mergeTraces", "parse", "countSpans", "loading", "wrap", "getPanels", "minHeight", "getPanel", "from", "to", "openTrace", "getOpenTrace", "setTitle", "setOption", "setData", "SceneDataNode", "buildData", "getTrace", "traceName", "createDataFrame", "FieldType", "other", "references", "string", "spanID", "parentSpanId", "number", "duration", "startTime", "statusCode", "erroredSpans", "refType", "durationNanos", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "explorationDS", "buildQuery", "metric", "filterStreamingProgressTransformations", "metricQuery", "<PERSON><PERSON><PERSON><PERSON>", "VAR_LATENCY_PARTIAL_THRESHOLD_EXPR", "VAR_LATENCY_THRESHOLD_EXPR", "VAR_FILTERS_EXPR", "tableType", "limit", "spss", "filters", "isLoading", "emptyMsg", "tabName", "structureDisplayName", "noDataMessage", "Text", "textAlignment", "longText", "<PERSON><PERSON>", "actionContainer", "action", "LinkButton", "fill", "toLowerCase", "baseColor", "highlightColor", "traceViewList", "EmptyState", "flexDirection", "x1", "margin", "marginLeft", "GroupBySelector", "radioAttributes", "showAll", "radioOptions", "otherAttrOptions", "select<PERSON><PERSON><PERSON>", "setSelectQuery", "allowAutoUpdate", "setAllowAutoUpdate", "availableWidth", "setAvailableWidth", "controlsContainer", "useRef", "initialGroupBy", "getFiltersVariable", "metricValue", "useResizeObserver", "ref", "onResize", "element", "current", "clientWidth", "radioOptionsWidth", "filter", "op", "checks", "o", "operator", "attribute", "replace", "SPAN_ATTR", "RESOURCE_ATTR", "option", "textWidth", "measureText", "ops", "ro", "filteredOptions", "getModifiedSelectOptions", "ignoredAttributes", "defaultValue", "useEffect", "some", "showAllOption", "ALL", "defaultOnChangeValue", "RadioButtonGroup", "selected", "newSelected", "select", "onInputChange", "onCloseMenu", "maxOptions", "queryLowerCase", "tag", "LayoutSwitcher", "Selector", "active", "onLayoutChange", "layout_type_changed", "layout", "layouts", "index", "findIndex", "linesPanelConfig", "timeseries", "showLegend", "mode", "TooltipDisplayMode", "Multi", "setCustomFieldConfig", "syncYAxis", "vizPanel", "maxima", "Map", "eventSub", "subscribeToEvent", "EventTimeseriesDataReceived", "event", "payload", "for<PERSON>ach", "set", "v", "findAllObjects", "VizPanel", "clearFieldConfigCache", "fieldConfig", "merge", "cloneDeep", "defaults", "updateTimeseriesAxis", "unsubscribe", "buildNormalLayout", "scene", "actionsFn", "getMetricsTempoQuery", "groupByKey", "getValueText", "panels", "$behaviors", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "maxDataPoints", "exemplarsTransformations", "reduceField", "field", "reducers", "ReducerID", "calcs", "setUnit", "By<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SceneCSSGridLayout", "templateColumns", "GRID_TEMPLATE_COLUMNS", "autoRows", "isLazy", "groupBy", "getLayoutChild", "getLabelValue", "getTitle", "existingGridItem", "undefined", "dataNode", "annotations", "labels", "status", "localeCompare", "interpolate", "generateMetricsQuery", "extraFilters", "formatLabelValue", "barsPanelConfig", "setMenu", "PanelMenu", "labelValue", "actions", "setHeaderActions", "gridItem", "SceneCSSGridItem", "AttributesDescription", "tags", "infoFlex", "tagsFlex", "borderRadius", "style", "AttributesBreakdownScene", "getGroupByVariable", "setBody", "onReferencedVariableValueChanged", "radioAttributesResource", "onAddToFiltersClick", "breakdown_add_to_filters_clicked", "_variableDependency", "VariableDependencyConfig", "variableNames", "VAR_FILTERS", "VAR_METRIC", "AddToFiltersAction", "labelKey", "ignore", "breakdown_group_by_changed", "flexGrow", "content", "paddingTop", "controls", "controlsRight", "scope", "marginRight", "controlsLeft", "justifyItems", "groupByValue", "defaultScope", "radioAttributesSpan", "SPAN", "RESOURCE", "setScope", "filterType", "filteredAttributes", "attr", "concat", "getDescription", "getAttributesAsOptions", "BreakdownScene", "createTimeSeries", "timestamps", "timeRangeSeconds", "bucketSizeMs", "calculateBucketSize", "buckets", "timestamp", "<PERSON><PERSON><PERSON>", "floor", "get", "entries", "time", "normalizeExceptionMessage", "trim", "ExceptionsScene", "exceptionsCount", "calculateExceptionsCount", "TableCellHeight", "Lg", "createTransformation", "messageField", "typeField", "serviceField", "timeField", "messages", "types", "occurrences", "lastSeenTimes", "services", "timeSeries", "aggregated", "messageTimestamps", "service", "normalizedMessage", "has", "timestampMs", "parseFloat", "timeSeriesData", "sortedEntries", "lastSeenMs", "diffMs", "Date", "now", "aggregateExceptions", "seriesData", "renderSparklineCell", "links", "createDataLink", "url", "exception_message_clicked", "navigateToTracesWithFilter", "escapeFilterV<PERSON>ue", "occurrencesField", "total", "getExceptionsCount", "SparklineCell", "sparklineMessage", "count<PERSON><PERSON><PERSON>", "point", "timeValues", "validCount<PERSON><PERSON><PERSON>", "isFinite", "isNaN", "validTimeValues", "minCount", "maxCount", "minTime", "maxTime", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "safeCountDelta", "safeTimeDelta", "sparklineData", "y", "delta", "sparklineContainer", "Sparkline", "sparkline", "drawStyle", "GraphDrawStyle", "Line", "fillOpacity", "fillColor", "lineWidth", "showPoints", "VisibilityMode", "Never", "exceptionMessage", "filtersVariable", "traceByServiceScene", "setActionView", "currentFilters", "escapedMessage", "existingFilterIndex", "newFilter", "newFilters", "dataTransformer", "loadingContainer", "bodySmall", "comparisonQuery", "selection", "selector", "fromTimerange", "toTimerange", "buildAttributeComparison", "attributeSeries", "d", "splitFrames", "baselineField", "selection<PERSON><PERSON>", "displayName", "get<PERSON><PERSON><PERSON>", "getPanelConfig", "InspectAttributeAction", "<PERSON><PERSON>", "AttributesComparisonScene", "updateData", "getPrimarySignalVariable", "isEqual", "byServiceScene", "sceneTimeRange", "unix", "primarySignal", "getFilteredAttributes", "grouped<PERSON>rames", "groupFrameListByAttribute", "frames", "frameGroupToDataframe", "aCompare", "computeHighestDifference", "b<PERSON>om<PERSON>e", "abs", "maxDifference", "comparison_add_to_filters_clicked", "VAR_PRIMARY_SIGNAL", "traceExploration", "hasAllValue", "buildAllComparisonLayout", "getMetricFunction", "select_attribute_in_comparison_clicked", "compare<PERSON><PERSON>y", "durString", "asSeconds", "step", "BaselineColor", "getTheme", "visualization", "getColorByName", "SelectionColor", "numberField", "nonInternalKey", "keys", "newFrame", "valueNameField", "val", "baselineTotal", "getTotalForMetaType", "selectionTotal", "metaType", "calculatedTotal", "currentValue", "ComparisonScene", "tracesByService", "getDefaultSelectionForMetric", "actionViewsDefinitions", "getScene", "TabsBarScene", "breakpoints", "up", "md", "top", "zIndex", "setExceptionsCount", "metricScene", "actionView", "allowedActionViews", "tracesCount", "enabledViews", "view", "primarySignalVariable", "timeRangeValue", "exceptionsScene", "getExceptionsScene", "subscription", "hasSetView", "embedded", "useMount", "Box", "TabsBar", "tab", "Tab", "onChangeTab", "counter", "MiniREDPanel", "buildHistogramQuery", "sample", "removeExemplarsTransformation", "getVizPanel", "getDurationVizPanel", "getRateOrErrorPanel", "setDisplayMode", "setColor", "fixedColor", "histogramPanelConfig", "isStreaming", "fieldHasEmptyValues", "imgWidth", "maxHeight", "MINI_PANEL_HEIGHT", "flex", "borderColor", "headerWrapper", "clickable", "input", "radioButton", "indicatorWrapper", "selectMetric", "common", "metric_changed", "location", "onChangeMetricFunction", "RadioButtonList", "StreamingIndicator", "iconSize", "TracesByServiceScene", "urlActionView", "URLSearchParams", "window", "search", "metricVariable", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "updateExceptionsScene", "selectionFrom", "getDatasourceVariable", "updateAttributes", "actionViewDef", "buildGraphScene", "activate", "ds", "getDataSourceSrv", "VAR_DATASOURCE_EXPR", "__sceneObject", "getTagKeys", "then", "tagKeys", "l", "getUrlState", "updateFromUrl", "newSelection", "onUserUpdateSelection", "_urlSync", "performBrowserHistoryAction", "action_view_changed", "oldAction", "newAction", "$timeRange", "timeRangeFromSelection", "spanListTransformations", "SceneObjectUrlSyncConfig", "<PERSON><PERSON><PERSON>", "MetricTypeTooltip", "placement", "interactive", "hand", "subtitle", "emphasize", "button", "metric_docs_link_clicked", "x0_5", "paddingBottom", "fontWeight", "MAIN_PANEL_HEIGHT", "typeQuery", "SceneTimeRange", "toFixed", "dateTime", "secondaryPanel", "tertiaryPanel", "behaviors", "sync", "DashboardCursorSync", "<PERSON><PERSON><PERSON>", "ySizing", "REDPanel", "id", "desc", "indexByName", "Duration", "labelName", "addToFilters", "filterExistsForKey", "filtersWithoutNew", "DATABASE_CALLS_KEY", "history", "pushState", "AttributePanelRow", "labelTitle", "valueTitle", "<PERSON><PERSON><PERSON><PERSON>", "home", "panel_row_clicked", "locationService", "actionText", "actionIcon", "isDark", "ErroredServicesRows", "valuesField", "getUrl", "params", "EXPLORATIONS_ROUTE", "getTotalErrs", "SlowestTracesRows", "<PERSON>r<PERSON><PERSON>", "sortedByDuration", "sortedFields", "traceServiceField", "traceNameField", "console", "error", "ROUTES", "Explore", "getDuration", "durationField", "formatDuration", "SlowestServicesRows", "AttributePanelRows", "AttributePanelScene", "getIcon", "medium", "borderTopLeftRadius", "borderTopRightRadius", "titleText", "AttributePanel", "exemplars", "renderDurationPanel", "yBuckets", "getYBuckets", "minDuration", "getMinimumsForDuration", "getNoDataMessage", "getErrorMessage", "tracesContainer", "rowLeft", "rowRight", "textAlign", "LightModeRocket", "svg", "xmlns", "viewBox", "path", "DarkModeRocket", "cleanupParams", "delete", "SELECTION", "VAR_LATENCY_THRESHOLD", "VAR_LATENCY_PARTIAL_THRESHOLD", "useBookmarksStorage", "storage", "usePluginUserStorage", "getBookmarks", "removeBookmark", "bookmark", "bookmarkExists", "toggleBookmark", "getBookmarkForUrl", "urlQueryMap", "fromEntries", "getAll", "setBookmarks", "bookmarks", "setItem", "BOOKMARKS_LS_KEY", "e", "getItem", "addBookmark", "filteredBookmarks", "storedBookmark", "areBookmarksEqual", "bookmarkParams", "storedBookmarkParams", "<PERSON><PERSON><PERSON>", "bookmarkKeys", "k", "storedKeys", "allKeysMatch", "every", "bookmarkFilters", "storedFilters", "BookmarkItem", "ACTION_VIEW", "PRIMARY_SIGNAL", "FILTER_SEPARATOR", "getBookmarkParams", "primarySignalFilter", "signalData", "getSignalForKey", "getPrimarySignalFilter", "filtersArray", "getFiltersWithoutPrimarySignal", "EVENT_ATTR", "capitalizeFirstChar", "WebkitLineClamp", "WebkitBoxOrient", "Bookmarks", "setIsLoading", "isRemoving", "setIsRemoving", "loadedBookmarks", "h4", "LoadingPlaceholder", "p", "noBookmarks", "go_to_bookmark_clicked", "goToBookmark", "bookmarkItem", "remove", "disabled", "stopPropagation", "updatedBookmarks", "removeBookmarkClicked", "flexWrap", "shape", "radius", "default", "HeaderScene", "headerTitleContainer", "headerActions", "documentationLink", "subHeader", "variablesAndControls", "variables", "getTagKeysProvider", "dsVar", "datasource_", "DataSourceWithBackend", "isArray", "resourceAttributes", "spanAttributes", "otherAttributes", "EVENT_INTRINSIC", "ignoredAttributesHomeFilter", "filterKeys", "getHomeScene", "navigate", "useNavigate", "dsVariable", "filterVariable", "getHomeFilterVariable", "h2", "explore_traces_clicked", "read_documentation_clicked", "control", "renderFilter", "isNumber", "test", "endsWith", "Home", "localStorage", "DATASOURCE_LS_KEY", "buildPanels", "HOMEPAGE_FILTERS_LS_KEY", "pf", "filter_changed", "renderedFilters", "expr", "renderTraceQLLabelFilters", "columnGap", "rowGap", "initialFilters", "initialDS", "$variables", "SceneVariableSet", "DataSourceVariable", "VAR_DATASOURCE", "pluginId", "AdHocFiltersVariable", "VAR_HOME_FILTER", "allowCustomValue", "SceneTimePicker", "SceneRefreshPicker", "getAncestor", "TraceExploration", "tracesByServiceScene", "newTracesExploration", "context", "sceneUtils", "lookupVariable", "VAR_GROUPBY", "CustomVariable", "VAR_SPAN_LIST_COLUMNS", "getLatencyThresholdVariable", "getLatencyPartialThresholdVariable", "PrimarySignalVariable", "getCurrentStep", "targetQuery", "request", "targets", "shouldShowSelection", "getMetricValue", "str", "toUpperCase", "webpackContext", "req", "webpackContextResolve", "__webpack_require__", "code", "resolve", "module", "exports", "AddToInvestigationButton", "getQueries", "getContext", "queryRunner", "findObject", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "q", "dsUid", "ctx", "uid", "logoPath", "Logo", "TraceViewPanelScene", "panelContainer", "service1", "bar1", "service2", "bar2", "service3", "bar3", "service4", "bar4", "service5", "bar5", "service6", "bar6", "marginTop", "TraceDrawerScene", "open_trace", "TraceQLIssueDetector", "runIssueDetectionQuery", "datasourceVar", "resetIssues", "subtract", "minimalTimeRange", "toISOString", "issueDetector", "String", "errors", "hasIssue", "TraceQLConfigWarning", "detector", "<PERSON><PERSON>", "severity", "EntityAssertionsWidget", "EntityAssertionsWidgetExternal", "usePluginComponent", "setTimeRange", "sub", "start", "valueOf", "end", "entityName", "entityType", "returnT<PERSON><PERSON><PERSON><PERSON>", "drawerSizes", "sm", "lg", "Drawer", "onClose", "closeOnMaskClick", "scrollableContent", "tabs", "drawerWidth", "onMouseDown", "onTouchStart", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onMouseMove", "useCallback", "getCustomDrawerWidth", "clientX", "onTouchMove", "touch", "touches", "onMouseUp", "document", "removeEventListener", "onTouchEnd", "preventDefault", "addEventListener", "useResizebleDrawer", "wrapperStyles", "getWrapperStyles", "dragStyles", "getDragStyles", "overlayRef", "React", "dialogProps", "titleProps", "useDialog", "overlayProps", "useOverlay", "isDismissable", "isOpen", "classList", "overrideWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "open", "getContainer", "drawerContent", "rootClassName", "drawer", "classNames", "wrapper", "motion", "motionAppear", "motionName", "drawerMotion", "maskClassName", "mask", "maskClosable", "maskMotion", "FocusScope", "restoreFocus", "contain", "autoFocus", "aria-label", "selectors", "components", "General", "cx", "dragHandleVertical", "resizer", "Boolean", "headerWithTabs", "IconButton", "data-testid", "close", "titleWrapper", "tabsWrapper", "<PERSON>rollC<PERSON>r", "showScrollIndicators", "offsetRight", "offsetWidth", "offsetLeft", "boxShadow", "shadows", "z3", "transform", "transition", "transitions", "create", "overlay", "bottom", "opacity", "borderBottom", "overflowWrap", "paddingLeft", "modal", "down", "SmartDrawer", "forceNoDrawer", "investigationButton", "<PERSON><PERSON><PERSON>er", "AttributeFiltersVariable", "addFilterButtonText", "hide", "VariableHide", "<PERSON><PERSON><PERSON><PERSON>", "readOnly", "embedderName", "expressionBuilder", "has<PERSON><PERSON><PERSON>", "updatedFilters", "initialFilter", "buildTime", "process", "compositeVersion", "topScene", "setupInvestigationButton", "isActive", "stateUpdate", "initialMetric", "closeDrawer", "addToInvestigationButton", "updateInvestigationLink", "serviceNameField", "getInvestigationLink", "investigationLink", "getVariableSet", "TraceExplorationScene", "drawerScene", "bodyContainer", "Embedded<PERSON><PERSON><PERSON>", "TraceExplorationHeader", "add_to_investigation_trace_view_clicked", "ADD_TO_INVESTIGATION_MENU_TEXT", "useServiceName", "setServiceName", "getServiceNameFromFilters", "serviceNameFilter", "setReturnToPrevious", "useReturnToPrevious", "returnToPreviousSource", "timeRangeControl", "timeRangeState", "filtersVariableState", "metricVariableState", "explorationUrl", "setExplorationUrl", "primarySignalOptions", "headerContainer", "go_to_full_app_clicked", "menuVisible", "setMenuVisible", "VersionHeader", "menuHeader", "h5", "menuHeaderSubtitle", "menu", "<PERSON><PERSON>", "feedbackLinksEnabled", "<PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "global_docs_link_clicked", "feedback_link_clicked", "datasourceLabel", "Dropdown", "onVisibleChange", "helpIcon", "isReadOnly", "hideVariable", "defaultToAll", "h3", "drawerHeaderButtons", "canvas", "fontWeightMedium", "lineHeight", "testIds", "loadingState", "SkeletonTheme", "fadeIn", "keyframes", "animationName", "animationDelay", "animationTimingFunction", "animationDuration", "animationFillMode", "streamingIndicator", "success", "topic", "DataTopic", "Annotations", "traceIDField", "parentAnchorHref", "parentElement", "throttleInterval", "mousePosition", "setMousePosition", "updateMousePosition", "throttle", "clientY", "GrotNotFound", "show404", "useMousePosition", "SVG", "src", "dark404", "light404", "xPos", "yPos", "innerWidth", "innerHeight", "heightRatio", "widthRatio", "rotation", "getIntermediateValue", "translation", "transform<PERSON><PERSON>in", "ratio", "emptyState", "remedy", "extensionPointId", "ADD_TO_INVESTIGATION_MENU_DIVIDER_TEXT", "ADD_TO_INVESTIGATION_MENU_GROUP_TEXT", "addItem", "item", "setItems", "items", "iconClassName", "getExploreHref", "onExploreClick", "VizPanelMenu", "existingMenuItems", "existingAddToInvestigationLink", "add_to_investigation_clicked", "open_in_explore_clicked", "addToInvestigations", "getPluginLinkExtensions", "extensions", "getObservablePluginLinks", "firstValueFrom", "maxDifferenceIndex", "diff", "CustomMenu", "customMenu", "DropdownIndicator", "selectProps", "menuIsOpen", "GroupHeading", "heading", "weight", "buttonGroupOptions", "currentSignal", "selectOptions", "primary_signal_changed", "primary_signal", "buttonGroup", "isSearchable", "IndicatorSep<PERSON><PERSON>", "SingleValue", "borderLeft", "page", "properties", "reportInteraction", "pluginJson", "createInteractionName", "start_investigation", "stop_investigation", "toggle_bookmark_clicked", "homepage_initialized", "new_filter_added_manually", "app_initialized", "_onActivateStep", "getStepForTimeRange", "newStep", "axisWidth", "isErrorsMetric", "DrawStyle", "Bars", "StackingMode", "Normal", "overrides", "matchFieldsWithNameByRegex", "overrideColor", "DurationComparisonControl", "startInvestigation", "isDisabled", "InsightsTimelineWidget", "InsightsTimelineWidgetExternal", "filterBySeverity", "filterBySummaryKeywords", "isDuration", "getHistogramVizPanel", "getRateOrErrorVizPanel", "buildSelectionAnnotation", "xSel", "ySel", "arrayToDataFrame", "xMin", "xMax", "timeEnd", "yMin", "yMax", "isRegion", "lineStyle", "newData", "minBucket", "yBucketToDuration", "<PERSON><PERSON><PERSON><PERSON>", "titleRadioWrapper", "slowestBuckets", "fontWeightBold", "ONE_MILLISECOND", "ONE_SECOND", "ONE_MINUTE", "ONE_HOUR", "UNIT_STEPS", "log10", "unit", "microseconds", "ofPrevious", "primaryUnit", "secondaryUnit", "_dropWhile", "_round", "primaryValue", "secondaryValue", "secondaryValueRounded", "round", "primaryUnitString", "dataPoints", "dur", "ErrorStateScene", "errorState", "Search", "searchQuery", "onSearchQueryChange", "searchField", "Input", "cloneDataFrame", "renderFilteredData", "filtered", "performRepeat", "groupSeriesBy", "groupedData", "newSeries", "mainFrame", "sortDataFrame", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "frameIndex", "sum", "vSum", "<PERSON><PERSON><PERSON><PERSON>", "evt", "currentTarget", "onSearchQueryChangeDebounced", "debounce", "doesQueryMatchDataFrameLabels", "repeat", "itemContainer", "yAxis", "yAxisItem", "xAxis", "xAxisItem", "gridTemplateColumns", "gridAutoRows", "dataFrame", "pattern", "regex", "RegExp", "extendPanelContext", "onSelectRange", "args", "rawSelection", "yFrom", "yTo", "heatmap", "show", "axisLabel", "scheme", "steps", "yValue", "multiplier", "rawValue", "metricFn", "groupByAttrs", "HighestDifferencePanel", "getAttribute", "valueField", "onAddToFilters", "differenceContainer", "differenceValue", "textWrap", "whiteSpace", "filterExists", "getFrameName", "barchart", "setMax", "AxisPlacement", "Hidden", "overrideUnit"], "sourceRoot": ""}