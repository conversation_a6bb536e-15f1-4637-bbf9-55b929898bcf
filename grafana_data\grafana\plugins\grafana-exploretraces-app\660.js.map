{"version": 3, "file": "660.js?_cache=e00eec86d5ee7174f79c", "mappings": "mKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,yBACxB,iBAAkB,mBAClB,yBAA0B,0BAE3B,yBAA0B,CACzB,sBAAuB,yBACvB,mBAAoB,wBAErB,gBAAiB,CAChBC,QAAS,iCACTC,SAAU,gBACVC,MAAO,OAER,wBAAyB,CACxB,wBAAyB,OACzB,sBAAuB,OACvB,sBAAuB,QAExB,iBAAkB,CACjB,iBAAkB,OAClB,cAAe,MACf,uBAAwB,SAEzB,iBAAkB,CACjB,eAAgB,MAChB,aAAc,OAEf,oCAAqC,CACpC,eAAgB,KAChB,iBAAkB,YAEnBC,SAAU,CACT,gCAAiC,aAElC,YAAa,CACZD,MAAO,CACNA,MAAO,OAGT,2BAA4B,CAC3BE,QAAS,MAEV,qBAAsB,CACrB,uBAAwB,YACxB,sCAAuC,cAExC,yBAA0B,CACzB,2DAA4D,gCAC5D,kBAAmB,2BAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,MAEnB,oBAAqB,CACpB,uBAAwB,SAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,YAEpC,wBAAyB,CACxB,wBAAyB,SACzB,mBAAoB,UAErB,yBAA0B,CACzB,2BAA4B,SAC5B,aAAc,CACb,2BAA4B,QAE7B,qBAAsB,OACtB,sBAAuB,SACvB,eAAgB,CACf,2BAA4B,QAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,OAGb,8CAA+C,CAC9C,mBAAoB,KACpBC,QAAS,gCACT,gDAAiD,iBAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,QACzB,uBAAwB,OACxB,gCAAiC,aACjC,oDAAqD,uCACrD,0BAA2B,aAC3B,uBAAwB,aACxB,mBAAoB,QACpB,mDAAoD,yBACpD,uBAAwB,OACxB,kDAAmD,yBACnD,iCAAkC,cAClC,oCAAqC,eAIxC,6BAA8B,CAC7B,+BAAgC,SAChC,6BAA8B,SAE/B,oBAAqB,CACpB,2BAA4B,OAE7B,8BAA+B,CAC9B,kBAAmB,WAEpB,2BAA4B,CAC3BC,MAAO,MAER,yBAA0B,CACzB,mBAAoB,SAErB,4BAA6B,CAC5B,6CAA8C,kBAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,OAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,OAGR,uBAAwB,CACvB,0BAA2B,QAE5B,wBAAyB,CACxB,2BAA4B,S", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/zh-Hans/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"使用键 {{keyLabel}} 编辑筛选器\",\n\t\t\t\"managed-filter\": \"{{origin}} 托管筛选器\",\n\t\t\t\"remove-filter-with-key\": \"使用键 {{keyLabel}} 移除筛选器\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"移除筛选器值 - {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"使用自定义值：{{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"如果您使用链接找到了此处的路径，则此应用程序中可能存在错误。\",\n\t\t\tsubTitle: \"URL 与任何页面都不匹配\",\n\t\t\ttitle: \"未找到\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"折叠场景\",\n\t\t\t\"expand-button-label\": \"展开场景\",\n\t\t\t\"remove-button-label\": \"移除场景\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"对象详情\",\n\t\t\t\"scene-graph\": \"场景图\",\n\t\t\t\"title-scene-debugger\": \"场景调试器\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"折叠行\",\n\t\t\t\"expand-row\": \"展开行\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"比较\",\n\t\t\t\"button-tooltip\": \"启用时间范围比较\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"窗格大小调整小部件\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"标题\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"探索\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"正在加载插件面板…\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"面板插件没有面板组件\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"在单个面板中呈现太多系列可能会影响性能，并使数据难以阅读。\",\n\t\t\t\"warning-message\": \"仅显示 {{seriesLimit}} 系列\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"移除\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"取消查询\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"编辑筛选器运算符\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"添加筛选条件\",\n\t\t\t\"title-add-filter\": \"添加筛选条件\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"移除筛选条件\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"选择标签\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"选择标签\",\n\t\t\t\"title-remove-filter\": \"移除筛选条件\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"选择值\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"默认\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"清除\",\n\t\t\ttooltip: \"在此数据面板中默认应用。如果编辑，它将转移到其他数据面板。\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"还原此数据面板设置的分组。\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"逗号分隔值\",\n\t\t\t\t\t\"double-quoted-values\": \"双引号值\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"以不同方式格式化日期\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"使用 glob 语法格式化多值变量，例如 {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"值的 HTML 转义\",\n\t\t\t\t\t\"json-stringify-value\": \"JSON 字符串化值\",\n\t\t\t\t\t\"keep-value-as-is\": \"保持值不变\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"多个值的格式为 variable=value\",\n\t\t\t\t\t\"single-quoted-values\": \"单引号值\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"用于 URL 转义值，采用 URI 语法字符\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"适用于 URL 转义值\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"值由 | 字符分隔\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"按选择器分组\",\n\t\t\t\"placeholder-group-by-label\": \"按标签分组\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"选择值\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"正在加载选项…\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"应用\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"未找到选项\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"获取标签时发生错误。单击重试\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"您好\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"文本\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"输入数值\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"选择值\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}