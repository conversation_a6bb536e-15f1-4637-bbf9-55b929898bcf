{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "liveNow": false, "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 9, "panels": [], "title": "Traces", "type": "row"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 100, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 0, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 1}, "id": 2, "maxDataPoints": 50, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-traces"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"aggregates": [{"aggregateType": "count", "column": ""}], "columns": [{"hint": "time", "name": "Timestamp", "type": "DateTime64(9)"}], "database": "default", "filters": [{"condition": "AND", "filterType": "custom", "hint": "time", "key": "Timestamp", "operator": "WITH IN DASHBOARD TIME RANGE", "restrictToFields": [{"label": "Timestamp", "name": "Timestamp", "picklistValues": [], "type": "DateTime64(9)"}], "type": "datetime"}], "groupBy": ["ServiceName"], "limit": 10000, "mode": "trend", "orderBy": [], "queryType": "timeseries", "table": "otel_traces"}}, "pluginVersion": "4.0.6", "queryType": "timeseries", "rawSql": "SELECT\r\n  $__timeInterval(Timestamp) as time,\r\n  ServiceName,\r\n  count() as ` `\r\nFROM otel_traces\r\nWHERE\r\n  $__conditionalAll(TraceId IN (${trace_id:singlequote}),  $trace_id)\r\n  AND $__timeFilter(Timestamp)\r\n  AND ServiceName IN (${serviceName:singlequote})\r\nGROUP BY ServiceName, time\r\nORDER BY time ASC\r\nLIMIT 100000\r\n", "refId": "A"}], "title": "Traces per Service", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 10}, "id": 7, "maxDataPoints": 50, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-traces"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"aggregates": [{"aggregateType": "count", "column": ""}], "columns": [{"hint": "time", "name": "Timestamp", "type": "DateTime64(9)"}], "database": "default", "filters": [{"condition": "AND", "filterType": "custom", "hint": "time", "key": "Timestamp", "operator": "WITH IN DASHBOARD TIME RANGE", "restrictToFields": [{"label": "Timestamp", "name": "Timestamp", "picklistValues": [], "type": "DateTime64(9)"}], "type": "datetime"}], "groupBy": ["ServiceName"], "limit": 10000, "mode": "trend", "orderBy": [], "queryType": "timeseries", "table": "otel_traces"}}, "pluginVersion": "4.0.6", "queryType": "timeseries", "rawSql": "SELECT\r\n  $__timeInterval(Timestamp) as time,\r\n  ServiceName,\r\n  quantile(0.99)(Duration)/1000000 AS p99\r\nFROM otel_traces\r\nWHERE\r\n  $__conditionalAll(TraceId IN (${trace_id:singlequote}),  $trace_id)\r\n  AND $__timeFilter(Timestamp)\r\n  AND ( Timestamp  >= $__fromTime AND Timestamp <= $__toTime )\r\n  AND ServiceName IN (${serviceName:singlequote})\r\n  AND ServiceName != 'loadgenerator'\r\nGROUP BY time, ServiceName\r\nORDER BY time ASC\r\nLIMIT 100000\r\n", "refId": "A"}], "title": "Service Performance - p99", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 24, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 19}, "id": 8, "maxDataPoints": 50, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "desc"}}, "pluginVersion": "9.4.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-traces"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"aggregates": [{"aggregateType": "count", "column": ""}], "columns": [{"hint": "time", "name": "Timestamp", "type": "DateTime64(9)"}], "database": "default", "filters": [{"condition": "AND", "filterType": "custom", "hint": "time", "key": "Timestamp", "operator": "WITH IN DASHBOARD TIME RANGE", "restrictToFields": [{"label": "Timestamp", "name": "Timestamp", "picklistValues": [], "type": "DateTime64(9)"}], "type": "datetime"}], "groupBy": ["ServiceName"], "limit": 10000, "mode": "trend", "orderBy": [], "queryType": "timeseries", "table": "otel_traces"}}, "pluginVersion": "4.0.6", "queryType": "timeseries", "rawSql": "SELECT\r\n  $__timeInterval(Timestamp) as time,\r\n  count(*) as ` `,\r\n  ServiceName\r\nFROM otel_traces\r\nWHERE\r\n  $__conditionalAll(TraceId IN (${trace_id:singlequote}),  $trace_id)\r\n  AND $__timeFilter(Timestamp)\r\n  AND ServiceName IN (${serviceName:singlequote})\r\n AND StatusCode IN ('Error', 'STATUS_CODE_ERROR')\r\n  AND ServiceName != 'loadgenerator' GROUP BY ServiceName, time\r\nORDER BY time ASC\r\nLIMIT 100000", "refId": "A"}], "title": "Error rates", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Trace ID"}, "properties": [{"id": "custom.width", "value": 207}, {"id": "links", "value": [{"title": "__value.raw", "url": "/d/8klBUGfVk/otel-traces?${__url_time_range}﻿&﻿${serviceName:queryparam}﻿&var-trace_id=${__value.raw}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Service Name"}, "properties": [{"id": "custom.width", "value": 184}, {"id": "links", "value": [{"title": "__value.raw", "url": "/d/8klBUGfVk/otel-traces?${__url_time_range}﻿&﻿${trace_id:queryparam}﻿&var-serviceName=${__value.raw}"}]}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration"}, "properties": [{"id": "unit", "value": "ms"}, {"id": "custom.cellOptions", "value": {"mode": "lcd", "type": "gauge", "valueDisplayMode": "text"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 216}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Service Tags"}, "properties": [{"id": "custom.inspect", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 248}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "timestamp"}, "properties": [{"id": "custom.width", "value": 234}]}]}, "gridPos": {"h": 15, "w": 12, "x": 0, "y": 28}, "id": 4, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Duration"}]}, "pluginVersion": "11.2.0-72343", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-traces"}, "editorType": "sql", "format": 1, "meta": {"builderOptions": {"columns": [], "database": "default", "filters": [{"condition": "AND", "filterType": "custom", "key": "Timestamp", "operator": "WITH IN DASHBOARD TIME RANGE", "restrictToFields": [{"label": "Timestamp", "name": "Timestamp", "picklistValues": [], "type": "DateTime64(9)"}], "type": "datetime"}], "limit": 100, "mode": "list", "orderBy": [], "queryType": "table", "table": "otel_traces"}}, "pluginVersion": "4.0.6", "queryType": "table", "rawSql": "SELECT\r\n  min(Timestamp) as timestamp,\r\n  TraceId as `Trace ID`,\r\n  argMin(ServiceName, Timestamp) as `Service Name`,\r\n  divide(max(Duration), 1000000) as Duration\r\nFROM otel_traces\r\nWHERE\r\n  $__conditionalAll(TraceId IN (${trace_id:singlequote}),  $trace_id)\r\n  AND ServiceName IN (${serviceName:singlequote})\r\n  AND ServiceName != 'loadgenerator'\r\n  AND $__timeFilter(Timestamp)\r\nGROUP BY TraceId\r\nORDER BY Duration DESC\r\nLIMIT 100\r\n", "refId": "A"}], "title": "Traces", "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "gridPos": {"h": 15, "w": 12, "x": 12, "y": 28}, "id": 6, "targets": [{"builderOptions": {"columns": [{"hint": "trace_id", "name": "TraceId"}, {"hint": "trace_span_id", "name": "SpanId"}, {"hint": "trace_parent_span_id", "name": "ParentSpanId"}, {"hint": "trace_service_name", "name": "ServiceName"}, {"hint": "trace_operation_name", "name": "SpanName"}, {"hint": "time", "name": "Timestamp"}, {"hint": "trace_duration_time", "name": "Duration"}, {"hint": "trace_tags", "name": "SpanAttributes"}, {"hint": "trace_service_tags", "name": "ResourceAttributes"}, {"hint": "trace_status_code", "name": "StatusCode"}], "database": "default", "filters": [{"condition": "AND", "filterType": "custom", "hint": "time", "key": "", "operator": "WITH IN DASHBOARD TIME RANGE", "type": "datetime"}, {"condition": "AND", "filterType": "custom", "hint": "trace_duration_time", "key": "", "operator": ">", "type": "UInt64", "value": 0}, {"condition": "AND", "filterType": "custom", "hint": "trace_service_name", "key": "", "operator": "IS ANYTHING", "type": "string", "value": ""}], "limit": 1000, "meta": {"isTraceIdMode": true, "otelEnabled": true, "otelVersion": "latest", "traceDurationUnit": "nanoseconds", "traceId": "${trace_id}"}, "mode": "list", "orderBy": [{"default": true, "dir": "DESC", "hint": "time", "name": ""}, {"default": true, "dir": "DESC", "hint": "trace_duration_time", "name": ""}], "queryType": "traces", "table": "otel_traces"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "clickhouse-traces"}, "editorType": "builder", "format": 3, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 100, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.0.6", "queryType": "traces", "rawSql": "WITH '${trace_id}' as trace_id, (SELECT min(Start) FROM \"otel_traces_trace_id_ts\" WHERE TraceId = trace_id) as trace_start, (SELECT max(End) + 1 FROM \"otel_traces_trace_id_ts\" WHERE TraceId = trace_id) as trace_end SELECT \"TraceId\" as traceID, \"SpanId\" as spanID, \"ParentSpanId\" as parentSpanID, \"ServiceName\" as serviceName, \"SpanName\" as operationName, \"Timestamp\" as startTime, multiply(\"Duration\", 0.000001) as duration, arrayMap(key -> map('key', key, 'value',\"SpanAttributes\"[key]), mapKeys(\"SpanAttributes\")) as tags, arrayMap(key -> map('key', key, 'value',\"ResourceAttributes\"[key]), mapKeys(\"ResourceAttributes\")) as serviceTags FROM \"otel_traces\" WHERE traceID = trace_id AND startTime >= trace_start AND startTime <= trace_end AND ( Timestamp >= $__fromTime AND Timestamp <= $__toTime ) AND ( Duration > 0 ) ORDER BY Timestamp DESC, Duration DESC LIMIT 1000", "refId": "A"}], "title": "Trace Details", "type": "traces"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 43}, "id": 10, "panels": [], "title": "Logs", "type": "row"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 44}, "id": 11, "options": {"dedupStrategy": "none", "enableLogDetails": true, "prettifyLogMessage": false, "showCommonLabels": false, "showLabels": false, "showTime": true, "sortOrder": "Descending", "wrapLogMessage": false}, "targets": [{"builderOptions": {"columns": [{"hint": "time", "name": "Timestamp", "type": "DateTime64(9)"}, {"hint": "log_level", "name": "SeverityText", "type": "LowCardinality(String)"}, {"hint": "log_message", "name": "Body", "type": "String"}], "database": "default", "filters": [], "limit": 1000, "meta": {"logMessageLike": "", "otelVersion": "latest"}, "mode": "list", "orderBy": [], "queryType": "logs", "table": "otel_logs"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "editorType": "builder", "format": 2, "pluginVersion": "4.0.6", "rawSql": "SELECT Timestamp as timestamp, Body as body, SeverityText as level FROM \"otel_logs\" LIMIT 1000", "refId": "A"}], "title": "Trace Logs", "type": "logs"}], "refresh": "", "revision": 1, "schemaVersion": 39, "tags": [], "templating": {"list": [{"hide": 0, "includeAll": false, "label": "ClickHouse instance", "multi": false, "name": "datasource", "options": [], "query": "grafana-clickhouse-datasource", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {"selected": true, "text": ["All"], "value": ["$__all"]}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "definition": "SELECT DISTINCT ServiceName FROM otel_traces", "hide": 0, "includeAll": true, "label": "Service Name", "multi": true, "name": "serviceName", "options": [], "query": "SELECT DISTINCT ServiceName FROM otel_traces", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"allValue": "ALL", "current": {"selected": false, "text": "All", "value": "$__all"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "definition": "SELECT DISTINCT TraceId FROM otel_traces WHERE ParentSpanId = '' LIMIT 100", "hide": 0, "includeAll": true, "label": "Trace Id", "multi": false, "name": "trace_id", "options": [], "query": "SELECT DISTINCT TraceId FROM otel_traces WHERE ParentSpanId = '' LIMIT 100", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "Simple ClickHouse OTel Dashboard", "uid": "8klBUGfVk", "version": 2, "weekStart": ""}