#!/usr/bin/env python3
"""
Fake Data Generator - Main Entry Point

This is the Python equivalent of the original Kotlin fake-iot-generator,
with added support for Order data generation alongside Station Status.
"""

import sys
import argparse
import signal
import logging
from typing import Optional

from utils.config import Config
from utils.database import DatabaseConnection
from utils.kafka_producer import KafkaProducerManager
from generator.station_generator import StationStatusGenerator
from generator.order_generator import OrderGenerator

logger = logging.getLogger(__name__)


class FakeDataGenerator:
    """
    Main fake data generator application
    """

    def __init__(self):
        """Initialize the fake data generator"""
        self.db_connection = None
        self.kafka_producer = None
        self.station_generator = None
        self.order_generator = None
        self._setup_signal_handlers()

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(
                f"Received signal {signum}, shutting down gracefully...")
            self.cleanup()
            sys.exit(0)

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)

    def initialize(self):
        """Initialize database and Kafka connections"""
        try:
            # Initialize database connection
            self.db_connection = DatabaseConnection()
            self.db_connection.connect()

            # Initialize Kafka producer
            self.kafka_producer = KafkaProducerManager()
            self.kafka_producer.create_producer()

            # Initialize generators
            self.station_generator = StationStatusGenerator(
                self.db_connection, self.kafka_producer)
            self.order_generator = OrderGenerator(
                self.db_connection, self.kafka_producer)

            logger.info("Fake data generator initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize fake data generator: {e}")
            raise

    def run_random_station_generation(self, iterations: int = 10000, delay: int = 200):
        """
        Run random station status generation (equivalent to RANDOM_GEN mode)

        Args:
            iterations: Number of iterations
            delay: Delay between messages in milliseconds
        """
        logger.info("Running random station status generation")
        self.station_generator.run_random_generation(iterations, delay)

    def run_single_station_generation(self, iterations: int = 10, delay: int = 200):
        """
        Run single station status generation (equivalent to ONE_GEN mode)

        Args:
            iterations: Number of messages
            delay: Delay between messages in milliseconds
        """
        logger.info("Running single station status generation")
        self.station_generator.run_single_station_generation(iterations, delay)

    def run_order_generation(self, iterations: int = 10000, delay: int = 200):
        """
        Run order generation (new ORDER_GEN mode)

        Args:
            iterations: Number of orders
            delay: Delay between orders in milliseconds
        """
        logger.info("Running order generation")
        self.order_generator.run_order_generation(iterations, delay)

    def cleanup(self):
        """Cleanup resources"""
        try:

            if self.kafka_producer:
                self.kafka_producer.close()

            if self.db_connection:
                self.db_connection.close()

            logger.info("Cleanup completed")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")


def create_argument_parser() -> argparse.ArgumentParser:
    """Create command line argument parser"""
    parser = argparse.ArgumentParser(
        description="Fake Data Generator - Generate station status and order data for Kafka",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --mode RANDOM_GEN --iterations 1000 --delay 300
  python main.py --mode ONE_GEN --iterations 10 --delay 500
  python main.py --mode ORDER_GEN --iterations 100 --delay 1000
        """
    )

    parser.add_argument(
        "--mode",
        choices=[Config.RANDOM_GEN, Config.ONE_GEN, Config.ORDER_GEN],
        default=Config.MODE,
        help="Generation mode (default: %(default)s)"
    )

    parser.add_argument(
        "--iterations",
        type=int,
        default=1000,
        help="Number of iterations/messages (default: %(default)s)"
    )

    parser.add_argument(
        "--delay",
        type=int,
        default=Config.DELAY,
        help="Delay between messages in milliseconds (default: %(default)s)"
    )

    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: %(default)s)"
    )

    return parser


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="Fake Data Generator")
    parser.add_argument("generator_type", choices=[
                        "station", "order"], help="Type of data to generate")
    args = parser.parse_args()

    generator = FakeDataGenerator()
    try:
        generator.initialize()
        delay_ms = Config.DELAY
        if args.generator_type == "station":
            logger.info("Starting station data generation...")
            generator.run_random_station_generation(delay=delay_ms)
        elif args.generator_type == "order":
            logger.info("Starting order data generation...")
            generator.run_order_generation(delay=delay_ms)
    except Exception as e:
        logger.error(f"An error occurred: {e}")
        sys.exit(1)
    finally:
        generator.cleanup()


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    main()
