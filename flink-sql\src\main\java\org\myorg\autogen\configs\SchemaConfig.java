package org.myorg.autogen.configs;

import java.util.List;

/**
 * Configuration for table schema definition
 */
public class SchemaConfig {
    private List<ColumnDefinition> columns;
    private List<String> primaryKey;
    private WatermarkConfig watermark;

    public List<ColumnDefinition> getColumns() {
        return columns;
    }

    public void setColumns(List<ColumnDefinition> columns) {
        this.columns = columns;
    }

    public List<String> getPrimaryKey() {
        return primaryKey;
    }

    public void setPrimaryKey(List<String> primaryKey) {
        this.primaryKey = primaryKey;
    }

    public WatermarkConfig getWatermark() {
        return watermark;
    }

    public void setWatermark(WatermarkConfig watermark) {
        this.watermark = watermark;
    }

    /**
     * Column definition for schema
     */
    public static class ColumnDefinition {
        private String name;
        private String type;
        private boolean nullable = true;
        private String metadata;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public boolean isNullable() {
            return nullable;
        }

        public void setNullable(boolean nullable) {
            this.nullable = nullable;
        }

        public String getMetadata() {
            return metadata;
        }

        public void setMetadata(String metadata) {
            this.metadata = metadata;
        }
    }

    /**
     * Watermark configuration for event time processing
     */
    public static class WatermarkConfig {
        private String column;
        private String strategy;
        private String delay;

        public String getColumn() {
            return column;
        }

        public void setColumn(String column) {
            this.column = column;
        }

        public String getStrategy() {
            return strategy;
        }

        public void setStrategy(String strategy) {
            this.strategy = strategy;
        }

        public String getDelay() {
            return delay;
        }

        public void setDelay(String delay) {
            this.delay = delay;
        }
    }
}
