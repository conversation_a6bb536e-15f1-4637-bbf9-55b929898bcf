jobs:
  - name: order_processing_autogen
    readers:
      type: KafkaReader
      topic: cf.iot.order.events
      format:
        type: json
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: order_id
             expr: id
           - colname: total_amount
             expr: amount
           - colname: order_status
             expr: status
       - type: ProjectionTransformer
         columns:
           - colname: order_id
           - colname: station_id
           - colname: total_amount
           - colname: order_status
    writers:
      type: KafkaClickHouseWriter
      topic: cf.data_platform.processed.orders
      mode: upsert
      format:
        type: clickhouse

  - name: station_status_processing
    readers:
      type: KafkaReader
      topic: cf.iot.station.status
      format:
        type: json
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: station_id
             expr: id
           - colname: current_status
             expr: status
       - type: ProjectionTransformer
         columns:
           - colname: station_id
           - colname: current_status
    writers:
      type: KafkaClickHouseWriter
      topic: cf.data_platform.station.status
      mode: upsert
      format:
        type: clickhouse


