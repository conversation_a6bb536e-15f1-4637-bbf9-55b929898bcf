# YAML Job Configuration Guide

This guide explains how to create and configure streaming jobs using the Flink SQL Autogen system. The system uses YAML files to define complete data processing pipelines without writing Java code.

## 📋 **Basic Structure**

Every YAML configuration file follows this structure:

```yaml
jobs:
  - name: job_name_1
    readers: { ... }
    transformers: [ ... ]
    writers: { ... }
  - name: job_name_2
    readers: { ... }
    transformers: [ ... ]
    writers: { ... }
```

## 🔧 **Configuration Components**

### 1. **Readers** - Data Sources

#### **KafkaReader**
Reads data from Kafka topics with automatic schema detection.

```yaml
readers:
  type: KafkaReader
  topic: cf.iot.order.events    # Kafka topic name
  format:
    type: json                  # Data format (json, avro, csv)
```

**Supported Options:**
- `topic`: Kafka topic name (required)
- `format.type`: Data format - `json`, `avro`, `csv`
- Auto-detects schema based on topic name patterns:
  - Topics containing "order" → Order schema (id, station_id, customer_id, amount, currency, status)
  - Topics containing "station" → Station schema (id, status)

### 2. **Transformers** - Data Processing

#### **WithColumnTransformer**
Creates new columns or renames existing ones using expressions.

```yaml
transformers:
  - type: WithColumnTransformer
    columns:
      - colname: order_id        # New column name
        expr: id                 # Expression (can be column name or SQL expression)
      - colname: total_amount
        expr: amount * 1.1       # SQL expressions supported
      - colname: order_status
        expr: UPPER(status)      # SQL functions supported
```

**Supported Options:**
- `columns`: Array of column definitions
  - `colname`: Name of the new/renamed column
  - `expr`: SQL expression or source column name

#### **ProjectionTransformer**
Selects specific columns from the data stream.

```yaml
transformers:
  - type: ProjectionTransformer
    columns:
      - colname: order_id        # Column to include in output
      - colname: station_id
      - colname: total_amount
      - colname: order_status
```

**Supported Options:**
- `columns`: Array of columns to include
  - `colname`: Name of column to project

### 3. **Writers** - Data Sinks

#### **KafkaClickHouseWriter**
Writes data to Kafka topics in ClickHouse-compatible format.

```yaml
writers:
  type: KafkaClickHouseWriter
  topic: cf.data_platform.processed.orders  # Output topic
  mode: upsert                               # Write mode
  format:
    type: clickhouse                         # Output format
```

**Supported Options:**
- `topic`: Output Kafka topic name (required)
- `mode`: Write mode - `upsert` (with primary key) or `standard` (append-only)
- `format.type`: Output format - `clickhouse`, `json`, `avro`
- Auto-generates schema based on topic name patterns

#### **KafkaAvroWriter**
Writes data in Avro format with schema registry support.

```yaml
writers:
  type: KafkaAvroWriter
  topic: cf.data_platform.avro.orders
  format:
    type: avro
```

#### **KafkaPostgreSQLWriter**
Writes data to PostgreSQL via Kafka Connect.

```yaml
writers:
  type: KafkaPostgreSQLWriter
  topic: cf.data_platform.postgres.orders
  format:
    type: json
```

## 📝 **Complete Examples**

### **Example 1: Simple Order Processing**

```yaml
jobs:
  - name: order_processing_simple
    readers:
      type: KafkaReader
      topic: cf.iot.order.events
      format:
        type: json
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: order_id
            expr: id
          - colname: total_amount
            expr: amount
      - type: ProjectionTransformer
        columns:
          - colname: order_id
          - colname: station_id
          - colname: total_amount
    writers:
      type: KafkaClickHouseWriter
      topic: cf.data_platform.processed.orders
      mode: upsert
      format:
        type: clickhouse
```

### **Example 2: Station Status Monitoring**

```yaml
jobs:
  - name: station_monitoring
    readers:
      type: KafkaReader
      topic: cf.iot.station.status
      format:
        type: json
    transformers:
      - type: WithColumnTransformer
        columns:
          - colname: station_id
            expr: id
          - colname: current_status
            expr: UPPER(status)
          - colname: status_timestamp
            expr: CURRENT_TIMESTAMP
      - type: ProjectionTransformer
        columns:
          - colname: station_id
          - colname: current_status
          - colname: status_timestamp
    writers:
      type: KafkaClickHouseWriter
      topic: cf.data_platform.station.monitoring
      mode: upsert
      format:
        type: clickhouse
```

### **Example 3: Multi-Job Configuration**

```yaml
jobs:
  # Job 1: Order Processing
  - name: order_processing_autogen
    readers:
      type: KafkaReader
      topic: cf.iot.order.events
      format:
        type: json
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: order_id
             expr: id
           - colname: total_amount
             expr: amount
           - colname: order_status
             expr: status
       - type: ProjectionTransformer
         columns:
           - colname: order_id
           - colname: station_id
           - colname: total_amount
           - colname: order_status
    writers:
      type: KafkaClickHouseWriter
      topic: cf.data_platform.processed.orders
      mode: upsert
      format:
        type: clickhouse

  # Job 2: Station Status Processing
  - name: station_status_processing
    readers:
      type: KafkaReader
      topic: cf.iot.station.status
      format:
        type: json
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: station_id
             expr: id
           - colname: current_status
             expr: status
       - type: ProjectionTransformer
         columns:
           - colname: station_id
           - colname: current_status
    writers:
      type: KafkaClickHouseWriter
      topic: cf.data_platform.station.status
      mode: upsert
      format:
        type: clickhouse
```

## 🚀 **How to Use**

1. **Create YAML File**: Save your configuration as `my-jobs.yaml`
2. **Build Autogen JAR**: `cd flink-sql && mvn clean package -DskipTests`
3. **Deploy to Flink**: 
   ```bash
   docker cp flink-sql/target/autogen-job.jar jobmanager:/tmp/autogen-job.jar
   docker cp my-jobs.yaml jobmanager:/tmp/my-jobs.yaml
   docker exec -it jobmanager /opt/flink/bin/flink run -d /tmp/autogen-job.jar /tmp/my-jobs.yaml
   ```

## 💡 **Best Practices**

1. **Naming**: Use descriptive job names that indicate their purpose
2. **Schema Consistency**: Ensure output column names match your downstream systems
3. **Error Handling**: The system includes built-in error handling for JSON parsing
4. **Performance**: Use projection transformers to reduce data volume
5. **Monitoring**: Check Flink UI at http://localhost:8081 for job status

## 🔍 **Troubleshooting**

- **Schema Mismatch**: Ensure transformer output columns match writer expectations
- **Topic Names**: Use consistent naming patterns for automatic schema detection
- **SQL Expressions**: Test complex expressions in Flink SQL client first
- **Resource Usage**: Monitor memory and CPU usage for large-scale deployments

## 📚 **Advanced Features**

- **Custom Expressions**: Use any valid Flink SQL expression in WithColumnTransformer
- **Multiple Outputs**: Create multiple jobs in one YAML file for related processing
- **Schema Evolution**: The system handles schema changes gracefully
- **Fault Tolerance**: Built-in checkpointing and recovery mechanisms
