{"version": 3, "file": "727.js?_cache=9e9b1715d3dac8b90007", "mappings": "2MAgBO,MAAMA,EAAe,CAC1BC,UAAYC,MACZC,UAAW,KACXC,kBAAcC,EACdC,eAAWD,GAGAE,GAAeC,EAAAA,EAAAA,eAAgCR,GAE/CS,EAAuB,EAClCR,YACAS,WACAP,YACAC,eACAE,eASE,kBAACC,EAAaI,SAAQ,CACpBC,MAAO,CACLX,YACAE,YACAC,eACAE,cAGDI,GAKMG,EAAkB,KACtBC,EAAAA,EAAAA,YAAWP,G,sICvBb,SAASQ,GAAiB,wBAC/BC,EAAuB,wBACvBC,EAAuB,6BACvBC,EAA4B,YAC5BC,EAAW,eACXC,IAEA,MAAM,QAAEC,EAAO,mBAAEC,IAAuBC,EAAAA,EAAAA,MAGlCC,EAAcC,IAClB,MAAMC,EAAUD,EAAK,GACrB,IAAIE,EAAyC,CAAC,EAC1CC,EAAkB,EAqD1B,IAA2BC,EApDvBH,EAAQI,QAASC,IACXA,KAASV,IACXM,EAAmBI,GAASV,EAAQU,GACpCH,OAGJN,EAAmBK,GA8CIE,EA7CLD,GA8CpBI,EAAAA,EAAAA,mBAAkB,kDAAmD,CACnEC,YAAaJ,KA1BTK,EA5DR,SAAmBC,GACjB,MAAO,CACLC,4BAA4BC,EAAAA,EAAAA,KAAI,CAC9BC,SAAU,WACVC,MAAOJ,EAAMK,QAAQ,IACrBC,IAAKN,EAAMK,QAAQ,KAGzB,CAoDiBE,EADDC,EAAAA,EAAAA,cAEd,OACE,oCACE,kBAACC,EAAAA,WAAUA,CACTC,UAAW7B,GAA2BkB,EAAOE,2BAC7CU,QAAS5B,EACT6B,KAAM9B,EAA0B,cAAgB,aAChD+B,QAAS/B,EAA0B,iBAAmB,mBACtDgC,KAAK,QAELhC,GACA,kBAACiC,EAAAA,MAAKA,KACJ,kBAACC,EAAAA,MAAKA,CACJvC,MAAOO,EACPiC,KAAM,OACNC,YAAa,wBACbC,SA5BmBC,I,IACbA,EAAd,MAAM3C,EAAuB,QAAf2C,EAAAA,EAAEC,qBAAFD,IAAAA,OAAAA,EAAAA,EAAiB3C,MANlB,IAAC6C,EAOdrC,EAAeR,GACXA,GARU6C,EASL7C,GART8C,EAAAA,EAAAA,GAAqBC,OAAOC,KAAKvC,GAAUoC,EAAQjC,IAWjDF,OAAmBjB,OA2BzB,C,cC7EO,SAASwD,IACd,MACM3B,EAZR,SAAmBC,GACjB,MAAO,CACL2B,OAAOzB,EAAAA,EAAAA,KAAI,CACT0B,SAAU5B,EAAM6B,WAAWD,SAC3BE,aAAc9B,EAAMK,QAAQ,GAC5B0B,WAAY/B,EAAMK,QAAQ,QAGhC,CAIiBE,EADDC,EAAAA,EAAAA,cAEd,OAAO,kBAACwB,MAAAA,CAAItB,UAAWX,EAAO4B,OAAO,YACvC,CCZO,SAASM,EAAkBC,GAShC,MACMnC,EAsCR,SAAmBC,GACjB,MAAO,CAELmC,eAAejC,EAAAA,EAAAA,KAAI,CACjB,SAAU,CACRkC,QAAS,QACTC,SAAU,OACVC,SAAU,SACVC,aAAc,WACdC,WAAY,YAGhBC,aAAavC,EAAAA,EAAAA,KAAI,CACfwC,WAAY,SACZN,QAAS,OACTO,eAAgB,gBAChBC,MAAO,SAETC,iBAAiB3C,EAAAA,EAAAA,KAAI,CACnB4C,OAAQ,UACRlB,SAAU5B,EAAM6B,WAAWkB,UAAUnB,WAEvCoB,UAAU9C,EAAAA,EAAAA,KAAI,CACZ4C,OAAQ,OACRf,WAAY/B,EAAMK,QAAQ,GAC1B4C,QAAS,KAEXC,YAAYhD,EAAAA,EAAAA,KAAI,CACdwC,WAAY,WACZS,WAAY,OACZC,WAAY,OACZC,OAAQ,OACRjB,QAAS,OACTkB,cAAe,SACf1B,SAAU5B,EAAM6B,WAAW0B,QAAQ,IACnCxB,WAAY/B,EAAMK,QAAQ,IAC1BmD,YAAaxD,EAAMK,QAAQ,IAC3B4C,QAAS,KAEXQ,YAAYvD,EAAAA,EAAAA,KAAI,CACdwD,SAAU,WAGhB,CAjFiBnD,EADDC,EAAAA,EAAAA,c,IAWK0B,EAIDA,EAEHA,EACAA,EAPIA,EARnB,OAAIA,EAAMyB,OAAOzB,EAAM0B,OAEnB,oCACE,kBAAC5B,MAAAA,CAAItB,UAAWX,EAAO0C,aACrB,kBAACoB,EAAAA,SAAQA,CACPnD,UAAWX,EAAOoC,cAClByB,MAAO1B,EAAM0B,MACbzC,SAAUe,EAAMf,SAChB2C,QAA0C,QAAjC5B,EAAyB,QAAzBA,EAAAA,EAAMyB,OAAOzB,EAAM0B,cAAnB1B,IAAAA,OAAAA,EAAAA,EAA2B6B,cAA3B7B,IAAAA,GAAAA,IAEVA,EAAM8B,WACL,kBAAChC,MAAAA,CAAItB,UAAWX,EAAOmD,YACrB,kBAAClB,MAAAA,KAA8B,QAAzBE,EAAAA,EAAMyB,OAAOzB,EAAM0B,cAAnB1B,IAAAA,OAAAA,EAAAA,EAA2B+B,wBAAwB,KACzD,kBAACjC,MAAAA,CAAItB,UAAWX,EAAO0D,YACK,QAAzBvB,EAAAA,EAAMyB,OAAOzB,EAAM0B,cAAnB1B,IAAAA,OAAAA,EAAAA,EAA2BgC,YAAa,IACG,KAAlB,QAAzBhC,EAAAA,EAAMyB,OAAOzB,EAAM0B,cAAnB1B,IAAAA,OAAAA,EAAAA,EAA2BgC,aAAoB,QAAU,YAKjEhC,EAAMiC,WACL,kBAACC,EAAAA,KAAIA,CACHC,aAAW,qBACXC,MAAM,2BACN1D,KAAK,gBACLE,KAAK,KACLJ,UAAWX,EAAOiD,YAOrB,IACT,C,g4BCRO,MAAMuB,EAAyBrC,IAOpC,MAAM,eAAEsC,EAAc,kBAAEC,IAAsBrF,EAAAA,EAAAA,OACxC,OAAEuE,EAAM,cAAEe,EAAa,aAAEC,EAAY,YAAEC,GAAgB1C,EACvDlC,GAAQQ,EAAAA,EAAAA,cACR,QAAEtB,IAAYE,EAAAA,EAAAA,MACdW,EA7CD,SAA6BC,GAClC,MAAO,CACL6E,eAAe3E,EAAAA,EAAAA,KAAI,CACjB4B,aAAc9B,EAAMK,QAAQ,KAE5ByE,YAAa9E,EAAMK,QAAQ,MAE7B0E,UAAU7E,EAAAA,EAAAA,KAAI,CACZkD,WAAYpD,EAAMgF,OAAO5B,WAAW6B,YAEtCC,MAAMhF,EAAAA,EAAAA,KAAI,CACRkD,WAAYpD,EAAMgF,OAAO5B,WAAW+B,QACpC/C,QAAS,OACTN,aAAc9B,EAAMK,QAAQ,GAC5B+E,UAAWpF,EAAMK,QAAQ,KAG/B,CA4BiBgF,CAAoBrF,GAC7BsF,EAAY9D,OAAOC,KAAKkC,GAAQ5F,OAAQwH,GAAcX,EAAYW,IAElEC,EAAaC,IACZA,EAAOC,aAGZhB,EAAcxF,EAASuG,EAAOE,OAAOC,MAAOH,EAAOC,YAAYE,QAG3DC,EAAeN,IACnB,MAAM3B,EAAQD,EAAO4B,GACrB,GAAI3B,EACF,MAAO,GAAG2B,gBAAwB3B,aAAAA,EAAAA,EAAOK,yCAM7C,OAAIqB,EAAUQ,OAEV,kBAACC,EAAAA,GAAeA,CAACP,UAAWA,GAC1B,kBAACQ,EAAAA,GAASA,CAACC,YAAY,eAAeC,UAAU,YAC5CC,GACA,kBAACnE,MAAAA,EAAAA,EAAAA,CAAItB,UAAWX,EAAO8E,eAAmBsB,EAASC,gBAAc,CAAEC,IAAKF,EAASG,WAC9EhB,EAAUiB,KAnDzB,SAAoB5C,GAClB,MAAO,CAAC6C,EAAWC,KACjB,MAAMC,EAAK/C,EAAO6C,GACZG,EAAKhD,EAAO8C,GAGlB,OAAgB,MAAZC,EAAGd,OAA6B,MAAZe,EAAGf,MAClBc,EAAGd,MAAQe,EAAGf,MAIhB,EAEX,CAsC8BgB,CAAWjD,IAASkD,IAAI,CAACtB,EAAWK,IAClD,kBAACkB,EAAAA,GAASA,CAACC,YAAaxB,EAAWyB,IAAKzB,EAAWK,MAAOA,GACvD,CAACO,EAA6Bc,IAC7B,kBAACjF,MAAAA,EAAAA,EAAAA,CACCtB,WAAWwG,EAAAA,EAAAA,IAAGnH,EAAOmF,KAAM+B,EAASE,WAAapH,EAAOgF,cAAW7G,GACnEmI,IAAKF,EAASG,UACVH,EAASiB,eACTjB,EAASkB,iBAAe,CAC5B/C,MAAOuB,EAAYN,KAEnB,kBAACtD,EAAiBA,CAChBwC,kBAAmBA,EACnBD,eAAgBA,EAChBZ,MAAO2B,EACPpE,SAAU,IAAMwD,EAAaY,GAC7B5B,OAAQA,EACRQ,WAAW,OAMpBgC,EAASjF,eAQf,kBAACQ,EAAoBA,OCtGxB4F,EAAW,IAAIC,KAAKC,cAAStJ,EAAW,CAAEuJ,YAAa,SAyCtD,MAAMC,EAA4BxF,IAKvC,MAAM,OAAEyB,EAAM,aAAEgB,EAAY,YAAEC,GAAgB1C,EAExCnC,EA9CR,SAA6BC,GAC3B,MAAO,CACL6E,eAAe3E,EAAAA,EAAAA,KAAI,CACjB4B,aAAc9B,EAAMK,QAAQ,KAE5ByE,YAAa9E,EAAMK,QAAQ,MAE7B0E,UAAU7E,EAAAA,EAAAA,KAAI,CACZkD,WAAYpD,EAAMgF,OAAO5B,WAAW6B,YAEtCC,MAAMhF,EAAAA,EAAAA,KAAI,CACRkD,WAAYpD,EAAMgF,OAAO5B,WAAW+B,QACpCwC,aAAc,aAAa3H,EAAMgF,OAAO5B,WAAWwE,SACnDxF,QAAS,OACTN,aAAc9B,EAAMK,QAAQ,KAC5B+E,UAAWpF,EAAMK,QAAQ,OAG/B,CA4BiBgF,EADD7E,EAAAA,EAAAA,cAER8E,EAAY9D,OAAOC,KAAKkC,GAAQ5F,OAAQwH,GAAcX,EAAYW,IACxE,OAAID,EAAUQ,OAGV,kBAAC9D,MAAAA,CAAItB,UAAWX,EAAO8E,eACpBS,EAAUiB,KAhCnB,SAAoB5C,GAClB,MAAO,CAAC6C,EAAWC,KACjB,MAAMC,EAAK/C,EAAO6C,GACZG,EAAKhD,EAAO8C,GAGlB,OAAU,MAANC,GAAoB,MAANC,EAEdkB,OAAmB,eAAZlB,EAAG1F,MAAyB4G,OAAmB,eAAZnB,EAAGzF,OAC7C4G,OAAmB,eAAZlB,EAAG1F,MAAyB4G,OAAmB,eAAZnB,EAAGzF,OAC7CqG,EAASQ,QAAQtB,EAAGC,GAKjB,EAEX,CAewBG,CAAWjD,IAASkD,IAAKtB,I,IAIH5B,E,OAHpC,kBAAC3B,MAAAA,CACCgF,IAAKzB,EACL7E,UAAWX,EAAOmF,KAClBZ,MAAO,GAAGiB,gBAAyC,QAAjB5B,EAAAA,EAAO4B,UAAP5B,IAAAA,OAAAA,EAAAA,EAAmBM,yCAErD,kBAAChC,EAAiBA,CAChB+B,WAAW,EACXJ,MAAO2B,EACPpE,SAAU,IAAMwD,EAAaY,GAC7B5B,OAAQA,QAQb,kBAACjC,EAAoBA,OCpCvB,MAAMqG,EAAwB7F,IAOnC,MACMnC,EA5CR,SAAmBC,GACjB,MAAO,CACLgI,cAAc9H,EAAAA,EAAAA,KAAI,CAChBkD,WAAYpD,EAAMgF,OAAO5B,WAAW6B,UACpC7C,QAAS,OACTR,SAAU5B,EAAM6B,WAAWoG,GAAGrG,SAC9Be,eAAgB,gBAChBuF,KAAM,EACNpG,aAAc9B,EAAMK,QAAQ,GAC5B8H,cAAenI,EAAMK,QAAQ,KAC7ByE,YAAa9E,EAAMK,QAAQ,KAC3B+H,aAAcpI,EAAMK,QAAQ,KAC5BgI,WAAYrI,EAAMK,QAAQ,KAC1BF,SAAU,SACVG,IAAK,EACLgI,OAAQ,IAEVC,oBAAoBrI,EAAAA,EAAAA,KAAI,CACtBiD,WAAY,OACZC,WAAY,OACZC,OAAQ,OACRzB,SAAU5B,EAAM6B,WAAW0B,QAAQ,MAErCiF,aAAatI,EAAAA,EAAAA,KAAI,CAEf,uBAAwB,CACtBkC,QAAS,QAEXqG,OAAQ,oBACRC,UAAW,SAEXC,eAAgB,SAGtB,CAUiBpI,EADDC,EAAAA,EAAAA,c,IAgBE0B,EAQAA,EArBhB,OACE,kBAACF,MAAAA,CAAItB,UAAWX,EAAOyI,aAErB,oCACE,kBAACxG,MAAAA,CAAItB,UAAWX,EAAOiI,cAAc,kBAEnC,kBAACY,SAAAA,CAAOjI,QAASuB,EAAM2G,MAAOnI,UAAWX,EAAOwI,oBAAoB,UAItE,kBAAChE,EAAqBA,CACpBG,cAAexC,EAAMwC,cACrBC,aAAczC,EAAMyC,aACpBhB,OAAqC,QAA7BzB,EAAAA,EAAM4G,+BAAN5G,IAAAA,EAAAA,EAAiCA,EAAM6G,gBAC/CnE,YAAcnG,I,IAAUyD,EAAAA,E,OAAoC,QAApCA,EAA4B,QAA5BA,EAAAA,EAAM6G,gBAAgBtK,UAAtByD,IAAAA,OAAAA,EAAAA,EAA8B6B,cAA9B7B,IAAAA,GAAAA,GACxB8G,GAAI,oBAGN,kBAAChH,MAAAA,CAAItB,UAAWX,EAAOiI,cAAc,UACrC,kBAACN,EAAwBA,CACvB/C,aAAczC,EAAMyC,aACpBhB,OAAqC,QAA7BzB,EAAAA,EAAM4G,+BAAN5G,IAAAA,EAAAA,EAAiCA,EAAM6G,gBAC/CnE,YAAcnG,I,IAAWyD,E,QAA4B,QAA5BA,EAAAA,EAAM6G,gBAAgBtK,UAAtByD,IAAAA,OAAAA,EAAAA,EAA8B6B,c,g4BCrE1D,SAASkF,EAAiBC,GAC/B,MAAO,CAAChK,EAA6BiK,EAAqBC,KACxD,GAAID,IAAgBC,EAClB,OAGF,MAAMC,EAAoB,KAAKnK,GACzBuC,EAAOD,OAAOC,KAAK4H,GACtBtL,OAAQiJ,GAAQqC,EAAkBrC,GAAKjD,QACvC8C,IAAKG,I,IAEGqC,E,MAFM,CACbC,UAAWtC,EACXpB,MAAmC,QAA5ByD,EAAAA,EAAkBrC,GAAKpB,aAAvByD,IAAAA,EAAAA,EAAgC,KAExC9C,KAAK,CAACC,EAAGC,IAAMD,EAAEZ,MAAQa,EAAEb,QAEvBD,GAAUlE,EAAK8H,OAAOJ,EAAa,GAC1C1H,EAAK8H,OAAOH,EAAkB,EAAGzD,GAEjClE,EACG1D,OAAQiJ,QAAgB9I,IAAR8I,GAChBrH,QAAQ,CAACqH,EAAKpB,KACbyD,EAAkBrC,EAAIsC,WAAW1D,MAAQA,IAI7CsD,EAAWG,GAEf,CAwBO,SAASG,EAA0BtH,GACxC,MAAM,QAAEhD,EAAO,gBAAEuK,EAAe,WAAEP,EAAU,mBAAE/J,IAAuBC,EAAAA,EAAAA,OAC9DJ,EAAaC,IAAkByK,EAAAA,EAAAA,UAAiB,IAkEjDhF,EAAgBuE,EAAiBC,GAgCvC,OACE,oCACE,kBAACtK,EAAgBA,CACfE,wBAAyBoD,EAAMpD,wBAC/BC,6BAA8BmD,EAAMnD,6BACpCC,YAAaA,EACbC,eAAgBA,KAEhBiD,EAAMpD,yBACN,kBAACiJ,EAAoBA,CACnBpD,aA3GcgF,IACpB,IAAKzK,KAAayK,KAAczK,GAE9B,YA5BN,SAAkByK,EAAoBzK,GACpC,IAAI0K,EACJ,IACEA,EAAa,CACXD,WAAYA,EACZzK,QAAS2K,KAAKC,UAAU5K,GAE5B,CAAE,MAAOkC,GACPwI,EAAa,CACXD,WAAYA,EACZI,IAAK,4DAET,CACAC,EAAAA,EAAOC,KAAK,uBAAwBL,EACtC,CAaMM,CAASP,EAAYzK,GAIvB,MAAM4G,EAAStE,OAAOC,KAAKvC,GAASnB,OAAQoM,GAAMjL,EAAQiL,GAAGpG,QAAQ+B,OAC/DsE,GAAYlL,EAAQyK,GAAY5F,aAAgB7F,EAEtD,IAAImL,EA4BJ,GA1BEA,EAAoB,OACfnK,GAFHkL,EAEGlL,CACH,CAACyK,GAAa,OACTzK,EAAQyK,IAAW,CACtB5F,OAAQqG,EACRxE,MAAOE,KAKN5G,CACH,CAACyK,GAAa,OACTzK,EAAQyK,IAAW,CACtB5F,QAAQ,EACR6B,WAAO1H,MA4Df,SAA2ByL,GACzB,GAAIzK,EAAS,C,IACOA,EACOsC,EADzB,MAAM6I,IAA+B,QAAnBnL,EAAAA,EAAQyK,UAARzK,IAAAA,OAAAA,EAAAA,EAAqB6E,QACjCuG,E,QAAmB9I,EAAAA,OAAOC,KAAKvC,GAASnB,OAAQwM,I,IAAWrL,E,OAAe,QAAfA,EAAAA,EAAQqL,UAARrL,IAAAA,OAAAA,EAAAA,EAAiB6E,gBAAzDvC,IAAAA,OAAAA,EAAAA,EAAkEsE,OACrF0E,EAAQ,CACZC,aAAcJ,EAAW,MAAQ,SACjCK,YAAaL,EAAWC,EAAmB,EAAIA,EAAmB,IAEpEzK,EAAAA,EAAAA,mBAAkB,+CAAgD2K,EACpE,CACF,CAhEEG,CAAkBhB,GAGlBT,EAAWG,GAGPI,EAAiB,C,IACHA,EAAhB,MAAM1F,IAAqC,QAA3B0F,EAAAA,EAAgBE,UAAhBF,IAAAA,OAAAA,EAAAA,EAA6B1F,QAC7C,IAAI6G,EAEFA,EAA4B,OACvBnB,GAFH1F,EAEG0F,CACH,CAACE,GAAa,OACTF,EAAgBE,IAAW,CAC9B5F,OAAQA,EACR6B,MAAOE,KAKN2D,CACH,CAACE,GAAa,OACTF,EAAgBE,IAAW,CAC9B5F,QAAQ,EACR6B,WAAO1H,MAKbiB,EAAmByL,GACnB3L,EAAe,GACjB,GA8CM6J,wBAAyBW,EACzBV,gBAAiB7J,EACjB2J,MA3Ce,KACrB,MAAMQ,EAAoB,KAAKnK,GAC/B,IAAI0G,EAAQ,EACZpE,OAAOC,KAAK4H,GAAmB1J,QAASqH,IACtC,MAAM6D,EAC4B,eAAhCxB,EAAkBrC,GAAK/F,MAAyD,eAAhCoI,EAAkBrC,GAAK/F,KAEzEoI,EAAkBrC,GAAKjD,OAAS8G,EAEhCxB,EAAkBrC,GAAKpB,MAAQiF,EAAiBjF,SAAU1H,IAG5DgL,EAAWG,GACXlK,EAAmBkK,GACnBpK,EAAe,KA8BTyF,cAAeA,IAKzB,CCrKA,MAAMoG,GAAmBzM,EAAAA,EAAAA,eAAoC,CAC3D0M,UAAW,CAAEnF,MAAO,KAAMoF,kBAAmB,GAC7CC,mBAAqBF,IAAyB,IAGnCG,EAA2B,EAAG3M,eACzC,MAAO4M,EAAYC,IAAiB1B,EAAAA,EAAAA,UAAoB,CAAE9D,MAAO,OAE3DyF,GAAmBC,EAAAA,EAAAA,aAAaP,IACpCK,EAAcL,IACb,IAEH,OACE,kBAACD,EAAiBtM,SAAQ,CAACC,MAAO,CAAEsM,UAAWI,EAAYF,mBAAoBI,IAC5E9M,IAKMgN,EAAsB,KAC1B5M,EAAAA,EAAAA,YAAWmM,GC5BdU,GAAqBnN,EAAAA,EAAAA,eAAsC,CAC/DoN,oBAAoB,EACpBC,oBAAsBD,IAAgC,IAG3CE,EAA6B,EAAGpN,eAC3C,MAAOkN,EAAoBC,IAAuBhC,EAAAA,EAAAA,WAAkB,GAE9DkC,GAA2BN,EAAAA,EAAAA,aAAaG,IAC5CC,EAAoBD,IACnB,IAEH,OACE,kBAACD,EAAmBhN,SAAQ,CAACC,MAAO,CAAEgN,qBAAoBC,oBAAqBE,IAC5ErN,ICkBMsN,EAA4B3J,GAErC,kBAAC4J,EAAAA,CACCC,WAAY7J,EAAM6J,WAClBC,UAAW9J,EAAM8J,UACjBrL,QAASuB,EAAMvB,QACfsL,MAAO/J,EAAM+J,MACbC,SAAUhK,EAAMgK,UAEfhK,EAAM3D,UAKPuN,EAA0B5J,I,IAGaiK,EAF3C,MAAMnM,GAAQQ,EAAAA,EAAAA,aACR2L,EAAYZ,IACZxL,EArCU,EAACC,EAAsBoM,KAAkD,CACzFrI,QAAQ7D,EAAAA,EAAAA,KAAI,CACVkD,WAAY,cAEZqF,OAAQ,oBACRH,OAAQtI,EAAMsI,OAAOzH,UAEvBqE,MAAMhF,EAAAA,EAAAA,KAAI,CACRkD,WAAYgJ,QAAAA,EAAW,cACvB3D,OAAQ,OACRP,KAAM,EACNmE,OAAQ,OACRC,UAAW,SACXnM,SAAU,WACVG,IAAK,EACLkC,WAAY,SACZI,MAAO,WAqBMrC,CAAUP,OAAO9B,EAA8B,QAAnBiO,EAAAA,EAAUpB,iBAAVoB,IAAAA,GAAAA,EAAqBnB,mBAEhE,OACE,kBAAChJ,MAAAA,CACCuK,aAAcrK,EAAM6J,WACpBS,aAActK,EAAM8J,UACpBrL,QAASuB,EAAMvB,QACfD,UACEyL,EAAUpB,UAAUnF,QAAU1D,EAAMgK,UAAYC,EAAUpB,UAAUzB,YAAcpH,EAAM+J,MAAMrL,MAC1FsG,EAAAA,EAAAA,IAAGnH,EAAOmF,KAAMnF,EAAOgE,QACvBhE,EAAOmF,KAEbuH,UAAYrL,I,IAERc,EADY,UAAVd,EAAE4F,KAA6B,MAAV5F,EAAE4F,MACZ,QAAb9E,EAAAA,EAAMvB,eAANuB,IAAAA,GAAAA,EAAAA,KAAAA,KAGJwK,KAAK,SACLC,SAAU,GAETzK,EAAM3D,W,wBCzDb,MA0BaqO,EAAmB1K,IAC9B,MACMnC,EA5BU,EAACC,EAAsB6M,KAAoC,CAC3EC,MAAM5M,EAAAA,EAAAA,KAAI,CACRkC,QAAS,OACTO,eAAgB,aAChBoK,SAAU,OACV3E,aAAc,MACdjI,SAAU,aAEZ6M,UAAU9M,EAAAA,EAAAA,KAAI,CACZwC,WAAY,SACZI,OAAQ,UACRV,QAAS,OACTE,SAAU,OACVwC,YAAa,MACbsD,aAAc,MACd7F,aAAc,aAEhB0K,eAAe/M,EAAAA,EAAAA,KAAI,CACjBkD,WAAYpD,EAAMgF,OAAO5B,WAAW6B,UACpCiI,UAAWlN,EAAMmN,QAAQC,GACzBhL,QAAS,OACTL,WAAyB,WAAb8K,EAAwB,WAAQ3O,EAC5CmP,QAAS,YAMI9M,EADDC,EAAAA,EAAAA,aACkB0B,EAAM2K,WAChC,UAAE/O,GAAcY,IAEtB,OACE,kBAAC4O,OAAAA,CAAK5M,UAAWX,EAAO+M,MACtB,kBAACQ,OAAAA,CAAK5M,UAAWX,EAAOkN,eACD,YAApB/K,EAAMqL,WACL,oCACE,kBAACvL,MAAAA,CACCtB,UAAWX,EAAOiN,SAClBN,KAAK,SACLC,SAAU,EACVhM,QAAS,KACP7C,EAAU,CACRkJ,IAAK9E,EAAM0B,MACX4J,SAAUC,EAAAA,GAASC,MACnBjP,MAAOyD,EAAMzD,SAGjBgO,UAAYrL,IACI,UAAVA,EAAE4F,KAA6B,MAAV5F,EAAE4F,KACzBlJ,EAAU,CACRkJ,IAAK9E,EAAM0B,MACX4J,SAAUC,EAAAA,GAASC,MACnBjP,MAAOyD,EAAMzD,UAKnB,kBAAC2F,EAAAA,KAAIA,CAACE,MAAO,gBAAiBxD,KAAM,KAAMF,KAAM,iBAElD,kBAACoB,MAAAA,CACCtB,UAAWX,EAAOiN,SAClBN,KAAK,SACLC,SAAU,EACVhM,QAAS,KACP7C,EAAU,CACRkJ,IAAK9E,EAAM0B,MACX4J,SAAUC,EAAAA,GAASE,SACnBlP,MAAOyD,EAAMzD,SAGjBgO,UAAYrL,IACI,UAAVA,EAAE4F,KAA6B,MAAV5F,EAAE4F,KACzBlJ,EAAU,CACRkJ,IAAK9E,EAAM0B,MACX4J,SAAUC,EAAAA,GAASE,SACnBlP,MAAOyD,EAAMzD,UAKnB,kBAAC2F,EAAAA,KAAIA,CAACE,MAAO,sBAAuBxD,KAAM,KAAMF,KAAM,mBAK3DsB,EAAM0L,YACL,kBAAC5L,MAAAA,CACCsC,MAAO,aACPoI,KAAK,SACLC,SAAU,EACVjM,UAAWX,EAAOiN,SAClBrM,QAASuB,EAAM0L,WACfnB,UAAYrL,I,IAERc,EADY,UAAVd,EAAE4F,KAA6B,MAAV5F,EAAE4F,MACT,QAAhB9E,EAAAA,EAAM0L,kBAAN1L,IAAAA,GAAAA,EAAAA,KAAAA,MAIJ,kBAAC2L,MAAAA,CAAIjL,MAAM,KAAK6F,OAAO,KAAKqF,QAAQ,YAAYC,KAAK,OAAOC,MAAM,8BAChE,kBAACC,OAAAA,CACCC,SAAS,UACTC,SAAS,UACTC,EAAE,srBACFL,KAAK,UACLM,YAAY,MAEd,kBAACJ,OAAAA,CACCG,EAAE,6xBACFL,KAAK,UACLM,YAAY,QAMnBnM,EAAMoM,OACLpM,EAAMoM,MAAMzH,IAAK0H,I,IAgBEA,EAfjB,OACE,kBAACvM,MAAAA,CACCtB,UAAWX,EAAOiN,SAClBN,KAAK,SACLC,SAAU,EACVhM,QAAS,KACP6N,OAAOC,KAAKF,EAAKG,KAAM,WAEzBjC,UAAYrL,IACI,UAAVA,EAAE4F,KAA6B,MAAV5F,EAAE4F,KACzBwH,OAAOC,KAAKF,EAAKG,KAAM,WAG3B1H,IAAKuH,EAAKG,MAEV,kBAACtK,EAAAA,KAAIA,CAACE,MAAiB,QAAViK,EAAAA,EAAKjK,aAALiK,IAAAA,EAAAA,EAAc,OAAQvH,IAAKuH,EAAKG,KAAM5N,KAAM,KAAMF,KAAM,eCxFxE+N,EAAezM,IAC1B,MAAM,MAAE0B,EAAK,MAAEnF,GAAUyD,EACnBlC,GAAQQ,EAAAA,EAAAA,cACR,UAAEuK,GAAcQ,IACtB,IAAIqD,EAEJ,GAAIhL,IAAUiL,EAAAA,EAAY,CACxB,MAAMC,EAAWC,KAAmBC,QACf,iBAAVvQ,GAAsBA,KAASqQ,IACxCF,EAAaE,EAASrQ,GAAOwQ,MAEjC,CAEA,MAAMC,EAAenE,EAAUnF,QAAU1D,EAAMgK,UAAYhK,EAAM+J,MAAMrL,OAASmK,EAAUzB,UAEpFvJ,EA1DU,EAACC,EAAsB4O,KAAyB,CAChEO,gBAAgBjP,EAAAA,EAAAA,KAAI,CAAC,GACrB4M,MAAM5M,EAAAA,EAAAA,KAAI,CACR0C,MAAO,SAEToK,UAAU9M,EAAAA,EAAAA,KAAI,CACZoC,SAAU,OACVC,aAAc,aAEhB6M,cAAclP,EAAAA,EAAAA,KAAI,CAChBkC,QAAS,eACTQ,MAAO,SAETyM,MAAMnP,EAAAA,EAAAA,KAAI,CACR,WAAY,CACVoP,gBAAiBV,EACjBW,QAAS,KACT9G,OAAQ,OACRP,KAAM,EACN/H,SAAU,WACVG,IAAK,EACLsC,MAAO,GAAG5C,EAAMK,QAAQ,QAE1B,UAAW,CACTgD,OAAQ,aAAarD,EAAMgF,OAAO3B,OAAOmM,UAE3CF,gBAAiB,cACjBjM,OAAQ,aAAarD,EAAMgF,OAAO3B,OAAOoM,OACzCrN,QAAS,cACTkB,cAAe,cACfvB,WAAY,MACZyB,YAAa,MACb4B,UAAW,MACXiI,QAAS,UAETvI,YAAa8J,EAAa,GAAG5O,EAAMK,QAAQ,OAAU,MAErDF,SAAU,aAEZuP,UAAUxP,EAAAA,EAAAA,KAAI,CACZ0C,MAAO,WAkBMrC,CAAUP,EAAO4O,GAChC,OACE,kBAAC5M,MAAAA,CAAItB,WAAWwG,EAAAA,EAAAA,IAAGnH,EAAO2P,SAAUR,EAAenP,EAAOoP,oBAAiBjR,MACtEO,GACD,oCACE,kBAAC6O,OAAAA,CAAK5M,UAAWX,EAAOsP,MACtB,oCAAG5Q,IAEJyQ,GAAiC,iBAAVzQ,GAAsByD,EAAM+J,MAAMhL,OAAS0O,EAAAA,UAAUC,MAC3E,kBAAChD,EAAeA,CAAChJ,MAAO1B,EAAM0B,MAAOnF,MAAOA,EAAOoO,SAAU,c,cCxClE,SAASgD,EAAgB3N,G,IAMhBlE,EAJd,MAAM8R,EAAkC,iBAAhB5N,EAAMzD,QAAuBsR,MAAMlI,OAAO3F,EAAMzD,QAElEsB,EAxCiB,EAACC,EAAsB8P,KAAwB,CACtEE,iBAAiB9P,EAAAA,EAAAA,KAAI,CACnBuI,OAAQ,OACRwH,WAAY,IACZ5C,QAAS,EACTzK,MAAO,SAETsN,aAAahQ,EAAAA,EAAAA,KAAI,CACfkD,WAAYpD,EAAMgF,OAAO5B,WAAW6B,UACpCiI,UAAWlN,EAAMmN,QAAQgD,GACzB/N,QAAS,OACTkB,cAAewM,EAAW,cAAgB,MAC1CrH,OAAQ,OACRP,KAAM,EACNmF,QAAS,KAAKrN,EAAMK,QAAQ,MAC5BF,SAAU2P,EAAW,WAAa,SAClCxH,OAAQ,IAEV8H,SAASlQ,EAAAA,EAAAA,KAAI,CACX,eAAgB,CACdsD,YAAasM,EAAW,IAAM,QAEhC,UAAW,CACTb,MAAOjP,EAAMgF,OAAOqL,KAAK9B,KACzBzL,OAAQ,WAEVuK,QAAS,YAEXiD,eAAepQ,EAAAA,EAAAA,KAAI,CACjBqQ,aAAc,MACdnO,QAAS,cACTiK,OAAQ,EACR/J,SAAU,SACVkO,cAAe,aAOFjQ,EADDC,EAAAA,EAAAA,aACkBsP,IAC1B,UAAE9R,EAAS,UAAEG,GAAcO,IAC3B+R,EAAQzS,SAAkB,QAAlBA,EAAAA,EAAW0S,eAAX1S,IAAAA,OAAAA,EAAAA,EAAoB2S,OAAOzO,EAAMgK,UACzC0E,EAAY5S,aAAAA,EAAAA,EAAW6S,UAAUF,OAAOzO,EAAMgK,WAC7C4E,EAAcC,IAAmBrH,EAAAA,EAAAA,WAAS,GAC3CsH,GAAU1F,EAAAA,EAAAA,aAAY,IACtBnN,GACK8S,EAAAA,EAAAA,IAAqB,eAAgB,CAAEjI,GAAIyH,EAAOS,IAAKhP,EAAMgK,UAAY/N,GAE3E,GACN,CAACsS,EAAOvO,EAAMgK,SAAU/N,IAC3B,OACE,oCACE,kBAAC6D,MAAAA,CAAItB,UAAWX,EAAOmQ,aACrB,kBAAClO,MAAAA,CAAItB,UAAWX,EAAOqQ,SACrB,kBAAC3P,EAAAA,WAAUA,CACT0Q,cAAaC,EAAAA,EAAQC,MAAMC,YAC3B5Q,UAAWX,EAAOuQ,cAClBzP,QAAQ,gBACR0Q,QAAQ,YACRlN,aAAW,gBACXmN,iBAAiB,MACjB1Q,KAAK,KACLF,KAAK,MACLD,QAAS,IAAMoQ,GAAgB,GAC/BpE,SAAU,KAGd,kBAAC3K,MAAAA,CAAItB,UAAWX,EAAOqQ,SACrB,kBAACqB,EAAAA,gBAAeA,CACd/Q,UAAWX,EAAOiQ,gBAClB0B,KAAK,YACLH,QAAQ,YACRxD,KAAK,OACLjN,KAAK,KACLD,QAAQ,wBACR2Q,iBAAiB,MACjB7E,SAAU,EACVqE,QAASA,MAIf,oCACGF,GACC,kBAACa,EAAAA,MAAKA,CAACC,UAAW,IAAMb,GAAgB,GAAQc,QAAQ,EAAMvN,MAAM,iBAClE,kBAACwN,MAAAA,KAAKlB,GACN,kBAACe,EAAAA,MAAMI,UAAS,KACd,kBAACN,EAAAA,gBAAeA,CAACC,KAAK,OAAOV,QAAS,IAAM9O,EAAMzD,OAAiB,wBASjF,CC9FA,MAwBauT,EAAwB9P,I,IAUV+P,EATzB,IAAIxT,EAAQyD,EAAMzD,MAClB,MAAMwN,EAAQ/J,EAAM+J,MACdiG,EAAejG,EAAM7J,QAAS3D,GAE9BsB,EA7BU,CAACC,IAAiD,CAClEuP,SAASrP,EAAAA,EAAAA,KAAI,CACXkC,QAAS,OACTqG,OAAQ,OACRnG,SAAU,SACVnC,SAAU,aAEZgS,UAAUjS,EAAAA,EAAAA,KAAI,CACZwC,WAAY,aACZN,QAAS,SAEXgQ,aAAalS,EAAAA,EAAAA,KAAI,CACf,UAAW,CACTmS,eAAgB,aAElBpD,MAAOjP,EAAMgF,OAAOqL,KAAK9B,KACzBxM,WAAY,MACZqD,UAAW,UAYE7E,EADDC,EAAAA,EAAAA,aACkB0B,EAAM+J,MAAMhL,OACtC,UAAE8J,EAAS,mBAAEE,GAAuBM,IAGpC2F,EAAM,CAAEtL,MAAO1D,EAAMgK,UACrBoG,EAAWC,QAAkCrB,QAA1Be,GAAAA,EAAAA,EAAAA,cAAa/P,EAAM+J,MAAOiF,UAA1Be,IAAAA,OAAAA,EAAAA,EAAgCnM,QAEzD,GAAc,OAAVrH,EACF,OAAO,qCAIPA,EADE+T,IAAAA,eAAqBtQ,EAAMzD,OACrByD,EAAMzD,MACY,iBAAVA,EACRoL,KAAKC,UAAU5H,EAAMzD,QAErBgU,EAAAA,EAAAA,wBAAuBP,GAOjC,OACE,kBAACrG,EAAwBA,CACvBlL,QAAS,IACHuB,EAAMgK,WAAanB,EAAUnF,OAAS1D,EAAM+J,MAAMrL,OAASmK,EAAUzB,UAChE2B,EAAmB,CAAErF,MAAO,OAE9BqF,EAAmB,CAAE3B,UAAWpH,EAAM+J,MAAMrL,KAAMgF,MAAO1D,EAAMgK,SAAUlB,kBAAmB,IAErGiB,MAAO/J,EAAM+J,MACbC,SAAUhK,EAAMgK,UAEhB,kBAAClK,MAAAA,CAAItB,UAAWX,EAAOwP,SACC,IAArBrN,EAAMwQ,YAAoB,kBAAC7C,EAAeA,CAACpR,MAAOA,EAAOyN,SAAUhK,EAAMgK,WAC1E,kBAAClK,MAAAA,CAAItB,UAAWX,EAAOoS,YAErBG,GAnBY,EAAC7T,EAAwCmF,IACpD,kBAAC+K,EAAWA,CAAC1C,MAAO/J,EAAM+J,MAAOC,SAAUhK,EAAMgK,SAAUtI,MAAOA,EAAOnF,MAAOA,IAkBrEkU,CAAYlU,EAAOwN,EAAMrL,MAEtC0R,GAAYrG,EAAM2G,UACjB,kBAACC,EAAAA,qBAAoBA,CAACvE,MAAO,K,IAAM2D,E,OAAoBf,QAApBe,GAAAA,EAAAA,EAAAA,cAAahG,EAAOiF,UAApBe,IAAAA,EAAAA,EAA4B,KAC3Da,GACIA,EAAIC,SAEJ,kBAACnK,SAAAA,CAAOlI,UAAWX,EAAOqS,YAAazR,QAASmS,EAAIC,UAClD,oCAAGtU,IAKL,kBAACuD,MAAAA,CAAItB,UAAWX,EAAOqS,aACrB,oCAAG3T,QCzFVuU,GAAkB,KACtBC,EAAAA,EAAAA,YAAYjT,IACV,CACLkT,YAAYhT,EAAAA,EAAAA,KAAI,CACd,UAAW,CACTiT,QAAS,QAEXhQ,WAAY,OACZC,WAAY,OACZC,OAAQ,OACR4L,MAAO,UACPnM,OAAQ,UACRsQ,KAAM,UACNnD,WAAY,SACZ5D,OAAQ,EACRgH,oBAAqB,UACrBhG,QAAS,EACTiG,UAAW,UACXC,iBAAkB,OAClBC,oBAAqB,e,o4BCyC7B,SAASC,GAAiBvR,GASxB,MAAMlC,GAAQQ,EAAAA,EAAAA,cACR,WAAE0S,GAAeF,KAEvB,IAAIpE,EACJ,GAAI1M,EAAM0B,QAAUiL,EAAAA,EAAY,CAC9B,MAAMC,EAAWC,KAAmBC,QAChC9M,EAAMzD,SAASqQ,IACjBF,EAAaE,EAAS5M,EAAMzD,OAAOwQ,MAEvC,CAEA,MAAMlP,EAxDU,EAACC,EAAsB4O,KAAyB,CAChE8E,YAAYxT,EAAAA,EAAAA,KAAI,CAAC,GACjBmP,MAAMnP,EAAAA,EAAAA,KAAI,CACRkC,QAAS,cACTuR,KAAM,WACNrQ,cAAe,SACfvB,WAAY/B,EAAMK,QAAQ,IAC1BmD,YAAaxD,EAAMK,QAAQ,IAC3B+E,UAAWpF,EAAMK,QAAQ,IACzBF,SAAU,aAEZyT,WAAW1T,EAAAA,EAAAA,KAAI,CACb,WAAY,CACVoP,gBAAiBV,EACjBW,QAAS,KACT9G,OAAQ,OACRP,KAAM,EACN/H,SAAU,WACVG,IAAK,EACLsC,MAAO,GAAG5C,EAAMK,QAAQ,QAE1B,UAAW,CACTgD,OAAQ,aAAarD,EAAMgF,OAAO3B,OAAOmM,UAE3CF,gBAAiB,cACjBjM,OAAQ,aAAarD,EAAMgF,OAAO3B,OAAOoM,OAEzC3M,OAAQ,UAERuK,QAAS,kBACTvI,YAAa8J,EAAa,GAAG5O,EAAMK,QAAQ,OAAU,GAAGL,EAAMK,QAAQ,MAEtEF,SAAU,eAwBGI,CAAUP,EAAO4O,GAEhC,OACE,kBAAChG,SAAAA,CACClI,WAAWwG,EAAAA,EAAAA,IAAGgM,EAAYnT,EAAOsP,KAAMnN,EAAM2R,WAAa9T,EAAO2T,gBAAaxV,GAC9EyC,QAASuB,EAAMvB,SAEf,kBAAC2M,OAAAA,CAAK5M,UAAWX,EAAO6T,WACrB1R,EAAM0B,MAAM,IAAE1B,EAAMzD,OAEtByD,EAAM2R,YACL,kBAACjH,EAAeA,CACdC,SAAU,UACVU,UAAWrL,EAAMqL,UACjBe,MAAOpM,EAAMoM,MACb1K,MAAO1B,EAAM0B,MACbnF,MAAOyD,EAAMzD,MACbmP,WAAY1L,EAAM4R,aAK5B,CAEO,MAAMC,GAAe7R,IAC1B,MAAM,MAAE0B,GAAU1B,GACZ,UAAE6I,EAAS,mBAAEE,GAAuBM,KACpC,QAAErM,EAAO,WAAEgK,IAAe9J,EAAAA,EAAAA,MAC1BX,EAAQyD,EAAMzD,MACduV,GAAcC,EAAAA,EAAAA,kBACdC,GAAUC,EAAAA,EAAAA,SAAQ,IAAMH,EAAYE,QAAQE,KAAKJ,GAAc,CAACA,IAGhE/H,EAAQ/J,EAAM+J,MAEpB,IAAKA,IAASA,aAAAA,EAAAA,EAAOhL,QAAS0O,EAAAA,UAAU0E,MACtC,OAAO,KAET,MAAMnD,EAAM,CAAEtL,MAAO1D,EAAMgK,UAEvBhK,EAAMoS,eAAiBpS,EAAMqS,gBAAkBrS,EAAMsS,gBACvDtS,EAAMoS,cAAc1B,UAAW6B,EAAAA,EAAAA,kBAAiBvS,EAAMsS,cAAetS,EAAMoS,cAAe,CAAC,EAAGJ,IAGhG,MAAM5F,EAAQpM,EAAMoS,gBAAiBrC,EAAAA,EAAAA,cAAa/P,EAAMoS,cAAepD,GA4BvE,OACE,kBAACuC,GAAAA,CACC9S,QAAS,IAELuB,EAAMgK,WAAanB,EAAUnF,OAC7BqG,EAAMrL,OAASmK,EAAUzB,WACzB1F,IAAUmH,EAAU2J,aAEbzJ,EAAmB,CAAErF,MAAO,OAG9BqF,EAAmB,CACxB3B,UAAW2C,EAAMrL,KACjBgF,MAAO1D,EAAMgK,SACblB,kBAAmB9I,EAAMqS,eAAiB,EAAI,EAC9CG,aAAc9Q,IAGlBiQ,WACE9I,EAAUnF,QAAU1D,EAAMgK,UAAYnB,EAAUzB,YAAc2C,EAAMrL,MAAQmK,EAAU2J,eAAiB9Q,EAEzG2J,UAAWrL,EAAMqS,eAAiB,eAAYrW,EAC9C0F,MAAOA,EACPnF,MAAOA,EACPqV,WAAY,IA7CU,CAACxK,IACzB,MAAMqL,EAAiB,MAAKzV,GAEtB4G,EAAStE,OAAOC,KAAKvC,GAASnB,OAAQoM,GAAMjL,EAAQiL,GAAGpG,QAAQ+B,OACjE6O,EAAerL,GAAWvF,OAC5B4Q,EAAerL,GAAa,SACvBqL,EAAerL,IAAU,CAC5BvF,QAAQ,EACR6B,WAAO1H,IAGTyW,EAAerL,GAAa,SACvBqL,EAAerL,IAAU,CAC5BvF,QAAQ,EACR6B,MAAOE,IAIXoD,EAAWyL,IA2BSC,CAAkBhR,GACpC0K,MAAOA,KC7KN,SAASuG,GAAe3S,GAC7B,MAAMlC,GAAQQ,EAAAA,EAAAA,aACRT,EAASQ,GAAUP,GACzB,OACE,kBAACgC,MAAAA,CAAImP,cAAaC,EAAAA,EAAQC,MAAMyD,WAAYpU,UAAWX,EAAO+U,YAC5D,oCAAG5S,EAAMzD,OAGf,CAEO,MAAM8B,GAAY,CAACP,EAAsBoM,KAAsB,CACpE0I,YAAY5U,EAAAA,EAAAA,KAAI,CACd6U,WAAY/U,EAAM6B,WAAWmT,oBAC7BpT,SAAU5B,EAAM6B,WAAWkB,UAAUnB,SACrC6G,OAAQ,OACRwH,WAAY,OACZnL,YAAa9E,EAAMK,QAAQ,GAC3B+H,aAAcpI,EAAMK,QAAQ,SCa1B4U,GAAcjM,I,IAEVA,EADRA,EAAAA,SAAW,QAAXA,EAAAA,EAAIkM,eAAJlM,IAAAA,GAAAA,EAAamM,SAAS,CACpBjN,KAAgB,QAAVc,EAAAA,EAAGkM,eAAHlM,IAAAA,OAAAA,EAAAA,EAAYoM,cAoBf,SAASC,IAAWC,YAAajP,IACtC,MACMtG,EAxDU,CAACC,IAA0B,CAC3CuV,SAAUrV,EAAAA,GAAG;;;;;;;;;;;;IAabkV,WAAYlV,EAAAA,GAAG;;kBAECF,EAAMgF,OAAO5B,WAAW+B;;;oBAGtBnF,EAAMgF,OAAO5B,WAAW6B;;IAG1CuQ,YAAatV,EAAAA,GAAG;;kBAEAF,EAAMgF,OAAO5B,WAAW+B;;;oBAGtBnF,EAAMgF,OAAO5B,WAAW6B;;MA6B3B1E,EADDC,EAAAA,EAAAA,cAEd,OACE,kBAACwB,MAAAA,CAAItB,UAAWX,EAAOwV,UACrB,kBAACjI,OAAAA,CAAKmI,cAAe,KArBZ,IAACzM,EACdA,EAAAA,OADcA,EAqBwB3C,IApB3B,QAAX2C,EAAAA,EAAIkM,eAAJlM,IAAAA,GAAAA,EAAamM,SAAS,CACpBO,SAAU,SACVxN,KAAM,EACN5H,IAAK,KAiBqCqV,YAAa,IAAMV,GAAW5O,GAAM3F,UAAWX,EAAOqV,YAC5F,kBAAChR,EAAAA,KAAIA,CAACxD,KAAM,gBAEd,kBAAC0M,OAAAA,CAAKmI,cAAe,KAhBX,IAACzM,EACfA,EAAAA,OADeA,EAgBwB3C,IAf5B,QAAX2C,EAAAA,EAAIkM,eAAJlM,IAAAA,GAAAA,EAAamM,SAAS,CACpBO,SAAU,SACVxN,KAAMc,EAAGkM,QAAQU,YACjBtV,IAAK,KAYsCqV,YAAa,IAAMV,GAAW5O,GAAM3F,UAAWX,EAAOyV,aAC7F,kBAACpR,EAAAA,KAAIA,CAACxD,KAAM,iBAIpB,CC/CO,MAAMiV,GAAwB3T,IACnC,IAAIzD,EAAQyD,EAAMzD,MAClB,MAAMwN,EAAQ/J,EAAM+J,MACdiG,EAAejG,EAAM7J,QAAS3D,GAC9BuB,GAAQQ,EAAAA,EAAAA,aACRT,EAASQ,GAAUP,IACnB,UAAE8V,EAAS,QAAE5W,IAAYE,EAAAA,EAAAA,OACzB,UAAEpB,GAAcU,KACfqX,EAASC,IAActM,EAAAA,EAAAA,WAAS,GACjCrD,GAAM4P,EAAAA,EAAAA,QAA8B,MAGxCxX,EADE+T,IAAAA,eAAqBtQ,EAAMzD,OACrByD,EAAMzD,MACY,iBAAVA,EACRoL,KAAKC,UAAU5H,EAAMzD,QAErBgU,EAAAA,EAAAA,wBAAuBP,GAOjC,MAkFMvO,EAlFe,CAACA,GACKnC,OAAOC,KAAKvC,GAElCnB,OAAQ6C,GAASA,KAASsV,EAAAA,EAAAA,IAAYlY,IACtCuI,KAAK,CAACC,EAAGC,IAEJD,IAAMqI,EAAAA,GACA,EAENpI,IAAMoI,EAAAA,EACD,EAGe,eAApB3P,EAAQsH,GAAGvF,MACL,EAEc,eAApB/B,EAAQuH,GAAGxF,KACN,EAIF/B,EAAQsH,GAAGtC,YAAchF,EAAQuH,GAAGvC,aAAe,EAAI,GAGhCnG,OAC/B6F,IAEE1E,EAAQ0E,GAAOG,QAEhB7E,EAAQ0E,GAAOM,YAAc,GAI9B2C,IAAKjD,I,IAEuB5F,EAD3B,MAAMmY,EAAaxS,EAAOC,GACpBwS,EAAqBpY,SAAc,QAAdA,EAAAA,EAAWqY,WAAXrY,IAAAA,OAAAA,EAAAA,EAAgBsY,OAAOC,KAAMtK,GAAUA,EAAMrL,OAASgD,GAC3E4S,EAAWvK,aAAAA,EAAAA,EAAO0E,OAAOzO,EAAMgK,UAC/BuK,GAAaN,KAAgBK,EAGnC,GAAIL,EACF,OACE,kBAACpC,GAAWA,CACVS,mBAAetW,EACf+N,MAAOA,EACP/M,QAASA,EACTgN,SAAUhK,EAAMgK,SAChBwK,MAAOxU,EAAMwU,MACb1P,IAAKpD,EACLA,MAAOA,EACP2Q,gBAAgB,EAChB9V,MAAO0X,IAMb,GAAIM,IAAaL,aAAAA,EAAAA,EAAoBxV,MAAM,CACzC,MAAM+V,EAAqBP,aAAAA,EAAAA,EAAoBzF,OAAOzO,EAAMgK,UAC5D,IAAIkK,aAAAA,EAAAA,EAAoBnV,QAAS0O,EAAAA,UAAUiH,QAAUD,EACnD,OACE,kBAAC5C,GAAWA,CACVS,cAAexW,aAAAA,EAAAA,EAAWqY,IAC1B/B,cAAe8B,EACfnK,MAAOA,EACPxN,MAAOkY,EACPzX,QAASA,EACTgN,SAAUhK,EAAMgK,SAChBwK,MAAOxU,EAAMwU,MACb1P,IAAKoP,EAAmBxV,KACxBgD,MAAOwS,EAAmBxV,KAC1B2T,gBAAgB,GAIxB,CAEA,OAAO,OAERxW,OAAQ8Y,GAAMA,GAGJC,CAAa5U,EAAMyB,QAC5BoT,EAASjB,IAAckB,EAAAA,GAAaC,KACpCC,EAAYvT,EAAOmC,OAAS,EAElC,OACE,kBAAC+F,EAAwBA,CACvBG,UAAW,KACTgK,GAAW,IAEbjK,WAAY,KACViK,GAAW,IAEb9J,SAAUhK,EAAMgK,SAChBD,MAAO/J,EAAM+J,OAEb,kBAACkL,EAAAA,eAAcA,CAAC7Q,SAAUD,EAAK+Q,MAAM,cACnC,kBAACpV,MAAAA,CAAItB,UAAWX,EAAOwP,SAEC,IAArBrN,EAAMwQ,YAAoB,kBAAC7C,EAAeA,CAAC3D,SAAUhK,EAAMgK,SAAUzN,MAAOA,IAE5EsY,GAAUG,GAAa,oCAAGvT,GAC1BmS,IAAckB,EAAAA,GAAarT,QAAUuT,GAAa,oCAAGvT,GACrDmS,IAAckB,EAAAA,GAAarT,SAAWuT,GAAa,kBAACrC,GAAcA,CAACpW,MAAOA,IAE1EsY,IAAWG,GAAa,kBAACrC,GAAcA,CAACpW,MAAOA,IAC/CqX,IAAckB,EAAAA,GAAa3G,MAAQ,kBAACwE,GAAcA,CAACpW,MAAOA,IAC1DsX,GAAW,kBAACV,GAAQA,CAACC,YAAajP,QAOhC9F,GAAaP,IAA0B,CAClDuP,QAASrP,EAAAA,GAAG;;;;;;;;;;;;;;;;;;;;;;6EAsB+DF,EAAMgF,OAAO5B,WAAW+B;;gCChL9F,SAASkS,IAAoB,oBAAE3L,EAAmB,SAAEnN,IACzD,MAAMwB,GAASkT,EAAAA,EAAAA,YAAW1S,IACpB8F,GAAM4P,EAAAA,EAAAA,QAAiC,MAI7C,OAHAqB,EAAAA,EAAAA,WAAU,K,IACRjR,EAAW,QAAXA,EAAAA,EAAI6O,eAAJ7O,IAAAA,GAAAA,EAAakR,SACZ,IAED,kBAACC,EAAAA,oBAAmBA,CAACC,oBAAoB,EAAO9W,QAAS,IAAM+K,GAAoB,GAAQgM,YAAY,GACrG,kBAAC1V,MAAAA,CAAItB,UAAWX,EAAO4X,iBACrB,kBAAClX,EAAAA,WAAUA,CACT4F,IAAKA,EACL3F,UAAWX,EAAO6X,YAClBvT,cAAYwT,EAAAA,GAAAA,GAAE,0BAA2B,SACzCjX,KAAM,QACND,QAAS,IAAM+K,GAAoB,KAEpCnN,GAIT,CAEA,MAAMgC,GAAaP,IAA0B,CAC3C4X,aAAa1X,EAAAA,EAAAA,KAAI,CACfC,SAAU,WACVG,IAAK,OACLF,MAAO,QAETuX,iBAAiBzX,EAAAA,EAAAA,KAAI,CACnBkC,QAAS,QACTjC,SAAU,SACVmP,gBAAiBtP,EAAMgF,OAAO5B,WAAW+B,QACzC9B,OAAQ,aAAarD,EAAMgF,OAAO3B,OAAOoM,OACzCc,aAAcvQ,EAAM8X,MAAMC,OAAOC,QACjC9K,UAAWlN,EAAMmN,QAAQC,GACzB3E,OAAQ,OACR7E,MAAO,kBACPyI,OAAQrM,EAAMK,QAAQ,EAAG,GACzB4X,UAAW,QACXlL,SAAU,QACVM,QAASrN,EAAMK,QAAQ,GACvBuC,MAAO,W,q5BC9BX,MAwDasV,GAAmBhW,IAC9B,MAAM,mBAAEuJ,EAAkB,oBAAEC,IZrDrB/M,EAAAA,EAAAA,YAAW6M,IYsDZ,UAAExN,GAAcU,IAChByZ,GAAmBlC,EAAAA,EAAAA,QAAiC,MAEpDlW,EA7DU,EAACC,EAAsBoY,EAAwBC,KAAqB,CACpFT,aAAa1X,EAAAA,EAAAA,KAAI,CACfC,SAAU,WACVG,IAAK,OACLF,MAAO,QAETkY,aAAapY,EAAAA,EAAAA,KAAI,CACf6B,WAAY,QAEdwW,uBAAuBrY,EAAAA,EAAAA,KAAI,CACzBsY,WAAYJ,EAAgB,aAAapY,EAAMgF,OAAO3B,OAAOoM,OAAS,OACtErN,QAAS,OACTL,WAAYqW,EAAgB,OAAS,EACrCtT,YAAasT,EAAgB,OAAS,IAExCK,WAAWvY,EAAAA,EAAAA,KAAI,CACbkC,QAAS,OACTwB,MAAO,aACPhB,MAAO,sBAET8V,eAAexY,EAAAA,EAAAA,KAAI,CACjB6B,WAAY,QAEd4W,YAAYzY,EAAAA,EAAAA,KAAI,CACdkC,QAAS,OACTwB,MAAO,cACPJ,YAAa,QAEfmU,iBAAiBzX,EAAAA,EAAAA,KAAI,CACnBkC,QAAS,QACTjC,SAAU,SACVmP,gBAAiBtP,EAAMgF,OAAO5B,WAAW+B,QACzC9B,OAAQ,aAAarD,EAAMgF,OAAO3B,OAAOoM,OACzCc,aAAcvQ,EAAM8X,MAAMC,OAAOC,QACjC9K,UAAWlN,EAAMmN,QAAQC,GACzB3E,OAAQ,OACR7E,MAAO,kBACPyI,OAAQrM,EAAMK,QAAQ,EAAG,GACzB4X,UAAW,QACXlL,SAAU,QACVM,QAASrN,EAAMK,QAAQ,GACvBuC,MAAO,SAETgW,SAAS1Y,EAAAA,EAAAA,KAAI,CAEX2Y,YAAa,aAAa7Y,EAAMgF,OAAO3B,OAAOoM,OAC9CrN,QAAS,OACTwB,MAAO,UACP7B,WAAYqW,EAAgB,OAAS,MAErC5U,YAAa,OAEbZ,MAAOyV,EAAS,mBAAqB,WASxB9X,EADDC,EAAAA,EAAAA,aACuC,IAArB0B,EAAMwQ,WAAkBxQ,EAAM+J,MAAMrL,QAASsV,EAAAA,EAAAA,IAAYlY,KACnF,UAAE8X,EAAS,eAAEtR,EAAc,aAAEsU,EAAY,kBAAErU,IAAsBrF,EAAAA,EAAAA,MACjE2Z,EAAc7W,EAAM+J,MAAMrL,QAASsV,EAAAA,EAAAA,IAAYlY,GAE/Cgb,EAAkB,KACtBF,EAAahD,IAAckB,EAAAA,GAAa3G,KAAO2G,EAAAA,GAAarT,OAASqT,EAAAA,GAAa3G,OAGpF,OACE,kBAAC/C,OAAAA,CAAK5M,UAAWX,EAAO6Y,SACtB,kBAACtL,OAAAA,CAAK5M,UAAWX,EAAO0Y,WACtB,kBAACnL,OAAAA,CAAK5M,UAAWX,EAAOwY,uBAAwBrW,EAAM+W,gBACrDzU,GAAkBC,QAA4DvG,KAAvCsG,aAAAA,EAAAA,EAAiBtC,EAAM+J,MAAMrL,QACnE,kBAACH,EAAAA,WAAUA,CACTI,QAAS,qBACT2Q,iBAAkB,MAClB9Q,UAAWX,EAAOuY,YAClBjU,aAAY,qBACZzD,KAAM,IACND,QAAS,KACP,MAA6C,E,mUAAA,IAAK6D,GAAzCtC,EAAAA,EAAM+J,MAAMrL,MAAb,CAACsB,GAAmBgX,GAAiB,EAARrS,EAAAA,GAAQ,GAApC3E,G,SACTuC,SAAAA,EAAoBoC,IACpBsS,EAAAA,GAAAA,IACEC,GAAAA,GAAkBC,gBAClBC,GAAAA,GAAoBD,gBAAgBE,4CAK3CR,GACC,oCACGjD,IAAckB,EAAAA,GAAa3G,KAC1B,kBAAC5P,EAAAA,WAAUA,CACT+Q,iBAAkB,MAClB3Q,QAAS,kBACTwD,aAAY,kBACZ1D,QAAS,KACPqY,KACAG,EAAAA,GAAAA,IACEC,GAAAA,GAAkBC,gBAClBC,GAAAA,GAAoBD,gBAAgBG,0CAGxC9Y,UAAWX,EAAO2Y,cAClB9X,KAAM,UACNE,KAAM,OAGR,kBAACL,EAAAA,WAAUA,CACT+Q,iBAAkB,MAClB3Q,QAAS,gBACTwD,aAAY,gBACZ1D,QAAS,KACPqY,KACAG,EAAAA,GAAAA,IACEC,GAAAA,GAAkBC,gBAClBC,GAAAA,GAAoBD,gBAAgBI,wCAGxC/Y,UAAWX,EAAO2Y,cAClB9X,KAAM,cACNE,KAAM,SAMhB,kBAACwM,OAAAA,CAAK5M,UAAWX,EAAO4Y,YACtB,kBAAClY,EAAAA,WAAUA,CACTI,QAAS,QAAQqB,EAAM+J,MAAMrL,YAC7B4Q,iBAAkB,MAClBnL,IAAK8R,EACL9T,aAAY,QAAQnC,EAAM+J,MAAMrL,YAChCD,QAAUS,IACRsK,GAAqBD,IACrB0N,EAAAA,GAAAA,IACEC,GAAAA,GAAkBC,gBAClBC,GAAAA,GAAoBD,gBAAgBK,iCAGxC9Y,KAAM,gBAITuX,EAAiBjD,SAChB,kBAACyE,EAAAA,QAAOA,CACNC,KAAMnO,EACN8D,QACE,kBAAC8H,GAAmBA,CAClB3L,oBAAsB3H,I,IAEpBoU,EADAzM,EAAoB3H,GACI,QAAxBoU,EAAAA,EAAiBjD,eAAjBiD,IAAAA,GAAAA,EAA0BZ,UAG3BrV,EAAM3D,UAGX4Z,iBAAkBA,EAAiBjD,Y,q5BCtKtC,SAAS2E,GAAoB3X,GASlC,MAAM,UAAE4T,EAAS,QAAE5W,EAAO,aAAE4Z,EAAY,WAAE5P,EAAU,eAAE1E,EAAc,kBAAEC,IAAsBrF,EAAAA,EAAAA,OACtF,UAAEpB,GAAcU,IAChBqB,EAASQ,MACT,WAAE2S,GAAeF,KAEjB8G,GAAaxO,EAAAA,EAAAA,aAChBW,IACC,MAAM8N,E,mUAAqB,IAAK7a,GAqBhC,GAnBmCsC,OAAOC,KAAKsY,GAC5Chc,OAAQic,IACP,MAAMC,EAAcF,EAAmBC,GAAKpU,MACtC8M,EAAaqH,EAAmB9N,EAAMrL,MAAMgF,MAClD,OAAOmU,EAAmBC,GAAKjW,QAAU2O,GAAcuH,GAAeA,EAAcvH,IAErF7L,IAAKqT,GAASH,EAAmBG,IAETva,QAASqa,SAChB9b,IAAd8b,EAAIpU,OACNoU,EAAIpU,UAIRmU,EAAmB9N,EAAMrL,MAAMmD,QAAS,EACxCgW,EAAmB9N,EAAMrL,MAAMgF,WAAQ1H,EACvCgL,EAAW6Q,QAGwB7b,IAA/BsG,EAAeyH,EAAMrL,MAAqB,CAC5C,MAASqL,EAAAA,EAAMrL,MAAP,CAACqL,GAAaiN,GAAmC1U,EAA1B2V,EAAAA,GAA0B3V,EAAAA,CAAhDyH,G,SACTxH,EAAkB0V,EACpB,GAEF,CAACjb,EAASgK,EAAY1E,EAAgBC,IAGlCsU,EAAc7W,EAAMkY,YAAYnO,MAAMrL,QAASsV,EAAAA,EAAAA,IAAYlY,GAEjE,OACE,kBAACka,GAAoBhW,EAAMkY,YACzB,kBAACpY,MAAAA,CAAItB,UAAWX,EAAOsa,UACrB,kBAACzR,SAAAA,CACClI,WAAWwG,EAAAA,EAAAA,IAAGgM,EAAYnT,EAAOwO,MACjC5N,QAAS,KACPmZ,EAAW5X,EAAMkY,YAAYnO,QAC7BkN,EAAAA,GAAAA,IACEC,GAAAA,GAAkBC,gBAClBC,GAAAA,GAAoBD,gBAAgBiB,yCAIxC,kBAACzM,MAAAA,CACCG,MAAM,6BACNF,QAAQ,YACRlL,MAAM,KACN6F,OAAO,KACP/H,UAAU,mBAEV,kBAACuN,OAAAA,CACCC,SAAS,UACTC,SAAS,UACTC,EAAE,+sBACFL,KAAK,UACLM,YAAY,MAEd,kBAACJ,OAAAA,CACCG,EAAE,iwCACFL,KAAK,UACLM,YAAY,OAEV,kBAITnM,EAAMqY,WACL,kBAACvY,MAAAA,CAAItB,UAAWX,EAAOsa,UACrB,kBAACzR,SAAAA,CACClI,WAAWwG,EAAAA,EAAAA,IAAGgM,EAAYnT,EAAOwO,MACjC5N,QAAS,K,IACPuB,EAAe,QAAfA,EAAAA,EAAMqY,iBAANrY,IAAAA,GAAAA,EAAAA,KAAAA,EAAkBhD,IAClBia,EAAAA,GAAAA,IACEC,GAAAA,GAAkBC,gBAClBC,GAAAA,GAAoBD,gBAAgBmB,wCAIxC,kBAACpW,EAAAA,KAAIA,CAAC1D,WAAWwG,EAAAA,EAAAA,IAAGnH,EAAO2R,KAAM3R,EAAO0a,SAAU7Z,KAAM,mBAAoBE,KAAM,OAAQ,cAK/FoB,EAAMwY,YACL,kBAAC1Y,MAAAA,CAAItB,UAAWX,EAAOsa,UACrB,kBAACzR,SAAAA,CACClI,WAAWwG,EAAAA,EAAAA,IAAGgM,EAAYnT,EAAOwO,MACjC5N,QAAS,K,IACPuB,EAAgB,QAAhBA,EAAAA,EAAMwY,kBAANxY,IAAAA,GAAAA,EAAAA,KAAAA,EAAmBhD,IACnBia,EAAAA,GAAAA,IACEC,GAAAA,GAAkBC,gBAClBC,GAAAA,GAAoBD,gBAAgBsB,yCAIxC,kBAACvW,EAAAA,KAAIA,CAAC1D,UAAWX,EAAO2R,KAAM9Q,KAAM,mBAAoBE,KAAM,OAAQ,eAK3EiY,GACC,kBAAC/W,MAAAA,CAAItB,UAAWX,EAAOsa,UACrB,kBAACzR,SAAAA,CACClI,WAAWwG,EAAAA,EAAAA,IAAGgM,EAAYnT,EAAOwO,MACjC5N,QAAS,KACHmV,IAAckB,EAAAA,GAAa3G,KAC7ByI,EAAa9B,EAAAA,GAAarT,QAE1BmV,EAAa9B,EAAAA,GAAa3G,OAG5B8I,EAAAA,GAAAA,IACEC,GAAAA,GAAkBC,gBAClBC,GAAAA,GAAoBD,gBAAgBuB,sCACpC,CACEC,MAAO/E,IAAckB,EAAAA,GAAa3G,KAAO2G,EAAAA,GAAarT,OAASqT,EAAAA,GAAa3G,SAKjFyF,IAAckB,EAAAA,GAAa3G,KAC1B,kBAACjM,EAAAA,KAAIA,CAAC1D,UAAWX,EAAO2R,KAAM9Q,KAAM,iBAAkBE,KAAM,OAE5D,kBAACsD,EAAAA,KAAIA,CAAC1D,UAAWX,EAAO2R,KAAM9Q,KAAM,cAAeE,KAAM,OAG1DgV,IAAckB,EAAAA,GAAa3G,KAAO,cAAgB,kBAKxDnO,EAAM4Y,kBACL,kBAAC9Y,MAAAA,CAAItB,UAAWX,EAAOsa,UACrB,kBAACzR,SAAAA,CACClI,WAAWwG,EAAAA,EAAAA,IAAGgM,EAAYnT,EAAOwO,MACjC5N,QAAS,K,IACPuB,EAAsB,QAAtBA,EAAAA,EAAM4Y,wBAAN5Y,IAAAA,GAAAA,EAAAA,KAAAA,IACAiX,EAAAA,GAAAA,IACEC,GAAAA,GAAkBC,gBAClBC,GAAAA,GAAoBD,gBAAgB0B,yCAIxC,kBAAC3W,EAAAA,KAAIA,CAAC1D,UAAWX,EAAO2R,KAAM9Q,KAAM,WAAYE,KAAM,OAAQ,wBAO1E,CAEA,MAAMP,GAAY,KACT,CACLmR,MAAMxR,EAAAA,EAAAA,KAAI,CACRsD,YAAa,SAEf+K,MAAMrO,EAAAA,EAAAA,KAAI,CACRiI,cAAe,MACfE,WAAY,QAEdgS,UAAUna,EAAAA,EAAAA,KAAI,CAAC,GACfua,SAASva,EAAAA,EAAAA,KAAI,CACX8a,UAAW,iB,8/BC9EjB,SAASC,GAAgB/Y,GASvB,OACE,kBAACgZ,EAAAA,MAAYA,CACXC,eAAgBjZ,EAAMkZ,SACtBC,gBAAiBnZ,EAAMjE,aACvBqd,WAAYC,EAAAA,GAAgBC,GAC5Blc,KAAM4C,EAAM5C,KACZmJ,OAAQvG,EAAMuG,OACd7F,MAAOV,EAAMU,MACb6Y,cAAe,CAAEC,WAAW,EAAMC,QAAS,CAAC,SAAU/B,MAAM,IAGlE,CAEO,MAAMgC,GAAS1Z,IACpB,MAAM,OAAEuG,EAAM,OAAE9E,EAAM,UAAE3F,EAAS,SAAE6d,EAAQ,MAAEjZ,GAAUV,EAEjDlC,GAAQQ,EAAAA,EAAAA,cAEPsb,EAAYC,IAAiBrS,EAAAA,EAAAA,eAAgCxL,IAC7D8d,EAAcC,IAAmBvS,EAAAA,EAAAA,UAAS,MAC1C5K,EAAyBod,IAA8BxS,EAAAA,EAAAA,WAAS,GACjEyS,EAAavZ,GAAS9D,EAA0B,GAAKkd,GACrDjc,EA/FU,EAACC,EAAsByI,EAAgB2T,KAA0B,CAEjFC,uBAAuBnc,EAAAA,EAAAA,KAAI,CACzBwC,WAAY,SACZN,QAAS,OACTkB,cAAe,SACfX,eAAgB,aAChByF,aAAcpI,EAAMK,QAAQ,GAC5BgI,WAAYrI,EAAMK,QAAQ,GAC1BuC,MAAO,oBAET3C,4BAA4BC,EAAAA,EAAAA,KAAI,CAC9B,UAAW,CACTkD,WAAYpD,EAAMgF,OAAO5B,WAAW+B,QACpCmX,YAAatc,EAAMgF,OAAO3B,OAAOkZ,QAEnCnZ,WAAYpD,EAAMgF,OAAO5B,WAAW6B,UACpC5B,OAAQ,aAAarD,EAAMgF,OAAO3B,OAAOoM,OACzCc,aAAcvQ,EAAM8X,MAAMC,OAAOC,QACjClV,OAAQ,UACRuK,QAASrN,EAAMK,QAAQ,IACvBF,SAAU,WACVC,MAAOJ,EAAMK,QAAQ,GACrBC,IAAKN,EAAMK,QAAQ,GACnBmc,WAAY,uBACZlU,OAAQ,KAEVmU,UAAUvc,EAAAA,EAAAA,KAAI,CACZ,CAACF,EAAM0c,YAAYC,aAAa,gBAAiB,WAAY,CAC3DH,WAAY,+BAEd,UAAa,CACXpZ,WAAYpD,EAAMgF,OAAOC,UAAU2X,OAErCxZ,WAAYpD,EAAMgF,OAAOC,UAAU4X,KACnCtM,aAAcvQ,EAAM8X,MAAMC,OAAO1I,KACjCvM,OAAQ,OACR2F,OAAQ,iBACRtI,SAAU,WACVC,MAAO,GAAGJ,EAAMK,QAAQ,gBACxBC,IAAK,iBACLsC,MAAO,GAAG5C,EAAMK,QAAQ,kBAE1Byc,SAAS5c,EAAAA,EAAAA,KAAI,CACX0B,SAAU5B,EAAM6B,WAAW0B,QAAQ,IACnCkF,OAAQA,EACRC,UAAW,SACXN,aAAcpI,EAAMK,QAAQ,GAC5BF,SAAU,WACVyC,MAAOwZ,IAETW,WAAW7c,EAAAA,EAAAA,KAAI,CACb,eAAgB,CAEdkC,QAAS,qBAGbwW,SAAS1Y,EAAAA,EAAAA,KAAI,CACXkC,QAAS,OACTjC,SAAU,WACVgS,SAAU,WAmCG5R,CAAUP,EAAOyI,EAAQuT,IAElC,kBAAEgB,EAAiB,QAAE9d,EAAO,eAAEsF,EAAc,WAAE0E,EAAU,kBAAEzE,IAAsBrF,EAAAA,EAAAA,OAEhF,aAAEnB,GAAiBS,KAGlBue,IAAqBvT,EAAAA,EAAAA,UAASzL,GAE/ByG,EAAgBuE,EAAiBC,GAEjC8K,GAAcC,EAAAA,EAAAA,kBACdC,GAAUC,EAAAA,EAAAA,SAAQ,IAAMH,EAAYE,QAAQE,KAAKJ,GAAc,CAACA,IAEhEkJ,GAAoB5R,EAAAA,EAAAA,aACvBoL,IACC,IAAKA,EAAM5Q,OACT,OAAO4Q,EAET,MAAOyG,IAAsBC,EAAAA,EAAAA,qBAAoB,CAC/C9d,KAAM,CAACoX,GACP2G,YAAa,CACXC,SAAU,CACRC,OAAQ,CAAC,GAEXC,UAAW,IAEbC,iBAAkBvJ,EAClBlU,MAAOA,EACP6b,SAAUA,IAIZ,IAAK,MAAOjW,EAAOqG,KAAUkR,EAAmB7G,OAAOoH,UAAW,C,IAG5BC,EAgC9BnZ,EAjCNyH,EAAMhL,KACJgL,EAAMhL,OAAS0O,EAAAA,UAAUiH,OAAoC3K,QAA3B0R,EAAAA,GAA2B1R,UAA3B0R,IAAAA,EAAAA,EAAqChO,EAAAA,UAAUiH,OAAS3K,EAAMhL,KAElGgL,EAAM2R,OAAS,SACV3R,EAAM2R,QAAM,CAEfL,OAAQ,IACNM,YAAaC,GAAoB7R,EAAOrG,EAAOjC,EAAQ3F,GACvD+f,YAAY,EACZC,gBAAkB9b,GAChB,kBAACyJ,EAA0BA,KACzB,kBAACkO,GAAmBA,CAClBO,YAAa,SAAKlY,GAAAA,CAAOwQ,WAAY9M,IACrC2U,UACY,IAAV3U,EAAesU,GAA6BxV,EAAcwV,EAAMtU,EAAOA,EAAQ,QAAK1H,EAEtFwc,WACE9U,IAAU8Q,EAAMJ,OAAOxQ,OAAS,EAC3BoU,GAA6BxV,EAAcwV,EAAMtU,EAAOA,EAAQ,QACjE1H,EAEN4c,iBACEtZ,OAAOC,KAAK+C,GAAgBsB,OAAS,EACjC,KACErB,EAAkB,CAAC,SAErBvG,KAKZkS,SAAS,EACTxN,MAC4B,QAA1B4B,EAAAA,EAAeyH,EAAMrL,aAArB4D,IAAAA,EAAAA,EACAyZ,GAAqBhS,EAAOrG,EAAO1G,EAAS0D,EAAOua,EAAmB7G,OAAOxQ,OAAQ9H,IACpFiO,EAAM2R,OAAOL,QAIlBQ,YAAY,GAEhB,CAEA,OAAOZ,GAKT,CAACtB,EAAU7b,EAAO2D,EAAQf,EAAOsR,EAAS1P,KAI5C8S,EAAAA,EAAAA,WAAU,KACQ,I,KAAA,YACd,MAAM4G,GAmP8BC,EAlPlCngB,EAAUqY,KAmPCC,OACdvY,OAAQkO,I,IAELA,EAEAkS,EAE6DA,EAL/D,MAAMC,EACsB,qBAAZ,QAAdnS,EAAAA,EAAMoS,gBAANpS,IAAAA,OAAAA,EAAAA,EAAgByK,QACD,WAAfzK,EAAMrL,OACNud,SAAe,QAAfA,EAAAA,EAAWG,YAAXH,IAAAA,OAAAA,EAAAA,EAAiBld,QAASsd,EAAAA,cAAcC,SACpCC,EACW,WAAfxS,EAAMrL,MAAqBqL,EAAMhL,OAAS0O,EAAAA,UAAU0E,QAAS8J,SAAe,QAAfA,EAAAA,EAAWG,YAAXH,IAAAA,OAAAA,EAAAA,EAAiBld,QAASsd,EAAAA,cAAcC,SACvG,OAAOJ,GAAqBK,IAE7BC,QAASzS,GACD,CACL,CACEjD,GAAI,gBACJgG,QAAS,CACP2P,OAAQ,OACRC,UAAU,EACV1K,SAAS,EACTvO,OAAQsG,EAAMrL,SAnBnB,IAAmCud,EA7OpC,MAAMU,EA0NZ,SAAoCC,GAClC,IAAIC,EAAkD,CAAC,EAEvD,IAAK,MAAM/X,KAAO8X,EAChBC,EAAoB/X,IAAO,EAG7B,OAAIxF,OAAOC,KAAKqd,GAAchZ,OAAS,EAC9B,CACLkD,GAAI,WACJgG,QAAS,CACPgQ,cAAeD,EACfE,YAAaH,IAIZ,IACT,CA3OoCI,CA0MpC,SAA8BnW,GAE5B,IAAI+V,EAA0C,CAAC,EAW/C,OAVAtd,OAAOC,KAAKsH,GACThL,OAAQiJ,GAAQ+B,EAAgB/B,GAAKjD,QACrCpE,QAASqH,IACR,MAAMpB,EAAQmD,EAAgB/B,GAAKpB,WAErB1H,IAAV0H,IACFkZ,EAAa9X,GAAOpB,KAInBkZ,CACT,CA1NyBK,CAAqBjgB,IAGxC,GAAI2f,EACFX,EAAgBkB,KAAKP,OAChB,CACL,MAAMQ,EAAgB,CACpBC,KAAMthB,EAAU6S,UAChB0O,YAAavhB,EAAUuhB,YACvB3P,KAAM5R,EAAUwhB,WAEdH,QAAwCnhB,IAAvBmhB,EAAcC,WAA6CphB,IAAvBmhB,EAAczP,MACrEsO,EAAgBkB,KA2G1B,SAAgDC,GAC9C,MAAO,CACLrW,GAAI,WACJgG,QAAS,CACPgQ,cAAe,CACb,CAACK,EAAcC,KAAK1e,OAAO,EAC3B,CAACye,EAAczP,KAAKhP,OAAO,GAE7Bqe,YAAa,CACX,CAACI,EAAczP,KAAKhP,MAAO,EAC3B,CAACye,EAAcC,KAAK1e,MAAO,IAInC,CAxHY6e,CACEJ,GAOR,CAEA,GAAInB,EAAgBpY,OAAS,EAAG,CAC9B,MAAM4Z,QAA0CC,EAAAA,EAAAA,gBAE9CC,EAAAA,EAAAA,oBAAmB1B,EAAiB,CAAClgB,EAAUqY,OAE3CyF,EAAaoB,EAAkBwC,EAAqB,IAC1D3D,EAAcD,EAChB,MACEC,EAAcmB,EAAkBlf,EAAUqY,KAE9C,E,kLAEC,CAACrY,EAAUqY,IAAKrY,EAAU6S,UAAW7S,EAAUwhB,UAAWxhB,EAAUuhB,YAAarC,EAAmBhe,KAGvGoY,EAAAA,EAAAA,WAAU,KACJ2F,GAAqBhf,GACvB+e,KAGD,CAACC,EAAmBD,EAAmB/e,IAE1C,MAAMyS,EAAU1S,EAAUqY,IAAIC,OAAOC,KAAMtK,GAAUA,EAAMrL,QAASif,EAAAA,EAAAA,IAAU7hB,IACxE8hB,EAAYpP,aAAAA,EAAAA,EAASC,OAAOoP,UAAWlJ,GAAMA,KAAMoG,aAAAA,EAAAA,EAAmBjU,KACtEgX,EAAiBF,IAA4B,IAAfA,EAAmBA,OAAY5hB,EAEnE,IAAK4d,EACH,OAAO,qCA0BT,OACE,kBAAC9Z,MAAAA,CAAImP,cAAaC,EAAAA,EAAQC,MAAMuH,QAASlY,UAAWX,EAAO6Y,SACzD,kBAACqH,EAAAA,EAASA,CACRC,OAAQ,CACN9f,OAAQtB,GAEVqhB,cAAe,CAAE/f,MAAOL,EAAO0c,UAC/BrB,SAlB8B,CAAC5Q,EAAOtE,EAAWG,KACrD,MAAM+Z,EAAkBvY,OAAOxB,EAAIga,MAAMzd,MAAM0d,MAAM,GAAI,IACpDvQ,MAAMqQ,IACTnE,EAAgBmE,IAgBdrT,SAAUjO,EAA0B,GAAK,IACzCuD,SAAUvD,EAA0B,GAAa,GAAR8D,EACzC9B,KAAM,CACJ2H,OAAQA,EACR7F,MAAO9D,EAA0B,GAAKkd,IAGxC,kBAACuE,UAAAA,CAAQ7f,UAAW,GAAGX,EAAO+c,WAAWhe,EAA0BiB,EAAOsc,sBAAwB,MAChG,kBAAC7S,EAAyBA,CACxB1K,wBAAyBA,EACzBC,6BAtByB,KACjCmd,GAA4Bpd,IAsBpB0hB,oCAAqCzgB,EAAOE,+BAKlD,kBAAC+B,MAAAA,CAAItB,UAAWX,EAAOgd,WACrB,kBAAC7R,EAAwBA,KACvB,kBAACuV,EAAAA,WAAUA,CAACC,YAAY,EAAMC,UAAU,EAAOC,cAAc,GAC3D,kBAAC3F,GAAAA,CACCjd,UAAWA,EACXC,aAAc+hB,EACd1gB,KAAMwc,EACNrT,OAAQA,EACR7F,MAAOuZ,EACPf,UAAUyF,EAAAA,EAAAA,UAxDL,CAACC,EAA0Ble,KAC1C,MAAMoE,EAAMxF,OAAOC,KAAKvC,GACrBnB,OAAQiJ,GAAQ9H,EAAQ8H,GAAKjD,QAC7BwS,KAAMvP,GAAQA,IAAQ8Z,GAEzB,GAAI9Z,GAAOpE,EAAQ,EAAG,CACpB,MAAMiE,EAAM,MAAKrC,GACjBqC,EAAIG,GAAOpE,EACX6B,EAAkBoC,EACpB,GA+CuC,KAC7Bka,cAAe7e,EAAM6e,qBAyBnC,SAASpD,GAA2B1R,GAElC,GAAIA,EAAMrL,KAAM,CACd,MAAMA,EAAOqL,EAAMrL,KAAKogB,cACxB,GAAa,SAATpgB,GAA4B,SAATA,EACrB,OAAO+O,EAAAA,UAAUC,IAErB,CAGA,IAAK,IAAIqR,EAAI,EAAGA,EAAIhV,EAAM0E,OAAO7K,OAAQmb,IAAK,CAC5C,MAAMpK,EAAI5K,EAAM0E,OAAOsQ,GACvB,GAAS,MAALpK,EACF,OAAOqK,GAA2BrK,EAEtC,CAIF,CAEO,MAAM9H,GAAmB,KACvB,CACLC,QAAS,CACPmS,KAAM,CACJlS,MAAO,UACPrJ,MAAO,GAETwb,SAAU,CACRnS,MAAO,UACPrJ,MAAO,GAETyb,MAAO,CACLpS,MAAO,UACPrJ,MAAO,GAET0b,KAAM,CACJrS,MAAO,UACPrJ,MAAO,GAET2b,IAAK,CACHtS,MAAO,UACPrJ,MAAO,GAET4b,MAAO,CACLvS,MAAO,UACPrJ,MAAO,GAET6b,KAAM,CACJxS,MAAO,UACPrJ,MAAO,GAET8b,MAAO,CACLzS,MAAO,UACPrJ,MAAO,GAETqE,KAAM,CACJgF,MAAO,UACPrJ,MAAO,GAET+b,QAAS,CACP1S,MAAO,UACPrJ,MAAO,IAGX3E,KAAM2gB,EAAAA,YAAYC,cAiEtB,SAAS/D,GACP7R,EACAyG,EACA/O,EACA3F,GAEA,OAAIiO,EAAMrL,QAASsV,EAAAA,EAAAA,IAAYlY,GACtB,CACL8jB,cAAgB5f,GACd,kBAAC2T,GAAoBA,GAAAA,GAAAA,CAAAA,EAAK3T,GAAAA,CAAOwQ,WAAYA,EAAY/O,OAAQA,EAAOzB,EAAMgK,aAEhFjL,KAAM8gB,EAAAA,qBAAqBC,QAIxB,CACLF,cAAgB5f,GAAU,kBAAC8P,EAAoBA,GAAAA,GAAAA,CAAAA,EAAK9P,GAAAA,CAAOwQ,WAAYA,KACvEzR,KAAM8gB,EAAAA,qBAAqBC,OAE/B,CAEA,SAAS/D,GACPhS,EACAyG,EACAxT,EACAid,EACA8F,EACAjkB,G,IAyCYiO,EAAAA,EAvCZ,MAGM5J,EAAW4f,GAAkB,EAAI9F,EAAa+F,KAAKC,IAAIhG,EAAa,GAGpEiG,EAA8B,IAAf1P,EAAmB,GAAK,EAM7C,GAAIzG,EAAMhL,OAAS0O,EAAAA,UAAUC,KAC3B,OAAO,IAAMwS,EAGf,MAAMC,EAAanjB,EAAQ+M,EAAMrL,MAEjC,QAAmB1C,IAAfmkB,EACF,O,IAGyBA,EAA3B,MAAMC,EAAYJ,KAAKK,IAAwB,QAApBF,EAAAA,EAAWC,iBAAXD,IAAAA,EAAAA,EAAwB,EAAGpW,EAAMrL,KAAKkF,QAEjE,OAAIuc,EAAWC,UAGNJ,KAAKC,IACVD,KAAKK,IAAgB,IAAZD,EAAkB,GAnBL,GAmB8BF,EA5BvC,GA4BgEA,GAC7E/f,GAIA4J,EAAMrL,QAASsV,EAAAA,EAAAA,IAAYlY,GAKxBkkB,KAAKC,IACVD,KAAKK,IAAwC,KAAV,QAAzBtW,EAAY,QAAZA,EAAAA,EAAM0E,cAAN1E,IAAAA,GAAiB,QAAjBA,EAAAA,EAAe,UAAfA,IAAAA,OAAAA,EAAAA,EAAmBnG,cAAnBmG,IAAAA,EAAAA,EAA6B,IAAY,GA9B3B,GA8BoDmW,EAvC7D,GAuCsFA,GACrG/f,QAPF,E,IAMY4J,CAGd,C,eCriBA,MAAMuW,GAAe,2EAkBRC,GAAavgB,IACxB,MAAM,UAAElE,GAAcU,KAEfgkB,EAAeC,IAAoBjZ,EAAAA,EAAAA,UAAS,CAAEjB,OAAQ,EAAG7F,MAAO,KAGvEggB,EAAAA,EAAAA,GAAkB,CAChBxH,SAAU,KACR,MAAMyH,EAAU3gB,EAAM4gB,UAAU5N,QAC5B2N,IACEH,EAAc9f,QAAUigB,EAAQE,aAAeL,EAAcja,SAAWoa,EAAQG,cAClFL,EAAiB,CACfla,OAAQoa,EAAQG,aAChBpgB,MAAOigB,EAAQE,gBAKvB1c,IAAKnE,EAAM4gB,YAGb,MAAM/iB,EA3BiB,CACvBwgB,SAASrgB,EAAAA,EAAAA,KAAI,CACXC,SAAU,cA0BN0b,GAAWoH,EAAAA,EAAAA,eAIXC,GAAsB5X,EAAAA,EAAAA,aACzB6X,IACC,MAAMC,EAAqBlhB,EAAMmhB,WAUjC,OATID,aAAAA,EAAAA,EAAoBtd,SACtBtE,OAAOmP,OAAOyS,GAAoBzjB,QAAQ,CAACqH,EAAKpB,KAC1Cud,EAAWnc,KACbmc,EAAWnc,GAAKjD,QAAS,EACzBof,EAAWnc,GAAKpB,MAAQA,KAKvBud,GAET,CAACjhB,EAAMmhB,aAIT,IAAKrlB,IAAcA,EAAUqY,IAAIvQ,OAC/B,OAAO,K,IAGM9H,EAAf,MAAM2F,EAA4C,QAAnC3F,EAAAA,EAAUslB,mCAAVtlB,IAAAA,EAAAA,EAAyC,GAClDulB,EAAmBvlB,EAAYA,EAAUqY,IAAIvQ,OAAS,EAG5D,IAAIuD,EA+FN,SAAiC8U,EAAsBxa,GACrD,IAAI0F,EAAwC,CAAC,EAG7C,MAAMma,EAAW,IAAIC,IACfvf,EA9CD,SAAqCP,GAC1C,MAAM+f,EAAiB,IAAID,IAwB3B,OAvBA9f,EAAOhE,QAASgkB,IACKniB,OAAOC,KAAKkiB,GACpBhkB,QAAS4F,IAClB,GAAIme,EAAeE,IAAIre,GAAY,CACjC,MAAMse,EAASH,EAAeI,IAAIve,GAC5BoL,EAASkT,aAAAA,EAAAA,EAAQE,SACjBzB,EAAYuB,aAAAA,EAAAA,EAAQvB,UAEtB3R,KAAWA,aAAAA,EAAAA,EAAQiT,IAAID,EAAYpe,OACrCoL,SAAAA,EAAQqT,IAAIL,EAAYpe,IACpB+c,GAAaqB,EAAYpe,GAAWO,OAASwc,GAC/CoB,EAAeO,IAAI1e,EAAW,CAAE+c,UAAWqB,EAAYpe,GAAWO,OAAQie,SAAUpT,IAG1F,MACE+S,EAAeO,IAAI1e,EAAW,CAC5B+c,UAAWqB,EAAYpe,GAAWO,OAClCie,SAAU,IAAIG,IAAI,CAACP,EAAYpe,WAMhCme,CACT,CAoBsBS,CAA4BxgB,GAC1C4f,EAAmBpF,EAAYA,EAAUrY,OAAS,GAEpDnC,aAAAA,EAAAA,EAAQmC,SAAUyd,IAEpB5f,EAAOhE,QAASgE,IACMnC,OAAOC,KAAKkC,GAEpBhE,QAASiE,I,IAEM8f,EADzB,MAAMA,EAAiBxf,EAAY4f,IAAIlgB,G,IACd8f,EAAzB,MAAMU,EAAiD,QAA9BV,EAAAA,SAAwB,QAAxBA,EAAAA,EAAgBK,gBAAhBL,IAAAA,OAAAA,EAAAA,EAA0B5iB,YAA1B4iB,IAAAA,EAAAA,EAAkC,EAE3D,GAAIF,EAASI,IAAIhgB,GAAQ,CACvB,MAAMnF,EAAQ+kB,EAASM,IAAIlgB,GAEvBnF,KACEA,aAAAA,EAAAA,EAAOsF,QACTyf,EAASS,IAAIrgB,EAAO,CAClBG,QAAQ,EACRG,YAAakgB,EACbxe,MAAOnH,EAAMmH,MACb0c,UAAWoB,aAAAA,EAAAA,EAAgBpB,UAC3Bre,wBAAyBxF,EAAMwF,wBAA0B,IAG3Duf,EAASS,IAAIrgB,EAAO,CAClBG,QAAQ,EACRG,YAAakgB,EACbxe,WAAO1H,EACPokB,UAAWoB,aAAAA,EAAAA,EAAgBpB,UAC3Bre,wBAAyBxF,EAAMwF,wBAA0B,IAKjE,MACEuf,EAASS,IAAIrgB,EAAO,CAClBG,QAAQ,EACRG,YAAakgB,EACbxe,WAAO1H,EACPokB,UAAWoB,aAAAA,EAAAA,EAAgBpB,UAC3Bre,wBAAyB,QAOjCoF,EAAoB7H,OAAO6iB,YAAYb,GAGvChiB,OAAOC,KAAK4H,GAAmB1J,QAASqH,IACtCqC,EAAkBrC,GAAK/C,wBAA0BqgB,GAC/Cjb,EAAkBrC,GAAK/C,wBACvBsf,MAIN,OAAOla,CACT,CA/J0Bkb,CAAwBvmB,EAAUqY,IAAK1S,GAC/D,MAAM0b,EAAgB,CACpBC,KAAMthB,EAAU6S,UAChB0O,YAAavhB,EAAUuhB,YACvB3P,KAAM5R,EAAUwhB,WAIlB,GAAIH,EAAe,EA+JrB,SACEmF,EACAnb,EACAka,GAEAiB,EAAkB7kB,QAASsM,I,IAIR5C,EACHA,EAJd,IAAK4C,EACH,OAEF,MAAM7B,EAAwC,QAA7Bf,EAAAA,EAAkB4C,EAAMrL,aAAxByI,IAAAA,OAAAA,EAAAA,EAA+BtF,OAC1C6B,EAAqC,QAA7ByD,EAAAA,EAAkB4C,EAAMrL,aAAxByI,IAAAA,OAAAA,EAAAA,EAA+BzD,MAE3CyD,EAAkB4C,EAAMrL,MADtBwJ,QAAsBlM,IAAV0H,EACkB,CAC9B7B,QAAQ,EACRG,YAAaqf,EACb3d,MAAOA,EACP3B,wBAAyBqgB,GACvBrY,EAAM0E,OAAO5S,OAAQU,GAAUA,SAAuCqH,OACtEyd,IAI4B,CAC9Bxf,QAAQ,EACRG,YAAaqf,EACb3d,WAAO1H,EACP+F,wBAAyBqgB,GACvBrY,EAAM0E,OAAO5S,OAAQU,GAAUA,SAAuCqH,OACtEyd,KAOV,CAjMIkB,CACE,CAACpF,EAAczP,KAAMyP,EAAcC,QAASD,EAAcE,aAC1DlW,EACAka,GAGFla,EAAoB6Z,EAAoB7Z,IA6L5C,SACEtF,EACAsb,EACAhW,G,IAcIgW,EAA4BA,EAXhC,GAAsB,IAAlBtb,EAAO+B,OAAc,C,IACnBuZ,EAIAA,EAHgBA,EACAA,EAGAA,EACAA,EANpB,GAAsB,QAAlBA,EAAAA,EAAcC,YAAdD,IAAAA,OAAAA,EAAAA,EAAoBze,KACtByI,EAAoC,QAAlBgW,EAAAA,EAAcC,YAAdD,IAAAA,OAAAA,EAAAA,EAAoBze,MAAMmD,QAAS,EACrDsF,EAAoC,QAAlBgW,EAAAA,EAAcC,YAAdD,IAAAA,OAAAA,EAAAA,EAAoBze,MAAMgF,MAAQ,EAEtD,GAAsB,QAAlByZ,EAAAA,EAAczP,YAAdyP,IAAAA,OAAAA,EAAAA,EAAoBze,KACtByI,EAAoC,QAAlBgW,EAAAA,EAAczP,YAAdyP,IAAAA,OAAAA,EAAAA,EAAoBze,MAAMmD,QAAS,EACrDsF,EAAoC,QAAlBgW,EAAAA,EAAczP,YAAdyP,IAAAA,OAAAA,EAAAA,EAAoBze,MAAMgF,MAAQ,CAExD,CAEA,IAAsB,QAAlByZ,EAAAA,EAAczP,YAAdyP,IAAAA,OAAAA,EAAAA,EAAoBze,QAA0B,QAAlBye,EAAAA,EAAcC,YAAdD,IAAAA,OAAAA,EAAAA,EAAoBze,MAAM,C,IACtCye,EACAA,EADlBhW,EAAoC,QAAlBgW,EAAAA,EAAcC,YAAdD,IAAAA,OAAAA,EAAAA,EAAoBze,MAAMK,KAAO,aACnDoI,EAAoC,QAAlBgW,EAAAA,EAAczP,YAAdyP,IAAAA,OAAAA,EAAAA,EAAoBze,MAAMK,KAAO,YACrD,CAEIoe,EAAcE,YAAYzZ,QAC5BuZ,EAAcE,YAAY5f,QAASsM,I,IAChBA,GAAkB,QAAlBA,EAAAA,EAAM2R,OAAOtP,aAAbrC,IAAAA,OAAAA,EAAAA,EAAoBnG,UAEnCuD,EAAkB4C,EAAMrL,MAAMK,KAAO,eAI7C,CArNIyjB,CAHeljB,OAAOC,KAAK4H,GAAmBtL,OAAQiJ,GAAQqC,EAAkBrC,GAAKjD,QAGzDsb,EAAehW,EAC7C,CAEA,OACE,kBAACkX,UAAAA,CAAQ7f,UAAWX,EAAOwgB,SACzB,kBAACoE,EAAAA,GAA0BA,CACzBC,qBAAsB1iB,EAAM0iB,qBAC5B5mB,UAAWA,EACX6mB,eAAgBxb,EAChByb,cAAe5iB,EAAM4iB,cACrB9H,kBAAmB9a,EAAM8a,kBACzB+H,kBAAmB7iB,EAAM6iB,mBAEzB,kBAACnJ,GAAKA,CACJ5d,UAAWA,EACX6d,SAAUA,EACVpT,OAAQia,EAAcja,OAAS,GAC/B7F,MAAO8f,EAAc9f,MAAQ,IAAMoiB,GAAAA,IAAyB,GAAK,GACjErhB,OAAQA,EACRod,cAAe7e,EAAM6e,mBAOzBuD,GAAY,CAAC7lB,EAAewmB,IACzB/C,KAAKgD,KAAK,IAAOzmB,EAASwmB,GAsC5B,SAAS/D,GAA2BziB,GACzC,IAAI8O,GAAY4X,EAAAA,EAAAA,yBAAwB1mB,GAKxC,MAJgC,WAAd8O,GAA0BiV,GAAa4C,KAAK3mB,KAE5D8O,EAAYoC,EAAAA,UAAUC,MAEjBrC,CACT,CCvKe,SAAS8X,IAAc,UACpCvnB,EAAS,kBACTkf,EAAiB,UACjBmB,EAAS,cACT4C,EAAa,UACb+B,EAAS,aACT7kB,EAAY,cACZ6mB,EAAa,qBACbF,EAAoB,UACpBzmB,EAAS,WACTklB,EAAU,kBACV0B,IAEA,MAAM/mB,GAAYmW,EAAAA,EAAAA,SAAQ,KACxB,IAAKgK,EACH,OAAO,KAET,MAAMmH,EAAYnH,EAAU7H,OAAOyJ,UAAW9T,GAAUA,EAAMhL,OAAS0O,EAAAA,UAAUC,MAC3E2V,GAAcC,EAAAA,EAAAA,eAAcrH,EAAWmH,EAAWvE,IAAkB0E,EAAAA,cAAcC,YAExF,OADkBC,EAAAA,EAAAA,IAAeJ,IAEhC,CAACpH,EAAW4C,IAEf,OAAK/iB,EAKH,kBAACM,EAAoBA,CAACR,UAAWA,EAAWG,aAAcA,EAAcE,UAAWA,EAAWH,UAAWA,GACvG,kBAACykB,GAASA,CACRsC,kBAAmBA,EACnBD,cAAeA,EACfF,qBAAsBA,EACtBvB,WAAYA,EACZP,UAAWA,EACX9F,kBAAmBA,EACnB+D,cAAeA,KAZZ,IAgBX,C", "sources": ["webpack://grafana-lokiexplore-app/./Components/Table/Context/QueryContext.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/ColumnSelection/LogsColumnSearch.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/ColumnSelection/LogsTableEmptyFields.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/ColumnSelection/LogsTableNavField.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/ColumnSelection/LogsTableActiveFields.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/ColumnSelection/LogsTableAvailableFields.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/ColumnSelection/LogsTableMultiSelect.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/ColumnSelection/ColumnSelectionDrawerWrap.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/Context/TableCellContext.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/Context/TableHeaderContext.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/DefaultCellWrapComponent.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/CellContextMenu.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/DefaultPill.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/LineActionIcons.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/DefaultCellComponent.tsx", "webpack://grafana-lokiexplore-app/./styles/shared-styles.ts", "webpack://grafana-lokiexplore-app/./Components/Table/LogLinePill.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/RawLogLineText.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/Scroller.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/LogLineCellComponent.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/LogsTableHeaderMenu.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/LogsTableHeader.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/LogsTableHeaderWrap.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/Table.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/TableWrap.tsx", "webpack://grafana-lokiexplore-app/./Components/Table/TableProvider.tsx"], "sourcesContent": ["import React, { createContext, ReactNode, useContext } from 'react';\n\nimport { AdHocVariableFilter, TimeRange } from '@grafana/data';\n\nimport { LogsFrame } from '../../../services/logsFrame';\nimport { SelectedTableRow } from '../LogLineCellComponent';\n\nexport type Label = { indexed: boolean; name: string; values: string[] };\n\nexport type QueryContextType = {\n  addFilter: (filter: AdHocVariableFilter) => void;\n  logsFrame: LogsFrame | null;\n  selectedLine?: SelectedTableRow;\n  timeRange?: TimeRange;\n};\n\nexport const initialState = {\n  addFilter: (filter: AdHocVariableFilter) => {},\n  logsFrame: null,\n  selectedLine: undefined,\n  timeRange: undefined,\n};\n\nexport const QueryContext = createContext<QueryContextType>(initialState);\n\nexport const QueryContextProvider = ({\n  addFilter,\n  children,\n  logsFrame,\n  selectedLine,\n  timeRange,\n}: {\n  addFilter: (filter: AdHocVariableFilter) => void;\n  children: ReactNode;\n  logsFrame: LogsFrame;\n  selectedLine?: SelectedTableRow;\n  timeRange?: TimeRange;\n}) => {\n  return (\n    <QueryContext.Provider\n      value={{\n        addFilter,\n        logsFrame,\n        selectedLine,\n        timeRange,\n      }}\n    >\n      {children}\n    </QueryContext.Provider>\n  );\n};\n\nexport const useQueryContext = () => {\n  return useContext(QueryContext);\n};\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { reportInteraction } from '@grafana/runtime';\nimport { Field, IconButton, Input, useTheme2 } from '@grafana/ui';\n\nimport { debouncedFuzzySearch } from '../../../services/search';\nimport { useTableColumnContext } from 'Components/Table/Context/TableColumnsContext';\nimport { FieldNameMetaStore } from 'Components/Table/TableTypes';\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    collapseTableSidebarButton: css({\n      position: 'absolute',\n      right: theme.spacing(0.2),\n      top: theme.spacing(1),\n    }),\n  };\n}\n\ninterface LogsColumnSearchProps {\n  collapseButtonClassName?: string;\n  isTableSidebarCollapsed?: boolean;\n  onToggleTableSidebarCollapse?: () => void;\n  searchValue: string;\n  setSearchValue: (value: string) => void;\n}\n\nexport function LogsColumnSearch({\n  collapseButtonClassName,\n  isTableSidebarCollapsed,\n  onToggleTableSidebarCollapse,\n  searchValue,\n  setSearchValue,\n}: LogsColumnSearchProps) {\n  const { columns, setFilteredColumns } = useTableColumnContext();\n\n  // uFuzzy search dispatcher, adds any matches to the local state\n  const dispatcher = (data: string[][]) => {\n    const matches = data[0];\n    let newColumnsWithMeta: FieldNameMetaStore = {};\n    let numberOfResults = 0;\n    matches.forEach((match) => {\n      if (match in columns) {\n        newColumnsWithMeta[match] = columns[match];\n        numberOfResults++;\n      }\n    });\n    setFilteredColumns(newColumnsWithMeta);\n    searchFilterEvent(numberOfResults);\n  };\n\n  // uFuzzy search\n  const search = (needle: string) => {\n    debouncedFuzzySearch(Object.keys(columns), needle, dispatcher);\n  };\n\n  // onChange handler for search input\n  const onSearchInputChange = (e: React.FormEvent<HTMLInputElement>) => {\n    const value = e.currentTarget?.value;\n    setSearchValue(value);\n    if (value) {\n      search(value);\n    } else {\n      // If the search input is empty, reset the local search state.\n      setFilteredColumns(undefined);\n    }\n  };\n\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n  return (\n    <>\n      <IconButton\n        className={collapseButtonClassName || styles.collapseTableSidebarButton}\n        onClick={onToggleTableSidebarCollapse}\n        name={isTableSidebarCollapsed ? 'angle-right' : 'angle-left'}\n        tooltip={isTableSidebarCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}\n        size=\"sm\"\n      />\n      {!isTableSidebarCollapsed && (\n        <Field>\n          <Input\n            value={searchValue}\n            type={'text'}\n            placeholder={'Search fields by name'}\n            onChange={onSearchInputChange}\n          />\n        </Field>\n      )}\n    </>\n  );\n}\n\nfunction searchFilterEvent(searchResultCount: number) {\n  reportInteraction('grafana_logs_app_table_text_search_result_count', {\n    resultCount: searchResultCount,\n  });\n}\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    empty: css({\n      fontSize: theme.typography.fontSize,\n      marginBottom: theme.spacing(2),\n      marginLeft: theme.spacing(1.75),\n    }),\n  };\n}\n\nexport function LogsTableEmptyFields() {\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n  return <div className={styles.empty}>No fields</div>;\n}\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Checkbox, Icon, useTheme2 } from '@grafana/ui';\n\nimport { FieldNameMeta } from '../TableTypes';\n\nexport function LogsTableNavField(props: {\n  columnWidthMap?: Record<string, number>;\n  draggable?: boolean;\n  label: string;\n  labels: Record<string, FieldNameMeta>;\n  onChange: () => void;\n  setColumnWidthMap?: (map: Record<string, number>) => void;\n  showCount?: boolean;\n}): React.JSX.Element | null {\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n\n  if (props.labels[props.label]) {\n    return (\n      <>\n        <div className={styles.contentWrap}>\n          <Checkbox\n            className={styles.checkboxLabel}\n            label={props.label}\n            onChange={props.onChange}\n            checked={props.labels[props.label]?.active ?? false}\n          />\n          {props.showCount && (\n            <div className={styles.labelCount}>\n              <div>{props.labels[props.label]?.percentOfLinesWithLabel}%</div>\n              <div className={styles.valueCount}>\n                {props.labels[props.label]?.cardinality}{' '}\n                {props.labels[props.label]?.cardinality === 1 ? 'value' : 'values'}\n              </div>\n            </div>\n          )}\n        </div>\n        {props.draggable && (\n          <Icon\n            aria-label=\"Drag and drop icon\"\n            title=\"Drag and drop to reorder\"\n            name=\"draggabledots\"\n            size=\"lg\"\n            className={styles.dragIcon}\n          />\n        )}\n      </>\n    );\n  }\n\n  return null;\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    // Hide text that overflows, had to select elements within the Checkbox component, so this is a bit fragile\n    checkboxLabel: css({\n      '> span': {\n        display: 'block',\n        maxWidth: '100%',\n        overflow: 'hidden',\n        textOverflow: 'ellipsis',\n        whiteSpace: 'nowrap',\n      },\n    }),\n    contentWrap: css({\n      alignItems: 'center',\n      display: 'flex',\n      justifyContent: 'space-between',\n      width: '100%',\n    }),\n    customWidthWrap: css({\n      cursor: 'pointer',\n      fontSize: theme.typography.bodySmall.fontSize,\n    }),\n    dragIcon: css({\n      cursor: 'drag',\n      marginLeft: theme.spacing(1),\n      opacity: 0.4,\n    }),\n    labelCount: css({\n      alignItems: 'self-end',\n      appearance: 'none',\n      background: 'none',\n      border: 'none',\n      display: 'flex',\n      flexDirection: 'column',\n      fontSize: theme.typography.pxToRem(11),\n      marginLeft: theme.spacing(0.5),\n      marginRight: theme.spacing(0.5),\n      opacity: 0.6,\n    }),\n    valueCount: css({\n      textWrap: 'nowrap',\n    }),\n  };\n}\n", "import React, { ReactElement } from 'react';\n\nimport { css, cx } from '@emotion/css';\nimport { DragDropContext, Draggable, DraggableProvided, Droppable, DropResult } from '@hello-pangea/dnd';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\nimport { FieldNameMeta, FieldNameMetaStore } from '../TableTypes';\nimport { LogsTableEmptyFields } from './LogsTableEmptyFields';\nimport { LogsTableNavField } from './LogsTableNavField';\nimport { useTableColumnContext } from 'Components/Table/Context/TableColumnsContext';\n\nexport function getLogsFieldsStyles(theme: GrafanaTheme2) {\n  return {\n    columnWrapper: css({\n      marginBottom: theme.spacing(1.5),\n      // need some space or the outline of the checkbox is cut off\n      paddingLeft: theme.spacing(0.5),\n    }),\n    dragging: css({\n      background: theme.colors.background.secondary,\n    }),\n    wrap: css({\n      background: theme.colors.background.primary,\n      display: 'flex',\n      marginBottom: theme.spacing(1),\n      marginTop: theme.spacing(1),\n    }),\n  };\n}\n\nfunction sortLabels(labels: Record<string, FieldNameMeta>) {\n  return (a: string, b: string) => {\n    const la = labels[a];\n    const lb = labels[b];\n\n    // Sort by index\n    if (la.index != null && lb.index != null) {\n      return la.index - lb.index;\n    }\n\n    // otherwise do not sort\n    return 0;\n  };\n}\n\nexport const LogsTableActiveFields = (props: {\n  id: string;\n  labels: Record<string, FieldNameMeta>;\n  reorderColumn: (cols: FieldNameMetaStore, sourceIndex: number, destinationIndex: number) => void;\n  toggleColumn: (columnName: string) => void;\n  valueFilter: (value: string) => boolean;\n}): ReactElement => {\n  const { columnWidthMap, setColumnWidthMap } = useTableColumnContext();\n  const { labels, reorderColumn, toggleColumn, valueFilter } = props;\n  const theme = useTheme2();\n  const { columns } = useTableColumnContext();\n  const styles = getLogsFieldsStyles(theme);\n  const labelKeys = Object.keys(labels).filter((labelName) => valueFilter(labelName));\n\n  const onDragEnd = (result: DropResult) => {\n    if (!result.destination) {\n      return;\n    }\n    reorderColumn(columns, result.source.index, result.destination.index);\n  };\n\n  const renderTitle = (labelName: string) => {\n    const label = labels[labelName];\n    if (label) {\n      return `${labelName} appears in ${label?.percentOfLinesWithLabel}% of log lines`;\n    }\n\n    return undefined;\n  };\n\n  if (labelKeys.length) {\n    return (\n      <DragDropContext onDragEnd={onDragEnd}>\n        <Droppable droppableId=\"order-fields\" direction=\"vertical\">\n          {(provided) => (\n            <div className={styles.columnWrapper} {...provided.droppableProps} ref={provided.innerRef}>\n              {labelKeys.sort(sortLabels(labels)).map((labelName, index) => (\n                <Draggable draggableId={labelName} key={labelName} index={index}>\n                  {(provided: DraggableProvided, snapshot) => (\n                    <div\n                      className={cx(styles.wrap, snapshot.isDragging ? styles.dragging : undefined)}\n                      ref={provided.innerRef}\n                      {...provided.draggableProps}\n                      {...provided.dragHandleProps}\n                      title={renderTitle(labelName)}\n                    >\n                      <LogsTableNavField\n                        setColumnWidthMap={setColumnWidthMap}\n                        columnWidthMap={columnWidthMap}\n                        label={labelName}\n                        onChange={() => toggleColumn(labelName)}\n                        labels={labels}\n                        draggable={true}\n                      />\n                    </div>\n                  )}\n                </Draggable>\n              ))}\n              {provided.placeholder}\n            </div>\n          )}\n        </Droppable>\n      </DragDropContext>\n    );\n  }\n\n  return <LogsTableEmptyFields />;\n};\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\nimport { FieldNameMeta } from '../TableTypes';\nimport { LogsTableEmptyFields } from './LogsTableEmptyFields';\nimport { LogsTableNavField } from './LogsTableNavField';\n\nconst collator = new Intl.Collator(undefined, { sensitivity: 'base' });\n\nfunction getLogsFieldsStyles(theme: GrafanaTheme2) {\n  return {\n    columnWrapper: css({\n      marginBottom: theme.spacing(1.5),\n      // need some space or the outline of the checkbox is cut off\n      paddingLeft: theme.spacing(0.5),\n    }),\n    dragging: css({\n      background: theme.colors.background.secondary,\n    }),\n    wrap: css({\n      background: theme.colors.background.primary,\n      borderBottom: `1px solid ${theme.colors.background.canvas}`,\n      display: 'flex',\n      marginBottom: theme.spacing(0.25),\n      marginTop: theme.spacing(0.25),\n    }),\n  };\n}\n\nfunction sortLabels(labels: Record<string, FieldNameMeta>) {\n  return (a: string, b: string) => {\n    const la = labels[a];\n    const lb = labels[b];\n\n    // ...sort by type and alphabetically\n    if (la != null && lb != null) {\n      return (\n        Number(lb.type === 'TIME_FIELD') - Number(la.type === 'TIME_FIELD') ||\n        Number(lb.type === 'BODY_FIELD') - Number(la.type === 'BODY_FIELD') ||\n        collator.compare(a, b)\n      );\n    }\n\n    // otherwise do not sort\n    return 0;\n  };\n}\n\nexport const LogsTableAvailableFields = (props: {\n  labels: Record<string, FieldNameMeta>;\n  toggleColumn: (columnName: string) => void;\n  valueFilter: (value: string) => boolean;\n}): React.ReactElement => {\n  const { labels, toggleColumn, valueFilter } = props;\n  const theme = useTheme2();\n  const styles = getLogsFieldsStyles(theme);\n  const labelKeys = Object.keys(labels).filter((labelName) => valueFilter(labelName));\n  if (labelKeys.length) {\n    // Otherwise show list with a hardcoded order\n    return (\n      <div className={styles.columnWrapper}>\n        {labelKeys.sort(sortLabels(labels)).map((labelName) => (\n          <div\n            key={labelName}\n            className={styles.wrap}\n            title={`${labelName} appears in ${labels[labelName]?.percentOfLinesWithLabel}% of log lines`}\n          >\n            <LogsTableNavField\n              showCount={true}\n              label={labelName}\n              onChange={() => toggleColumn(labelName)}\n              labels={labels}\n            />\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  return <LogsTableEmptyFields />;\n};\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\nimport { FieldNameMeta, FieldNameMetaStore } from '../TableTypes';\nimport { LogsTableActiveFields } from 'Components/Table/ColumnSelection/LogsTableActiveFields';\nimport { LogsTableAvailableFields } from 'Components/Table/ColumnSelection/LogsTableAvailableFields';\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    columnHeader: css({\n      background: theme.colors.background.secondary,\n      display: 'flex',\n      fontSize: theme.typography.h6.fontSize,\n      justifyContent: 'space-between',\n      left: 0,\n      marginBottom: theme.spacing(2),\n      paddingBottom: theme.spacing(0.75),\n      paddingLeft: theme.spacing(1.5),\n      paddingRight: theme.spacing(0.75),\n      paddingTop: theme.spacing(0.75),\n      position: 'sticky',\n      top: 0,\n      zIndex: 3,\n    }),\n    columnHeaderButton: css({\n      appearance: 'none',\n      background: 'none',\n      border: 'none',\n      fontSize: theme.typography.pxToRem(11),\n    }),\n    sidebarWrap: css({\n      /* Hide scrollbar for Chrome, Safari, and Opera */\n      '&::-webkit-scrollbar': {\n        display: 'none',\n      },\n      height: 'calc(100% - 50px)',\n      overflowY: 'scroll',\n      /* Hide scrollbar for Firefox */\n      scrollbarWidth: 'none',\n    }),\n  };\n}\n\nexport const LogsTableMultiSelect = (props: {\n  clear: () => void;\n  columnsWithMeta: Record<string, FieldNameMeta>;\n  filteredColumnsWithMeta: Record<string, FieldNameMeta> | undefined;\n  reorderColumn: (cols: FieldNameMetaStore, oldIndex: number, newIndex: number) => void;\n  toggleColumn: (columnName: string) => void;\n}) => {\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n\n  return (\n    <div className={styles.sidebarWrap}>\n      {/* Sidebar columns */}\n      <>\n        <div className={styles.columnHeader}>\n          Selected fields\n          <button onClick={props.clear} className={styles.columnHeaderButton}>\n            Reset\n          </button>\n        </div>\n        <LogsTableActiveFields\n          reorderColumn={props.reorderColumn}\n          toggleColumn={props.toggleColumn}\n          labels={props.filteredColumnsWithMeta ?? props.columnsWithMeta}\n          valueFilter={(value) => props.columnsWithMeta[value]?.active ?? false}\n          id={'selected-fields'}\n        />\n\n        <div className={styles.columnHeader}>Fields</div>\n        <LogsTableAvailableFields\n          toggleColumn={props.toggleColumn}\n          labels={props.filteredColumnsWithMeta ?? props.columnsWithMeta}\n          valueFilter={(value) => !props.columnsWithMeta[value]?.active}\n        />\n      </>\n    </div>\n  );\n};\n", "import React, { useState } from 'react';\n\nimport { reportInteraction } from '@grafana/runtime';\n\nimport { logger } from '../../../services/logger';\nimport { FieldNameMetaStore } from '../TableTypes';\nimport { LogsColumnSearch } from 'Components/Table/ColumnSelection/LogsColumnSearch';\nimport { LogsTableMultiSelect } from 'Components/Table/ColumnSelection/LogsTableMultiSelect';\nimport { useTableColumnContext } from 'Components/Table/Context/TableColumnsContext';\n\nexport function getReorderColumn(setColumns: (cols: FieldNameMetaStore) => void) {\n  return (columns: FieldNameMetaStore, sourceIndex: number, destinationIndex: number) => {\n    if (sourceIndex === destinationIndex) {\n      return;\n    }\n\n    const pendingLabelState = { ...columns };\n    const keys = Object.keys(pendingLabelState)\n      .filter((key) => pendingLabelState[key].active)\n      .map((key) => ({\n        fieldName: key,\n        index: pendingLabelState[key].index ?? 0,\n      }))\n      .sort((a, b) => a.index - b.index);\n\n    const [source] = keys.splice(sourceIndex, 1);\n    keys.splice(destinationIndex, 0, source);\n\n    keys\n      .filter((key) => key !== undefined)\n      .forEach((key, index) => {\n        pendingLabelState[key.fieldName].index = index;\n      });\n\n    // Set local state\n    setColumns(pendingLabelState);\n  };\n}\n\nfunction logError(columnName: string, columns: FieldNameMetaStore) {\n  let logContext;\n  try {\n    logContext = {\n      columnName: columnName,\n      columns: JSON.stringify(columns),\n    };\n  } catch (e) {\n    logContext = {\n      columnName: columnName,\n      msg: 'Table: ColumnSelectionDrawerWrap failed to encode context',\n    };\n  }\n  logger.warn('failed to get column', logContext);\n}\n\ninterface ColumnSelectionDrawerWrapProps {\n  collapseTableSidebarButtonClassName?: string;\n  isTableSidebarCollapsed?: boolean;\n  onToggleTableSidebarCollapse?: () => void;\n}\n\nexport function ColumnSelectionDrawerWrap(props: ColumnSelectionDrawerWrapProps) {\n  const { columns, filteredColumns, setColumns, setFilteredColumns } = useTableColumnContext();\n  const [searchValue, setSearchValue] = useState<string>('');\n  const toggleColumn = (columnName: string) => {\n    if (!columns || !(columnName in columns)) {\n      logError(columnName, columns);\n      return;\n    }\n\n    const length = Object.keys(columns).filter((c) => columns[c].active).length;\n    const isActive = !columns[columnName].active ? true : undefined;\n\n    let pendingLabelState: FieldNameMetaStore;\n    if (isActive) {\n      pendingLabelState = {\n        ...columns,\n        [columnName]: {\n          ...columns[columnName],\n          active: isActive,\n          index: length,\n        },\n      };\n    } else {\n      pendingLabelState = {\n        ...columns,\n        [columnName]: {\n          ...columns[columnName],\n          active: false,\n          index: undefined,\n        },\n      };\n    }\n\n    // Analytics\n    columnFilterEvent(columnName);\n\n    // Set local state\n    setColumns(pendingLabelState);\n\n    // If user is currently filtering, update filtered state\n    if (filteredColumns) {\n      const active = !filteredColumns[columnName]?.active;\n      let pendingFilteredLabelState: FieldNameMetaStore;\n      if (active) {\n        pendingFilteredLabelState = {\n          ...filteredColumns,\n          [columnName]: {\n            ...filteredColumns[columnName],\n            active: active,\n            index: length,\n          },\n        };\n      } else {\n        pendingFilteredLabelState = {\n          ...filteredColumns,\n          [columnName]: {\n            ...filteredColumns[columnName],\n            active: false,\n            index: undefined,\n          },\n        };\n      }\n\n      setFilteredColumns(pendingFilteredLabelState);\n      setSearchValue('');\n    }\n  };\n\n  const reorderColumn = getReorderColumn(setColumns);\n\n  const clearSelection = () => {\n    const pendingLabelState = { ...columns };\n    let index = 0;\n    Object.keys(pendingLabelState).forEach((key) => {\n      const isDefaultField =\n        pendingLabelState[key].type === 'BODY_FIELD' || pendingLabelState[key].type === 'TIME_FIELD';\n      // after reset the only active fields are the special time and body fields\n      pendingLabelState[key].active = isDefaultField;\n      // reset the index\n      pendingLabelState[key].index = isDefaultField ? index++ : undefined;\n    });\n\n    setColumns(pendingLabelState);\n    setFilteredColumns(pendingLabelState);\n    setSearchValue('');\n  };\n\n  // Tracking event for filtering columns\n  function columnFilterEvent(columnName: string) {\n    if (columns) {\n      const newState = !columns[columnName]?.active;\n      const priorActiveCount = Object.keys(columns).filter((column) => columns[column]?.active)?.length;\n      const event = {\n        columnAction: newState ? 'add' : 'remove',\n        columnCount: newState ? priorActiveCount + 1 : priorActiveCount - 1,\n      };\n      reportInteraction('grafana_logs_app_table_column_filter_clicked', event);\n    }\n  }\n\n  return (\n    <>\n      <LogsColumnSearch\n        isTableSidebarCollapsed={props.isTableSidebarCollapsed}\n        onToggleTableSidebarCollapse={props.onToggleTableSidebarCollapse}\n        searchValue={searchValue}\n        setSearchValue={setSearchValue}\n      />\n      {!props.isTableSidebarCollapsed && (\n        <LogsTableMultiSelect\n          toggleColumn={toggleColumn}\n          filteredColumnsWithMeta={filteredColumns}\n          columnsWithMeta={columns}\n          clear={clearSelection}\n          reorderColumn={reorderColumn}\n        />\n      )}\n    </>\n  );\n}\n", "import React, { createContext, ReactNode, useCallback, useContext, useState } from 'react';\n\nexport type CellIndex = {\n  fieldName?: string;\n  index: number | null;\n  numberOfMenuItems?: number;\n  // If the field contains labels (like log line), we need to know which field (line) and which label (e.g. level)\n  subFieldName?: string;\n};\n\ntype TableCellContextType = {\n  cellIndex: CellIndex;\n  setActiveCellIndex(cellIndex: CellIndex): void;\n};\n\nconst TableCellContext = createContext<TableCellContextType>({\n  cellIndex: { index: null, numberOfMenuItems: 3 },\n  setActiveCellIndex: (cellIndex: CellIndex) => false,\n});\n\nexport const TableCellContextProvider = ({ children }: { children: ReactNode }) => {\n  const [cellActive, setCellActive] = useState<CellIndex>({ index: null });\n\n  const handleCellActive = useCallback((cellIndex: CellIndex) => {\n    setCellActive(cellIndex);\n  }, []);\n\n  return (\n    <TableCellContext.Provider value={{ cellIndex: cellActive, setActiveCellIndex: handleCellActive }}>\n      {children}\n    </TableCellContext.Provider>\n  );\n};\n\nexport const useTableCellContext = () => {\n  return useContext(TableCellContext);\n};\n", "import React, { createContext, ReactNode, useCallback, useContext, useState } from 'react';\n\ntype TableHeaderContextType = {\n  isHeaderMenuActive: boolean;\n  setHeaderMenuActive: (isHeaderMenuActive: boolean) => void;\n};\n\nconst TableHeaderContext = createContext<TableHeaderContextType>({\n  isHeaderMenuActive: false,\n  setHeaderMenuActive: (isHeaderMenuActive: boolean) => false,\n});\n\nexport const TableHeaderContextProvider = ({ children }: { children: ReactNode }) => {\n  const [isHeaderMenuActive, setHeaderMenuActive] = useState<boolean>(false);\n\n  const handleisHeaderMenuActive = useCallback((isHeaderMenuActive: boolean) => {\n    setHeaderMenuActive(isHeaderMenuActive);\n  }, []);\n\n  return (\n    <TableHeaderContext.Provider value={{ isHeaderMenuActive, setHeaderMenuActive: handleisHeaderMenuActive }}>\n      {children}\n    </TableHeaderContext.Provider>\n  );\n};\n\nexport const useTableHeaderContext = () => {\n  return useContext(TableHeaderContext);\n};\n", "import React, { PropsWithChildren } from 'react';\n\nimport { css, cx } from '@emotion/css';\n\nimport { Field, GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\nimport { useTableCellContext } from 'Components/Table/Context/TableCellContext';\n\ninterface DefaultCellWrapComponentProps {}\n\ninterface Props extends PropsWithChildren<DefaultCellWrapComponentProps> {\n  field: Field;\n  onClick?: () => void;\n  onMouseIn?: () => void;\n  onMouseOut?: () => void;\n  rowIndex: number;\n}\n\nconst getStyles = (theme: GrafanaTheme2, bgColor?: string, numberOfMenuItems?: number) => ({\n  active: css({\n    background: 'transparent',\n    // Save 20px for context menu\n    height: `calc(${100}% + 36px)`,\n    zIndex: theme.zIndex.tooltip,\n  }),\n  wrap: css({\n    background: bgColor ?? 'transparent',\n    height: '100%',\n    left: 0,\n    margin: 'auto',\n    overflowX: 'hidden',\n    position: 'absolute',\n    top: 0,\n    whiteSpace: 'nowrap',\n    width: '100%',\n  }),\n});\n\nexport const DefaultCellWrapComponent = (props: Props) => {\n  return (\n    <CellWrapInnerComponent\n      onMouseOut={props.onMouseOut}\n      onMouseIn={props.onMouseIn}\n      onClick={props.onClick}\n      field={props.field}\n      rowIndex={props.rowIndex}\n    >\n      {props.children}\n    </CellWrapInnerComponent>\n  );\n};\n\nconst CellWrapInnerComponent = (props: Props) => {\n  const theme = useTheme2();\n  const cellState = useTableCellContext();\n  const styles = getStyles(theme, undefined, cellState.cellIndex?.numberOfMenuItems);\n\n  return (\n    <div\n      onMouseLeave={props.onMouseOut}\n      onMouseEnter={props.onMouseIn}\n      onClick={props.onClick}\n      className={\n        cellState.cellIndex.index === props.rowIndex && cellState.cellIndex.fieldName === props.field.name\n          ? cx(styles.wrap, styles.active)\n          : styles.wrap\n      }\n      onKeyDown={(e) => {\n        if (e.key === 'Enter' || e.key === ' ') {\n          props.onClick?.();\n        }\n      }}\n      role=\"button\"\n      tabIndex={0}\n    >\n      {props.children}\n    </div>\n  );\n};\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2, LinkModel } from '@grafana/data';\nimport { Icon, useTheme2 } from '@grafana/ui';\n\nimport { FilterOp } from '../../services/filterTypes';\nimport { useQueryContext } from './Context/QueryContext';\n\ninterface Props {\n  fieldType?: 'derived';\n  label: string;\n  links?: LinkModel[];\n  pillType: 'column' | 'logPill';\n  showColumn?: () => void;\n  value: string;\n}\n\nconst getStyles = (theme: GrafanaTheme2, pillType: 'column' | 'logPill') => ({\n  menu: css({\n    display: 'flex',\n    justifyContent: 'flex-start',\n    minWidth: '60px',\n    paddingRight: '5px',\n    position: 'relative',\n  }),\n  menuItem: css({\n    alignItems: 'center',\n    cursor: 'pointer',\n    display: 'flex',\n    overflow: 'auto',\n    paddingLeft: '5px',\n    paddingRight: '5px',\n    textOverflow: 'ellipsis',\n  }),\n  menuItemsWrap: css({\n    background: theme.colors.background.secondary,\n    boxShadow: theme.shadows.z3,\n    display: 'flex',\n    marginLeft: pillType === 'column' ? '5px' : undefined,\n    padding: '5px 0',\n  }),\n});\n\nexport const CellContextMenu = (props: Props) => {\n  const theme = useTheme2();\n  const styles = getStyles(theme, props.pillType);\n  const { addFilter } = useQueryContext();\n\n  return (\n    <span className={styles.menu}>\n      <span className={styles.menuItemsWrap}>\n        {props.fieldType !== 'derived' && (\n          <>\n            <div\n              className={styles.menuItem}\n              role=\"button\"\n              tabIndex={0}\n              onClick={() => {\n                addFilter({\n                  key: props.label,\n                  operator: FilterOp.Equal,\n                  value: props.value,\n                });\n              }}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  addFilter({\n                    key: props.label,\n                    operator: FilterOp.Equal,\n                    value: props.value,\n                  });\n                }\n              }}\n            >\n              <Icon title={'Add to search'} size={'md'} name={'plus-circle'} />\n            </div>\n            <div\n              className={styles.menuItem}\n              role=\"button\"\n              tabIndex={0}\n              onClick={() => {\n                addFilter({\n                  key: props.label,\n                  operator: FilterOp.NotEqual,\n                  value: props.value,\n                });\n              }}\n              onKeyDown={(e) => {\n                if (e.key === 'Enter' || e.key === ' ') {\n                  addFilter({\n                    key: props.label,\n                    operator: FilterOp.NotEqual,\n                    value: props.value,\n                  });\n                }\n              }}\n            >\n              <Icon title={'Exclude from search'} size={'md'} name={'minus-circle'} />\n            </div>\n          </>\n        )}\n\n        {props.showColumn && (\n          <div\n            title={'Add column'}\n            role=\"button\"\n            tabIndex={0}\n            className={styles.menuItem}\n            onClick={props.showColumn}\n            onKeyDown={(e) => {\n              if (e.key === 'Enter' || e.key === ' ') {\n                props.showColumn?.();\n              }\n            }}\n          >\n            <svg width=\"18\" height=\"16\" viewBox=\"0 0 18 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path\n                fillRule=\"evenodd\"\n                clipRule=\"evenodd\"\n                d=\"M1.38725 1.33301H13.3872C13.5641 1.33301 13.7336 1.40325 13.8587 1.52827C13.9837 1.65329 14.0539 1.82286 14.0539 1.99967V2.33333C14.0539 2.70152 13.7554 3 13.3872 3H13.0542C12.87 3 12.7206 2.85062 12.7206 2.66634H8.05391V13.333H12.7206C12.7206 13.1491 12.8697 13 13.0536 13H13.3872C13.7554 13 14.0539 13.2985 14.0539 13.6667V13.9997C14.0539 14.1765 13.9837 14.3461 13.8587 14.4711C13.7336 14.5961 13.5641 14.6663 13.3872 14.6663H1.38725C1.21044 14.6663 1.04087 14.5961 0.915843 14.4711C0.790819 14.3461 0.720581 14.1765 0.720581 13.9997V1.99967C0.720581 1.82286 0.790819 1.65329 0.915843 1.52827C1.04087 1.40325 1.21044 1.33301 1.38725 1.33301ZM2.05391 13.333H6.72058V2.66634H2.05391V13.333Z\"\n                fill=\"#CCCCDC\"\n                fillOpacity=\"1\"\n              />\n              <path\n                d=\"M13.8538 7.19999H16.2538C16.466 7.19999 16.6695 7.28429 16.8195 7.4343C16.9696 7.58432 17.0538 7.78783 17.0538 7.99999C17.0538 8.21214 16.9696 8.41566 16.8195 8.56567C16.6695 8.71569 16.466 8.79999 16.2538 8.79999H13.8538V11.2C13.8538 11.4121 13.7696 11.6156 13.6195 11.7657C13.4695 11.9157 13.266 12 13.0538 12C12.8416 12 12.6382 11.9157 12.4881 11.7657C12.3381 11.6156 12.2538 11.4121 12.2538 11.2V8.79999H9.85384C9.64165 8.79999 9.43819 8.71569 9.28815 8.56567C9.13811 8.41566 9.05383 8.21214 9.05383 7.99999C9.05383 7.78783 9.13811 7.58432 9.28815 7.4343C9.43819 7.28429 9.64165 7.19999 9.85384 7.19999H12.2538V4.8C12.2538 4.58784 12.3381 4.38433 12.4881 4.23431C12.6382 4.0843 12.8416 4 13.0538 4C13.266 4 13.4695 4.0843 13.6195 4.23431C13.7696 4.38433 13.8538 4.58784 13.8538 4.8V7.19999Z\"\n                fill=\"#CCCCDC\"\n                fillOpacity=\"1\"\n              />\n            </svg>\n          </div>\n        )}\n\n        {props.links &&\n          props.links.map((link) => {\n            return (\n              <div\n                className={styles.menuItem}\n                role=\"button\"\n                tabIndex={0}\n                onClick={() => {\n                  window.open(link.href, '_blank');\n                }}\n                onKeyDown={(e) => {\n                  if (e.key === 'Enter' || e.key === ' ') {\n                    window.open(link.href, '_blank');\n                  }\n                }}\n                key={link.href}\n              >\n                <Icon title={link.title ?? 'Link'} key={link.href} size={'md'} name={'link'} />\n              </div>\n            );\n          })}\n      </span>\n    </span>\n  );\n};\n", "import React, { ReactElement } from 'react';\n\nimport { css, cx } from '@emotion/css';\n\nimport { Field, FieldType, GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\nimport { LEVEL_NAME } from './constants';\nimport { CellContextMenu } from 'Components/Table/CellContextMenu';\nimport { useTableCellContext } from 'Components/Table/Context/TableCellContext';\nimport { getFieldMappings } from 'Components/Table/Table';\n\ninterface DefaultPillProps {\n  field: Field;\n  label: string;\n  rowIndex: number;\n  showColumns?: () => void;\n  value: string | unknown | ReactElement;\n}\n\nconst getStyles = (theme: GrafanaTheme2, levelColor?: string) => ({\n  activePillWrap: css({}),\n  menu: css({\n    width: '100%',\n  }),\n  menuItem: css({\n    overflow: 'auto',\n    textOverflow: 'ellipsis',\n  }),\n  menuItemText: css({\n    display: 'inline-block',\n    width: '65px',\n  }),\n  pill: css({\n    '&:before': {\n      backgroundColor: levelColor,\n      content: '\"\"',\n      height: '100%',\n      left: 0,\n      position: 'absolute',\n      top: 0,\n      width: `${theme.spacing(0.25)}`,\n    },\n    '&:hover': {\n      border: `1px solid ${theme.colors.border.strong}`,\n    },\n    backgroundColor: 'transparent',\n    border: `1px solid ${theme.colors.border.weak}`,\n    display: 'inline-flex',\n    flexDirection: 'row-reverse',\n    marginLeft: '5px',\n    marginRight: '5px',\n    marginTop: '4px',\n    padding: '2px 5px',\n\n    paddingLeft: levelColor ? `${theme.spacing(0.75)}` : `2px`,\n\n    position: 'relative',\n  }),\n  pillWrap: css({\n    width: '100%',\n  }),\n});\nexport const DefaultPill = (props: DefaultPillProps) => {\n  const { label, value } = props;\n  const theme = useTheme2();\n  const { cellIndex } = useTableCellContext();\n  let levelColor;\n\n  if (label === LEVEL_NAME) {\n    const mappings = getFieldMappings().options;\n    if (typeof value === 'string' && value in mappings) {\n      levelColor = mappings[value].color;\n    }\n  }\n\n  const isPillActive = cellIndex.index === props.rowIndex && props.field.name === cellIndex.fieldName;\n\n  const styles = getStyles(theme, levelColor);\n  return (\n    <div className={cx(styles.pillWrap, isPillActive ? styles.activePillWrap : undefined)}>\n      {!!value && (\n        <>\n          <span className={styles.pill}>\n            <>{value}</>\n          </span>\n          {isPillActive && typeof value === 'string' && props.field.type !== FieldType.time && (\n            <CellContextMenu label={props.label} value={value} pillType={'column'} />\n          )}\n        </>\n      )}\n    </div>\n  );\n};\n", "import React, { useCallback, useState } from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { ClipboardButton, IconButton, Modal, useTheme2 } from '@grafana/ui';\n\nimport { testIds } from '../../services/testIds';\nimport { useQueryContext } from 'Components/Table/Context/QueryContext';\nimport { generateLogShortlink } from 'services/text';\n\nexport const getStyles = (theme: GrafanaTheme2, isNumber?: boolean) => ({\n  clipboardButton: css({\n    height: '100%',\n    lineHeight: '1',\n    padding: 0,\n    width: '20px',\n  }),\n  iconWrapper: css({\n    background: theme.colors.background.secondary,\n    boxShadow: theme.shadows.z2,\n    display: 'flex',\n    flexDirection: isNumber ? 'row-reverse' : 'row',\n    height: '35px',\n    left: 0,\n    padding: `0 ${theme.spacing(0.5)}`,\n    position: isNumber ? 'absolute' : 'sticky',\n    zIndex: 1,\n  }),\n  inspect: css({\n    '& button svg': {\n      marginRight: isNumber ? '0' : 'auto',\n    },\n    '&:hover': {\n      color: theme.colors.text.link,\n      cursor: 'pointer',\n    },\n    padding: '5px 3px',\n  }),\n  inspectButton: css({\n    borderRadius: '5px',\n    display: 'inline-flex',\n    margin: 0,\n    overflow: 'hidden',\n    verticalAlign: 'middle',\n  }),\n});\nexport function LineActionIcons(props: { rowIndex: number; value: unknown }) {\n  // Check if the value is a number to reset the position of the icons for direction 'rtl'\n  const isNumber = typeof props.value === 'string' && !isNaN(Number(props.value));\n  const theme = useTheme2();\n  const styles = getStyles(theme, isNumber);\n  const { logsFrame, timeRange } = useQueryContext();\n  const logId = logsFrame?.idField?.values[props.rowIndex];\n  const lineValue = logsFrame?.bodyField.values[props.rowIndex];\n  const [isInspecting, setIsInspecting] = useState(false);\n  const getText = useCallback(() => {\n    if (timeRange) {\n      return generateLogShortlink('selectedLine', { id: logId, row: props.rowIndex }, timeRange);\n    }\n    return '';\n  }, [logId, props.rowIndex, timeRange]);\n  return (\n    <>\n      <div className={styles.iconWrapper}>\n        <div className={styles.inspect}>\n          <IconButton\n            data-testid={testIds.table.inspectLine}\n            className={styles.inspectButton}\n            tooltip=\"View log line\"\n            variant=\"secondary\"\n            aria-label=\"View log line\"\n            tooltipPlacement=\"top\"\n            size=\"md\"\n            name=\"eye\"\n            onClick={() => setIsInspecting(true)}\n            tabIndex={0}\n          />\n        </div>\n        <div className={styles.inspect}>\n          <ClipboardButton\n            className={styles.clipboardButton}\n            icon=\"share-alt\"\n            variant=\"secondary\"\n            fill=\"text\"\n            size=\"md\"\n            tooltip=\"Copy link to log line\"\n            tooltipPlacement=\"top\"\n            tabIndex={0}\n            getText={getText}\n          />\n        </div>\n      </div>\n      <>\n        {isInspecting && (\n          <Modal onDismiss={() => setIsInspecting(false)} isOpen={true} title=\"Inspect value\">\n            <pre>{lineValue}</pre>\n            <Modal.ButtonRow>\n              <ClipboardButton icon=\"copy\" getText={() => props.value as string}>\n                Copy to Clipboard\n              </ClipboardButton>\n            </Modal.ButtonRow>\n          </Modal>\n        )}\n      </>\n    </>\n  );\n}\n", "import React, { ReactElement } from 'react';\n\nimport { css } from '@emotion/css';\nimport { Row } from 'react-table';\n\nimport { FieldType, formattedValueToString, GrafanaTheme2 } from '@grafana/data';\nimport { CustomCellRendererProps, DataLinksContextMenu, getCellLinks, useTheme2 } from '@grafana/ui';\n\nimport { useTableCellContext } from 'Components/Table/Context/TableCellContext';\nimport { DefaultCellWrapComponent } from 'Components/Table/DefaultCellWrapComponent';\nimport { DefaultPill } from 'Components/Table/DefaultPill';\nimport { LineActionIcons } from 'Components/Table/LineActionIcons';\n\nconst getStyles = (theme: GrafanaTheme2, fieldType?: FieldType) => ({\n  content: css({\n    display: 'flex',\n    height: '100%',\n    overflow: 'hidden',\n    position: 'relative',\n  }),\n  flexWrap: css({\n    alignItems: 'flex-start',\n    display: 'flex',\n  }),\n  linkWrapper: css({\n    '&:hover': {\n      textDecoration: 'underline',\n    },\n    color: theme.colors.text.link,\n    marginLeft: '7px',\n    marginTop: '7px',\n  }),\n});\n\ninterface DefaultCellComponentCustomProps {\n  fieldIndex: number;\n}\nexport const DefaultCellComponent = (props: CustomCellRendererProps & DefaultCellComponentCustomProps) => {\n  let value = props.value;\n  const field = props.field;\n  const displayValue = field.display!(value);\n  const theme = useTheme2();\n  const styles = getStyles(theme, props.field.type);\n  const { cellIndex, setActiveCellIndex } = useTableCellContext();\n\n  // We don't get back the full react.table row here, but the calling function only uses the index, which are in `CustomCellRendererProps`\n  const row = { index: props.rowIndex } as Row;\n  const hasLinks = Boolean(getCellLinks(props.field, row)?.length);\n\n  if (value === null) {\n    return <></>;\n  }\n\n  if (React.isValidElement(props.value)) {\n    value = props.value;\n  } else if (typeof value === 'object') {\n    value = JSON.stringify(props.value);\n  } else {\n    value = formattedValueToString(displayValue);\n  }\n\n  const renderValue = (value: string | unknown | ReactElement, label: string) => {\n    return <DefaultPill field={props.field} rowIndex={props.rowIndex} label={label} value={value} />;\n  };\n\n  return (\n    <DefaultCellWrapComponent\n      onClick={() => {\n        if (props.rowIndex === cellIndex.index && props.field.name === cellIndex.fieldName) {\n          return setActiveCellIndex({ index: null });\n        }\n        return setActiveCellIndex({ fieldName: props.field.name, index: props.rowIndex, numberOfMenuItems: 3 });\n      }}\n      field={props.field}\n      rowIndex={props.rowIndex}\n    >\n      <div className={styles.content}>\n        {props.fieldIndex === 0 && <LineActionIcons value={value} rowIndex={props.rowIndex} />}\n        <div className={styles.flexWrap}></div>\n\n        {!hasLinks && renderValue(value, field.name)}\n\n        {hasLinks && field.getLinks && (\n          <DataLinksContextMenu links={() => getCellLinks(field, row) ?? []}>\n            {(api) => {\n              if (api.openMenu) {\n                return (\n                  <button className={styles.linkWrapper} onClick={api.openMenu}>\n                    <>{value as React.ReactNode}</>\n                  </button>\n                );\n              } else {\n                return (\n                  <div className={styles.linkWrapper}>\n                    <>{value as React.ReactNode}</>\n                  </div>\n                );\n              }\n            }}\n          </DataLinksContextMenu>\n        )}\n      </div>\n    </DefaultCellWrapComponent>\n  );\n};\n", "import { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\n\nexport const useSharedStyles = () => {\n  return useStyles2((theme: GrafanaTheme2) => {\n    return {\n      linkButton: css({\n        '&:focus': {\n          outline: 'none',\n        },\n        appearance: 'none',\n        background: 'none',\n        border: 'none',\n        color: 'inherit',\n        cursor: 'pointer',\n        font: 'inherit',\n        lineHeight: 'normal',\n        margin: 0,\n        MozOsxFontSmoothing: 'inherit',\n        padding: 0,\n        textAlign: 'inherit',\n        WebkitAppearance: 'none',\n        WebkitFontSmoothing: 'inherit',\n      }),\n    };\n  });\n};\n", "import React, { useMemo } from 'react';\n\nimport { css, cx } from '@emotion/css';\nimport { Row } from 'react-table';\n\nimport { DataFrame, Field, FieldType, getLinksSupplier, GrafanaTheme2, LinkModel } from '@grafana/data';\nimport { getTemplateSrv } from '@grafana/runtime';\nimport { getCellLinks, useTheme2 } from '@grafana/ui';\n\nimport { LEVEL_NAME } from './constants';\nimport { CellContextMenu } from 'Components/Table/CellContextMenu';\nimport { useTableCellContext } from 'Components/Table/Context/TableCellContext';\nimport { useTableColumnContext } from 'Components/Table/Context/TableColumnsContext';\nimport { getFieldMappings } from 'Components/Table/Table';\nimport { FieldNameMetaStore } from 'Components/Table/TableTypes';\nimport { useSharedStyles } from 'styles/shared-styles';\n\ninterface LogLinePillProps {\n  columns: FieldNameMetaStore;\n  field?: Field;\n  frame: DataFrame;\n  isDerivedField: boolean;\n  label: string;\n  originalField?: Field;\n  originalFrame: DataFrame | undefined;\n  rowIndex: number;\n  value: string;\n}\n\nconst getStyles = (theme: GrafanaTheme2, levelColor?: string) => ({\n  activePill: css({}),\n  pill: css({\n    display: 'inline-flex',\n    flex: '0 1 auto',\n    flexDirection: 'column',\n    marginLeft: theme.spacing(0.5),\n    marginRight: theme.spacing(0.5),\n    marginTop: theme.spacing(0.5),\n    position: 'relative',\n  }),\n  valueWrap: css({\n    '&:before': {\n      backgroundColor: levelColor,\n      content: '\"\"',\n      height: '100%',\n      left: 0,\n      position: 'absolute',\n      top: 0,\n      width: `${theme.spacing(0.25)}`,\n    },\n    '&:hover': {\n      border: `1px solid ${theme.colors.border.strong}`,\n    },\n    backgroundColor: 'transparent',\n    border: `1px solid ${theme.colors.border.weak}`,\n\n    cursor: 'pointer',\n\n    padding: '5px 5px 4px 2px',\n    paddingLeft: levelColor ? `${theme.spacing(0.75)}` : `${theme.spacing(0.5)}`,\n\n    position: 'relative',\n  }),\n});\n\nfunction LogLinePillValue(props: {\n  fieldType?: 'derived';\n  label: string;\n  links?: LinkModel[];\n  menuActive: boolean;\n  onClick: () => void;\n  onClickAdd: () => void;\n  value: string;\n}) {\n  const theme = useTheme2();\n  const { linkButton } = useSharedStyles();\n\n  let levelColor;\n  if (props.label === LEVEL_NAME) {\n    const mappings = getFieldMappings().options;\n    if (props.value in mappings) {\n      levelColor = mappings[props.value].color;\n    }\n  }\n\n  const styles = getStyles(theme, levelColor);\n\n  return (\n    <button\n      className={cx(linkButton, styles.pill, props.menuActive ? styles.activePill : undefined)}\n      onClick={props.onClick}\n    >\n      <span className={styles.valueWrap}>\n        {props.label}={props.value}\n      </span>\n      {props.menuActive && (\n        <CellContextMenu\n          pillType={'logPill'}\n          fieldType={props.fieldType}\n          links={props.links}\n          label={props.label}\n          value={props.value}\n          showColumn={props.onClickAdd}\n        />\n      )}\n    </button>\n  );\n}\n\nexport const LogLinePill = (props: LogLinePillProps) => {\n  const { label } = props;\n  const { cellIndex, setActiveCellIndex } = useTableCellContext();\n  const { columns, setColumns } = useTableColumnContext();\n  const value = props.value;\n  const templateSrv = getTemplateSrv();\n  const replace = useMemo(() => templateSrv.replace.bind(templateSrv), [templateSrv]);\n\n  // Need untransformed frame for links?\n  const field = props.field;\n\n  if (!field || field?.type === FieldType.other) {\n    return null;\n  }\n  const row = { index: props.rowIndex } as Row;\n\n  if (props.originalField && props.isDerivedField && props.originalFrame) {\n    props.originalField.getLinks = getLinksSupplier(props.originalFrame, props.originalField, {}, replace);\n  }\n\n  const links = props.originalField && getCellLinks(props.originalField, row);\n\n  /**\n   * This Could be moved?\n   * Callback called by the pill context menu\n   * @param fieldName\n   */\n  const addFieldToColumns = (fieldName: string) => {\n    const pendingColumns = { ...columns };\n\n    const length = Object.keys(columns).filter((c) => columns[c].active).length;\n    if (pendingColumns[fieldName].active) {\n      pendingColumns[fieldName] = {\n        ...pendingColumns[fieldName],\n        active: false,\n        index: undefined,\n      };\n    } else {\n      pendingColumns[fieldName] = {\n        ...pendingColumns[fieldName],\n        active: true,\n        index: length,\n      };\n    }\n\n    setColumns(pendingColumns);\n  };\n\n  return (\n    <LogLinePillValue\n      onClick={() => {\n        if (\n          props.rowIndex === cellIndex.index &&\n          field.name === cellIndex.fieldName &&\n          label === cellIndex.subFieldName\n        ) {\n          return setActiveCellIndex({ index: null });\n        }\n\n        return setActiveCellIndex({\n          fieldName: field.name,\n          index: props.rowIndex,\n          numberOfMenuItems: props.isDerivedField ? 2 : 3,\n          subFieldName: label,\n        });\n      }}\n      menuActive={\n        cellIndex.index === props.rowIndex && cellIndex.fieldName === field.name && cellIndex.subFieldName === label\n      }\n      fieldType={props.isDerivedField ? 'derived' : undefined}\n      label={label}\n      value={value}\n      onClickAdd={() => addFieldToColumns(label)}\n      links={links}\n    />\n  );\n};\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useTheme2 } from '@grafana/ui';\n\nimport { testIds } from '../../services/testIds';\n\nexport function RawLogLineText(props: { value: unknown }) {\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n  return (\n    <div data-testid={testIds.table.rawLogLine} className={styles.rawLogLine}>\n      <>{props.value}</>\n    </div>\n  );\n}\n\nexport const getStyles = (theme: GrafanaTheme2, bgColor?: string) => ({\n  rawLogLine: css({\n    fontFamily: theme.typography.fontFamilyMonospace,\n    fontSize: theme.typography.bodySmall.fontSize,\n    height: '35px',\n    lineHeight: '35px',\n    paddingLeft: theme.spacing(1),\n    paddingRight: theme.spacing(1.5),\n  }),\n});\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { Icon, useTheme2 } from '@grafana/ui';\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  scroller: css`\n    position: absolute;\n    right: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 40px;\n    height: 20px;\n    top: 32px;\n    margin-top: -24px;\n    // For some reason clicking on this button causes text to be selected in the following row\n    user-select: none;\n  `,\n  scrollLeft: css`\n    cursor: pointer;\n    background: ${theme.colors.background.primary};\n\n    &:hover {\n      background: ${theme.colors.background.secondary};\n    }\n  `,\n  scrollRight: css`\n    cursor: pointer;\n    background: ${theme.colors.background.primary};\n\n    &:hover {\n      background: ${theme.colors.background.secondary};\n    }\n  `,\n});\n\nconst stopScroll = (id: React.MutableRefObject<HTMLDivElement | null>) => {\n  id?.current?.scrollTo({\n    left: id.current?.scrollLeft,\n  });\n};\n\nconst goLeft = (id: React.MutableRefObject<HTMLDivElement | null>) => {\n  id?.current?.scrollTo({\n    behavior: 'smooth',\n    left: 0,\n    top: 0,\n  });\n};\n\nconst goRight = (id: React.MutableRefObject<HTMLDivElement | null>) => {\n  id?.current?.scrollTo({\n    behavior: 'smooth',\n    left: id.current.scrollWidth,\n    top: 0,\n  });\n};\n\nexport function Scroller({ scrollerRef: ref }: { scrollerRef: React.MutableRefObject<HTMLDivElement | null> }) {\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n  return (\n    <div className={styles.scroller}>\n      <span onPointerDown={() => goLeft(ref)} onPointerUp={() => stopScroll(ref)} className={styles.scrollLeft}>\n        <Icon name={'arrow-left'} />\n      </span>\n      <span onPointerDown={() => goRight(ref)} onPointerUp={() => stopScroll(ref)} className={styles.scrollRight}>\n        <Icon name={'arrow-right'} />\n      </span>\n    </div>\n  );\n}\n", "import React, { useRef, useState } from 'react';\n\nimport { css } from '@emotion/css';\nimport { ScrollSyncPane } from 'react-scroll-sync';\n\nimport { FieldType, formattedValueToString, GrafanaTheme2, Labels } from '@grafana/data';\nimport { CustomCellRendererProps, useTheme2 } from '@grafana/ui';\n\nimport { getBodyName } from '../../services/logsFrame';\nimport { LEVEL_NAME } from './constants';\nimport { useQueryContext } from 'Components/Table/Context/QueryContext';\nimport { LogLineState, useTableColumnContext } from 'Components/Table/Context/TableColumnsContext';\nimport { DefaultCellWrapComponent } from 'Components/Table/DefaultCellWrapComponent';\nimport { LineActionIcons } from 'Components/Table/LineActionIcons';\nimport { LogLinePill } from 'Components/Table/LogLinePill';\nimport { RawLogLineText } from 'Components/Table/RawLogLineText';\nimport { Scroller } from 'Components/Table/Scroller';\n\nexport type SelectedTableRow = {\n  id: string;\n  row: number;\n};\n\ninterface Props extends CustomCellRendererProps {\n  fieldIndex: number;\n  labels: Labels;\n}\nexport const LogLineCellComponent = (props: Props) => {\n  let value = props.value;\n  const field = props.field;\n  const displayValue = field.display!(value);\n  const theme = useTheme2();\n  const styles = getStyles(theme);\n  const { bodyState, columns } = useTableColumnContext();\n  const { logsFrame } = useQueryContext();\n  const [isHover, setIsHover] = useState(false);\n  const ref = useRef<HTMLDivElement | null>(null);\n\n  if (React.isValidElement(props.value)) {\n    value = props.value;\n  } else if (typeof value === 'object') {\n    value = JSON.stringify(props.value);\n  } else {\n    value = formattedValueToString(displayValue);\n  }\n\n  /**\n   * Render labels as log line pills\n   * @param labels Label[]\n   */\n  const renderLabels = (labels: Labels) => {\n    const columnLabelNames = Object.keys(columns);\n    const labelNames = columnLabelNames\n      .filter((name) => name !== getBodyName(logsFrame))\n      .sort((a, b) => {\n        // Sort level first\n        if (a === LEVEL_NAME) {\n          return -1;\n        }\n        if (b === LEVEL_NAME) {\n          return 1;\n        }\n        // Then sort links\n        if (columns[a].type === 'LINK_FIELD') {\n          return -1;\n        }\n        if (columns[b].type === 'LINK_FIELD') {\n          return 1;\n        }\n\n        // Finally sort fields by cardinality descending\n        return columns[a].cardinality > columns[b].cardinality ? -1 : 1;\n      });\n\n    const filteredLabels = labelNames.filter(\n      (label) =>\n        // Not already visible in another column\n        !columns[label].active &&\n        // And the cardinality is greater than 1\n        columns[label].cardinality > 1\n    );\n\n    return filteredLabels\n      .map((label) => {\n        const labelValue = labels[label];\n        const untransformedField = logsFrame?.raw?.fields.find((field) => field.name === label);\n        const rawValue = field?.values[props.rowIndex];\n        const isDerived = !labelValue && !!rawValue;\n\n        // If we have a label value, the field is not derived\n        if (labelValue) {\n          return (\n            <LogLinePill\n              originalFrame={undefined}\n              field={field}\n              columns={columns}\n              rowIndex={props.rowIndex}\n              frame={props.frame}\n              key={label}\n              label={label}\n              isDerivedField={false}\n              value={labelValue}\n            />\n          );\n        }\n\n        // Otherwise, the field might be derived\n        if (isDerived && untransformedField?.name) {\n          const untransformedValue = untransformedField?.values[props.rowIndex];\n          if (untransformedField?.type === FieldType.string && untransformedValue) {\n            return (\n              <LogLinePill\n                originalFrame={logsFrame?.raw}\n                originalField={untransformedField}\n                field={field}\n                value={untransformedValue}\n                columns={columns}\n                rowIndex={props.rowIndex}\n                frame={props.frame}\n                key={untransformedField.name}\n                label={untransformedField.name}\n                isDerivedField={true}\n              />\n            );\n          }\n        }\n\n        return null;\n      })\n      .filter((v) => v);\n  };\n\n  const labels = renderLabels(props.labels);\n  const isAuto = bodyState === LogLineState.auto;\n  const hasLabels = labels.length > 0;\n\n  return (\n    <DefaultCellWrapComponent\n      onMouseIn={() => {\n        setIsHover(true);\n      }}\n      onMouseOut={() => {\n        setIsHover(false);\n      }}\n      rowIndex={props.rowIndex}\n      field={props.field}\n    >\n      <ScrollSyncPane innerRef={ref} group=\"horizontal\">\n        <div className={styles.content}>\n          {/* First Field gets the icons */}\n          {props.fieldIndex === 0 && <LineActionIcons rowIndex={props.rowIndex} value={value} />}\n          {/* Labels */}\n          {isAuto && hasLabels && <>{labels}</>}\n          {bodyState === LogLineState.labels && hasLabels && <>{labels}</>}\n          {bodyState === LogLineState.labels && !hasLabels && <RawLogLineText value={value} />}\n          {/* Raw log line*/}\n          {isAuto && !hasLabels && <RawLogLineText value={value} />}\n          {bodyState === LogLineState.text && <RawLogLineText value={value} />}\n          {isHover && <Scroller scrollerRef={ref} />}\n        </div>\n      </ScrollSyncPane>\n    </DefaultCellWrapComponent>\n  );\n};\n\nexport const getStyles = (theme: GrafanaTheme2) => ({\n  content: css`\n    white-space: nowrap;\n    overflow-x: auto;\n    -ms-overflow-style: none; /* IE and Edge */\n    scrollbar-width: none; /* Firefox */\n    padding-right: 30px;\n    display: flex;\n    align-items: flex-start;\n    height: 100%;\n    &::-webkit-scrollbar {\n      display: none; /* Chrome, Safari and Opera */\n    }\n\n    &:after {\n      pointer-events: none;\n      content: '';\n      width: 100%;\n      height: 100%;\n      position: absolute;\n      left: 0;\n      top: 0;\n      // Fade out text in last 10px to background color to add affordance to horiziontal scroll\n      background: linear-gradient(to right, transparent calc(100% - 10px), ${theme.colors.background.primary});\n    }\n  `,\n});\n", "import React, { PropsWithChildren, useEffect, useRef } from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { t } from '@grafana/i18n';\nimport { ClickOutsideWrapper, IconButton, useStyles2 } from '@grafana/ui';\n\ninterface LogsTableHeaderProps extends PropsWithChildren {\n  setHeaderMenuActive: (active: boolean) => void;\n}\n\nexport function LogsTableHeaderMenu({ setHeaderMenuActive, children }: LogsTableHeaderProps) {\n  const styles = useStyles2(getStyles);\n  const ref = useRef<null | HTMLButtonElement>(null);\n  useEffect(() => {\n    ref.current?.focus();\n  }, []);\n  return (\n    <ClickOutsideWrapper includeButtonPress={false} onClick={() => setHeaderMenuActive(false)} useCapture={true}>\n      <div className={styles.tableHeaderMenu}>\n        <IconButton\n          ref={ref}\n          className={styles.closeButton}\n          aria-label={t('logs.table.header.close', 'Close')}\n          name={'times'}\n          onClick={() => setHeaderMenuActive(false)}\n        />\n        {children}\n      </div>\n    </ClickOutsideWrapper>\n  );\n}\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  closeButton: css({\n    position: 'absolute',\n    top: '14px',\n    right: '2px',\n  }),\n  tableHeaderMenu: css({\n    display: 'block',\n    position: 'static',\n    backgroundColor: theme.colors.background.primary,\n    border: `1px solid ${theme.colors.border.weak}`,\n    borderRadius: theme.shape.radius.default,\n    boxShadow: theme.shadows.z3,\n    height: '100%',\n    label: 'tableHeaderMenu',\n    margin: theme.spacing(1, 0),\n    maxHeight: '400px',\n    minWidth: '250px',\n    padding: theme.spacing(2),\n    width: '100%',\n  }),\n});\n", "import React, { PropsWithChildren, useRef } from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { Field, GrafanaTheme2 } from '@grafana/data';\nimport { IconButton, Popover, useTheme2 } from '@grafana/ui';\n\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../services/analytics';\nimport { getBodyName } from '../../services/logsFrame';\nimport { useQueryContext } from './Context/QueryContext';\nimport { LogLineState, useTableColumnContext } from './Context/TableColumnsContext';\nimport { LogsTableHeaderMenu } from './LogsTableHeaderMenu';\nimport { useTableHeaderContext } from 'Components/Table/Context/TableHeaderContext';\n\nexport interface LogsTableHeaderProps extends PropsWithChildren<CustomHeaderRendererProps> {\n  fieldIndex: number;\n}\n//@todo delete when released in Grafana core\nexport interface CustomHeaderRendererProps {\n  defaultContent: React.ReactNode;\n  field: Field;\n}\n\nconst getStyles = (theme: GrafanaTheme2, isFirstColumn: boolean, isLine: boolean) => ({\n  closeButton: css({\n    position: 'absolute',\n    top: '14px',\n    right: '2px',\n  }),\n  clearButton: css({\n    marginLeft: '5px',\n  }),\n  defaultContentWrapper: css({\n    borderLeft: isFirstColumn ? `1px solid ${theme.colors.border.weak}` : 'none',\n    display: 'flex',\n    marginLeft: isFirstColumn ? '-6px' : 0,\n    paddingLeft: isFirstColumn ? '12px' : 0,\n  }),\n  leftAlign: css({\n    display: 'flex',\n    label: 'left-align',\n    width: 'calc(100% - 20px)',\n  }),\n  logLineButton: css({\n    marginLeft: '5px',\n  }),\n  rightAlign: css({\n    display: 'flex',\n    label: 'right-align',\n    marginRight: '5px',\n  }),\n  tableHeaderMenu: css({\n    display: 'block',\n    position: 'static',\n    backgroundColor: theme.colors.background.primary,\n    border: `1px solid ${theme.colors.border.weak}`,\n    borderRadius: theme.shape.radius.default,\n    boxShadow: theme.shadows.z3,\n    height: '100%',\n    label: 'tableHeaderMenu',\n    margin: theme.spacing(1, 0),\n    maxHeight: '400px',\n    minWidth: '250px',\n    padding: theme.spacing(2),\n    width: '100%',\n  }),\n  wrapper: css({\n    // Hack to show a visible resize indicator, despite 6px of padding on the header in grafana/table\n    borderRight: `1px solid ${theme.colors.border.weak}`,\n    display: 'flex',\n    label: 'wrapper',\n    marginLeft: isFirstColumn ? '56px' : '6px',\n\n    marginRight: '-6px',\n    // Body has extra padding then other columns\n    width: isLine ? 'calc(100% + 6px)' : '100%',\n  }),\n});\n\nexport const LogsTableHeader = (props: LogsTableHeaderProps) => {\n  const { isHeaderMenuActive, setHeaderMenuActive } = useTableHeaderContext();\n  const { logsFrame } = useQueryContext();\n  const referenceElement = useRef<HTMLButtonElement | null>(null);\n  const theme = useTheme2();\n  const styles = getStyles(theme, props.fieldIndex === 0, props.field.name === getBodyName(logsFrame));\n  const { bodyState, columnWidthMap, setBodyState, setColumnWidthMap } = useTableColumnContext();\n  const isBodyField = props.field.name === getBodyName(logsFrame);\n\n  const onLogTextToggle = () => {\n    setBodyState(bodyState === LogLineState.text ? LogLineState.labels : LogLineState.text);\n  };\n\n  return (\n    <span className={styles.wrapper}>\n      <span className={styles.leftAlign}>\n        <span className={styles.defaultContentWrapper}>{props.defaultContent}</span>\n        {columnWidthMap && setColumnWidthMap && columnWidthMap?.[props.field.name] !== undefined && (\n          <IconButton\n            tooltip={'Reset column width'}\n            tooltipPlacement={'top'}\n            className={styles.clearButton}\n            aria-label={'Reset column width'}\n            name={'x'}\n            onClick={() => {\n              const { [props.field.name]: omit, ...map } = { ...columnWidthMap };\n              setColumnWidthMap?.(map);\n              reportAppInteraction(\n                USER_EVENTS_PAGES.service_details,\n                USER_EVENTS_ACTIONS.service_details.table_columns_header_button_reset_width\n              );\n            }}\n          />\n        )}\n        {isBodyField && (\n          <>\n            {bodyState === LogLineState.text ? (\n              <IconButton\n                tooltipPlacement={'top'}\n                tooltip={'Show log labels'}\n                aria-label={'Show log labels'}\n                onClick={() => {\n                  onLogTextToggle();\n                  reportAppInteraction(\n                    USER_EVENTS_PAGES.service_details,\n                    USER_EVENTS_ACTIONS.service_details.table_columns_header_button_show_labels\n                  );\n                }}\n                className={styles.logLineButton}\n                name={'tag-alt'}\n                size={'md'}\n              />\n            ) : (\n              <IconButton\n                tooltipPlacement={'top'}\n                tooltip={'Show log text'}\n                aria-label={'Show log text'}\n                onClick={() => {\n                  onLogTextToggle();\n                  reportAppInteraction(\n                    USER_EVENTS_PAGES.service_details,\n                    USER_EVENTS_ACTIONS.service_details.table_columns_header_button_show_text\n                  );\n                }}\n                className={styles.logLineButton}\n                name={'text-fields'}\n                size={'md'}\n              />\n            )}\n          </>\n        )}\n      </span>\n      <span className={styles.rightAlign}>\n        <IconButton\n          tooltip={`Show ${props.field.name} menu`}\n          tooltipPlacement={'top'}\n          ref={referenceElement}\n          aria-label={`Show ${props.field.name} menu`}\n          onClick={(e) => {\n            setHeaderMenuActive(!isHeaderMenuActive);\n            reportAppInteraction(\n              USER_EVENTS_PAGES.service_details,\n              USER_EVENTS_ACTIONS.service_details.table_columns_header_menu_show\n            );\n          }}\n          name={'ellipsis-v'}\n        />\n      </span>\n\n      {referenceElement.current && (\n        <Popover\n          show={isHeaderMenuActive}\n          content={\n            <LogsTableHeaderMenu\n              setHeaderMenuActive={(active) => {\n                setHeaderMenuActive(active);\n                referenceElement.current?.focus();\n              }}\n            >\n              {props.children}\n            </LogsTableHeaderMenu>\n          }\n          referenceElement={referenceElement.current}\n        />\n      )}\n    </span>\n  );\n};\n", "import React, { useCallback } from 'react';\n\nimport { css, cx } from '@emotion/css';\n\nimport { Field } from '@grafana/data';\nimport { Icon } from '@grafana/ui';\n\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from '../../services/analytics';\nimport { getBodyName } from '../../services/logsFrame';\nimport { useQueryContext } from './Context/QueryContext';\nimport { LogLineState, useTableColumnContext } from 'Components/Table/Context/TableColumnsContext';\nimport { LogsTableHeader, LogsTableHeaderProps } from 'Components/Table/LogsTableHeader';\nimport { FieldNameMetaStore } from 'Components/Table/TableTypes';\nimport { useSharedStyles } from 'styles/shared-styles';\n\nexport function LogsTableHeaderWrap(props: {\n  autoColumnWidths?: () => void;\n  headerProps: LogsTableHeaderProps;\n\n  // Moves the current column forward or backward one index\n  slideLeft?: (cols: FieldNameMetaStore) => void;\n\n  slideRight?: (cols: FieldNameMetaStore) => void;\n}) {\n  const { bodyState, columns, setBodyState, setColumns, columnWidthMap, setColumnWidthMap } = useTableColumnContext();\n  const { logsFrame } = useQueryContext();\n  const styles = getStyles();\n  const { linkButton } = useSharedStyles();\n\n  const hideColumn = useCallback(\n    (field: Field) => {\n      const pendingColumnState = { ...columns };\n\n      const columnsThatNeedIndexUpdate = Object.keys(pendingColumnState)\n        .filter((col) => {\n          const columnIndex = pendingColumnState[col].index;\n          const fieldIndex = pendingColumnState[field.name].index;\n          return pendingColumnState[col].active && fieldIndex && columnIndex && columnIndex > fieldIndex;\n        })\n        .map((cols) => pendingColumnState[cols]);\n\n      columnsThatNeedIndexUpdate.forEach((col) => {\n        if (col.index !== undefined) {\n          col.index--;\n        }\n      });\n\n      pendingColumnState[field.name].active = false;\n      pendingColumnState[field.name].index = undefined;\n      setColumns(pendingColumnState);\n\n      // Remove the column width from columnWidthMap when hiding the column\n      if (columnWidthMap[field.name] !== undefined) {\n        const { [field.name]: omit, ...updatedColumnWidthMap } = columnWidthMap;\n        setColumnWidthMap(updatedColumnWidthMap);\n      }\n    },\n    [columns, setColumns, columnWidthMap, setColumnWidthMap]\n  );\n\n  const isBodyField = props.headerProps.field.name === getBodyName(logsFrame);\n\n  return (\n    <LogsTableHeader {...props.headerProps}>\n      <div className={styles.linkWrap}>\n        <button\n          className={cx(linkButton, styles.link)}\n          onClick={() => {\n            hideColumn(props.headerProps.field);\n            reportAppInteraction(\n              USER_EVENTS_PAGES.service_details,\n              USER_EVENTS_ACTIONS.service_details.table_columns_header_menu_hide_column\n            );\n          }}\n        >\n          <svg\n            xmlns=\"http://www.w3.org/2000/svg\"\n            viewBox=\"0 0 17 16\"\n            width=\"17\"\n            height=\"16\"\n            className=\"css-q2u0ig-Icon\"\n          >\n            <path\n              fillRule=\"evenodd\"\n              clipRule=\"evenodd\"\n              d=\"M1.73446 1.33301H12.2345C12.3892 1.33301 12.5375 1.40325 12.6469 1.52827C12.7563 1.65329 12.8178 1.82286 12.8178 1.99967V4.74967C12.8178 5.07184 12.5566 5.33301 12.2345 5.33301C11.9123 5.33301 11.6511 5.07184 11.6511 4.74967V2.66634H7.56779V13.333H11.6511V10.9163C11.6511 10.5942 11.9123 10.333 12.2345 10.333C12.5566 10.333 12.8178 10.5942 12.8178 10.9163V13.9997C12.8178 14.1765 12.7563 14.3461 12.6469 14.4711C12.5375 14.5961 12.3892 14.6663 12.2345 14.6663H1.73446C1.57975 14.6663 1.43137 14.5961 1.32198 14.4711C1.21258 14.3461 1.15112 14.1765 1.15112 13.9997V1.99967C1.15112 1.82286 1.21258 1.65329 1.32198 1.52827C1.43137 1.40325 1.57975 1.33301 1.73446 1.33301ZM2.31779 13.333H6.40112V2.66634H2.31779V13.333Z\"\n              fill=\"#CCCCDC\"\n              fillOpacity=\"1\"\n            />\n            <path\n              d=\"M15.9893 10.6315C15.9498 10.7263 15.8919 10.8123 15.819 10.8846C15.7467 10.9575 15.6607 11.0154 15.5659 11.0549C15.4712 11.0943 15.3695 11.1147 15.2668 11.1147C15.1641 11.1147 15.0625 11.0943 14.9677 11.0549C14.8729 11.0154 14.7869 10.9575 14.7146 10.8846L12.9335 9.09573L11.1524 10.8846C11.0801 10.9575 10.9941 11.0154 10.8993 11.0549C10.8045 11.0943 10.7028 11.1147 10.6002 11.1147C10.4975 11.1147 10.3958 11.0943 10.301 11.0549C10.2063 11.0154 10.1202 10.9575 10.0479 10.8846C9.97504 10.8123 9.91717 10.7263 9.87769 10.6315C9.8382 10.5367 9.81787 10.4351 9.81787 10.3324C9.81787 10.2297 9.8382 10.1281 9.87769 10.0333C9.91717 9.9385 9.97504 9.85248 10.0479 9.78017L11.8368 7.99906L10.0479 6.21795C9.90148 6.07149 9.8192 5.87285 9.8192 5.66573C9.8192 5.4586 9.90148 5.25996 10.0479 5.1135C10.1944 4.96705 10.393 4.88477 10.6002 4.88477C10.8073 4.88477 11.0059 4.96705 11.1524 5.1135L12.9335 6.90239L14.7146 5.1135C14.8611 4.96705 15.0597 4.88477 15.2668 4.88477C15.4739 4.88477 15.6726 4.96705 15.819 5.1135C15.9655 5.25996 16.0478 5.4586 16.0478 5.66573C16.0478 5.87285 15.9655 6.07149 15.819 6.21795L14.0302 7.99906L15.819 9.78017C15.8919 9.85248 15.9498 9.9385 15.9893 10.0333C16.0288 10.1281 16.0491 10.2297 16.0491 10.3324C16.0491 10.4351 16.0288 10.5367 15.9893 10.6315Z\"\n              fill=\"#CCCCDC\"\n              fillOpacity=\"1\"\n            />\n          </svg>\n          Remove column\n        </button>\n      </div>\n      {props.slideLeft && (\n        <div className={styles.linkWrap}>\n          <button\n            className={cx(linkButton, styles.link)}\n            onClick={() => {\n              props.slideLeft?.(columns);\n              reportAppInteraction(\n                USER_EVENTS_PAGES.service_details,\n                USER_EVENTS_ACTIONS.service_details.table_columns_header_menu_slide_left\n              );\n            }}\n          >\n            <Icon className={cx(styles.icon, styles.reverse)} name={'arrow-from-right'} size={'md'} />\n            Move left\n          </button>\n        </div>\n      )}\n      {props.slideRight && (\n        <div className={styles.linkWrap}>\n          <button\n            className={cx(linkButton, styles.link)}\n            onClick={() => {\n              props.slideRight?.(columns);\n              reportAppInteraction(\n                USER_EVENTS_PAGES.service_details,\n                USER_EVENTS_ACTIONS.service_details.table_columns_header_menu_slide_right\n              );\n            }}\n          >\n            <Icon className={styles.icon} name={'arrow-from-right'} size={'md'} />\n            Move right\n          </button>\n        </div>\n      )}\n      {isBodyField && (\n        <div className={styles.linkWrap}>\n          <button\n            className={cx(linkButton, styles.link)}\n            onClick={() => {\n              if (bodyState === LogLineState.text) {\n                setBodyState(LogLineState.labels);\n              } else {\n                setBodyState(LogLineState.text);\n              }\n\n              reportAppInteraction(\n                USER_EVENTS_PAGES.service_details,\n                USER_EVENTS_ACTIONS.service_details.table_columns_header_menu_show_labels,\n                {\n                  state: bodyState === LogLineState.text ? LogLineState.labels : LogLineState.text,\n                }\n              );\n            }}\n          >\n            {bodyState === LogLineState.text ? (\n              <Icon className={styles.icon} name={'brackets-curly'} size={'md'} />\n            ) : (\n              <Icon className={styles.icon} name={'text-fields'} size={'md'} />\n            )}\n\n            {bodyState === LogLineState.text ? 'Show labels' : 'Show log text'}\n          </button>\n        </div>\n      )}\n\n      {props.autoColumnWidths && (\n        <div className={styles.linkWrap}>\n          <button\n            className={cx(linkButton, styles.link)}\n            onClick={() => {\n              props.autoColumnWidths?.();\n              reportAppInteraction(\n                USER_EVENTS_PAGES.service_details,\n                USER_EVENTS_ACTIONS.service_details.table_columns_header_menu_reset_width\n              );\n            }}\n          >\n            <Icon className={styles.icon} name={'arrows-h'} size={'md'} />\n            Reset column widths\n          </button>\n        </div>\n      )}\n    </LogsTableHeader>\n  );\n}\n\nconst getStyles = () => {\n  return {\n    icon: css({\n      marginRight: '10px',\n    }),\n    link: css({\n      paddingBottom: '5px',\n      paddingTop: '5px',\n    }),\n    linkWrap: css({}),\n    reverse: css({\n      transform: 'scaleX(-1)',\n    }),\n  };\n};\n", "import React, { useCallback, useEffect, useMemo, useState } from 'react';\n\nimport { css } from '@emotion/css';\nimport { debounce } from 'lodash';\nimport { Resizable, ResizeCallback } from 're-resizable';\nimport { ScrollSync } from 'react-scroll-sync';\nimport { lastValueFrom } from 'rxjs';\n\nimport {\n  applyFieldOverrides,\n  CustomTransformOperator,\n  DataFrame,\n  DataFrameType,\n  DataTransformerConfig,\n  Field,\n  FieldType,\n  FieldWithIndex,\n  GrafanaTheme2,\n  Labels,\n  MappingType,\n  transformDataFrame,\n  ValueMap,\n} from '@grafana/data';\nimport { getTemplateSrv } from '@grafana/runtime';\nimport { LogsSortOrder, TableCellHeight, TableColoredBackgroundCellOptions } from '@grafana/schema';\nimport { Table as GrafanaTable, TableCellDisplayMode, TableCustomCellOptions, useTheme2 } from '@grafana/ui';\n\nimport { getBodyName, getIdName, LogsFrame } from '../../services/logsFrame';\nimport { testIds } from '../../services/testIds';\nimport { useQueryContext } from './Context/QueryContext';\nimport {\n  ColumnSelectionDrawerWrap,\n  getReorderColumn,\n} from 'Components/Table/ColumnSelection/ColumnSelectionDrawerWrap';\nimport { TableCellContextProvider } from 'Components/Table/Context/TableCellContext';\nimport { useTableColumnContext } from 'Components/Table/Context/TableColumnsContext';\nimport { TableHeaderContextProvider } from 'Components/Table/Context/TableHeaderContext';\nimport { DefaultCellComponent } from 'Components/Table/DefaultCellComponent';\nimport { LogLineCellComponent } from 'Components/Table/LogLineCellComponent';\nimport { CustomHeaderRendererProps } from 'Components/Table/LogsTableHeader';\nimport { LogsTableHeaderWrap } from 'Components/Table/LogsTableHeaderWrap';\nimport { FieldName, FieldNameMeta, FieldNameMetaStore } from 'Components/Table/TableTypes';\nimport { guessLogsFieldTypeForValue } from 'Components/Table/TableWrap';\n\ninterface Props {\n  height: number;\n  labels: Labels[];\n  logsFrame: LogsFrame;\n  logsSortOrder: LogsSortOrder;\n  timeZone: string;\n  width: number;\n}\n\nconst getStyles = (theme: GrafanaTheme2, height: number, sideBarWidth: number) => ({\n  // Sidebar resize styles matching https://github.com/grafana/grafana/blob/main/public/app/features/explore/Logs/LogsTableWrap.tsx#L561\n  collapsedTableSidebar: css({\n    alignItems: 'center',\n    display: 'flex',\n    flexDirection: 'column',\n    justifyContent: 'flex-start',\n    paddingRight: theme.spacing(1),\n    paddingTop: theme.spacing(8),\n    width: '40px !important', // Space for the collapse button\n  }),\n  collapseTableSidebarButton: css({\n    '&:hover': {\n      background: theme.colors.background.primary,\n      borderColor: theme.colors.border.medium,\n    },\n    background: theme.colors.background.secondary,\n    border: `1px solid ${theme.colors.border.weak}`,\n    borderRadius: theme.shape.radius.default,\n    cursor: 'pointer',\n    padding: theme.spacing(0.5),\n    position: 'absolute',\n    right: theme.spacing(1),\n    top: theme.spacing(1),\n    transition: 'all 0.2s ease-in-out',\n    zIndex: 10,\n  }),\n  rzHandle: css({\n    [theme.transitions.handleMotion('no-preference', 'reduce')]: {\n      transition: '0.3s background ease-in-out',\n    },\n    ['&:hover']: {\n      background: theme.colors.secondary.shade,\n    },\n    background: theme.colors.secondary.main,\n    borderRadius: theme.shape.radius.pill,\n    cursor: 'grab',\n    height: '50% !important',\n    position: 'relative',\n    right: `${theme.spacing(1)} !important`,\n    top: '25% !important',\n    width: `${theme.spacing(1)} !important`,\n  }),\n  sidebar: css({\n    fontSize: theme.typography.pxToRem(11),\n    height: height,\n    overflowY: 'hidden',\n    paddingRight: theme.spacing(3),\n    position: 'relative',\n    width: sideBarWidth,\n  }),\n  tableWrap: css({\n    '.cellActions': {\n      // Hacky but without inspect turned on the table will change the width of the row on hover, but we don't want the default icons to show\n      display: 'none !important',\n    },\n  }),\n  wrapper: css({\n    display: 'flex',\n    position: 'relative',\n    flexWrap: 'wrap',\n  }),\n});\n\nfunction TableAndContext(props: {\n  data: DataFrame;\n  height: number;\n  logsFrame: LogsFrame;\n  logsSortOrder: LogsSortOrder;\n  onResize: (fieldDisplayName: string, width: number) => void;\n  selectedLine?: number;\n  width: number;\n}) {\n  return (\n    <GrafanaTable\n      onColumnResize={props.onResize}\n      initialRowIndex={props.selectedLine}\n      cellHeight={TableCellHeight.Sm}\n      data={props.data}\n      height={props.height}\n      width={props.width}\n      footerOptions={{ countRows: true, reducer: ['count'], show: true }}\n    />\n  );\n}\n\nexport const Table = (props: Props) => {\n  const { height, labels, logsFrame, timeZone, width } = props;\n\n  const theme = useTheme2();\n\n  const [tableFrame, setTableFrame] = useState<DataFrame | undefined>(undefined);\n  const [sidebarWidth, setSidebarWidth] = useState(220);\n  const [isTableSidebarCollapsed, setIsTableSidebarCollapsed] = useState(false);\n  const tableWidth = width - (isTableSidebarCollapsed ? 40 : sidebarWidth);\n  const styles = getStyles(theme, height, sidebarWidth);\n\n  const { clearSelectedLine, columns, columnWidthMap, setColumns, setColumnWidthMap } = useTableColumnContext();\n\n  const { selectedLine } = useQueryContext();\n\n  // Create a local state for selected line so we can clear the state tied to the URL\n  const [localSelectedLine] = useState(selectedLine);\n\n  const reorderColumn = getReorderColumn(setColumns);\n\n  const templateSrv = getTemplateSrv();\n  const replace = useMemo(() => templateSrv.replace.bind(templateSrv), [templateSrv]);\n\n  const prepareTableFrame = useCallback(\n    (frame: DataFrame): DataFrame => {\n      if (!frame.length) {\n        return frame;\n      }\n      const [frameWithOverrides] = applyFieldOverrides({\n        data: [frame],\n        fieldConfig: {\n          defaults: {\n            custom: {},\n          },\n          overrides: [],\n        },\n        replaceVariables: replace,\n        theme: theme,\n        timeZone: timeZone,\n      });\n\n      // `getLinks` and `applyFieldOverrides` are taken from TableContainer.tsx\n      for (const [index, field] of frameWithOverrides.fields.entries()) {\n        // If it's a string, then try to guess for a better type for numeric support in viz\n        field.type =\n          field.type === FieldType.string ? guessLogsFieldTypeForField(field) ?? FieldType.string : field.type;\n\n        field.config = {\n          ...field.config,\n\n          custom: {\n            cellOptions: getTableCellOptions(field, index, labels, logsFrame),\n            filterable: true, // This sets the columns to be filterable\n            headerComponent: (props: CustomHeaderRendererProps) => (\n              <TableHeaderContextProvider>\n                <LogsTableHeaderWrap\n                  headerProps={{ ...props, fieldIndex: index }}\n                  slideLeft={\n                    index !== 0 ? (cols: FieldNameMetaStore) => reorderColumn(cols, index, index - 1) : undefined\n                  }\n                  slideRight={\n                    index !== frame.fields.length - 1\n                      ? (cols: FieldNameMetaStore) => reorderColumn(cols, index, index + 1)\n                      : undefined\n                  }\n                  autoColumnWidths={\n                    Object.keys(columnWidthMap).length > 0\n                      ? () => {\n                          setColumnWidthMap({});\n                        }\n                      : undefined\n                  }\n                />\n              </TableHeaderContextProvider>\n            ),\n            inspect: true,\n            width:\n              columnWidthMap[field.name] ??\n              getInitialFieldWidth(field, index, columns, width, frameWithOverrides.fields.length, logsFrame),\n            ...field.config.custom,\n          },\n          // This sets the individual field value as filterable\n          // filterable: isFieldFilterable(field, logsFrame?.bodyField.name ?? '', logsFrame?.timeField.name ?? ''),\n          filterable: true,\n        };\n      }\n\n      return frameWithOverrides;\n    },\n    // This function is building the table dataframe that will be transformed, even though the components within the dataframe (cells, headers) can mutate the dataframe!\n    // If we try to update the dataframe whenever the columns are changed (which are rebuilt using this dataframe after being transformed), react will infinitely update frame -> columns -> frame -> ...\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [timeZone, theme, labels, width, replace, columnWidthMap]\n  );\n\n  // prepare dataFrame\n  useEffect(() => {\n    const prepare = async () => {\n      const transformations: Array<DataTransformerConfig | CustomTransformOperator> = getExtractFieldsTransform(\n        logsFrame.raw\n      );\n\n      let labelFilters = buildColumnsWithMeta(columns);\n\n      const labelFiltersTransform = getOrganizeFieldsTransform(labelFilters);\n      if (labelFiltersTransform) {\n        transformations.push(labelFiltersTransform);\n      } else {\n        const specialFields = {\n          body: logsFrame.bodyField,\n          extraFields: logsFrame.extraFields,\n          time: logsFrame.timeField,\n        };\n        if (specialFields && specialFields.body !== undefined && specialFields.time !== undefined) {\n          transformations.push(\n            getDefaultStateOrganizeFieldsTransform(\n              specialFields as {\n                body: FieldWithIndex;\n                time: FieldWithIndex;\n              }\n            )\n          );\n        }\n      }\n\n      if (transformations.length > 0) {\n        const transformedDataFrame: DataFrame[] = await lastValueFrom(\n          // @ts-ignore\n          transformDataFrame(transformations, [logsFrame.raw])\n        );\n        const tableFrame = prepareTableFrame(transformedDataFrame[0]);\n        setTableFrame(tableFrame);\n      } else {\n        setTableFrame(prepareTableFrame(logsFrame.raw));\n      }\n    };\n    prepare();\n  }, [logsFrame.raw, logsFrame.bodyField, logsFrame.timeField, logsFrame.extraFields, prepareTableFrame, columns]);\n\n  // Clear selected line from URL so it doesn't pollute future queries\n  useEffect(() => {\n    if (localSelectedLine && selectedLine) {\n      clearSelectedLine();\n      return;\n    }\n  }, [localSelectedLine, clearSelectedLine, selectedLine]);\n\n  const idField = logsFrame.raw.fields.find((field) => field.name === getIdName(logsFrame));\n  const lineIndex = idField?.values.findIndex((v) => v === localSelectedLine?.id);\n  const cleanLineIndex = lineIndex && lineIndex !== -1 ? lineIndex : undefined;\n\n  if (!tableFrame) {\n    return <></>;\n  }\n\n  const onResize = (fieldDisplayName: string, width: number) => {\n    const key = Object.keys(columns)\n      .filter((key) => columns[key].active)\n      .find((key) => key === fieldDisplayName);\n\n    if (key && width > 0) {\n      const map = { ...columnWidthMap };\n      map[key] = width;\n      setColumnWidthMap(map);\n    }\n  };\n\n  const getOnResize: ResizeCallback = (event, direction, ref) => {\n    const newSidebarWidth = Number(ref.style.width.slice(0, -2));\n    if (!isNaN(newSidebarWidth)) {\n      setSidebarWidth(newSidebarWidth);\n    }\n  };\n\n  const toggleTableSidebarCollapse = () => {\n    setIsTableSidebarCollapsed(!isTableSidebarCollapsed);\n  };\n\n  return (\n    <div data-testid={testIds.table.wrapper} className={styles.wrapper}>\n      <Resizable\n        enable={{\n          right: !isTableSidebarCollapsed,\n        }}\n        handleClasses={{ right: styles.rzHandle }}\n        onResize={getOnResize}\n        minWidth={isTableSidebarCollapsed ? 40 : 150}\n        maxWidth={isTableSidebarCollapsed ? 40 : width * 0.8}\n        size={{\n          height: height,\n          width: isTableSidebarCollapsed ? 40 : sidebarWidth,\n        }}\n      >\n        <section className={`${styles.sidebar} ${isTableSidebarCollapsed ? styles.collapsedTableSidebar : ''}`}>\n          <ColumnSelectionDrawerWrap\n            isTableSidebarCollapsed={isTableSidebarCollapsed}\n            onToggleTableSidebarCollapse={toggleTableSidebarCollapse}\n            collapseTableSidebarButtonClassName={styles.collapseTableSidebarButton}\n          />\n        </section>\n      </Resizable>\n\n      <div className={styles.tableWrap}>\n        <TableCellContextProvider>\n          <ScrollSync horizontal={true} vertical={false} proportional={false}>\n            <TableAndContext\n              logsFrame={logsFrame}\n              selectedLine={cleanLineIndex}\n              data={tableFrame}\n              height={height}\n              width={tableWidth}\n              onResize={debounce(onResize, 100)}\n              logsSortOrder={props.logsSortOrder}\n            />\n          </ScrollSync>\n        </TableCellContextProvider>\n      </div>\n    </div>\n  );\n};\n\nfunction getDefaultStateOrganizeFieldsTransform(specialFields: { body: FieldWithIndex; time: FieldWithIndex }) {\n  return {\n    id: 'organize',\n    options: {\n      includeByName: {\n        [specialFields.body.name]: true,\n        [specialFields.time.name]: true,\n      },\n      indexByName: {\n        [specialFields.time.name]: 0,\n        [specialFields.body.name]: 1,\n      },\n    },\n  };\n}\n\nfunction guessLogsFieldTypeForField(field: Field): FieldType | undefined {\n  // 1. Use the column name to guess\n  if (field.name) {\n    const name = field.name.toLowerCase();\n    if (name === 'date' || name === 'time') {\n      return FieldType.time;\n    }\n  }\n\n  // 2. Check the first non-null value\n  for (let i = 0; i < field.values.length; i++) {\n    const v = field.values[i];\n    if (v != null) {\n      return guessLogsFieldTypeForValue(v);\n    }\n  }\n\n  // Could not find anything\n  return undefined;\n}\n\nexport const getFieldMappings = (): ValueMap => {\n  return {\n    options: {\n      crit: {\n        color: '#705da0',\n        index: 1,\n      },\n      critical: {\n        color: '#705da0',\n        index: 0,\n      },\n      debug: {\n        color: '#1f78c1',\n        index: 8,\n      },\n      eror: {\n        color: '#e24d42',\n        index: 4,\n      },\n      err: {\n        color: '#e24d42',\n        index: 3,\n      },\n      error: {\n        color: '#e24d42',\n        index: 2,\n      },\n      info: {\n        color: '#7eb26d',\n        index: 7,\n      },\n      trace: {\n        color: '#6ed0e0',\n        index: 9,\n      },\n      warn: {\n        color: '#FF9900',\n        index: 6,\n      },\n      warning: {\n        color: '#FF9900',\n        index: 5,\n      },\n    },\n    type: MappingType.ValueToText,\n  };\n};\n\nfunction buildColumnsWithMeta(columnsWithMeta: Record<FieldName, FieldNameMeta>) {\n  // Create object of label filters to include columns selected by the user\n  let labelFilters: Record<FieldName, number> = {};\n  Object.keys(columnsWithMeta)\n    .filter((key) => columnsWithMeta[key].active)\n    .forEach((key) => {\n      const index = columnsWithMeta[key].index;\n      // Index should always be defined for any active column\n      if (index !== undefined) {\n        labelFilters[key] = index;\n      }\n    });\n\n  return labelFilters;\n}\n\nfunction getOrganizeFieldsTransform(labelFilters: Record<FieldName, number>) {\n  let labelFiltersInclude: Record<FieldName, boolean> = {};\n\n  for (const key in labelFilters) {\n    labelFiltersInclude[key] = true;\n  }\n\n  if (Object.keys(labelFilters).length > 0) {\n    return {\n      id: 'organize',\n      options: {\n        includeByName: labelFiltersInclude,\n        indexByName: labelFilters,\n      },\n    };\n  }\n  return null;\n}\n\nexport function getExtractFieldsTransform(dataFrame: DataFrame) {\n  return dataFrame.fields\n    .filter((field: Field & { typeInfo?: { frame: string } }) => {\n      const isFieldLokiLabels =\n        field.typeInfo?.frame === 'json.RawMessage' &&\n        field.name === 'labels' &&\n        dataFrame?.meta?.type !== DataFrameType.LogLines;\n      const isFieldDataplaneLabels =\n        field.name === 'labels' && field.type === FieldType.other && dataFrame?.meta?.type === DataFrameType.LogLines;\n      return isFieldLokiLabels || isFieldDataplaneLabels;\n    })\n    .flatMap((field: Field) => {\n      return [\n        {\n          id: 'extractFields',\n          options: {\n            format: 'json',\n            keepTime: false,\n            replace: false,\n            source: field.name,\n          },\n        },\n      ];\n    });\n}\n\nfunction getTableCellOptions(\n  field: Field,\n  fieldIndex: number,\n  labels: Labels[],\n  logsFrame: LogsFrame\n): TableCustomCellOptions | TableColoredBackgroundCellOptions {\n  if (field.name === getBodyName(logsFrame)) {\n    return {\n      cellComponent: (props) => (\n        <LogLineCellComponent {...props} fieldIndex={fieldIndex} labels={labels[props.rowIndex]} />\n      ),\n      type: TableCellDisplayMode.Custom,\n    };\n  }\n\n  return {\n    cellComponent: (props) => <DefaultCellComponent {...props} fieldIndex={fieldIndex} />,\n    type: TableCellDisplayMode.Custom,\n  };\n}\n\nfunction getInitialFieldWidth(\n  field: Field,\n  fieldIndex: number,\n  columns: FieldNameMetaStore,\n  tableWidth: number,\n  numberOfFields: number,\n  logsFrame: LogsFrame\n): number | undefined {\n  const minWidth = 90;\n\n  // Columns shouldn't take more than half the available space, unless there are only 2 columns\n  const maxWidth = numberOfFields <= 2 ? tableWidth : Math.min(tableWidth / 2);\n\n  // First field gets icons, and a little extra width\n  const extraPadding = fieldIndex === 0 ? 50 : 0;\n\n  // Width of the logs panel controls sidebar\n  const logsPanelControls = 35;\n\n  // Time fields have consistent widths\n  if (field.type === FieldType.time) {\n    return 200 + extraPadding;\n  }\n\n  const columnMeta = columns[field.name];\n\n  if (columnMeta === undefined) {\n    return undefined;\n  }\n\n  const maxLength = Math.max(columnMeta.maxLength ?? 0, field.name.length);\n\n  if (columnMeta.maxLength) {\n    // Super rough estimate, about 6.5px per char, and 95px for some space for the header icons (remember when sorted a new icon is added to the table header).\n    // I guess to be a little tighter we could only add the extra padding IF the field name is longer then the longest value\n    return Math.min(\n      Math.max(maxLength * 6.5 + 95 + logsPanelControls + extraPadding, minWidth + extraPadding),\n      maxWidth\n    );\n  }\n\n  if (field.name === getBodyName(logsFrame)) {\n    return undefined;\n  }\n\n  // Just derived fields, which should have uniform length\n  return Math.min(\n    Math.max((field.values?.[0]?.length ?? 80) * 6.5 + 95 + logsPanelControls + extraPadding, minWidth + extraPadding),\n    maxWidth\n  );\n}\n", "import React, { useCallback, useState } from 'react';\n\nimport { css } from '@emotion/css';\nimport { useResizeObserver } from '@react-aria/utils';\n\nimport {\n  DataFrame,\n  FieldType,\n  FieldWithIndex,\n  getTimeZone,\n  guessFieldTypeFromValue,\n  Labels,\n  LogsSortOrder,\n} from '@grafana/data';\n\nimport { useQueryContext } from 'Components/Table/Context/QueryContext';\nimport { LogLineState, TableColumnContextProvider } from 'Components/Table/Context/TableColumnsContext';\nimport { Table } from 'Components/Table/Table';\nimport { FieldNameMeta, FieldNameMetaStore } from 'Components/Table/TableTypes';\nimport { logsControlsSupported } from 'services/panel';\n\nexport type SpecialFieldsType = {\n  body: FieldWithIndex;\n  extraFields: FieldWithIndex[];\n  time: FieldWithIndex;\n};\n\n// matches common ISO 8601\nconst iso8601Regex = /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(?:\\.\\d{3,})?(?:Z|[-+]\\d{2}:?\\d{2})$/;\n\ninterface TableWrapProps {\n  clearSelectedLine: () => void;\n  logsSortOrder: LogsSortOrder;\n  panelWrap: React.RefObject<HTMLDivElement | null>;\n  setUrlColumns: (columns: string[]) => void;\n  setUrlTableBodyState: (logLineState: LogLineState) => void;\n  urlColumns: string[];\n  urlTableBodyState?: LogLineState;\n}\n\nconst getStyles = () => ({\n  section: css({\n    position: 'relative',\n  }),\n});\n\nexport const TableWrap = (props: TableWrapProps) => {\n  const { logsFrame } = useQueryContext();\n\n  const [panelWrapSize, setPanelWrapSize] = useState({ height: 0, width: 0 });\n\n  // Table needs to be positioned absolutely, passing in reference wrapping panelChrome from parent\n  useResizeObserver({\n    onResize: () => {\n      const element = props.panelWrap.current;\n      if (element) {\n        if (panelWrapSize.width !== element.clientWidth || panelWrapSize.height !== element.clientHeight) {\n          setPanelWrapSize({\n            height: element.clientHeight,\n            width: element.clientWidth,\n          });\n        }\n      }\n    },\n    ref: props.panelWrap,\n  });\n\n  const styles = getStyles();\n  const timeZone = getTimeZone();\n\n  // This function is called when we want to grab the column names that are currently stored in the URL.\n  // So instead we have to grab the current columns directly from the URL.\n  const getColumnsFromProps = useCallback(\n    (fieldNames: FieldNameMetaStore) => {\n      const previouslySelected = props.urlColumns;\n      if (previouslySelected?.length) {\n        Object.values(previouslySelected).forEach((key, index) => {\n          if (fieldNames[key]) {\n            fieldNames[key].active = true;\n            fieldNames[key].index = index;\n          }\n        });\n      }\n\n      return fieldNames;\n    },\n    [props.urlColumns]\n  );\n\n  // If the data frame is empty, there's nothing to viz, it could mean the user has unselected all columns\n  if (!logsFrame || !logsFrame.raw.length) {\n    return null;\n  }\n\n  const labels = logsFrame.getLogFrameLabelsAsLabels() ?? [];\n  const numberOfLogLines = logsFrame ? logsFrame.raw.length : 0;\n\n  // If we have labels and log lines\n  let pendingLabelState = mapLabelsToInitialState(logsFrame.raw, labels);\n  const specialFields = {\n    body: logsFrame.bodyField,\n    extraFields: logsFrame.extraFields,\n    time: logsFrame.timeField,\n  };\n\n  // Normalize the other fields\n  if (specialFields) {\n    addSpecialLabelsState(\n      [specialFields.time, specialFields.body, ...specialFields.extraFields],\n      pendingLabelState,\n      numberOfLogLines\n    );\n\n    pendingLabelState = getColumnsFromProps(pendingLabelState);\n\n    // Get all active columns\n    const active = Object.keys(pendingLabelState).filter((key) => pendingLabelState[key].active);\n\n    // If nothing is selected, then select the default columns\n    setSpecialFieldMeta(active, specialFields, pendingLabelState);\n  }\n\n  return (\n    <section className={styles.section}>\n      <TableColumnContextProvider\n        setUrlTableBodyState={props.setUrlTableBodyState}\n        logsFrame={logsFrame}\n        initialColumns={pendingLabelState}\n        setUrlColumns={props.setUrlColumns}\n        clearSelectedLine={props.clearSelectedLine}\n        urlTableBodyState={props.urlTableBodyState}\n      >\n        <Table\n          logsFrame={logsFrame}\n          timeZone={timeZone}\n          height={panelWrapSize.height - 50}\n          width={panelWrapSize.width - 25 + (logsControlsSupported ? -32 : 0)}\n          labels={labels}\n          logsSortOrder={props.logsSortOrder}\n        />\n      </TableColumnContextProvider>\n    </section>\n  );\n};\n\nconst normalize = (value: number, total: number): number => {\n  return Math.ceil((100 * value) / total);\n};\n\ntype labelName = string;\ntype labelValue = string;\n\nexport function getCardinalityMapFromLabels(labels: Labels[]) {\n  const cardinalityMap = new Map<labelName, { maxLength: number; valueSet: Set<labelValue> }>();\n  labels.forEach((fieldLabels) => {\n    const labelNames = Object.keys(fieldLabels);\n    labelNames.forEach((labelName) => {\n      if (cardinalityMap.has(labelName)) {\n        const setObj = cardinalityMap.get(labelName);\n        const values = setObj?.valueSet;\n        const maxLength = setObj?.maxLength;\n\n        if (values && !values?.has(fieldLabels[labelName])) {\n          values?.add(fieldLabels[labelName]);\n          if (maxLength && fieldLabels[labelName].length > maxLength) {\n            cardinalityMap.set(labelName, { maxLength: fieldLabels[labelName].length, valueSet: values });\n          }\n        }\n      } else {\n        cardinalityMap.set(labelName, {\n          maxLength: fieldLabels[labelName].length,\n          valueSet: new Set([fieldLabels[labelName]]),\n        });\n      }\n    });\n  });\n\n  return cardinalityMap;\n}\n\n/**\n * Guess the field type of the value\n * @param value\n */\nexport function guessLogsFieldTypeForValue(value: string) {\n  let fieldType = guessFieldTypeFromValue(value);\n  const isISO8601 = fieldType === 'string' && iso8601Regex.test(value);\n  if (isISO8601) {\n    fieldType = FieldType.time;\n  }\n  return fieldType;\n}\n\nfunction mapLabelsToInitialState(dataFrame: DataFrame, labels: Labels[]) {\n  let pendingLabelState: FieldNameMetaStore = {};\n\n  // Use a map to dedupe labels and count their occurrences in the logs\n  const labelMap = new Map<string, FieldNameMeta>();\n  const cardinality = getCardinalityMapFromLabels(labels);\n  const numberOfLogLines = dataFrame ? dataFrame.length : 0;\n\n  if (labels?.length && numberOfLogLines) {\n    // Iterate through all of Labels\n    labels.forEach((labels: Labels) => {\n      const labelsArray = Object.keys(labels);\n      // Iterate through the label values\n      labelsArray.forEach((label) => {\n        const cardinalityMap = cardinality.get(label);\n        const cardinalityCount = cardinalityMap?.valueSet?.size ?? 0;\n        // If it's already in our map, increment the count\n        if (labelMap.has(label)) {\n          const value = labelMap.get(label);\n\n          if (value) {\n            if (value?.active) {\n              labelMap.set(label, {\n                active: true,\n                cardinality: cardinalityCount,\n                index: value.index,\n                maxLength: cardinalityMap?.maxLength,\n                percentOfLinesWithLabel: value.percentOfLinesWithLabel + 1,\n              });\n            } else {\n              labelMap.set(label, {\n                active: false,\n                cardinality: cardinalityCount,\n                index: undefined,\n                maxLength: cardinalityMap?.maxLength,\n                percentOfLinesWithLabel: value.percentOfLinesWithLabel + 1,\n              });\n            }\n          }\n          // Otherwise add it\n        } else {\n          labelMap.set(label, {\n            active: false,\n            cardinality: cardinalityCount,\n            index: undefined,\n            maxLength: cardinalityMap?.maxLength,\n            percentOfLinesWithLabel: 1,\n          });\n        }\n      });\n    });\n\n    // Converting the map to an object\n    pendingLabelState = Object.fromEntries(labelMap);\n\n    // Convert count to percent of log lines\n    Object.keys(pendingLabelState).forEach((key) => {\n      pendingLabelState[key].percentOfLinesWithLabel = normalize(\n        pendingLabelState[key].percentOfLinesWithLabel,\n        numberOfLogLines\n      );\n    });\n  }\n  return pendingLabelState;\n}\n\n/**\n * Add special fields like time and body\n * @param specialFieldArray\n * @param pendingLabelState\n * @param numberOfLogLines\n */\nfunction addSpecialLabelsState(\n  specialFieldArray: Array<FieldWithIndex | undefined>,\n  pendingLabelState: FieldNameMetaStore,\n  numberOfLogLines: number\n) {\n  specialFieldArray.forEach((field) => {\n    if (!field) {\n      return;\n    }\n    const isActive = pendingLabelState[field.name]?.active;\n    const index = pendingLabelState[field.name]?.index;\n    if (isActive && index !== undefined) {\n      pendingLabelState[field.name] = {\n        active: true,\n        cardinality: numberOfLogLines,\n        index: index,\n        percentOfLinesWithLabel: normalize(\n          field.values.filter((value) => value !== null && value !== undefined).length,\n          numberOfLogLines\n        ),\n      };\n    } else {\n      pendingLabelState[field.name] = {\n        active: false,\n        cardinality: numberOfLogLines,\n        index: undefined,\n        percentOfLinesWithLabel: normalize(\n          field.values.filter((value) => value !== null && value !== undefined).length,\n          numberOfLogLines\n        ),\n      };\n    }\n  });\n\n  return pendingLabelState;\n}\n\nfunction setSpecialFieldMeta(\n  active: string[],\n  specialFields: SpecialFieldsType,\n  pendingLabelState: FieldNameMetaStore\n) {\n  // If no fields are visible, set defaults\n  if (active.length === 0) {\n    if (specialFields.body?.name) {\n      pendingLabelState[specialFields.body?.name].active = true;\n      pendingLabelState[specialFields.body?.name].index = 1;\n    }\n    if (specialFields.time?.name) {\n      pendingLabelState[specialFields.time?.name].active = true;\n      pendingLabelState[specialFields.time?.name].index = 0;\n    }\n  }\n\n  if (specialFields.time?.name && specialFields.body?.name) {\n    pendingLabelState[specialFields.body?.name].type = 'BODY_FIELD';\n    pendingLabelState[specialFields.time?.name].type = 'TIME_FIELD';\n  }\n\n  if (specialFields.extraFields.length) {\n    specialFields.extraFields.forEach((field) => {\n      const hasLinks = field.config.links?.length;\n      if (hasLinks) {\n        pendingLabelState[field.name].type = 'LINK_FIELD';\n      }\n    });\n  }\n}\n", "import React, { useMemo } from 'react';\n\nimport { AdHocVariableFilter, DataFrame, FieldType, LogsSortOrder, sortDataFrame, TimeRange } from '@grafana/data';\n\nimport { parseLogsFrame } from '../../services/logsFrame';\nimport { LogLineState } from './Context/TableColumnsContext';\nimport { SelectedTableRow } from './LogLineCellComponent';\nimport { QueryContextProvider } from 'Components/Table/Context/QueryContext';\nimport { TableWrap } from 'Components/Table/TableWrap';\n\ninterface TableProviderProps {\n  addFilter: (filter: AdHocVariableFilter) => void;\n  clearSelectedLine: () => void;\n  dataFrame: DataFrame;\n  logsSortOrder: LogsSortOrder;\n  panelWrap: React.RefObject<HTMLDivElement | null>;\n  selectedLine?: SelectedTableRow;\n  setUrlColumns: (columns: string[]) => void;\n  setUrlTableBodyState: (logLineState: LogLineState) => void;\n  timeRange?: TimeRange;\n  urlColumns: string[];\n  urlTableBodyState?: LogLineState;\n}\n\nexport default function TableProvider({\n  addFilter,\n  clearSelectedLine,\n  dataFrame,\n  logsSortOrder,\n  panelWrap,\n  selectedLine,\n  setUrlColumns,\n  setUrlTableBodyState,\n  timeRange,\n  urlColumns,\n  urlTableBodyState,\n}: TableProviderProps) {\n  const logsFrame = useMemo(() => {\n    if (!dataFrame) {\n      return null;\n    }\n    const timeIndex = dataFrame.fields.findIndex((field) => field.type === FieldType.time);\n    const sortedFrame = sortDataFrame(dataFrame, timeIndex, logsSortOrder === LogsSortOrder.Descending);\n    const logsFrame = parseLogsFrame(sortedFrame);\n    return logsFrame;\n  }, [dataFrame, logsSortOrder]);\n\n  if (!logsFrame) {\n    return null;\n  }\n\n  return (\n    <QueryContextProvider addFilter={addFilter} selectedLine={selectedLine} timeRange={timeRange} logsFrame={logsFrame}>\n      <TableWrap\n        urlTableBodyState={urlTableBodyState}\n        setUrlColumns={setUrlColumns}\n        setUrlTableBodyState={setUrlTableBodyState}\n        urlColumns={urlColumns}\n        panelWrap={panelWrap}\n        clearSelectedLine={clearSelectedLine}\n        logsSortOrder={logsSortOrder}\n      />\n    </QueryContextProvider>\n  );\n}\n"], "names": ["initialState", "addFilter", "filter", "logsFrame", "selectedLine", "undefined", "timeRange", "QueryContext", "createContext", "QueryContextProvider", "children", "Provider", "value", "useQueryContext", "useContext", "LogsColumnSearch", "collapseButtonClassName", "isTableSidebarCollapsed", "onToggleTableSidebarCollapse", "searchValue", "setSearchValue", "columns", "setFilteredColumns", "useTableColumnContext", "dispatcher", "data", "matches", "newColumnsWithMeta", "numberOfResults", "searchResultCount", "for<PERSON>ach", "match", "reportInteraction", "resultCount", "styles", "theme", "collapseTableSidebarButton", "css", "position", "right", "spacing", "top", "getStyles", "useTheme2", "IconButton", "className", "onClick", "name", "tooltip", "size", "Field", "Input", "type", "placeholder", "onChange", "e", "currentTarget", "needle", "debouncedFuzzySearch", "Object", "keys", "LogsTableEmptyFields", "empty", "fontSize", "typography", "marginBottom", "marginLeft", "div", "LogsTableNavField", "props", "checkboxLabel", "display", "max<PERSON><PERSON><PERSON>", "overflow", "textOverflow", "whiteSpace", "contentWrap", "alignItems", "justifyContent", "width", "customWidthWrap", "cursor", "bodySmall", "dragIcon", "opacity", "labelCount", "appearance", "background", "border", "flexDirection", "pxToRem", "marginRight", "valueCount", "textWrap", "labels", "label", "Checkbox", "checked", "active", "showCount", "percentOfLinesWithLabel", "cardinality", "draggable", "Icon", "aria-label", "title", "LogsTableActiveFields", "columnWidthMap", "setColumnWidthMap", "reorderColumn", "toggleColumn", "valueFilter", "columnWrapper", "paddingLeft", "dragging", "colors", "secondary", "wrap", "primary", "marginTop", "get<PERSON><PERSON><PERSON><PERSON><PERSON>sS<PERSON>les", "labelKeys", "labelName", "onDragEnd", "result", "destination", "source", "index", "renderTitle", "length", "DragDropContext", "Droppable", "droppableId", "direction", "provided", "droppableProps", "ref", "innerRef", "sort", "a", "b", "la", "lb", "sortLabels", "map", "Draggable", "draggableId", "key", "snapshot", "cx", "isDragging", "draggableProps", "dragHandleProps", "collator", "Intl", "Collator", "sensitivity", "LogsTableAvailableFields", "borderBottom", "canvas", "Number", "compare", "LogsTableMultiSelect", "columnHeader", "h6", "left", "paddingBottom", "paddingRight", "paddingTop", "zIndex", "columnHeaderButton", "sidebarWrap", "height", "overflowY", "scrollbarWidth", "button", "clear", "filteredColumnsWithMeta", "columnsWithMeta", "id", "getReorderColumn", "setColumns", "sourceIndex", "destinationIndex", "pendingLabelState", "fieldName", "splice", "ColumnSelectionDrawerWrap", "filteredColumns", "useState", "columnName", "logContext", "JSON", "stringify", "msg", "logger", "warn", "logError", "c", "isActive", "newState", "priorActiveCount", "column", "event", "columnAction", "columnCount", "columnFilterEvent", "pendingFilteredLabelState", "isDefaultField", "TableCellContext", "cellIndex", "numberOfMenuItems", "setActiveCellIndex", "TableCellContextProvider", "cellActive", "setCellActive", "handleCellActive", "useCallback", "useTableCellContext", "TableHeaderContext", "isHeaderMenuActive", "setHeaderMenuActive", "TableHeaderContextProvider", "handleisHeaderMenuActive", "DefaultCellWrapComponent", "CellWrapInnerComponent", "onMouseOut", "onMouseIn", "field", "rowIndex", "cellState", "bgColor", "margin", "overflowX", "onMouseLeave", "onMouseEnter", "onKeyDown", "role", "tabIndex", "CellContextMenu", "pillType", "menu", "min<PERSON><PERSON><PERSON>", "menuItem", "menuItemsWrap", "boxShadow", "shadows", "z3", "padding", "span", "fieldType", "operator", "FilterOp", "Equal", "NotEqual", "showColumn", "svg", "viewBox", "fill", "xmlns", "path", "fillRule", "clipRule", "d", "fillOpacity", "links", "link", "window", "open", "href", "DefaultPill", "levelColor", "LEVEL_NAME", "mappings", "getFieldMappings", "options", "color", "isPillActive", "activePillWrap", "menuItemText", "pill", "backgroundColor", "content", "strong", "weak", "pillWrap", "FieldType", "time", "LineActionIcons", "isNumber", "isNaN", "clipboardButton", "lineHeight", "iconWrapper", "z2", "inspect", "text", "inspectButton", "borderRadius", "verticalAlign", "logId", "idField", "values", "lineValue", "bodyField", "isInspecting", "setIsInspecting", "getText", "generateLogShortlink", "row", "data-testid", "testIds", "table", "inspectLine", "variant", "tooltipPlacement", "ClipboardButton", "icon", "Modal", "on<PERSON><PERSON><PERSON>", "isOpen", "pre", "ButtonRow", "DefaultCellComponent", "getCellLinks", "displayValue", "flexWrap", "linkWrapper", "textDecoration", "hasLinks", "Boolean", "React", "formattedValueToString", "fieldIndex", "renderValue", "getLinks", "DataLinksContextMenu", "api", "openMenu", "useSharedStyles", "useStyles2", "linkButton", "outline", "font", "MozOsxFontSmoothing", "textAlign", "WebkitAppearance", "WebkitFontSmoothing", "LogLinePillValue", "activePill", "flex", "valueWrap", "menuActive", "onClickAdd", "LogLinePill", "templateSrv", "getTemplateSrv", "replace", "useMemo", "bind", "other", "original<PERSON>ield", "isDerivedField", "originalFrame", "getLinksSupplier", "subFieldName", "pendingColumns", "addFieldToColumns", "RawLogLineText", "rawLogLine", "fontFamily", "fontFamilyMonospace", "stopScroll", "current", "scrollTo", "scrollLeft", "<PERSON><PERSON><PERSON>", "scrollerRef", "scroller", "scrollRight", "onPointerDown", "behavior", "onPointerUp", "scrollWidth", "LogLineCellComponent", "bodyState", "isHover", "setIsHover", "useRef", "getBodyName", "labelValue", "untransformedField", "raw", "fields", "find", "rawValue", "isDerived", "frame", "untransformedValue", "string", "v", "renderLabels", "isAuto", "LogLineState", "auto", "<PERSON><PERSON><PERSON><PERSON>", "ScrollSyncPane", "group", "LogsTableHeaderMenu", "useEffect", "focus", "ClickOutsideWrapper", "includeButtonPress", "useCapture", "tableHeaderMenu", "closeButton", "t", "shape", "radius", "default", "maxHeight", "LogsTableHeader", "referenceElement", "isFirstColumn", "isLine", "clearButton", "defaultContentWrapper", "borderLeft", "leftAlign", "logLineButton", "rightAlign", "wrapper", "borderRight", "setBodyState", "isBodyField", "onLogTextToggle", "defaultContent", "omit", "reportAppInteraction", "USER_EVENTS_PAGES", "service_details", "USER_EVENTS_ACTIONS", "table_columns_header_button_reset_width", "table_columns_header_button_show_labels", "table_columns_header_button_show_text", "table_columns_header_menu_show", "Popover", "show", "LogsTableHeaderWrap", "hideColumn", "pendingColumnState", "col", "columnIndex", "cols", "updatedColumnWidthMap", "headerProps", "linkWrap", "table_columns_header_menu_hide_column", "slideLeft", "table_columns_header_menu_slide_left", "reverse", "slideRight", "table_columns_header_menu_slide_right", "table_columns_header_menu_show_labels", "state", "autoColumnWidths", "table_columns_header_menu_reset_width", "transform", "TableAndContext", "GrafanaTable", "onColumnResize", "onResize", "initialRowIndex", "cellHeight", "TableCellHeight", "Sm", "footerOptions", "countRows", "reducer", "Table", "timeZone", "tableFrame", "setTableFrame", "sidebarWidth", "setSidebarWidth", "setIsTableSidebarCollapsed", "tableWidth", "sideBarWidth", "collapsedTableSidebar", "borderColor", "medium", "transition", "rzHandle", "transitions", "handleMotion", "shade", "main", "sidebar", "tableWrap", "clearSelectedLine", "localSelectedLine", "prepareTableFrame", "frameWithOverrides", "applyFieldOverrides", "fieldConfig", "defaults", "custom", "overrides", "replaceVariables", "entries", "guessLogsFieldTypeForField", "config", "cellOptions", "getTableCellOptions", "filterable", "headerComponent", "getInitialFieldWidth", "transformations", "dataFrame", "isFieldLokiLabels", "typeInfo", "meta", "DataFrameType", "LogLines", "isFieldDataplaneLabels", "flatMap", "format", "keepTime", "labelFiltersTransform", "labelFilters", "labelFiltersInclude", "includeByName", "indexByName", "getOrganizeFieldsTransform", "buildColumnsWithMeta", "push", "specialFields", "body", "extraFields", "timeField", "getDefaultStateOrganizeFieldsTransform", "transformedDataFrame", "lastValueFrom", "transformDataFrame", "getIdName", "lineIndex", "findIndex", "cleanLineIndex", "Resizable", "enable", "handleClasses", "newSidebarWidth", "style", "slice", "section", "collapseTableSidebarButtonClassName", "ScrollSync", "horizontal", "vertical", "proportional", "debounce", "fieldDisplayName", "logsSortOrder", "toLowerCase", "i", "guessLogsFieldTypeForValue", "crit", "critical", "debug", "eror", "err", "error", "info", "trace", "warning", "MappingType", "ValueToText", "cellComponent", "TableCellDisplayMode", "Custom", "numberOfFields", "Math", "min", "extraPadding", "columnMeta", "max<PERSON><PERSON><PERSON>", "max", "iso8601Regex", "TableWrap", "panelWrapSize", "setPanelWrapSize", "useResizeObserver", "element", "panelWrap", "clientWidth", "clientHeight", "getTimeZone", "getColumnsFromProps", "fieldNames", "previouslySelected", "urlColumns", "getLogFrameLabelsAsLabels", "numberOfLogLines", "labelMap", "Map", "cardinalityMap", "<PERSON><PERSON><PERSON><PERSON>", "has", "set<PERSON>bj", "get", "valueSet", "add", "set", "Set", "getCardinalityMapFromLabels", "cardinalityCount", "fromEntries", "normalize", "mapLabelsToInitialState", "specialFieldArray", "addSpecialLabelsState", "setSpecialFieldMeta", "TableColumnContextProvider", "setUrlTableBodyState", "initialColumns", "setUrlColumns", "urlTableBodyState", "logsControlsSupported", "total", "ceil", "guessFieldTypeFromValue", "test", "TableProvider", "timeIndex", "sortedFrame", "sortDataFrame", "LogsSortOrder", "Descending", "parseLogsFrame"], "sourceRoot": ""}