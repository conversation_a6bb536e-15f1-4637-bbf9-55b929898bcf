{"$schema": "https://raw.githubusercontent.com/grafana/grafana/main/docs/sources/developers/plugins/plugin.schema.json", "type": "app", "name": "<PERSON><PERSON>", "id": "grafana-metricsdrilldown-app", "dependencies": {"grafanaDependency": ">=11.6.0", "plugins": []}, "preload": true, "autoEnabled": true, "info": {"keywords": ["drilldown", "metrics", "app", "prometheus", "<PERSON>mir"], "description": "Quickly find related metrics with a few clicks, without needing to write PromQL queries to retrieve metrics.", "author": {"name": "<PERSON><PERSON>"}, "logos": {"small": "img/logo.svg", "large": "img/logo.svg"}, "screenshots": [{"name": "metricselect", "path": "img/metrics-drilldown.png"}, {"name": "breakdown", "path": "img/breakdown.png"}], "version": "1.0.13", "updated": "2025-09-11", "links": [{"name": "GitHub", "url": "https://github.com/grafana/metrics-drilldown"}, {"name": "Report a bug", "url": "https://github.com/grafana/metrics-drilldown/issues/new"}]}, "includes": [{"type": "page", "name": "<PERSON><PERSON>", "path": "/a/grafana-metricsdrilldown-app/drilldown", "action": "datasources:explore", "addToNav": true, "defaultNav": true}], "extensions": {"addedLinks": [{"targets": ["grafana/dashboard/panel/menu", "grafana/explore/toolbar/action", "grafana-assistant-app/navigateToDrilldown/v1", "grafana/alerting/alertingrule/queryeditor"], "title": "Open in Grafana Metrics Drilldown", "description": "Open current query in the Grafana Metrics Drilldown view"}, {"targets": ["grafana-metricsdrilldown-app/grafana-assistant-app/navigateToDrilldown/v0-alpha"], "title": "Navigate to metrics drilldown", "description": "Build a url path to the metrics drilldown"}], "extensionPoints": [{"id": "grafana-exploremetrics-app/investigation/v1"}, {"id": "grafana-metricsdrilldown-app/open-in-logs-drilldown/v1"}], "exposedComponents": [{"id": "grafana-metricsdrilldown-app/label-breakdown-component/v1", "title": "Label Breakdown", "description": "A metrics label breakdown view from the Metrics Drilldown app."}]}, "buildMode": "production"}