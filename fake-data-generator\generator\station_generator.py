"""
Station status data generator
"""

import time
import random
import logging
from typing import List, Optional
from datetime import datetime

from model.station_status import StationStatus
from utils.database import DatabaseConnection
from utils.kafka_producer import KafkaProducerManager
from utils.config import Config

logger = logging.getLogger(__name__)


class StationStatusGenerator:
    """
    Generator for station status events
    """

    def __init__(self, db_connection: DatabaseConnection, kafka_producer: KafkaProducerManager):
        """
        Initialize station status generator

        Args:
            db_connection: Database connection instance
            kafka_producer: Kafka producer instance
        """
        self.db_connection = db_connection
        self.kafka_producer = kafka_producer
        self.station_ids = []

    def load_station_ids(self, limit: int = 100) -> List[str]:
        """
        Load station IDs from database

        Args:
            limit: Maximum number of station IDs to load

        Returns:
            List of station ID strings
        """
        try:
            self.station_ids = self.db_connection.get_station_ids(limit)
            logger.info(f"Loaded {len(self.station_ids)} station IDs")
            return self.station_ids
        except Exception as e:
            logger.error(f"Failed to load station IDs: {e}")
            raise

    def generate_random_station_status(self, station_id: str) -> StationStatus:
        """
        Generate random station status for given station ID

        Args:
            station_id: Station ID string

        Returns:
            StationStatus instance
        """
        # Use the same probability logic as original Kotlin code
        status = "on" if Config.get_station_status_probability() else "off"
        return StationStatus(id=station_id, status=status)

    def run_random_generation(self, iterations: int = 1000, delay_ms: int = 300):
        """
        Run random station status generation (equivalent to randomPayloadGeneratorSituation)

        Args:
            iterations: Number of iterations to run
            delay_ms: Delay between messages in milliseconds
        """
        if not self.station_ids:
            self.load_station_ids()

        if not self.station_ids:
            logger.error("No station IDs available for generation")
            return

        topic = Config.KAFKA_TOPIC
        logger.info(
            f"Starting random station status generation: topic={topic}, delay={delay_ms}ms, iterations={iterations}")

        try:
            for iteration in range(iterations):
                # Shuffle station IDs for each iteration (like original code)
                shuffled_stations = self.station_ids.copy()
                random.shuffle(shuffled_stations)

                for station_id in shuffled_stations:
                    station_status = self.generate_random_station_status(
                        station_id)

                    # Send to Kafka
                    success = self.kafka_producer.send_station_status(
                        station_status.to_json(), topic)

                    if success:
                        logger.info(
                            f"Sent station[id={station_status.id}, status={station_status.status}] successfully")
                    else:
                        logger.error(
                            f"Failed to send station status for {station_id}")

                    # Sleep for specified delay
                    time.sleep(delay_ms / 1000.0)

                logger.info(
                    f"Completed iteration {iteration + 1}/{iterations}")

            logger.info("Finished random station status generation")

        except KeyboardInterrupt:
            logger.info("Generation interrupted by user")
        except Exception as e:
            logger.error(f"Error during random generation: {e}")
            raise
        finally:
            self.kafka_producer.flush()

    def run_single_station_generation(self, iterations: int = 10, delay_ms: int = 500):
        """
        Run single station status generation (equivalent to oneStationPayloadGeneratorSituation)

        Args:
            iterations: Number of messages to send
            delay_ms: Delay between messages in milliseconds
        """
        # Get a single random station ID
        station_id = self.db_connection.get_random_station_id()

        if not station_id:
            logger.error("No station ID available for single generation")
            return

        topic = Config.KAFKA_TOPIC
        logger.info(
            f"Starting single station generation: station_id={station_id}, topic={topic}, delay={delay_ms}ms")

        try:
            for i in range(iterations):
                # Always generate "off" status for single station (like original code)
                station_status = StationStatus(id=station_id, status="off")

                # Send to Kafka
                success = self.kafka_producer.send_station_status(
                    station_status.to_json(), topic)

                if success:
                    timestamp = datetime.now().isoformat()
                    logger.info(
                        f"Sent station[id={station_status.id}, status={station_status.status}], timestamp={timestamp} successfully")
                else:
                    logger.error(
                        f"Failed to send station status for {station_id}")

                # Sleep for specified delay
                time.sleep(delay_ms / 1000.0)

            logger.info("Finished single station generation")

        except KeyboardInterrupt:
            logger.info("Generation interrupted by user")
        except Exception as e:
            logger.error(f"Error during single station generation: {e}")
            raise
        finally:
            self.kafka_producer.flush()
