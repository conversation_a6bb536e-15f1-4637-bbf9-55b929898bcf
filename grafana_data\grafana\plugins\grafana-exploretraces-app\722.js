"use strict";(self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[]).push([[722],{8722:(e,t,n)=>{n.r(t),n.d(t,{default:()=>s});var r=n(5959),a=n.n(r),o=n(118),i=n(2395);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function u(e){var{initialTimeRange:t,onTimeRangeChange:n}=e,r=l(e,["initialTimeRange","onTimeRangeChange"]);const a=new o.JZ({value:t,from:t.raw.from.toString(),to:t.raw.to.toString()});var u;a.subscribeToState(e=>{n&&n(e.value)});const s=new i.Nr(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){c(e,t,n[t])})}return e}({$timeRange:a,embedded:!0,initialMetric:null!==(u=r.initialMetric)&&void 0!==u?u:"rate"},r)),f=new URLSearchParams(window.location.search);return o.Go.syncStateFromSearchParams(s,f),s}function s(e){const[t]=(0,r.useState)(u(e));return e.urlSync?a().createElement(o.$L,{namespace:"td",scene:t,updateUrlOnInit:!1,createBrowserHistorySteps:!0},a().createElement(t.Component,{model:t})):a().createElement(t.Component,{model:t})}}}]);
//# sourceMappingURL=722.js.map?_cache=be0474f2c387037968b8