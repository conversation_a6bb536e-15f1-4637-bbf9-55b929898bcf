{"version": 3, "file": "78.js?_cache=fe806019ac02064618d0", "mappings": "yMASe,SAASA,GAAM,MAAEC,IAC9B,OACE,kBAACC,EAAAA,GAAsBA,CACrBC,MAAOF,EACPG,2BAA2B,EAC3BC,iBAAiB,EACjBC,UAAWL,EAAMM,MAAMC,cAEvB,kBAACP,EAAMQ,UAAS,CAACC,MAAOT,IAG9B,C", "sources": ["webpack://grafana-metricsdrilldown-app/./pages/TrailWingman.tsx"], "sourcesContent": ["import { UrlSyncContextProvider } from '@grafana/scenes';\nimport React from 'react';\n\nimport type { DataTrail } from 'DataTrail';\n\ntype TrailProps = {\n  trail: DataTrail;\n};\n\nexport default function Trail({ trail }: Readonly<TrailProps>) {\n  return (\n    <UrlSyncContextProvider\n      scene={trail}\n      createBrowserHistorySteps={true}\n      updateUrlOnInit={true}\n      namespace={trail.state.urlNamespace}\n    >\n      <trail.Component model={trail} />\n    </UrlSyncContextProvider>\n  );\n}\n"], "names": ["Trail", "trail", "UrlSyncContextProvider", "scene", "createBrowserHistorySteps", "updateUrlOnInit", "namespace", "state", "urlNamespace", "Component", "model"], "sourceRoot": ""}