CREATE EXTENSION postgis;

CREATE EXTENSION "uuid-ossp";

create table stations
(
    id                  uuid                     default uuid_generate_v4() not null
    primary key,
    type                text                                                not null,
    device_model        text,
    device_manufacturer text,
    imei                text                                                not null,
    serial_number       text                                                not null,
    iot_id              text                                                not null,
    device_secret       text,
    created_at          timestamp with time zone default now()              not null,
    updated_at          timestamp with time zone,
    deleted_at          timestamp with time zone,
    address_id          uuid,
    legacy_id           text,
    apk_version_id      uuid,
    tag                 text,
    mac_address         text,
    pricing_id          uuid,
    station_setting_id  uuid,
    iot_migrated        boolean                  default true               not null
);
ALTER TABLE stations REPLICA IDENTITY FULL;


create unique index index_stations_by_imei
    on stations (imei)
    where (deleted_at IS NULL);

create unique index index_stations_by_legacy_id
    on stations (legacy_id)
    where ((legacy_id IS NOT NULL) AND (deleted_at IS NULL));

create unique index index_stations_by_serial_number
    on stations (serial_number)
    where (deleted_at IS NULL);


INSERT INTO stations (id, type, device_model, device_manufacturer, imei, serial_number, iot_id, device_secret, created_at, updated_at, deleted_at, address_id, legacy_id, apk_version_id, tag, mac_address, pricing_id, station_setting_id, iot_migrated) VALUES ('1217a8be-112f-36f2-b58b-f432bb8b03c4', 'UNKNOWN', 'msm8909', 'Quectel', '864228061613644', 'CF00500010867', 'TzXMYaNSRrMUYjQVhK1w000404', 'efa820820de9747204882fd11eee9a3e', '2022-12-25 05:22:07.028000 +00:00', '2023-11-11 09:06:55.978385 +00:00', null, '5fd1fc25-4aea-30da-9a6f-6764617d11e3', 'aaIAODb2Kk', null, null, '98:26:ad:a7:87:42', null, null, false);
INSERT INTO stations (id, type, device_model, device_manufacturer, imei, serial_number, iot_id, device_secret, created_at, updated_at, deleted_at, address_id, legacy_id, apk_version_id, tag, mac_address, pricing_id, station_setting_id, iot_migrated) VALUES ('4fab0e01-be0e-3da4-915e-bb4c1a3860ac', 'UNKNOWN', 'msm8909', 'Quectel', '864228061607448', 'CF00500010836', 'UOHkP1hrrY4O1oSYeFeC000404', 'e6f5a82cea47b71d952daa409974cbad', '2022-12-25 05:22:56.177000 +00:00', '2023-11-11 09:06:55.987941 +00:00', null, null, 'oZKTAveMdk', null, null, '98:26:ad:a7:86:0c', null, null, false);
INSERT INTO stations (id, type, device_model, device_manufacturer, imei, serial_number, iot_id, device_secret, created_at, updated_at, deleted_at, address_id, legacy_id, apk_version_id, tag, mac_address, pricing_id, station_setting_id, iot_migrated) VALUES ('0fe8d0c8-2076-3de6-95c8-ee9672c192bd', 'UNKNOWN', 'msm8909', 'Quectel', '864228061613362', 'CF00500010881', 'ImEjzR36uAogx20u6eK8000404', 'c8d15b9bd7d31209ff545bbc68b39e08', '2022-12-25 05:23:54.265000 +00:00', '2023-11-11 09:06:55.998508 +00:00', null, '982d0117-9804-3c23-a432-92835f805ca1', 'xSmo3HAeUV', null, null, '98:26:ad:a7:87:34', null, null, false);
INSERT INTO stations (id, type, device_model, device_manufacturer, imei, serial_number, iot_id, device_secret, created_at, updated_at, deleted_at, address_id, legacy_id, apk_version_id, tag, mac_address, pricing_id, station_setting_id, iot_migrated) VALUES ('d4745aa9-3b46-321b-9aba-749606dba22f', 'UNKNOWN', 'msm8909', 'Quectel', '864228061603868', 'CF00500010934', 'hsh234r5IqElkBui9BYB000404', 'c76a9431d650a675e6d2318cfd77d21c', '2022-12-25 05:30:48.104000 +00:00', '2023-11-11 09:06:56.010891 +00:00', null, null, 'twN8bORxD2', null, null, '98:26:ad:a7:85:59', null, null, false);
INSERT INTO stations (id, type, device_model, device_manufacturer, imei, serial_number, iot_id, device_secret, created_at, updated_at, deleted_at, address_id, legacy_id, apk_version_id, tag, mac_address, pricing_id, station_setting_id, iot_migrated) VALUES ('e92732b6-5743-3bdb-93ea-bbcfa26290d0', 'UNKNOWN', 'msm8909', 'Quectel', '864228061609725', 'CF00500010862', 'TTQjXFB3oaSzuZT85Lry000404', '47d868b96451b8c5073033553e97653c', '2022-12-25 05:32:33.149000 +00:00', '2023-11-11 09:06:56.022252 +00:00', null, null, 'LyXiX1RbtF', null, null, '98:26:ad:a7:86:7e', null, null, false);
INSERT INTO stations (id, type, device_model, device_manufacturer, imei, serial_number, iot_id, device_secret, created_at, updated_at, deleted_at, address_id, legacy_id, apk_version_id, tag, mac_address, pricing_id, station_setting_id, iot_migrated) VALUES ('612e2873-6bd3-3a44-95ba-f2f1ee81e6b2', 'UNKNOWN', 'msm8909', 'Quectel', '864228061605442', 'CF00500010956', 'eeT1yIZyd4uBckwm9BYU000404', 'bfdeb408c2764aaf01ef8614dca95f49', '2022-12-25 05:33:29.228000 +00:00', '2023-11-11 09:06:56.033687 +00:00', null, null, 'XfBJLCmkuG', null, null, '98:26:ad:a7:85:a8', null, null, false);
INSERT INTO stations (id, type, device_model, device_manufacturer, imei, serial_number, iot_id, device_secret, created_at, updated_at, deleted_at, address_id, legacy_id, apk_version_id, tag, mac_address, pricing_id, station_setting_id, iot_migrated) VALUES ('6043580e-8a25-3389-9548-247eddefddf5', 'UNKNOWN', 'msm8909', 'Quectel', '864228061609865', 'CF00500010964', 'ppCqmZB95aNdqo6kb9iH000404', 'ce062e5bbb37c5bdc4039ae37ac36a26', '2022-12-25 05:36:17.426000 +00:00', '2023-11-11 09:06:56.046526 +00:00', null, null, 'tQs7fSpKhy', null, null, '98:26:ad:a7:86:85', null, null, false);


create table addresses
(
    id                             uuid                     default uuid_generate_v4() not null
        primary key,
    location_name                  text                                                not null,
    zip                            text,
    stripe_address_id              text,
    venue_type                     text,
    customer_type                  text,
    address_line_1                 text,
    city                           text,
    state                          text,
    country                        text,
    opening_timings                text,
    location                       geometry(Point, 4326)                               not null,
    updated_at                     timestamp with time zone,
    deleted_at                     timestamp with time zone,
    created_at                     timestamp with time zone default now()              not null,
    address_line_2                 text,
    attachment_imei                text,
    attachment_full_station_screen text,
    attachment_station_left_side   text,
    attachment_station_right_side  text,
    attachment_station_perspective text,
    attachment_additional          text,
    legacy_id                      text,
    active                         boolean                  default true               not null,
    venue_type_id                  uuid,
    customer_type_id               uuid,
    address_contact_id             uuid,
    country_code                   text,
    group_id                       uuid
);

ALTER TABLE addresses REPLICA IDENTITY FULL;

create index index_addresses_by_location
    on addresses using gist (location)
    where (deleted_at IS NULL);

create index index_addresses_by_location_name
    on addresses (location_name)
    where (deleted_at IS NULL);

create index index_address_by_group_id
    on addresses (group_id)
    where (deleted_at IS NULL);

CREATE VIEW view_addresses AS (
    select CAST(a.id as VARCHAR), a.city, CAST(a.location as varchar)
    from addresses a
);

INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('b1984d2d-e63e-456e-b4c8-9f8157cb0c6e', 'Venue', '90036', 'tml_FSTXigdqzf590v', null, null, '7563 Beverly Blvd, Los Angeles, CA', 'California', 'Los Angeles County', 'US', '7563 Beverly Blvd.', '0101000020E61000009E280989B4965DC0728687D5C4094140', '2023-10-12 15:57:32.412983 +00:00', '2023-10-12 15:57:32.409714 +00:00', '2023-10-12 15:43:10.718184 +00:00', null, 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', null, null, true, null, null, null, null, null);
INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('5b86237a-d6d0-4792-b546-f9f9741393ff', 'Venue', '90036', 'tml_FSTYWwkQILhcaX', null, null, '7563 Beverly Blvd, Los Angeles, California', 'California', 'Los Angeles County', 'US', 'venue', '0101000020E61000009E280989B4965DC0728687D5C4094140', '2023-10-12 15:58:23.244497 +00:00', '2023-10-12 15:58:23.241735 +00:00', '2023-10-12 15:46:39.737712 +00:00', null, 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', null, null, true, null, null, null, null, null);
INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('f960629a-46d2-4cf6-beef-cfa7822b621c', 'Venue', 'D04 HR25', 'tml_FSWOYwLtM4VygU', null, null, '1 Grand Canal Street Upper, Ballsbridge, Dublin 4, Ireland', 'County Dublin', 'D', 'IE', 'NA', '0101000020E61000002704615DCBF218C059D70D805DAB4A40', '2023-10-14 15:15:20.164463 +00:00', '2023-10-14 15:15:20.161714 +00:00', '2023-10-13 04:43:20.006702 +00:00', null, 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', null, null, true, null, null, null, null, null);
INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('bd4a20e9-9025-4573-8c73-1a8f3f636289', '29 Palms, CA', '92277', 'tml_FSW4pAX3AJRlLP', null, null, '29 Palms, CA', 'Twentynine Palms', 'CA', 'US', '29 Palms, CA', '0101000020E6100000757DD58077035DC031DA99F859114140', '2023-10-14 15:15:28.816030 +00:00', '2023-10-14 15:15:28.812275 +00:00', '2023-10-13 07:43:36.222090 +00:00', null, 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', null, null, true, null, null, null, null, null);
INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('d3538afd-bc73-4f5f-a39e-605d4e9836a1', 'venue', 'D04 HR25', 'tml_FSXSjgC5JwC1l4', null, null, '1 Grand Canal Street Upper, Ballsbridge, Dublin 4, Ireland', 'County Dublin', 'D', 'IE', 'e', '0101000020E61000002704615DCBF218C059D70D805DAB4A40', '2023-10-14 15:15:34.730967 +00:00', '2023-10-14 15:15:34.728688 +00:00', '2023-10-13 09:34:10.327311 +00:00', null, 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', null, null, true, null, null, null, null, null);
INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('31aaf02e-3f1a-42f6-911c-ee30478c1623', '1', '60612', 'tml_FS1jUQCdmUX3hT', null, null, 'United Center, West Madison Street, Chicago, IL', 'Chicago', 'IL', 'US', '1', '0101000020E61000001690ACB225EB55C0ADF1E379BAF04440', null, null, '2023-10-19 03:17:41.727996 +00:00', null, 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/image/2de641ba-7e4b-4211-929b-99f6a04e6956_demo.png', null, null, true, null, null, null, null, null);
INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('2f67267d-b906-448a-8f39-6d21920052d1', '1', '60612', 'tml_FSzXzgdd6vMIqp', null, null, 'United Center, West Madison Street, Chicago, IL', 'Chicago', 'IL', 'US', '1', '0101000020E61000001690ACB225EB55C0ADF1E379BAF04440', '2023-11-17 07:12:48.519110 +00:00', null, '2023-10-18 17:22:26.549685 +00:00', null, 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/2a19a49d-c71d-410f-808a-c7f21798882b_Screenshot+2023-11-16+at+15.21.11.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/570d4085-bac9-467d-be69-59f3cdbb917a_Screenshot+2023-11-16+at+15.21.11.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/816f4cce-83d1-4845-8007-372304170d8f_Screenshot+2023-11-16+at+15.21.11.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/47bb945a-492f-4ff7-b8c1-9f9090336b9e_Screenshot+2023-11-16+at+15.21.11.png', 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/44c5d20f-b854-400b-a0dc-4862899fedd9_Screenshot+2023-11-16+at+15.21.11.png', '', null, true, null, null, null, null, null);
INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('0ec7056a-1b41-47c1-88b9-e8048908197e', '1', '60612', 'tml_FSz1GAItI7b9YA', null, null, 'United Center, West Madison Street, Chicago, IL', 'Chicago', 'IL', 'US', 'e', '0101000020E61000001690ACB225EB55C0ADF1E379BAF04440', '2023-11-28 08:47:13.803369 +00:00', null, '2023-10-18 19:27:24.802215 +00:00', null, 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/f3ebf527-7478-454b-a7e8-eeef834a20c8_8000-0807-01-front-large-500x500.jpg', 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/4d8699e2-94b6-4c01-9b31-f4f1372ae6e6_8000-0807-01-front-large-500x500.jpg', 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/c2141b96-9e3c-46cd-a639-9908df54dfb1_8000-0807-01-front-large-500x500.jpg', 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/a6babc85-a56f-4827-b1c1-fe870244c966_8000-0807-01-front-large-500x500.jpg', 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/1f07f815-f7cb-4c0f-98cb-2fddc10d3c01_8000-0807-01-front-large-500x500.jpg', 'https://storage.googleapis.com/charge-fuze-asset-dev/station/image/f80f818e-2034-473c-ac66-0cf08689be85_8000-0807-01-front-large-500x500.jpg', null, true, null, null, null, null, null);
INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('5fd1fc25-4aea-30da-9a6f-6764617d11e3', 'Pearl Bar', '77007', 'tml_FAjvEQaQwYr9pH', 'Nightlife', 'Other', '4216 Washington Ave, Houston, TX 77007, USA', 'Houston', 'Texas', 'United States', 'Tuesday-Saturday 6 PM–2 AM Sunday 3 PM–1 AM', '0101000020E6100000AF777FBCD7D957C055FE21A23DC53D40', '2023-10-30 02:45:15.326047 +00:00', null, '2023-02-22 16:26:43.953000 +00:00', '', null, null, null, null, null, null, 'NuyFJ4wAI5', true, '694ba390-2596-3149-8445-43e5b48b4d8c', '795f3202-b17c-36bc-bd4b-771d8c6c9eaf', null, null, null);
INSERT INTO addresses (id, location_name, zip, stripe_address_id, venue_type, customer_type, address_line_1, city, state, country, opening_timings, location, updated_at, deleted_at, created_at, address_line_2, attachment_imei, attachment_full_station_screen, attachment_station_left_side, attachment_station_right_side, attachment_station_perspective, attachment_additional, legacy_id, active, venue_type_id, customer_type_id, address_contact_id, country_code, group_id) VALUES ('982d0117-9804-3c23-a432-92835f805ca1', 'Pawn Pub', '78148', 'tml_EmqAAkYG8dh5p0', 'Bar', 'Bar', '100 Village Green, Universal City, TX 78148, USA', 'Universal City', 'Texas', 'United States', 'Monday-Sunday 12 PM–2 AM', '0101000020E61000000000202AB09258C0DF6F80573A8C3D40', '2023-10-30 02:45:16.125353 +00:00', null, '2023-02-27 17:21:08.521000 +00:00', '', null, null, null, null, null, null, 'JZ0i6lz5CD', true, '37b51d19-4a75-33e4-9b56-f6524f2d51f2', '37b51d19-4a75-33e4-9b56-f6524f2d51f2', null, null, null);