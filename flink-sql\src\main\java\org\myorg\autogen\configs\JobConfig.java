package org.myorg.autogen.configs;

import java.util.List;

/**
 * Root configuration class representing the entire job configuration file
 */
public class JobConfig {
    private List<Job> jobs;

    public List<Job> getJobs() {
        return jobs;
    }

    public void setJobs(List<Job> jobs) {
        this.jobs = jobs;
    }

    /**
     * Individual job configuration
     */
    public static class Job {
        private String name;
        private ReaderConfig readers;
        private List<TransformerConfig> transformers;
        private WriterConfig writers;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public ReaderConfig getReaders() {
            return readers;
        }

        public void setReaders(ReaderConfig readers) {
            this.readers = readers;
        }

        public List<TransformerConfig> getTransformers() {
            return transformers;
        }

        public void setTransformers(List<TransformerConfig> transformers) {
            this.transformers = transformers;
        }

        public WriterConfig getWriters() {
            return writers;
        }

        public void setWriters(WriterConfig writers) {
            this.writers = writers;
        }
    }
}
