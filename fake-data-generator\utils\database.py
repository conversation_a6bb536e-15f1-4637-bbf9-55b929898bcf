"""
Database connection utilities
"""

import os
import psycopg2
from typing import List, Tuple, Optional
import logging

logger = logging.getLogger(__name__)


class DatabaseConnection:
    """
    PostgreSQL database connection manager
    """
    
    def __init__(self):
        """Initialize database connection with environment variables"""
        self.host = os.getenv("DATABASE_HOST", "localhost")
        self.port = os.getenv("DATABASE_PORT", "5432")
        self.database = os.getenv("DATABASE_NAME", "service-rental-demo")
        self.username = os.getenv("DATABASE_USERNAME", "admin")
        self.password = os.getenv("DATABASE_PASSWORD", "admin")
        self.connection = None
        
    def connect(self) -> psycopg2.extensions.connection:
        """
        Establish database connection
        
        Returns:
            psycopg2 connection object
        """
        try:
            connection_string = f"postgresql://{self.username}:{self.password}@{self.host}:{self.port}/{self.database}"
            self.connection = psycopg2.connect(connection_string)
            logger.info(f"Connected to database: {self.host}:{self.port}/{self.database}")
            return self.connection
        except Exception as e:
            logger.error(f"Failed to connect to database: {e}")
            raise
    
    def get_connection(self) -> psycopg2.extensions.connection:
        """
        Get existing connection or create new one
        
        Returns:
            psycopg2 connection object
        """
        if self.connection is None or self.connection.closed:
            return self.connect()
        return self.connection
    
    def execute_query(self, query: str, params: Optional[tuple] = None) -> List[tuple]:
        """
        Execute a SELECT query and return results
        
        Args:
            query: SQL query string
            params: Query parameters
            
        Returns:
            List of result tuples
        """
        connection = self.get_connection()
        try:
            with connection.cursor() as cursor:
                cursor.execute(query, params)
                return cursor.fetchall()
        except Exception as e:
            logger.error(f"Query execution failed: {e}")
            connection.rollback()
            raise
    
    def get_station_ids(self, limit: int = 100) -> List[str]:
        """
        Get list of station IDs from database
        
        Args:
            limit: Maximum number of station IDs to return
            
        Returns:
            List of station ID strings
        """
        query = "SELECT id FROM stations WHERE deleted_at IS NULL LIMIT %s"
        results = self.execute_query(query, (limit,))
        return [str(row[0]) for row in results]
    
    def get_random_station_id(self) -> Optional[str]:
        """
        Get a single random station ID
        
        Returns:
            Station ID string or None if no stations found
        """
        query = "SELECT id FROM stations WHERE deleted_at IS NULL ORDER BY RANDOM() LIMIT 1"
        results = self.execute_query(query)
        return str(results[0][0]) if results else None
    
    def close(self):
        """Close database connection"""
        if self.connection and not self.connection.closed:
            self.connection.close()
            logger.info("Database connection closed")
    
    def __enter__(self):
        """Context manager entry"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.close()
