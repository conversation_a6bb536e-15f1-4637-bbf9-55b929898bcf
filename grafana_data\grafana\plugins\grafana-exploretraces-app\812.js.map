{"version": 3, "file": "812.js?_cache=4ec166e238793ea3adc8", "mappings": "wNAMe,SAASA,GAA0B,cAChDC,EAAa,SACbC,EAAQ,KACRC,EAAI,GACJC,EAAE,uBACFC,EAAsB,aACtBC,IAEA,MAAMC,GAAsBC,EAAAA,EAAAA,uBAEtBC,GAAOC,EAAAA,EAAAA,SAAQ,KACnB,IAAIC,EAAS,IAAIC,gBAoBjB,OAlBIX,GACFU,EAAOE,OAAO,SAAUZ,GAGtBE,GACFQ,EAAOE,OAAO,OAAQV,GAGpBC,GACFO,EAAOE,OAAO,KAAMT,GAGtBF,EAASY,QAASC,IAChBJ,EAAOE,OAAO,cAAe,GAAGE,EAAeC,QAAQD,EAAeE,YAAYF,EAAeG,WAGnGP,EAAOE,OAAO,oBAAqB,QAE5B,KAAKM,EAAAA,cAAyBR,EAAOS,cAC3C,CAACnB,EAAeE,EAAMC,EAAIF,IAE7B,OAAKO,EAIDH,EACKA,EAAa,CAAEG,SAItB,kBAACY,EAAAA,WAAUA,CACTC,QAAQ,YACRb,KAAMA,EACNc,QAAS,IAAMhB,EAAoBF,GAA0B,aAC9D,4BAZM,IAgBX,C", "sources": ["webpack://grafana-exploretraces-app/./exposedComponents/OpenInExploreTracesButton/OpenInExploreTracesButton.tsx"], "sourcesContent": ["import { useReturnToPrevious } from '@grafana/runtime';\nimport { LinkButton } from '@grafana/ui';\nimport React, { useMemo } from 'react';\nimport { OpenInExploreTracesButtonProps } from '../types';\nimport pluginJson from '../../plugin.json';\n\nexport default function OpenInExploreTracesButton({\n  datasourceUid,\n  matchers,\n  from,\n  to,\n  returnToPreviousSource,\n  renderButton,\n}: OpenInExploreTracesButtonProps) {\n  const setReturnToPrevious = useReturnToPrevious();\n\n  const href = useMemo(() => {\n    let params = new URLSearchParams();\n\n    if (datasourceUid) {\n      params.append('var-ds', datasourceUid);\n    }\n\n    if (from) {\n      params.append('from', from);\n    }\n\n    if (to) {\n      params.append('to', to);\n    }\n\n    matchers.forEach((streamSelector) => {\n      params.append('var-filters', `${streamSelector.name}|${streamSelector.operator}|${streamSelector.value}`);\n    });\n\n    params.append('var-primarySignal', 'true'); // so all spans is selected\n\n    return `a/${pluginJson.id}/explore?${params.toString()}`;\n  }, [datasourceUid, from, to, matchers]);\n\n  if (!href) {\n    return null;\n  }\n\n  if (renderButton) {\n    return renderButton({ href });\n  }\n\n  return (\n    <LinkButton\n      variant=\"secondary\"\n      href={href}\n      onClick={() => setReturnToPrevious(returnToPreviousSource || 'previous')}\n    >\n      Open in Traces Drilldown\n    </LinkButton>\n  );\n}\n"], "names": ["OpenInExploreTracesButton", "datasourceUid", "matchers", "from", "to", "returnToPreviousSource", "renderButton", "setReturnToPrevious", "useReturnToPrevious", "href", "useMemo", "params", "URLSearchParams", "append", "for<PERSON>ach", "streamSelector", "name", "operator", "value", "pluginJson", "toString", "LinkButton", "variant", "onClick"], "sourceRoot": ""}