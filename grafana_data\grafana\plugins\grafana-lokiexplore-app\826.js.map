{"version": 3, "file": "826.js?_cache=91e39090c5611938563c", "mappings": "m9CAkCA,MA0IMA,EAAaC,IAA0B,CAC3CC,UAAWC,EAAAA,GAAG;aACHF,EAAMG,OAAOC,KAAKC;IAE7BC,MAAMJ,EAAAA,EAAAA,KAAI,CACRK,WAAYP,EAAMQ,QAAQ,KAE5BC,OAAOP,EAAAA,EAAAA,KAAI,CACTQ,WAAY,SACZC,QAAS,OACTC,aAAcZ,EAAMQ,QAAQ,OAE9BK,UAAWX,EAAAA,GAAG;kBACEF,EAAMQ,QAAQ;IAE9BM,YAAaZ,EAAAA,GAAG;kBACAF,EAAMQ,QAAQ;IAE9BO,MAAMb,EAAAA,EAAAA,KAAI,CACRc,MAAOhB,EAAMG,OAAOC,KAAKC,UACzBO,aAAcZ,EAAMQ,QAAQ,GAC5BK,UAAWb,EAAMQ,QAAQ,OAIvBS,EAAwB,CAAOC,EAAkBC,IAAAA,EAAAA,YACrD,UACQC,EAAaF,EAAUC,GAI7BE,EAAAA,gBAAgBC,QAClB,CAAE,MAAOC,GACPC,EAAAA,EAAOC,MAAMF,EAAG,CAAEG,IAAK,mCACzB,CACF,EAVuDP,GAYjDQ,EAAU,CACdC,UAAW,CACTC,UAAW,2BACXC,WAAY,kCACZC,SAAU,gCACVC,QAAS,mCACTC,OAAQ,+BAICb,EAAe,CAAOF,EAAkBC,IAAAA,EAAAA,YACnD,MAAMe,GAAWC,EAAAA,EAAAA,iBAAgBC,MAAM,CACrCjB,OACAkB,OAAQ,OACRC,IAAK,gBAAgBpB,eAKvB,aAF2BqB,EAAAA,EAAAA,eAAcL,IAErBf,IACtB,EAVqDA,GAY/CqB,EAAWT,IACf,IACE,GAAIA,EAAU,CACZ,MAAMU,EAAUC,EAAAA,UAAUC,kBAAkBZ,GAC5C,OAAOa,EAAAA,EAAAA,UAASH,IAAYA,GAzML,IA0MzB,CAEE,OAAO,CAEX,CAAE,MAAOlB,GAAI,CAEb,OAAO,GAGT,EA/MkB,EAAGsB,aACnB,MAAMC,GAASC,EAAAA,EAAAA,YAAWhD,IACpB,QAAEiD,EAAO,SAAEC,EAAQ,OAAEC,GAAWL,EAAOM,K,IAIzCF,EAAAA,EAAAA,EACQA,EACOA,EACCA,EALpB,MAAOG,EAAOC,IAAYC,EAAAA,EAAAA,UAAgB,CACxCC,WACqGC,QAAnGP,EAA6DQ,QAA7DR,EAAoB,QAApBA,EAAAA,aAAAA,EAAAA,EAAUM,kBAAVN,IAAAA,EAAAA,GAAwBQ,EAAAA,EAAAA,aAAxBR,IAAAA,EAAAA,GAAmEO,EAAAA,EAAAA,aAAnEP,IAAAA,EAAAA,EAAyG,GAC3GlB,SAA4B,QAAlBkB,EAAAA,aAAAA,EAAAA,EAAUlB,gBAAVkB,IAAAA,EAAAA,EAAsB,GAChCT,QAASA,EAA0B,QAAlBS,EAAAA,aAAAA,EAAAA,EAAUlB,gBAAVkB,IAAAA,EAAAA,EAAsB,IACvCS,iBAA4C,QAA1BT,EAAAA,aAAAA,EAAAA,EAAUS,wBAAVT,IAAAA,GAAAA,IA2BpB,OACE,kBAACU,MAAAA,CAAIC,cAAajC,EAAQC,UAAUC,WAClC,kBAACgC,EAAAA,SAAQA,CAACpD,MAAM,YACd,kBAACqD,EAAAA,MAAKA,CACJC,YACE,kBAACC,OAAAA,KAAK,2JAKRvD,MAAO,uBAEP,kBAACwD,EAAAA,iBAAgBA,CACfC,MAAO,GACPC,OAASC,GAAmB,SAAZA,EAAGC,KACnBC,QAASlB,EAAMG,WACfgB,SAxCkBH,IAC1Bf,EAAS,OACJD,GAAAA,CACHG,WAAYa,EAAGI,WAyCb,kBAACV,EAAAA,MAAKA,CACJW,SAAUjC,EAAQY,EAAMrB,UACxBN,MAAO,2FACPsC,YACE,kBAACC,OAAAA,KAAK,qLAEuE,kBAACU,KAAAA,MAAK,+BAIrFjE,MAAO,+BACPkE,UAAW7B,EAAOjC,WAElB,kBAAC+D,EAAAA,MAAKA,CACJV,MAAO,GACPW,GAAG,WACHjB,cAAajC,EAAQC,UAAUG,SAC/BtB,MAAO,eACPqE,MAAO1B,aAAAA,EAAAA,EAAOrB,SACdgD,YAAa,KACbR,SAzDgBS,IACxB,MAAMjD,EAAWiD,EAAMC,OAAOH,MAAMI,OACpC7B,EAAS,OACJD,GAAAA,CACHrB,WACAS,QAASA,EAAQT,UAwDf,kBAAC+B,EAAAA,MAAKA,CACJa,UAAW7B,EAAOjC,UAClBkD,YACE,kBAACC,OAAAA,KAAK,yCACwC,IAC5C,kBAACmB,IAAAA,CACCR,UAAU,gBACVS,KAAK,mFACLH,OAAO,SACPI,IAAI,cACL,qBAEI,IAAI,2CAIb5E,MAAO,yBAEP,kBAAC6E,EAAAA,SAAQA,CACPT,GAAG,mBACHjB,cAAajC,EAAQC,UAAUG,SAC/BtB,MAAO,mBACPqE,MAAO1B,aAAAA,EAAAA,EAAOM,iBACdqB,YAAa,KACbR,SA5EwBS,IAChC,MAAMtB,EAAmBsB,EAAMO,cAAcC,QAC7CnC,EAAS,OACJD,GAAAA,CACHM,0BA4EE,kBAACC,MAAAA,CAAIgB,UAAW7B,EAAOjC,WACrB,kBAAC4E,EAAAA,OAAMA,CACLpB,KAAK,SACLT,cAAajC,EAAQC,UAAUK,OAC/ByD,QAAS,IACPzE,EAAsB4B,EAAOM,KAAK0B,GAAI,CACpC7B,UACAC,SAAU,CACRM,WAAYH,EAAMG,WAClBxB,SAAUqB,EAAMrB,SAChB2B,iBAAkBN,EAAMM,kBAE1BR,WAGJyC,UAAWnD,EAAQY,EAAMrB,WAC1B,kBAIH,kBAAC6D,IAAAA,CAAEjB,UAAW7B,EAAO/B,MAAM,gE", "sources": ["webpack://grafana-lokiexplore-app/./Components/AppConfig/AppConfig.tsx"], "sourcesContent": ["import React, { ChangeEvent, useState } from 'react';\n\nimport { css } from '@emotion/css';\nimport { isNumber } from 'lodash';\nimport { lastValueFrom } from 'rxjs';\n\nimport {\n  AppPluginMeta,\n  DataSourceInstanceSettings,\n  GrafanaTheme2,\n  PluginConfigPageProps,\n  PluginMeta,\n  rangeUtil,\n} from '@grafana/data';\nimport { DataSourcePicker, getBackendSrv, locationService } from '@grafana/runtime';\nimport { Button, Checkbox, Field, FieldSet, Input, useStyles2 } from '@grafana/ui';\n\nimport { logger } from '../../services/logger';\nimport { getDefaultDatasourceFromDatasourceSrv, getLastUsedDataSourceFromStorage } from '../../services/store';\n\nexport type JsonData = {\n  dataSource?: string;\n  interval?: string;\n  patternsDisabled?: boolean;\n};\n\ntype State = {\n  dataSource: string;\n  interval: string;\n  isValid: boolean;\n  patternsDisabled: boolean;\n};\n\n// 1 hour minimum\nconst MIN_INTERVAL_SECONDS = 3600;\n\ninterface Props extends PluginConfigPageProps<AppPluginMeta<JsonData>> {}\n\nconst AppConfig = ({ plugin }: Props) => {\n  const styles = useStyles2(getStyles);\n  const { enabled, jsonData, pinned } = plugin.meta;\n\n  const [state, setState] = useState<State>({\n    dataSource:\n      jsonData?.dataSource ?? getDefaultDatasourceFromDatasourceSrv() ?? getLastUsedDataSourceFromStorage() ?? '',\n    interval: jsonData?.interval ?? '',\n    isValid: isValid(jsonData?.interval ?? ''),\n    patternsDisabled: jsonData?.patternsDisabled ?? false,\n  });\n\n  const onChangeDatasource = (ds: DataSourceInstanceSettings) => {\n    setState({\n      ...state,\n      dataSource: ds.uid,\n    });\n  };\n\n  const onChangeInterval = (event: ChangeEvent<HTMLInputElement>) => {\n    const interval = event.target.value.trim();\n    setState({\n      ...state,\n      interval,\n      isValid: isValid(interval),\n    });\n  };\n\n  const onChangePatternsDisabled = (event: ChangeEvent<HTMLInputElement>) => {\n    const patternsDisabled = event.currentTarget.checked;\n    setState({\n      ...state,\n      patternsDisabled,\n    });\n  };\n\n  return (\n    <div data-testid={testIds.appConfig.container}>\n      <FieldSet label=\"Settings\">\n        <Field\n          description={\n            <span>\n              The default data source to be used for new Logs Drilldown users. Each user can override their default by\n              setting another data source in Logs Drilldown.\n            </span>\n          }\n          label={'Default data source'}\n        >\n          <DataSourcePicker\n            width={60}\n            filter={(ds) => ds.type === 'loki'}\n            current={state.dataSource}\n            onChange={onChangeDatasource}\n          />\n        </Field>\n\n        <Field\n          invalid={!isValid(state.interval)}\n          error={'Interval is invalid. Please enter an interval longer then \"60m\". For example: 3d, 1w, 1m'}\n          description={\n            <span>\n              The maximum interval that can be selected in the time picker within the Grafana Logs Drilldown app. If\n              empty, users can select any time range interval in Grafana Logs Drilldown. <br />\n              Example values: 7d, 24h, 2w\n            </span>\n          }\n          label={'Maximum time picker interval'}\n          className={styles.marginTop}\n        >\n          <Input\n            width={60}\n            id=\"interval\"\n            data-testid={testIds.appConfig.interval}\n            label={`Max interval`}\n            value={state?.interval}\n            placeholder={`7d`}\n            onChange={onChangeInterval}\n          />\n        </Field>\n\n        <Field\n          className={styles.marginTop}\n          description={\n            <span>\n              Disables Logs Drilldown&apos;s usage of the{' '}\n              <a\n                className=\"external-link\"\n                href=\"https://grafana.com/docs/loki/latest/reference/loki-http-api/#patterns-detection\"\n                target=\"_blank\"\n                rel=\"noreferrer\"\n              >\n                Loki Patterns API\n              </a>{' '}\n              endpoint, and removes the Patterns tab.\n            </span>\n          }\n          label={'Disable Loki patterns'}\n        >\n          <Checkbox\n            id=\"disable-patterns\"\n            data-testid={testIds.appConfig.interval}\n            label={`Disable patterns`}\n            value={state?.patternsDisabled}\n            placeholder={`7d`}\n            onChange={onChangePatternsDisabled}\n          />\n        </Field>\n\n        <div className={styles.marginTop}>\n          <Button\n            type=\"submit\"\n            data-testid={testIds.appConfig.submit}\n            onClick={() =>\n              updatePluginAndReload(plugin.meta.id, {\n                enabled,\n                jsonData: {\n                  dataSource: state.dataSource,\n                  interval: state.interval,\n                  patternsDisabled: state.patternsDisabled,\n                },\n                pinned,\n              })\n            }\n            disabled={!isValid(state.interval)}\n          >\n            Save settings\n          </Button>\n        </div>\n        <p className={styles.note}>Active users must refresh the app to update configuration.</p>\n      </FieldSet>\n    </div>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  colorWeak: css`\n    color: ${theme.colors.text.secondary};\n  `,\n  icon: css({\n    marginLeft: theme.spacing(1),\n  }),\n  label: css({\n    alignItems: 'center',\n    display: 'flex',\n    marginBottom: theme.spacing(0.75),\n  }),\n  marginTop: css`\n    margin-top: ${theme.spacing(3)};\n  `,\n  marginTopXl: css`\n    margin-top: ${theme.spacing(6)};\n  `,\n  note: css({\n    color: theme.colors.text.secondary,\n    marginBottom: theme.spacing(1),\n    marginTop: theme.spacing(1),\n  }),\n});\n\nconst updatePluginAndReload = async (pluginId: string, data: Partial<PluginMeta<JsonData>>) => {\n  try {\n    await updatePlugin(pluginId, data);\n\n    // Reloading the page as the changes made here wouldn't be propagated to the actual plugin otherwise.\n    // This is not ideal, however unfortunately currently there is no supported way for updating the plugin state.\n    locationService.reload();\n  } catch (e) {\n    logger.error(e, { msg: 'Error while updating the plugin' });\n  }\n};\n\nconst testIds = {\n  appConfig: {\n    container: 'data-testid ac-container',\n    datasource: 'data-testid ac-datasource-input',\n    interval: 'data-testid ac-interval-input',\n    pattern: 'data-testid ac-patterns-disabled',\n    submit: 'data-testid ac-submit-form',\n  },\n};\n\nexport const updatePlugin = async (pluginId: string, data: Partial<PluginMeta>) => {\n  const response = getBackendSrv().fetch({\n    data,\n    method: 'POST',\n    url: `/api/plugins/${pluginId}/settings`,\n  });\n\n  const dataResponse = await lastValueFrom(response);\n\n  return dataResponse.data;\n};\n\nconst isValid = (interval: string): boolean => {\n  try {\n    if (interval) {\n      const seconds = rangeUtil.intervalToSeconds(interval);\n      return isNumber(seconds) && seconds >= MIN_INTERVAL_SECONDS;\n    } else {\n      // Empty strings are fine\n      return true;\n    }\n  } catch (e) {}\n\n  return false;\n};\n\nexport default AppConfig;\n"], "names": ["getStyles", "theme", "colorWeak", "css", "colors", "text", "secondary", "icon", "marginLeft", "spacing", "label", "alignItems", "display", "marginBottom", "marginTop", "marginTopXl", "note", "color", "updatePluginAndReload", "pluginId", "data", "updatePlugin", "locationService", "reload", "e", "logger", "error", "msg", "testIds", "appConfig", "container", "datasource", "interval", "pattern", "submit", "response", "getBackendSrv", "fetch", "method", "url", "lastValueFrom", "<PERSON><PERSON><PERSON><PERSON>", "seconds", "rangeUtil", "intervalToSeconds", "isNumber", "plugin", "styles", "useStyles2", "enabled", "jsonData", "pinned", "meta", "state", "setState", "useState", "dataSource", "getLastUsedDataSourceFromStorage", "getDefaultDatasourceFromDatasourceSrv", "patternsDisabled", "div", "data-testid", "FieldSet", "Field", "description", "span", "DataSourcePicker", "width", "filter", "ds", "type", "current", "onChange", "uid", "invalid", "br", "className", "Input", "id", "value", "placeholder", "event", "target", "trim", "a", "href", "rel", "Checkbox", "currentTarget", "checked", "<PERSON><PERSON>", "onClick", "disabled", "p"], "sourceRoot": ""}