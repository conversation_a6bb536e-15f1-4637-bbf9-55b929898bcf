"""
Order data generator
"""

import time
import random
import logging
from typing import List, Optional

from model.order import Order
from utils.database import DatabaseConnection
from utils.kafka_producer import KafkaProducerManager
from utils.config import Config

logger = logging.getLogger(__name__)


class OrderGenerator:
    """
    Generator for order events
    """

    def __init__(self, db_connection: DatabaseConnection, kafka_producer: KafkaProducerManager):
        """
        Initialize order generator

        Args:
            db_connection: Database connection instance
            kafka_producer: Kafka producer instance
        """
        self.db_connection = db_connection
        self.kafka_producer = kafka_producer
        self.station_ids = []

    def load_station_ids(self, limit: int = 100) -> List[str]:
        """
        Load station IDs from database for order generation

        Args:
            limit: Maximum number of station IDs to load

        Returns:
            List of station ID strings
        """
        try:
            self.station_ids = self.db_connection.get_station_ids(limit)
            logger.info(
                f"Loaded {len(self.station_ids)} station IDs for order generation")
            return self.station_ids
        except Exception as e:
            logger.error(f"Failed to load station IDs: {e}")
            raise

    def generate_random_order(self, station_id: Optional[str] = None) -> Order:
        """
        Generate random order

        Args:
            station_id: Optional specific station ID, otherwise random from loaded stations

        Returns:
            Order instance
        """
        if station_id is None:
            if not self.station_ids:
                self.load_station_ids()
            station_id = random.choice(
                self.station_ids) if self.station_ids else None

        return Order(
            station_id=station_id,
            amount=Config.get_random_order_amount(),
            currency=Config.get_random_currency(),
            status=Config.get_random_order_status()
        )

    def run_order_generation(self, iterations: int = 1000, delay_ms: int = 1000):
        """
        Run order generation

        Args:
            iterations: Number of orders to generate
            delay_ms: Delay between orders in milliseconds
        """
        if not self.station_ids:
            self.load_station_ids()

        if not self.station_ids:
            logger.error("No station IDs available for order generation")
            return

        topic = Config.ORDER_KAFKA_TOPIC
        logger.info(
            f"Starting order generation: topic={topic}, delay={delay_ms}ms, iterations={iterations}")

        try:
            for iteration in range(iterations):
                # Generate random order
                order = self.generate_random_order()

                # Send to Kafka
                success = self.kafka_producer.send_order(
                    order.to_json(), topic)

                if success:
                    logger.info(
                        f"Sent order[id={order.id}, station_id={order.station_id}, amount={order.amount} {order.currency}, status={order.status}] successfully")
                else:
                    logger.error(f"Failed to send order {order.id}")

                # Sleep for specified delay
                time.sleep(delay_ms / 1000.0)

            logger.info("Finished order generation")

        except KeyboardInterrupt:
            logger.info("Order generation interrupted by user")
        except Exception as e:
            logger.error(f"Error during order generation: {e}")
            raise
        finally:
            self.kafka_producer.flush()

    def run_station_specific_orders(self, station_id: str, iterations: int = 50, delay_ms: int = 800):
        """
        Generate orders for a specific station

        Args:
            station_id: Specific station ID
            iterations: Number of orders to generate
            delay_ms: Delay between orders in milliseconds
        """
        topic = Config.ORDER_KAFKA_TOPIC
        logger.info(
            f"Starting station-specific order generation: station_id={station_id}, topic={topic}, delay={delay_ms}ms")

        try:
            for iteration in range(iterations):
                # Generate order for specific station
                order = self.generate_random_order(station_id=station_id)

                # Send to Kafka
                success = self.kafka_producer.send_order(
                    order.to_json(), topic)

                if success:
                    logger.info(
                        f"Sent order[id={order.id}, station_id={order.station_id}, amount={order.amount} {order.currency}, status={order.status}] successfully")
                else:
                    logger.error(f"Failed to send order {order.id}")

                # Sleep for specified delay
                time.sleep(delay_ms / 1000.0)

            logger.info(
                f"Finished station-specific order generation for {station_id}")

        except KeyboardInterrupt:
            logger.info(
                "Station-specific order generation interrupted by user")
        except Exception as e:
            logger.error(
                f"Error during station-specific order generation: {e}")
            raise
        finally:
            self.kafka_producer.flush()

    def run_bulk_order_generation(self, batch_size: int = 10, batches: int = 10, delay_ms: int = 2000):
        """
        Generate orders in bulk batches

        Args:
            batch_size: Number of orders per batch
            batches: Number of batches to generate
            delay_ms: Delay between batches in milliseconds
        """
        if not self.station_ids:
            self.load_station_ids()

        if not self.station_ids:
            logger.error("No station IDs available for bulk order generation")
            return

        topic = Config.ORDER_KAFKA_TOPIC
        logger.info(
            f"Starting bulk order generation: batch_size={batch_size}, batches={batches}, topic={topic}")

        try:
            for batch in range(batches):
                logger.info(f"Generating batch {batch + 1}/{batches}")

                # Generate batch of orders
                for i in range(batch_size):
                    order = self.generate_random_order()

                    # Send to Kafka
                    success = self.kafka_producer.send_order(
                        order.to_json(), topic)

                    if success:
                        logger.debug(
                            f"Sent order[id={order.id}, station_id={order.station_id}] in batch {batch + 1}")
                    else:
                        logger.error(
                            f"Failed to send order {order.id} in batch {batch + 1}")

                logger.info(f"Completed batch {batch + 1}/{batches}")

                # Sleep between batches
                if batch < batches - 1:  # Don't sleep after last batch
                    time.sleep(delay_ms / 1000.0)

            logger.info("Finished bulk order generation")

        except KeyboardInterrupt:
            logger.info("Bulk order generation interrupted by user")
        except Exception as e:
            logger.error(f"Error during bulk order generation: {e}")
            raise
        finally:
            self.kafka_producer.flush()
