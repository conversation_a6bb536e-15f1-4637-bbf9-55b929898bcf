(self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[]).push([[211],{271:(e,t,n)=>{"use strict";n.d(t,{VV:()=>xt,jc:()=>yt});var r=n(5959),a=n.n(r),i=n(7781),s=n(118),o=n(7197),l=n(1829),c=n(8531),u=n(6089),d=n(2007),m=n(1051),p=n(940);const f=({exploration:e})=>{const{origin:t}=(0,p.A)(),[n,i]=(0,r.useState)("Copy url");return a().createElement(d.<PERSON>,{variant:"canvas",icon:"share-alt",tooltip:n,onClick:()=>{navigator.clipboard&&(navigator.clipboard.writeText(t+(0,m.__)(e)),i("Copied!"),setTimeout(()=>{i("Copy url")},2e3))}})};var v=n(2645),g=n(775),h=n(3049),b=n(1269);function y(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function w(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){y(e,t,n[t])})}return e}function S(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const O=["span.http.method","span.http.request.method","span.http.route","span.http.path","span.http.status_code","span.http.response.status_code"],x=["Recommended","Resource","Span","Other"];function E({options:e,value:t,onChange:n}){var i;const s=(0,d.useStyles2)(j),o=(0,r.useMemo)(()=>Object.values(e.reduce((e,t)=>{if(t.label){const s=t.label.slice(t.label.indexOf(".")+1);if(O.includes(t.label)){var n;const r=null!==(n=e.recommended)&&void 0!==n?n:{label:"Recommended",options:[]};r.options.push(S(w({},t),{label:s})),e.recommended=r}else if(t.label.startsWith("resource.")){var r;const n=null!==(r=e.resource)&&void 0!==r?r:{label:"Resource",options:[]};n.options.push(S(w({},t),{label:s})),e.resource=n}else if(t.label.startsWith("span.")){var a;const n=null!==(a=e.span)&&void 0!==a?a:{label:"Span",options:[]};n.options.push(S(w({},t),{label:s})),e.span=n}else{var i;const n=null!==(i=e.other)&&void 0!==i?i:{label:"Other",options:[]};n.options.push(t),e.other=n}}return e},{})).sort((e,t)=>x.indexOf(e.label)-x.indexOf(t.label)),[e]);var l;return a().createElement("div",{className:s.container},a().createElement(d.Field,{label:"Add extra columns"},a().createElement(d.Select,{value:""!==(null==t?void 0:t.toString())&&null!==(l=null==t||null===(i=t.toString())||void 0===i?void 0:i.split(","))&&void 0!==l?l:"",placeholder:"Select an attribute",options:o,onChange:e=>n(e.map(e=>e.value).join(",")),isMulti:!0,isClearable:!0,virtualized:!0,prefix:a().createElement(d.Icon,{name:"columns"})})))}const j=()=>({container:(0,u.css)({display:"flex",minWidth:"300px","& > div":{width:"100%"}})});var k=n(6338);function C(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function P(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){C(e,t,n[t])})}return e}function _(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class N extends s.Bs{setupTransformations(){return[()=>e=>e.pipe((0,b.map)(e=>e.map(e=>{var t;const n=e.fields,r=n.find(e=>"traceName"===e.name),i={type:d.TableCellDisplayMode.Custom,cellComponent:e=>{const t=e.frame,n=null==t?void 0:t.fields.find(e=>"traceIdHidden"===e.name),r=null==t?void 0:t.fields.find(e=>"spanID"===e.name),i=null==n?void 0:n.values[e.rowIndex],s=null==r?void 0:r.values[e.rowIndex];if(!i)return e.value;const o=e.value?e.value:"<name not yet available>";return a().createElement("div",{className:"cell-link-wrapper"},a().createElement("div",{className:"cell-link",title:o,onClick:()=>{this.publishEvent(new l.vR({traceId:i,spanId:s}),!0)}},o),a().createElement(d.Link,{href:this.getLinkToExplore(i,s),target:"_blank",title:"Open in new tab"},a().createElement(d.Icon,{name:"external-link-alt",size:"sm"})))}};return(null==r||null===(t=r.config)||void 0===t?void 0:t.custom)&&(r.config.custom.cellOptions=i),_(P({},e),{fields:n})})))]}updatePanel(e){var t,n;if((null==e?void 0:e.state)!==i.LoadingState.Loading&&(null==e?void 0:e.state)!==i.LoadingState.NotStarted&&(null==e?void 0:e.state)&&((null==e?void 0:e.state)!==i.LoadingState.Streaming||(null===(n=e.series)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.length))){if((null==e?void 0:e.state)===i.LoadingState.Done||(null==e?void 0:e.state)===i.LoadingState.Streaming)if(0===e.series.length||0===e.series[0].length){if("empty"===this.state.dataState&&this.state.panel)return;this.setState({dataState:"empty",panel:new s.G1({children:[new s.vA({body:new g.v({message:l.PL,remedyMessage:l.a5,padding:"32px"})})]})})}else"done"!==this.state.dataState&&this.setState({dataState:"done",panel:new s.G1({direction:"row",children:[new s.vA({body:s.d0.table().setHoverHeader(!0).setOverrides(e=>e.matchFieldsWithName("spanID").overrideCustomFieldConfig("hidden",!0).matchFieldsWithName("traceService").overrideCustomFieldConfig("width",350).matchFieldsWithName("traceName").overrideCustomFieldConfig("width",350)).build()})]})})}else{if("loading"===this.state.dataState)return;this.setState({dataState:"loading",panel:new s.G1({direction:"row",children:[new v.G({component:T})]})})}}constructor(e){super(P({dataState:"empty"},e)),C(this,"getLinkToExplore",(e,t)=>{const n=(0,m.zY)(this),r=(0,m.U4)(n),a=s.jh.getTimeRange(this).state.value,o=JSON.stringify({"explore-traces":{range:(0,i.toURLRange)(a.raw),queries:[{refId:"traceId",queryType:"traceql",query:e,datasource:r}],panelsState:{trace:{spanId:t}},datasource:r}});var l;const u=null!==(l=c.config.appSubUrl)&&void 0!==l?l:"";return i.urlUtil.renderUrl(`${u}/explore`,{panes:o,schemaVersion:1})}),C(this,"onChange",e=>{const t=(0,m.gi)(this);t.getValue()!==e&&(t.changeValueTo(e),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.span_list_columns_changed,{columns:e}))}),this.addActivationHandler(()=>{this.setState({$data:new s.Es({transformations:this.setupTransformations()})});const e=s.jh.getData(this);this.updatePanel(e.state.data),this._subs.add(e.subscribeToState(e=>{this.updatePanel(e.data)}))})}}C(N,"Component",({model:e})=>{const{panel:t}=e.useState(),n=D((0,d.useTheme2)()),r=(0,m.gi)(e),{attributes:s}=(0,m.YX)(e).useState();var o;if(t)return a().createElement("div",{className:n.container},a().createElement("div",{className:n.header},a().createElement("div",{className:n.description},"View a list of spans for the current set of filters."),a().createElement(E,{options:null!==(o=null==s?void 0:s.map(e=>(0,i.toOption)(e)))&&void 0!==o?o:[],value:r.getValue(),onChange:e.onChange})),a().createElement(t.Component,{model:t}))});const D=e=>({container:(0,u.css)({display:"contents",'[role="cell"] > div':{display:"flex",width:"100%"},".cell-link-wrapper":{display:"flex",gap:"4px",justifyContent:"space-between",alignItems:"center",width:"100%",a:{padding:4,fontSize:0,":hover":{background:e.colors.background.secondary}}},".cell-link":{color:e.colors.text.link,cursor:"pointer",maxWidth:"300px",overflow:"hidden",textOverflow:"ellipsis",":hover":{textDecoration:"underline"}}}),description:(0,u.css)({fontSize:e.typography.h6.fontSize,padding:`${e.spacing(1)} 0 ${e.spacing(2)} 0`}),header:(0,u.css)({display:"flex",justifyContent:"space-between",alignItems:"flex-start",gap:"10px"})}),T=()=>{const e=(0,d.useStyles2)(I);return a().createElement("div",{className:e.container},a().createElement("div",{className:e.title},a().createElement(h.A,{count:1,width:80})),[...Array(3)].map((t,n)=>a().createElement("div",{className:e.row,key:n},[...Array(6)].map((t,n)=>a().createElement("span",{className:e.rowItem,key:n},a().createElement(h.A,{count:1}))))))};function I(e){return{container:(0,u.css)({height:"100%",width:"100%",position:"absolute",backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,padding:"5px"}),title:(0,u.css)({marginBottom:"20px"}),row:(0,u.css)({marginBottom:"5px",display:"flex",justifyContent:"space-around"}),rowItem:(0,u.css)({width:"14%"})}}function A(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class $ extends s.Bs{_onActivate(){var e;this._subs.add(null===(e=(0,m.YX)(this).state.$data)||void 0===e?void 0:e.subscribeToState(()=>{this.updateBody()})),this._subs.add((0,m.YX)(this).subscribeToState((e,t)=>{var n,r;(null===(n=e.$data)||void 0===n?void 0:n.state.key)!==(null===(r=t.$data)||void 0===r?void 0:r.state.key)&&this.updateBody()})),this._subs.add((0,m.H_)(this).subscribeToState((e,t)=>{e.value!==t.value&&this.updateBody()})),this.updateBody()}updateBody(){this.setState({body:new N({})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){A(e,t,n[t])})}return e}({},e)),this.addActivationHandler(this._onActivate.bind(this))}}function L(e){if(e.attributes)for(const n of e.attributes){var t;if("nestedSetLeft"===n.key)return parseInt(n.value.intValue||(null===(t=n.value.Value)||void 0===t?void 0:t.int_value)||"0",10)}throw new Error("nestedSetLeft not found!")}function V(e){if(e.attributes)for(const n of e.attributes){var t;if("nestedSetRight"===n.key)return parseInt(n.value.intValue||(null===(t=n.value.Value)||void 0===t?void 0:t.int_value)||"0",10)}throw new Error("nestedSetRight not found!")}function B(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}A($,"Component",({model:e})=>{const{body:t}=e.useState();return t&&a().createElement(t.Component,{model:t})});class F{addSpan(e){this.left=Math.min(L(e),this.left),this.right=Math.max(V(e),this.right),this.spans.push(e)}addChild(e){e.parent=this,this.children.push(e)}isChild(e){return L(e)>this.left&&V(e)<this.right}findMatchingChild(e){const t=M(e);for(const e of this.children)if(e.name===t)return e;return null}constructor({name:e,serviceName:t,operationName:n,spans:r,left:a,right:i,traceID:s}){B(this,"name",void 0),B(this,"serviceName",void 0),B(this,"operationName",void 0),B(this,"spans",void 0),B(this,"left",void 0),B(this,"right",void 0),B(this,"children",void 0),B(this,"parent",void 0),B(this,"traceID",void 0),this.name=e,this.serviceName=t,this.operationName=n,this.spans=r,this.left=a,this.right=i,this.children=[],this.parent=null,this.traceID=s}}function z(e){var t,n,r;const a=null===(t=e.attributes)||void 0===t?void 0:t.find(e=>"service.name"===e.key);var i,s,o,l;return new F({left:L(e),right:V(e),name:M(e),serviceName:null!==(s=null!==(i=null==a?void 0:a.value.stringValue)&&void 0!==i?i:null==a||null===(r=a.value)||void 0===r||null===(n=r.Value)||void 0===n?void 0:n.string_value)&&void 0!==s?s:"",operationName:null!==(o=e.name)&&void 0!==o?o:"",spans:[e],traceID:null!==(l=e.traceId)&&void 0!==l?l:""})}function M(e){let t="";for(const n of e.attributes||[])"service.name"===n.key&&n.value.stringValue&&(t=n.value.stringValue);return`${t}:${e.name}`}function R(e){e.left=Number.MAX_SAFE_INTEGER,e.right=Number.MIN_SAFE_INTEGER;for(const t of e.children)R(t)}var H=n(3733);function q(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function G(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){q(e,t,n[t])})}return e}const W="0000000000000000";class U extends s.Bs{_onActivate(){var e;this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState(e=>{var t,n,r,a;if((null===(t=e.data)||void 0===t?void 0:t.state)!==i.LoadingState.Loading&&(null===(n=e.data)||void 0===n?void 0:n.state)!==i.LoadingState.Streaming){if((null===(r=e.data)||void 0===r?void 0:r.state)===i.LoadingState.Done&&(null===(a=e.data)||void 0===a?void 0:a.series.length)){var o;const t=null===(o=e.data)||void 0===o?void 0:o.series[0].fields[0].values[0];if(t){const e=function(e){const t=new F({name:"root",serviceName:"",operationName:"",left:Number.MIN_SAFE_INTEGER,right:Number.MAX_SAFE_INTEGER,spans:[],traceID:""});if(e&&e.length>0)for(const r of e){var n;if(1!==(null===(n=r.spanSets)||void 0===n?void 0:n.length))throw new Error("there should be only 1 spanset!");const e=parseInt(r.startTimeUnixNano||"0",10),a=r.spanSets[0];a.spans.sort((e,t)=>L(e)-L(t));let i=t;R(t);for(const t of a.spans){for(t.traceId=r.traceID,t.startTimeUnixNano=""+(parseInt(t.startTimeUnixNano,10)-e);null!==i.parent&&!i.isChild(t);)i=i.parent;const n=i.findMatchingChild(t);if(n){n.addSpan(t),i=n;continue}const a=z(t);a.traceID=r.traceID,i.addChild(a),i=a}}return t}(JSON.parse(t));e.children.sort((e,t)=>X(t)-X(e)),this.setState({loading:!1,tree:e,panel:new s.G1({height:"100%",wrap:"wrap",children:this.getPanels(e)})})}}}else this.setState({loading:!0})}))}getPanels(e){return e.children.map(e=>new s.vA({height:150,width:"100%",minHeight:"400px",body:this.getPanel(e)}))}getPanel(e){const t=s.jh.getTimeRange(this),n=t.state.value.from,r=t.state.value.to,a=(0,m.w$)(this);return s.d0.traces().setTitle(`Structure for ${e.serviceName} [${X(e)} spans used]`).setOption("createFocusSpanLink",(e,t)=>({title:"Open trace",href:"#",onClick:()=>a(e,t),origin:{},target:"_self"})).setData(new s.Zv({data:{state:i.LoadingState.Done,timeRange:{from:n,to:r,raw:{from:n,to:r}},series:[G({},this.buildData(e))]}})).build()}buildData(e){const t=this.getTrace(e,W),n=t[0].serviceName+":"+t[0].operationName;return(0,i.createDataFrame)({name:`Trace ${n}`,refId:`trace_${n}`,fields:[{name:"references",type:i.FieldType.other,values:t.map(e=>e.references)},{name:"traceID",type:i.FieldType.string,values:t.map(e=>e.traceID)},{name:"spanID",type:i.FieldType.string,values:t.map(e=>e.spanID)},{name:"parentSpanID",type:i.FieldType.string,values:t.map(e=>e.parentSpanId)},{name:"serviceName",type:i.FieldType.string,values:t.map(e=>e.serviceName)},{name:"operationName",type:i.FieldType.string,values:t.map(e=>e.operationName)},{name:"duration",type:i.FieldType.number,values:t.map(e=>e.duration)},{name:"startTime",type:i.FieldType.number,values:t.map(e=>e.startTime)},{name:"statusCode",type:i.FieldType.number,values:t.map(e=>e.statusCode)}]})}getTrace(e,t){const n=e.spans.reduce((e,t)=>{var n,r;return"error"===(null===(r=t.attributes)||void 0===r||null===(n=r.find(e=>"status"===e.key))||void 0===n?void 0:n.value.stringValue)?e+1:e},0);let r=1e-4;t!==W&&(r=e.spans.reduce((e,t)=>e+parseInt(t.startTimeUnixNano,10),0)/e.spans.length/1e6);const a=[{references:e.spans.slice(-5).map(e=>({refType:"EXTERNAL",traceID:e.traceId,spanID:e.spanID})),traceID:e.traceID,spanID:e.spans[0].spanID,parentSpanId:t,serviceName:e.serviceName,operationName:e.operationName,statusCode:n>0?2:0,duration:e.spans.reduce((e,t)=>e+parseInt(t.durationNanos,10),0)/e.spans.length/1e6,startTime:r}];for(const t of e.children)a.push(...this.getTrace(t,e.spans[0].spanID));return a}constructor(e){super(G({$data:new s.Es({$data:new s.dt({datasource:l.Vl,queries:[Y(e.metric)]}),transformations:l.s9}),loading:!0},e)),this.addActivationHandler(this._onActivate.bind(this))}}function Y(e){let t,n="";switch(e){case"errors":t="status = error",n="status = error";break;case"duration":t=`duration > ${l.Ld}`,n=`duration > ${l.xT}`;break;default:t="kind = server"}return{refId:"A",query:`{${l.ui} ${n.length?`&& ${n}`:""}} &>> { ${t} } | select(status, resource.service.name, name, nestedSetParent, nestedSetLeft, nestedSetRight)`,queryType:"traceql",tableType:"raw",limit:200,spss:20,filters:[]}}q(U,"Component",({model:e})=>{var t,n;const{tree:r,loading:s,panel:o,$data:c}=e.useState(),u=K((0,d.useTheme2)()),p=(0,d.useTheme2)(),f=(0,m.zY)(e),{value:v}=f.getMetricVariable().useState(),g=v;let b,y=s||!(null==r?void 0:r.children.length);(null==c||null===(t=c.state.data)||void 0===t?void 0:t.state)===i.LoadingState.Done&&(y=!1);let w="";switch(g){case"rate":b=a().createElement(a().Fragment,null,a().createElement("div",null,"Analyse the service structure of the traces that match the current filters."),a().createElement("div",null,"Each panel represents an aggregate view compiled using spans from multiple traces.")),w="server";break;case"errors":b=a().createElement(a().Fragment,null,a().createElement("div",null,"Analyse the errors structure of the traces that match the current filters."),a().createElement("div",null,"Each panel represents an aggregate view compiled using spans from multiple traces.")),w="error";break;case"duration":b=a().createElement(a().Fragment,null,a().createElement("div",null,"Analyse the structure of slow spans from the traces that match the current filters."),a().createElement("div",null,"Each panel represents an aggregate view compiled using spans from multiple traces.")),w="slow"}const S=ot(g),O=a().createElement(a().Fragment,null,a().createElement(d.Text,{textAlignment:"center",variant:"h3"},l.PL),a().createElement(d.Text,{textAlignment:"center",variant:"body"},a().createElement("div",{className:u.longText},"The structure tab shows ",w," spans beneath what you are currently investigating. Currently, there are no descendant ",w," spans beneath the spans you are investigating.")),a().createElement(d.Stack,{gap:.5,alignItems:"center"},a().createElement(d.Icon,{name:"info-circle"}),a().createElement(d.Text,{textAlignment:"center",variant:"body"},"The structure tab works best with full traces.")),a().createElement("div",{className:u.actionContainer},"Read more about",a().createElement("div",{className:u.action},a().createElement(d.LinkButton,{icon:"external-link-alt",fill:"solid",size:"sm",target:"_blank",href:"https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/concepts/#trace-structure"},`${S.toLowerCase()}`))));return a().createElement(d.Stack,{direction:"column",gap:1},a().createElement("div",{className:u.description},b),y&&a().createElement(d.Stack,{direction:"column",gap:2},a().createElement(h.A,{count:4,height:200,baseColor:p.colors.background.secondary,highlightColor:p.colors.background.primary})),!y&&r&&r.children.length>0&&a().createElement("div",{className:u.traceViewList},o&&a().createElement(o.Component,{model:o})),(null==c||null===(n=c.state.data)||void 0===n?void 0:n.state)===i.LoadingState.Done&&!(null==r?void 0:r.children.length)&&a().createElement(H.p,{message:O,padding:"32px"}))});const K=e=>({description:(0,u.css)({fontSize:e.typography.h6.fontSize,padding:`${e.spacing(1)} 0`}),traceViewList:(0,u.css)({display:"flex",flexDirection:"column",gap:e.spacing.x1,'div[class*="panel-content"] > div':{overflow:"auto",'> :not([class*="TraceTimelineViewer"])':{display:"none"}},'div[data-testid="span-detail-component"] > :nth-child(4) > :nth-child(1)':{display:"none"},".span-detail-row":{display:"none"},'div[data-testid="TimelineRowCell"]':{'button[role="switch"]':{cursor:"text"}},'div[data-testid="span-view"]':{cursor:"text !important"}}),longText:(0,u.css)({maxWidth:"800px",margin:"0 auto"}),action:(0,u.css)({marginLeft:e.spacing(1)}),actionContainer:(0,u.css)({display:"flex",justifyContent:"space-between",alignItems:"center"})});function X(e){let t=e.spans.length;for(const n of e.children)t+=X(n);return t}var Q=n(5540);function Z({options:e,radioAttributes:t,value:n,onChange:i,showAll:s=!1,model:o}){var c,u;const p=(0,d.useStyles2)(J),f=(0,d.useTheme2)(),{fontSize:v}=f.typography,[g,h]=(0,r.useState)(""),[b,y]=(0,r.useState)(!0),[w,S]=(0,r.useState)(0),O=(0,r.useRef)(null),{initialGroupBy:x}=(0,m.zY)(o).useState(),{filters:E}=(0,m.gG)(o).useState(),{value:j}=(0,m.H_)(o).useState(),k=j;(0,Q.w)({ref:O,onResize:()=>{const e=O.current;e&&S(e.clientWidth)}});const C=(0,r.useMemo)(()=>{let n=0;return t.filter(t=>{let n=!!e.find(e=>e.value===t);return!E.find(e=>e.key===t&&("="===e.operator||"!="===e.operator))&&(E.find(e=>"nestedSetParent"===e.key)&&(n=n&&"rootName"!==t&&"rootServiceName"!==t),"rate"!==k&&"errors"!==k||(n=n&&"status"!==t),n)}).map(e=>({label:e.replace(l.zd,"").replace(l.$d,""),text:e,value:e})).filter(e=>{const t=e.label||e.text||"",r=(0,d.measureText)(t,v).width;return n+r+40+180<w&&(n+=r+40,!0)})},[t,e,E,k,v,w]),P=(0,r.useMemo)(()=>{const t=e.filter(e=>!C.find(t=>{var n;return t.value===(null===(n=e.value)||void 0===n?void 0:n.toString())}));return ee(t,g)},[g,e,C]),_=e=>e.filter(e=>{var t;return!l.uK.includes(null===(t=e.value)||void 0===t?void 0:t.toString())}).map(e=>{var t;return{label:null===(t=e.label)||void 0===t?void 0:t.replace(l.zd,"").replace(l.$d,""),value:e.value}});var N;const D=null!==(N=null!=x?x:null===(c=C[0])||void 0===c?void 0:c.value)&&void 0!==N?N:null===(u=P[0])||void 0===u?void 0:u.value;(0,r.useEffect)(()=>{D&&!s&&b&&(i(D,!0),y(!1))},[n,D,s,i,b]),(0,r.useEffect)(()=>{t.length>0&&y(!0)},[t]),(0,r.useEffect)(()=>{E.some(e=>e.key===n)&&y(!0)},[E,n]);const T=s?[{label:l.y2,value:l.y2}]:[],I=s?l.y2:"";return a().createElement(d.Field,{label:"Group by"},a().createElement("div",{ref:O,className:p.container},C.length>0&&a().createElement(d.RadioButtonGroup,{options:[...T,...C],value:n,onChange:i}),a().createElement(d.Select,{value:n&&_(P).some(e=>e.value===n)?n:null,placeholder:"Other attributes",options:_(P),onChange:e=>{var t;const n=null!==(t=null==e?void 0:e.value)&&void 0!==t?t:I;i(n)},className:p.select,isClearable:!0,onInputChange:(e,{action:t})=>{"input-change"===t&&h(e)},onCloseMenu:()=>h(""),virtualized:!0})))}function J(e){return{select:(0,u.css)({maxWidth:e.spacing(22)}),container:(0,u.css)({display:"flex",gap:e.spacing(1)})}}const ee=(e,t)=>{if(0===e.length)return[];if(0===t.length)return e.slice(0,l.nr);const n=t.toLowerCase();return e.filter(e=>!!(e.value&&e.value.length>0)&&e.value.toLowerCase().includes(n)).slice(0,l.nr)};function te(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ne extends s.Bs{Selector({model:e}){const{active:t,options:n}=e.useState();return a().createElement(d.Field,{label:"View"},a().createElement(d.RadioButtonGroup,{options:n,value:t,onChange:e.onLayoutChange}))}constructor(...e){super(...e),te(this,"onLayoutChange",e=>{this.setState({active:e}),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.layout_type_changed,{layout:e})})}}te(ne,"Component",({model:e})=>{const{layouts:t,options:n,active:r}=e.useState(),i=n.findIndex(e=>e.value===r);if(-1===i)return null;const s=t[i];return a().createElement(s.Component,{model:s})});var re=n(806),ae=n(8327),ie=n(9840),se=n(6997);const oe=()=>s.d0.timeseries().setOption("legend",{showLegend:!1}).setOption("tooltip",{mode:d.TooltipDisplayMode.Multi}).setCustomFieldConfig("fillOpacity",15);var le=n(6374),ce=n(3241);function ue(){return e=>{const t=new Map,n=e.subscribeToEvent(l.sv,n=>{const r=n.payload.series;null==r||r.forEach(e=>{e.fields.slice(1).forEach(n=>{t.set(e.refId,Math.max(...n.values.filter(e=>e)))})}),function(e,t){const n=s.jh.findAllObjects(e,e=>e instanceof s.Eb);for(const e of n)e.clearFieldConfigCache(),e.setState({fieldConfig:(0,ce.merge)((0,ce.cloneDeep)(e.state.fieldConfig),{defaults:{max:t}})})}(e,Math.max(...t.values()))});return()=>{n.unsubscribe()}}}var de=n(3247),me=n(4524);function pe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function fe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){pe(e,t,n[t])})}return e}function ve(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function ge(e,t,n){const r=(0,m.zY)(e).getMetricVariable().getValue(),a=(0,ie.l)({metric:r,groupByKey:t.getValueText()}),o={};return new ne({$behaviors:[ue()],$data:new s.Es({$data:new le.$({maxDataPoints:64,datasource:l.Vl,queries:[a]}),transformations:[...(0,de.G)((0,m.w$)(e)),()=>e=>e.pipe((0,b.map)(e=>(e.forEach(e=>(0,i.reduceField)({field:e.fields[1],reducers:[i.ReducerID.max]})),e.sort((e,t)=>{var n,r,a,i;return((null===(r=t.fields[1].state)||void 0===r||null===(n=r.calcs)||void 0===n?void 0:n.max)||0)-((null===(i=e.fields[1].state)||void 0===i||null===(a=i.calcs)||void 0===a?void 0:a.max)||0)}))))]}),options:[{value:"single",label:"Single"},{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new s.G1({direction:"column",children:[new s.vA({minHeight:300,body:("duration"===r?oe().setUnit("s"):oe()).build()})]}),new ae.hE({body:new s.gF({templateColumns:l.MV,autoRows:"200px",isLazy:!0,children:[]}),groupBy:!0,getLayoutChild:he(o,m.ee,t,r,n)}),new ae.hE({body:new s.gF({templateColumns:"1fr",autoRows:"200px",isLazy:!0,children:[]}),groupBy:!0,getLayoutChild:he(o,m.ee,t,r,n)})]})}function he(e,t,n,r,a){return(i,o)=>{var l;const c=o.name?e[o.name]:void 0,u=new s.Zv({data:ve(fe({},i),{annotations:null===(l=i.annotations)||void 0===l?void 0:l.filter(e=>e.refId===o.refId),series:[ve(fe({},o),{fields:o.fields.sort((e,t)=>{var n,r,a;return(null===(a=e.labels)||void 0===a||null===(r=a.status)||void 0===r?void 0:r.localeCompare((null===(n=t.labels)||void 0===n?void 0:n.status)||""))||0})})]})});var d;if(c)return null===(d=c.state.body)||void 0===d||d.setState({$data:u}),c;const p=s.jh.interpolate(n,(0,ie.n)({metric:r,extraFilters:`${n.getValueText()}=${(0,m.xo)((0,m.ee)(o))}`})),f=("duration"===r?oe().setUnit("s"):(0,se.z)(r)).setTitle(t(o,n.getValueText())).setMenu(new me.GD({query:p,labelValue:(0,m.ee)(o)})).setData(u),v=a(o);v&&f.setHeaderActions(v);const g=new s.xK({body:f.build()});return o.name&&(e[o.name]=g),g}}function be({description:e,tags:t}){const n=function(e){return{infoFlex:(0,u.css)({display:"flex",gap:e.spacing(2),alignItems:"center",padding:`${e.spacing(1)} 0 ${e.spacing(2)} 0`}),tagsFlex:(0,u.css)({display:"flex",gap:e.spacing(1),alignItems:"center"}),tag:(0,u.css)({display:"inline-block",width:e.spacing(2),height:e.spacing(.5),borderRadius:e.spacing(.5)})}}((0,d.useTheme2)());return a().createElement("div",{className:n.infoFlex},a().createElement("div",{className:n.tagsFlex},e),t.length>0&&t.map(e=>a().createElement("div",{className:n.tagsFlex,key:e.label},a().createElement("div",{className:n.tag,style:{backgroundColor:e.color}}),a().createElement("div",null,e.label))))}function ye(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class we extends s.Bs{_onActivate(){const e=(0,m.pl)(this);e.subscribeToState(()=>{this.setBody(e)}),(0,m.YX)(this).subscribeToState(()=>{this.setBody(e)}),this.setBody(e)}onReferencedVariableValueChanged(){const e=(0,m.pl)(this);e.changeValueTo(l.u0[0]),this.setBody(e)}onAddToFiltersClick(e){(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.breakdown_add_to_filters_clicked,e)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ye(e,t,n[t])})}return e}({},e)),ye(this,"_variableDependency",new s.Sh(this,{variableNames:[l.Ao,l.PU],onReferencedVariableValueChanged:this.onReferencedVariableValueChanged.bind(this)})),ye(this,"setBody",e=>{this.setState({body:ge(this,e,t=>[new re.Ms({frame:t,labelKey:e.getValueText(),onClick:this.onAddToFiltersClick})])})}),ye(this,"onChange",(e,t)=>{const n=(0,m.pl)(this);n.getValueText()!==e&&(n.changeValueTo(e,void 0,!t),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.breakdown_group_by_changed,{groupBy:e}))}),this.addActivationHandler(this._onActivate.bind(this))}}function Se(e){return{container:(0,u.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,u.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,u.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2)}),controlsRight:(0,u.css)({flexGrow:0,display:"flex",justifyContent:"flex-end"}),scope:(0,u.css)({marginRight:e.spacing(2)}),groupBy:(0,u.css)({width:"100%"}),controlsLeft:(0,u.css)({display:"flex",justifyContent:"flex-left",justifyItems:"left",width:"100%",flexDirection:"row"})}}function Oe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}ye(we,"Component",({model:e})=>{const{value:t}=(0,m.pl)(e).useState(),n=t,i=n.includes(l.zd)||l.jx.includes(n)?l.BS:l.bD,[s,o]=(0,r.useState)(i),{body:c}=e.useState(),u=(0,d.useStyles2)(Se),{attributes:p}=(0,m.YX)(e).useState(),f=s===l.bD?l.$d:l.zd;let v=null==p?void 0:p.filter(e=>e.includes(f));s===l.BS&&(v=null==v?void 0:v.concat(l.jx));const g=(0,m.zY)(e),{value:h}=g.getMetricVariable().useState(),b=(e=>{switch(e){case"rate":return"Attributes are ordered by their rate of requests per second.";case"errors":return"Attributes are ordered by their rate of errors per second.";case"duration":return"Attributes are ordered by their average duration.";default:throw new Error("Metric not supported")}})(h);return(0,r.useEffect)(()=>{s!==i&&o(i)},[n]),a().createElement("div",{className:u.container},a().createElement(be,{description:b,tags:"duration"===h?[]:[{label:"Rate",color:"green"},{label:"Error",color:"red"}]}),a().createElement("div",{className:u.controls},(null==v?void 0:v.length)&&a().createElement("div",{className:u.controlsLeft},a().createElement("div",{className:u.scope},a().createElement(d.Field,{label:"Scope"},a().createElement(d.RadioButtonGroup,{options:(0,m._g)([l.bD,l.BS]),value:s,onChange:o}))),a().createElement("div",{className:u.groupBy},a().createElement(Z,{options:(0,m._g)(v),radioAttributes:s===l.bD?l.u0:l.jx,value:n,onChange:e.onChange,model:e}))),c instanceof ne&&a().createElement("div",{className:u.controlsRight},a().createElement(c.Selector,{model:c}))),a().createElement("div",{className:u.content},c&&a().createElement(c.Component,{model:c})))});class xe extends s.Bs{_onActivate(){this.updateBody()}updateBody(){this.setState({body:new we({})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Oe(e,t,n[t])})}return e}({},e)),Oe(this,"_variableDependency",new s.Sh(this,{variableNames:[l.PU]})),this.addActivationHandler(this._onActivate.bind(this))}}Oe(xe,"Component",({model:e})=>{const{body:t}=e.useState();return t&&a().createElement(t.Component,{model:t})});var Ee=n(1625);var je=n(7975);function ke(e){if(!e.length)return[];e.sort((e,t)=>e-t);const t=(e[e.length-1]-e[0])/1e3,n=1e3*(0,je.KS)(t,50),r=new Map;for(const t of e){const e=Math.floor(t/n)*n;r.set(e,(r.get(e)||0)+1)}return Array.from(r.entries()).map(([e,t])=>({time:e,count:t})).sort((e,t)=>e.time-t.time)}function Ce(e){return e?e.replace(/\s+/g," ").trim():""}function Pe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _e(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Pe(e,t,n[t])})}return e}function Ne(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class De extends s.Bs{updatePanel(e){var t,n,r,a;if((null==e?void 0:e.state)===i.LoadingState.Loading||(null==e?void 0:e.state)===i.LoadingState.NotStarted||!(null==e?void 0:e.state)||(null==e?void 0:e.state)===i.LoadingState.Streaming&&!(null===(n=e.series)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.length))this.setState({dataState:"loading",panel:new s.G1({direction:"row",children:[new v.G({component:Ie})]})});else if((null==e?void 0:e.state)!==i.LoadingState.Done&&(null==e?void 0:e.state)!==i.LoadingState.Streaming||0!==e.series.length&&(null===(a=e.series)||void 0===a||null===(r=a[0])||void 0===r?void 0:r.length)){if(((null==e?void 0:e.state)===i.LoadingState.Done||(null==e?void 0:e.state)===i.LoadingState.Streaming)&&e.series.length>0){const t=this.calculateExceptionsCount(e);this.setState({dataState:"done",exceptionsCount:t,panel:new s.G1({children:[new s.vA({body:s.d0.table().setOption("cellHeight",Ee.qM.Lg).setHoverHeader(!0).setOverrides(e=>e.matchFieldsWithName("Service").overrideCustomFieldConfig("width",200).matchFieldsWithName("Occurrences").overrideCustomFieldConfig("width",120).matchFieldsWithName("Time Series").overrideCustomFieldConfig("width",220).matchFieldsWithName("Last Seen").overrideCustomFieldConfig("width",120)).build()})]})})}}else this.setState({dataState:"empty",exceptionsCount:0,panel:new s.G1({children:[new s.vA({body:new g.v({message:l.PL,remedyMessage:l.a5,padding:"32px"})})]})})}createTransformation(){return()=>e=>e.pipe((0,b.map)(e=>e.map(e=>{const t=e.fields.find(e=>"exception.message"===e.name),n=e.fields.find(e=>"exception.type"===e.name),r=e.fields.find(e=>"service.name"===e.name),a=e.fields.find(e=>"time"===e.name);let s=[],o=[],l=[],c=[],u=[],m=[];if(!(!t||!t.values.length)){const e=function(e,t,n,r){const a=new Map,i=new Map,s=new Map,o=new Map,l=new Map,c=new Map;for(let l=0;l<e.values.length;l++){const u=e.values[l],d=null==t?void 0:t.values[l],m=null==n?void 0:n.values[l],p=null==r?void 0:r.values[l];if(u){const e=Ce(u);if(a.set(e,(a.get(e)||0)+1),!i.has(e)&&d&&i.set(e,d),!o.has(e)&&p&&o.set(e,p),m){const t="string"==typeof m?parseFloat(m):m;c.has(e)||c.set(e,[]),c.get(e).push(t),t>(s.get(e)||0)&&s.set(e,t)}}}for(const[e,t]of c.entries()){const n=ke(t);l.set(e,n)}const u=Array.from(a.entries()).sort((e,t)=>t[1]-e[1]);return{messages:u.map(([e])=>e),types:u.map(([e])=>i.get(e)||""),occurrences:u.map(([,e])=>e),services:u.map(([e])=>o.get(e)||""),timeSeries:u.map(([e])=>l.get(e)||[]),lastSeenTimes:u.map(([e])=>{const t=s.get(e);if(!t)return"";const n=Date.now()-t;return n<6e4?"Just now":n<36e5?`${Math.floor(n/6e4)}m ago`:n<864e5?`${Math.floor(n/36e5)}h ago`:`${Math.floor(n/864e5)}d ago`})}}(t,n,a,r);s=e.messages,o=e.types,l=e.occurrences,c=e.lastSeenTimes,u=e.services,m=e.timeSeries}const p={type:d.TableCellDisplayMode.Custom,cellComponent:e=>{const t=e.value;return this.renderSparklineCell(t)}};return Ne(_e({},e),{length:s.length,fields:[{name:"Message",type:i.FieldType.string,values:s,config:{links:s.length>0?[this.createDataLink()]:[]}},{name:"Type",type:i.FieldType.string,values:o,config:{}},{name:"Trace Service",type:i.FieldType.string,values:u,config:{}},{name:"Occurrences",type:i.FieldType.number,values:l,config:{}},{name:"Frequency",type:i.FieldType.other,values:m,config:{custom:{cellOptions:p}}},{name:"Last Seen",type:i.FieldType.string,values:c,config:{}}]})})))}createDataLink(){return{title:"View traces for this exception",url:"",onClick:e=>{var t;const n=null===(t=e.origin)||void 0===t?void 0:t.rowIndex;if(void 0!==n){var r,a,i;const t=null===(i=e.origin)||void 0===i||null===(a=i.field)||void 0===a||null===(r=a.values)||void 0===r?void 0:r[n];t&&((0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.exception_message_clicked),this.navigateToTracesWithFilter(t))}}}}escapeFilterValue(e){return e.replace(/[\n\r\t]/g," ").replace(/\s+/g," ").replace(/\\/g,"\\\\").replace(/"/g,'"').trim()}calculateExceptionsCount(e){var t;if(!(null==e||null===(t=e.series)||void 0===t?void 0:t[0]))return 0;const n=e.series[0].fields.find(e=>"Occurrences"===e.name);return(null==n?void 0:n.values)?n.values.reduce((e,t)=>e+(t||0),0):0}getExceptionsCount(){return this.state.exceptionsCount||0}constructor(e){super(_e({$data:new s.Es({$data:new s.dt({datasource:l.Vl,queries:[{refId:"A",query:`{${l.ui} && status = error} | select(resource.service.name, event.exception.message,event.exception.stacktrace,event.exception.type) with(most_recent=true)`,queryType:"traceql",tableType:"spans",limit:400,spss:10,filters:[]}]}),transformations:[]}),dataState:"empty"},e)),Pe(this,"renderSparklineCell",e=>{const t=(0,d.useStyles2)(Te),n=()=>{const n=(0,d.useTheme2)();if(!e||!e.length)return a().createElement("div",{className:t.sparklineMessage},"No data");const r=e.map(e=>e.count),s=e.map(e=>e.time),o=r.filter(e=>isFinite(e)&&!isNaN(e)),l=s.filter(e=>isFinite(e)&&!isNaN(e));if(o.length<2||l.length<2)return a().createElement("div",{className:t.sparklineMessage},"Not enough data");const c=Math.min(...o),u=Math.max(...o),m=Math.min(...l),p=Math.max(...l),f=u-c,v=p-m,g=0===f?1:f,h=0===v?1:v,b={y:{name:"count",type:i.FieldType.number,values:o,config:{},state:{range:{min:c,max:u,delta:g}}},x:{name:"time",type:i.FieldType.time,values:l,config:{},state:{range:{min:m,max:p,delta:h}}}};return a().createElement("div",{className:t.sparklineContainer},a().createElement(d.Sparkline,{width:180,height:20,sparkline:b,theme:n,config:{custom:{drawStyle:Ee.GR.Line,fillOpacity:5,fillColor:n.colors.background.secondary,lineWidth:1,showPoints:Ee.yL.Never}}}))};return a().createElement(n,null)}),Pe(this,"navigateToTracesWithFilter",e=>{const t=(0,m.gG)(this);if(!t)return;const n=(0,m.YX)(this);null==n||n.setActionView("traceList");const r=t.state.filters||[],a=this.escapeFilterValue(e),i=r.findIndex(e=>"event.exception.message"===e.key),s={key:"event.exception.message",operator:"=",value:a},o=i>=0?r.map((e,t)=>t===i?s:e):[...r,s];t.setState({filters:o})});this.state.$data.setState({transformations:[...l.s9,this.createTransformation()]}),this.addActivationHandler(()=>{const e=this.state.$data;this._subs.add(e.subscribeToState((e,t)=>{e.data!==t.data&&this.updatePanel(e.data)}))})}}Pe(De,"Component",({model:e})=>{const t=(0,d.useStyles2)(Te),n=(0,d.useTheme2)(),{panel:r,dataState:i}=e.useState();return a().createElement("div",{className:t.container},a().createElement("div",{className:t.description},"View exception details from errored traces for the current set of filters."),"loading"===i&&a().createElement("div",{className:t.loadingContainer},a().createElement(h.A,{count:10,height:40,baseColor:n.colors.background.secondary,highlightColor:n.colors.background.primary})),r&&a().createElement(r.Component,{model:r}))});const Te=e=>({container:(0,u.css)({display:"flex",flexDirection:"column",gap:e.spacing(2),height:"100%"}),description:(0,u.css)({fontSize:e.typography.h6.fontSize,padding:`${e.spacing(1)} 0`}),loadingContainer:(0,u.css)({padding:e.spacing(2)}),sparklineContainer:(0,u.css)({width:"200px",display:"flex",alignItems:"center",justifyContent:"center"}),sparklineMessage:(0,u.css)({fontSize:e.typography.bodySmall.fontSize,color:e.colors.text.secondary,padding:e.spacing(1)})}),Ie=()=>{const e=(0,d.useStyles2)(Ae),t=(0,d.useTheme2)();return a().createElement("div",{className:e.container},a().createElement(h.A,{count:10,height:40,baseColor:t.colors.background.secondary,highlightColor:t.colors.background.primary}))};function Ae(e){return{container:(0,u.css)({padding:e.spacing(2)})}}var $e=n(9938),Le=n(2468);function Ve(e){var t,n,r,a;let i="";if(!e)return"{}";e.query&&(i+=e.query);const s=[];(null===(t=e.duration)||void 0===t?void 0:t.from.length)&&s.push(`duration >= ${e.duration.from}`),(null===(n=e.duration)||void 0===n?void 0:n.to.length)&&s.push(`duration <= ${e.duration.to}`),s.length&&(i.length&&(i+=" && "),i+=s.join(" && "));const o=null===(r=e.timeRange)||void 0===r?void 0:r.from,l=null===(a=e.timeRange)||void 0===a?void 0:a.to;return`{${i}}, 10${o&&l?`, ${1e9*o}, ${1e9*l}`:""}`}function Be(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Fe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Be(e,t,n[t])})}return e}function ze(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Me(e,t,n,r){var a;const o=s.jh.getTimeRange(e),c=s.jh.getData(e),u=t.getValueText(),d=null===(a=c.state.data)||void 0===a?void 0:a.series.find(e=>e.name===u),m=[],p=null==d?void 0:d.fields.find(e=>"Value"===e.name),f=null==d?void 0:d.fields.find(e=>"Baseline"===e.name),v=null==d?void 0:d.fields.find(e=>"Selection"===e.name);if(p&&f&&v)for(let e=0;e<p.values.length;e++)p.values[e]&&(f.values[e]||v.values[e])&&m.push({name:p.values[e].replace(/"/g,""),length:1,fields:[{name:"Value",type:i.FieldType.string,values:["Baseline","Comparison"],config:{}},ze(Fe({},f),{values:[f.values[e]],labels:{[u]:p.values[e]},config:{displayName:"Baseline"}}),ze(Fe({},v),{values:[v.values[e]]})]});return new ae.hE({$data:new s.Es({$data:new s.Zv({data:{timeRange:o.state.value,state:i.LoadingState.Done,series:m}}),transformations:[()=>e=>e.pipe((0,b.map)(e=>(e.forEach(e=>(0,i.reduceField)({field:e.fields[2],reducers:[i.ReducerID.max]})),e.sort((e,t)=>{var n,r,a,i;return((null===(r=t.fields[2].state)||void 0===r||null===(n=r.calcs)||void 0===n?void 0:n.max)||0)-((null===(i=e.fields[2].state)||void 0===i||null===(a=i.calcs)||void 0===a?void 0:a.max)||0)}))))]}),body:new s.gF({templateColumns:l.MV,autoRows:"200px",isLazy:!0,children:[]}),getLayoutChild:He({},Re,n,r)})}const Re=e=>e.name||"No name available";function He(e,t,n,r){return(a,i)=>{const o=i.name?e[i.name]:void 0,l=new s.Zv({data:ze(Fe({},a),{series:[Fe({},i)]})});var c;if(o)return null===(c=o.state.body)||void 0===c||c.setState({$data:l}),o;const u=(0,$e.x)(r).setTitle(t(i)).setData(l),d=n(i);d&&u.setHeaderActions(d);const m=new s.xK({body:u.build()});return i.name&&(e[i.name]=m),m}}class qe extends s.Bs{}var Ge,We,Ue;Ue=({model:e})=>e.state.attribute?a().createElement(d.Button,{variant:"secondary",size:"sm",fill:"solid",onClick:()=>e.state.onClick()},"Inspect"):null,(We="Component")in(Ge=qe)?Object.defineProperty(Ge,We,{value:Ue,enumerable:!0,configurable:!0,writable:!0}):Ge[We]=Ue;var Ye=n(4917);function Ke(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Xe extends s.Bs{_onActivate(){const e=(0,m.pl)(this);e.changeValueTo(l.y2),this.updateData(),e.subscribeToState((t,n)=>{t.value!==n.value&&this.setBody(e)}),(0,m.h7)(this).subscribeToState(()=>{this.updateData(),this.setBody(e)}),(0,m.YX)(this).subscribeToState((t,n)=>{(0,ce.isEqual)(t.selection,n.selection)||(this.updateData(),this.setBody(e))}),s.jh.getTimeRange(this).subscribeToState(()=>{this.updateData()}),this.setBody(e)}updateData(){const e=(0,m.YX)(this),t=s.jh.getTimeRange(this),n=t.state.value.from.unix(),r=t.state.value.to.unix(),a=(0,m.h7)(this).state.value,i=this.getFilteredAttributes(a);this.setState({$data:new s.Es({$data:new s.dt({datasource:l.Vl,queries:[Qe(n,r,Ve(e.state.selection))]}),transformations:[()=>e=>e.pipe((0,b.map)(e=>{const t=Ze(e);return Object.entries(t).filter(([e,t])=>!i.includes(e)).map(([e,t])=>Je(e,t)).sort((e,t)=>{const n=(0,Ye.p)(e),r=(0,Ye.p)(t);return Math.abs(r.maxDifference)-Math.abs(n.maxDifference)})}))]})})}onReferencedVariableValueChanged(){const e=(0,m.pl)(this);e.changeValueTo(l.y2),this.setBody(e)}onAddToFiltersClick(e){(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.comparison_add_to_filters_clicked,e)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Ke(e,t,n[t])})}return e}({},e)),Ke(this,"_variableDependency",new s.Sh(this,{variableNames:[l.Ao,l.CE],onReferencedVariableValueChanged:this.onReferencedVariableValueChanged.bind(this)})),Ke(this,"getFilteredAttributes",e=>"nestedSetParent<0"===e?["rootName","rootServiceName"]:[]),Ke(this,"setBody",e=>{const t=(0,m.zY)(this);this.setState({body:e.hasAllValue()||e.getValue()===l.y2?(0,$e.nF)(e=>new qe({attribute:e.name,onClick:()=>this.onChange(e.name||"")}),t.getMetricFunction()):Me(this,e,t=>[new re.Ms({frame:t,labelKey:e.getValueText(),onClick:this.onAddToFiltersClick})],t.getMetricFunction())})}),Ke(this,"onChange",(e,t)=>{(0,m.pl)(this).changeValueTo(e,void 0,!t),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.select_attribute_in_comparison_clicked,{value:e})}),this.addActivationHandler(this._onActivate.bind(this))}}function Qe(e,t,n){const r=`${(0,Le.duration)(t-e,"s").asSeconds()}s`;return{refId:"A",query:`{${l.ui}} | compare(${n})`,step:r,queryType:"traceql",tableType:"spans",limit:100,spss:10,filters:[]}}Ke(Xe,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,m.pl)(e),r=(0,m.zY)(e),{attributes:i}=(0,m.YX)(e).useState(),s=(0,d.useStyles2)(tt);return a().createElement("div",{className:s.container},a().createElement(be,{description:"Attributes are ordered by the difference between the baseline and selection values for each value.",tags:[{label:"Baseline",color:"duration"===r.getMetricFunction()?$e.bT:(0,d.getTheme)().visualization.getColorByName("semi-dark-green")},{label:"Selection",color:"duration"===r.getMetricFunction()?$e._E:(0,d.getTheme)().visualization.getColorByName("semi-dark-red")}]}),a().createElement("div",{className:s.controls},(null==i?void 0:i.length)&&a().createElement("div",{className:s.controlsLeft},a().createElement(Z,{options:(0,m._g)(i),radioAttributes:l.jx,value:n.getValueText(),onChange:e.onChange,showAll:!0,model:e})),t instanceof ne&&a().createElement("div",{className:s.controlsRight},a().createElement(t.Selector,{model:t}))),a().createElement("div",{className:s.content},t&&a().createElement(t.Component,{model:t})))});const Ze=e=>e.reduce((e,t)=>{const n=t.fields.find(e=>"number"===e.type),r=Object.keys((null==n?void 0:n.labels)||{}).find(e=>!e.startsWith("__"));return r&&(e[r]=[...e[r]||[],t]),e},{}),Je=(e,t)=>{const n={name:e,refId:e,fields:[],length:0},r={name:"Value",type:i.FieldType.string,values:[],config:{},labels:{[e]:e}},a={name:"Baseline",type:i.FieldType.number,values:[],config:{}},s={name:"Selection",type:i.FieldType.number,values:[],config:{}},o=t.reduce((t,n)=>{var r;const a=n.fields.find(e=>"number"===e.type),i=null==a||null===(r=a.labels)||void 0===r?void 0:r[e];return i&&(t[i]=[...t[i]||[],a]),t},{}),l=et(t,"baseline",o),c=et(t,"selection",o);return n.length=Object.keys(o).length,Object.entries(o).forEach(([e,t])=>{var n,i;r.values.push(e),a.values.push((null===(n=t.find(e=>{var t;return'"baseline"'===(null===(t=e.labels)||void 0===t?void 0:t.__meta_type)}))||void 0===n?void 0:n.values[0])/l),s.values.push((null===(i=t.find(e=>{var t;return'"selection"'===(null===(t=e.labels)||void 0===t?void 0:t.__meta_type)}))||void 0===i?void 0:i.values[0])/c)}),n.fields=[r,a,s],n};function et(e,t,n){const r=Object.values(n).reduce((e,n)=>{const r=n.find(e=>{var n;return(null===(n=e.labels)||void 0===n?void 0:n.__meta_type)===`"${t}"`});return e+((null==r?void 0:r.values[0])||0)},0);let a=e.reduce((e,n)=>{var r;const a=n.fields.find(e=>"number"===e.type);return(null==a||null===(r=a.labels)||void 0===r?void 0:r.__meta_type)===`"${t}_total"`?a.values[0]:e},1);return a<r||1===a||0===a?0===r?1:r:a}function tt(e){return{container:(0,u.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,u.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,u.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2)}),controlsRight:(0,u.css)({flexGrow:0,display:"flex",justifyContent:"flex-end"}),controlsLeft:(0,u.css)({display:"flex",justifyContent:"flex-left",justifyItems:"left",width:"100%",flexDirection:"column"})}}function nt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class rt extends s.Bs{_onActivate(){const e=(0,m.H_)(this).getValue(),t=(0,m.YX)(this);if(!t.state.selection){const n=(0,Ye.F)(e);n&&t.setState({selection:n})}this.updateBody()}updateBody(){this.setState({body:new Xe({})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){nt(e,t,n[t])})}return e}({},e)),nt(this,"_variableDependency",new s.Sh(this,{variableNames:[l.PU]})),this.addActivationHandler(this._onActivate.bind(this))}}nt(rt,"Component",({model:e})=>{const{body:t}=e.useState();return t&&a().createElement(t.Component,{model:t})});var at=n(3518);const it=[{displayName:function(e){return"Breakdown"},value:"breakdown",getScene:function(){return new s.vA({body:new xe({})})}},{displayName:ot,value:"structure",getScene:function(e){return new s.vA({body:new U({metric:e})})}},{displayName:function(e){return"Comparison"},value:"comparison",getScene:function(){return new s.vA({body:new rt({})})}},{displayName:function(e){return"Exceptions"},value:"exceptions",getScene:function(){return new s.vA({body:new De({})})}},{displayName:function(e){return"errors"===e?"Errored traces":"duration"===e?"Slow traces":"Traces"},value:"traceList",getScene:function(){return new s.vA({body:new $({})})}}];class st extends s.Bs{}function ot(e){switch(e){case"rate":return"Service structure";case"errors":return"Root cause errors";case"duration":return"Root cause latency"}}function lt(e){return{actions:(0,u.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,top:5,zIndex:2}})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(st,"Component",({model:e})=>{var t,n,o,l;const c=(0,d.useStyles2)(lt),[u,p]=(0,r.useState)(0),v=(0,m.YX)(e),g=(0,m.zY)(e),{actionView:h}=v.useState(),{value:b}=g.getMetricVariable().useState(),{allowedActionViews:y}=g.useState(),w=s.jh.getData(e).useState(),S=null===(o=w.data)||void 0===o||null===(n=o.series)||void 0===n||null===(t=n[0])||void 0===t?void 0:t.length,O=it.filter(e=>("exceptions"!==e.value||"errors"===b)&&(!(null==y?void 0:y.length)||y.includes(e.value))),x=(0,m.gG)(e),E=(0,m.h7)(e),j=s.jh.getTimeRange(e),{filters:k}=x.useState(),{value:C}=E.useState(),{value:P}=j.useState();return(0,r.useEffect)(()=>{if("errors"!==b)return void p(0);const t=(0,m.dB)(e);if(!t)return void p(0);p(t.getExceptionsCount());const n=t.subscribeToState((e,t)=>{e.exceptionsCount!==t.exceptionsCount&&p(e.exceptionsCount||0)});return()=>{n.unsubscribe()}},[b,e,h,k,C,P]),(0,r.useEffect)(()=>{var e;if(!v.state.hasSetView)return g.state.embedded&&(null===(e=w.data)||void 0===e?void 0:e.state)===i.LoadingState.Done&&void 0!==S&&S>20?(v.setState({hasSetView:!0}),void v.setActionView("traceList")):void 0},[null===(l=w.data)||void 0===l?void 0:l.state,g.state.embedded,v,S]),(0,at.A)(()=>{1===O.length&&v.setActionView(O[0].value)}),1===O.length?null:a().createElement(d.Box,null,a().createElement("div",{className:c.actions},a().createElement(d.Stack,{gap:1},a().createElement(f,{exploration:g}))),a().createElement(d.TabsBar,null,O.map((e,t)=>a().createElement(d.Tab,{key:t,label:e.displayName(b),active:h===e.value,onChangeTab:()=>v.setActionView(e.value),counter:"traceList"===e.value?S:"exceptions"===e.value?u:void 0}))))});var ct=n(892),ut=n(8855),dt=n(2860);function mt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class pt extends s.Bs{_onActivate(){this.setState({$data:new s.Es({$data:new le.$({maxDataPoints:"duration"===this.state.metric?24:64,datasource:l.Vl,queries:["duration"===this.state.metric?(0,ct.z)():(0,ie.l)({metric:this.state.metric,sample:!0})]}),transformations:"duration"===this.state.metric?[...(0,de.h)()]:[...(0,de.G)((0,m.w$)(this))]}),panel:this.getVizPanel(this.state.metric)})}getVizPanel(e){return new s.G1({direction:"row",children:[new s.vA({body:"duration"===e?this.getDurationVizPanel():this.getRateOrErrorPanel(e)})]})}getRateOrErrorPanel(e){const t=(0,se.z)(e).setHoverHeader(!0).setDisplayMode("transparent");return"rate"===e?t.setCustomFieldConfig("axisLabel","span/s"):"errors"===e&&t.setTitle("Errors rate").setCustomFieldConfig("axisLabel","error/s").setColor({fixedColor:"semi-dark-red",mode:"fixed"}),t.build()}getDurationVizPanel(){return(0,ut.dX)().setTitle("Histogram by duration").setHoverHeader(!0).setDisplayMode("transparent").build()}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){mt(e,t,n[t])})}return e}({isStreaming:!1},e)),this.addActivationHandler(()=>{this._onActivate();const e=s.jh.getData(this);this._subs.add(e.subscribeToState(e=>{var t,n,r;this.setState({isStreaming:(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Streaming}),(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done?0===e.data.series.length||0===e.data.series[0].length||(0,m.W6)(e)?this.setState({panel:new s.G1({children:[new s.vA({body:new g.v({imgWidth:110})})]})}):this.setState({panel:this.getVizPanel(this.state.metric)}):(null===(r=e.data)||void 0===r?void 0:r.state)===i.LoadingState.Loading&&this.setState({panel:new s.G1({direction:"column",maxHeight:xt,height:xt,children:[new v.G({component:()=>(0,ae.NO)(1)})]})})}))})}}function ft(e){return{container:(0,u.css)({flex:1,width:"100%",display:"flex",flexDirection:"column",border:`1px solid ${e.colors.border.weak}`,borderRadius:"2px",background:e.colors.background.primary,paddingTop:"8px","section, section:hover":{borderColor:"transparent"},"& .show-on-hover":{display:"none"}}),headerWrapper:(0,u.css)({display:"flex",alignItems:"center",position:"absolute",top:"4px",left:"8px",zIndex:2}),clickable:(0,u.css)({cursor:"pointer",maxHeight:xt,'[class*="loading-state-scene"]':{height:xt,overflow:"hidden"},":hover":{background:e.colors.background.secondary,input:{backgroundColor:"#ffffff",border:"5px solid #3D71D9",cursor:"pointer"}}}),radioButton:(0,u.css)({display:"block"}),indicatorWrapper:(0,u.css)({position:"absolute",top:"4px",right:"8px",zIndex:2})}}function vt(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function gt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ht(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){gt(e,t,n[t])})}return e}function bt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}mt(pt,"Component",({model:e})=>{const{panel:t,isStreaming:n}=e.useState(),r=(0,d.useStyles2)(ft),i=(0,m.zY)(e),s=()=>{(0,k.EE)(k.NO.common,k.ir.common.metric_changed,{metric:e.state.metric,location:"panel"}),i.onChangeMetricFunction(e.state.metric)};if(t)return a().createElement("div",{className:(0,u.css)([r.container,r.clickable]),onClick:s},a().createElement("div",{className:r.headerWrapper},a().createElement(d.RadioButtonList,{className:r.radioButton,name:`metric-${e.state.metric}`,options:[{title:"",value:"selected"}],onChange:()=>s(),value:"not-selected"})),n&&a().createElement("div",{className:r.indicatorWrapper},a().createElement(dt.M,{isStreaming:!0,iconSize:10})),a().createElement(t.Component,{model:t}))});class yt extends s.Bs{_onActivate(){const e=new URLSearchParams(window.location.search).get("actionView");e&&it.find(t=>t.value===e)&&this.setState({actionView:e}),this.updateBody();const t=(0,m.zY)(this).getMetricVariable();this._subs.add(t.subscribeToState((e,t)=>{if(e.value!==t.value){const t=(0,Ye.F)(e.value);t&&this.setState({selection:t}),this.updateQueryRunner(e.value),this.updateExceptionsScene(e.value),this.updateBody()}})),this.updateExceptionsScene(t.getValue()),this._subs.add(this.subscribeToState((e,n)=>{var r,a;const i=s.jh.getTimeRange(this),o=null===(a=e.selection)||void 0===a||null===(r=a.timeRange)||void 0===r?void 0:r.from;if(o&&o<i.state.value.from.unix()&&this.setState({selection:void 0}),!(0,ce.isEqual)(e.selection,n.selection)){(0,m.pl)(this).changeValueTo(l.y2),this.updateQueryRunner(t.getValue())}})),this._subs.add((0,m._b)(this).subscribeToState(()=>{this.updateAttributes()})),this._subs.add((0,m.gi)(this).subscribeToState(()=>{this.updateQueryRunner(t.getValue())})),this.updateQueryRunner(t.getValue()),this.updateAttributes()}updateBody(){const e=(0,m.zY)(this).getMetricVariable().getValue(),t=it.find(e=>e.value===this.state.actionView);this.setState({body:kt(e,t?[null==t?void 0:t.getScene(e)]:void 0)}),void 0===this.state.actionView&&this.setActionView("breakdown")}updateExceptionsScene(e){if("errors"===e){if(!this.state.exceptionsScene){const e=new De({});this.setState({exceptionsScene:e}),setTimeout(()=>{e.activate()},0)}}else this.state.exceptionsScene&&this.setState({exceptionsScene:void 0})}updateAttributes(){return(e=function*(){var e;const t=yield(0,c.getDataSourceSrv)().get(l.gR,{__sceneObject:{value:this}});if(!t)return;const n={timeRange:s.jh.getTimeRange(this).state.value,filters:[]};null===(e=t.getTagKeys)||void 0===e||e.call(t,n).then(e=>{let t=[];t="data"in e?e.data:e;const n=t.map(e=>e.text);n!==this.state.attributes&&this.setState({attributes:n})})},function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){vt(i,r,a,s,o,"next",e)}function o(e){vt(i,r,a,s,o,"throw",e)}s(void 0)})}).call(this);var e}getUrlState(){return{actionView:this.state.actionView,selection:this.state.selection?JSON.stringify(this.state.selection):void 0}}updateFromUrl(e){if("string"==typeof e.actionView){if(this.state.actionView!==e.actionView){const t=it.find(t=>t.value===e.actionView);t&&this.setActionView(t.value)}}else null===e.actionView&&this.setActionView("breakdown");if("string"==typeof e.selection){const t=JSON.parse(e.selection);(0,ce.isEqual)(t,this.state.selection)||this.setState({selection:t})}}onUserUpdateSelection(e){this._urlSync.performBrowserHistoryAction(()=>{this.setState({selection:e})})}setActionView(e){const{body:t}=this.state,n=it.find(t=>t.value===e),r=(0,m.zY)(this).getMetricVariable().getValue();if(t.state.children.length>1&&n){let a;a="exceptions"===e&&this.state.exceptionsScene?new s.vA({body:this.state.exceptionsScene}):n.getScene(r),t.setState({children:[...t.state.children.slice(0,2),a]}),(0,k.EE)(k.NO.analyse_traces,k.ir.analyse_traces.action_view_changed,{oldAction:this.state.actionView,newAction:e}),this.setState({actionView:n.value})}}updateQueryRunner(e){var t;const n=this.state.selection;var r;const a=null!==(r=null===(t=(0,m.gi)(this).getValue())||void 0===t?void 0:t.toString())&&void 0!==r?r:"";this.setState({$data:new s.Es({$data:new s.dt({datasource:l.Vl,queries:[Et(e,a,n)],$timeRange:jt(n)}),transformations:[...l.s9,...Ct]})})}constructor(e){var t;super(ht({body:null!==(t=e.body)&&void 0!==t?t:new s.G1({children:[]})},e)),gt(this,"_urlSync",new s.So(this,{keys:["actionView","selection"]})),this.addActivationHandler(this._onActivate.bind(this))}}gt(yt,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,d.useStyles2)(St);return a().createElement(a().Fragment,null,a().createElement("div",{className:n.title},a().createElement(d.Tooltip,{content:a().createElement(wt,null),placement:"right-start",interactive:!0},a().createElement("span",{className:n.hand},"Select metric type ",a().createElement(d.Icon,{name:"info-circle"})))),a().createElement(t.Component,{model:t}))});const wt=()=>{const e=(0,d.useStyles2)(St);return a().createElement(d.Stack,{direction:"column",gap:1},a().createElement("div",{className:e.tooltip.title},"RED metrics for traces"),a().createElement("span",{className:e.tooltip.subtitle},"Explore rate, errors, and duration (RED) metrics generated from traces by Tempo."),a().createElement("div",{className:e.tooltip.text},a().createElement("div",null,a().createElement("span",{className:e.tooltip.emphasize},"Rate")," - Spans per second that match your filter, useful to find unusual spikes in activity"),a().createElement("div",null,a().createElement("span",{className:e.tooltip.emphasize},"Errors")," -Spans that are failing, overall issues in tracing ecosystem"),a().createElement("div",null,a().createElement("span",{className:e.tooltip.emphasize},"Duration")," - Amount of time those spans take, represented as a heat map (responds time, latency)")),a().createElement("div",{className:e.tooltip.button},a().createElement(d.LinkButton,{icon:"external-link-alt",fill:"solid",size:"sm",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces/concepts/#rate-error-and-duration-metrics",onClick:()=>(0,k.EE)(k.NO.common,k.ir.common.metric_docs_link_clicked)},"Read documentation")))};function St(e){return{title:(0,u.css)({label:"title",display:"flex",gap:e.spacing.x0_5,fontSize:e.typography.bodySmall.fontSize,paddingBottom:e.spacing.x0_5,alignItems:"center"}),hand:(0,u.css)({label:"hand",cursor:"pointer"}),tooltip:{label:"tooltip",title:(0,u.css)({fontSize:"14px",fontWeight:500}),subtitle:(0,u.css)({marginBottom:e.spacing.x1}),text:(0,u.css)({label:"text",color:e.colors.text.secondary,div:{marginBottom:e.spacing.x0_5}}),emphasize:(0,u.css)({label:"emphasize",color:e.colors.text.primary}),button:(0,u.css)({marginBottom:e.spacing.x0_5})}}}const Ot=240,xt=(Ot-8)/2;function Et(e,t,n){const r=""!==t?` | select(${t})`:"";let a="";switch(e){case"errors":a=" && status = error";break;case"duration":if(n){var i,s;const e=[];(null===(i=n.duration)||void 0===i?void 0:i.from.length)&&e.push(`duration >= ${n.duration.from}`),(null===(s=n.duration)||void 0===s?void 0:s.to.length)&&e.push(`duration <= ${n.duration.to}`),e.length&&(a+="&& "+e.join(" && "))}a.length||(a=`&& duration > ${l.xT}`)}return{refId:"A",query:`{${l.ui}${a}}${r}`,queryType:"traceql",tableType:"spans",limit:200,spss:10,filters:[]}}function jt(e){var t,n;const r=1e3*((null==e||null===(t=e.timeRange)||void 0===t?void 0:t.from)||0),a=1e3*((null==e||null===(n=e.timeRange)||void 0===n?void 0:n.to)||0);return r&&a?new s.JZ({from:r.toFixed(0),to:a.toFixed(0),value:{from:(0,i.dateTime)(r),to:(0,i.dateTime)(a),raw:{from:(0,i.dateTime)(r),to:(0,i.dateTime)(a)}}}):void 0}function kt(e,t){const n=new pt("rate"===e?{metric:"errors"}:{metric:"rate"}),r=new pt("duration"===e?{metric:"errors"}:{metric:"duration"});return new s.G1({direction:"column",$behaviors:[new s.Gg.K2({key:"metricCrosshairSync",sync:i.DashboardCursorSync.Crosshair})],children:[new s.G1({direction:"row",ySizing:"content",children:[new s.vA({minHeight:Ot,maxHeight:Ot,width:"60%",body:new o.Mu({})}),new s.G1({direction:"column",minHeight:Ot,maxHeight:Ot,children:[new s.vA({minHeight:xt,maxHeight:xt,height:xt,body:n}),new s.vA({minHeight:xt,maxHeight:xt,height:xt,ySizing:"fill",body:r})]})]}),new s.vA({ySizing:"content",body:new st({})}),...t||[]]})}const Ct=[()=>e=>e.pipe((0,b.map)(e=>e.map(e=>bt(ht({},e),{fields:e.fields.filter(e=>!e.name.startsWith("nestedSet"))})))),{id:"sortBy",options:{fields:{},sort:[{field:"Duration",desc:!0}]}},{id:"organize",options:{indexByName:{"Start time":0,status:1,"Trace Service":2,"Trace Name":3,Duration:4,"Span ID":5,"span.http.method":6,"span.http.request.method":7,"span.http.path":8,"span.http.route":9,"span.http.status_code":10,"span.http.response.status_code":11}}}]},775:(e,t,n)=>{"use strict";n.d(t,{v:()=>u});var r,a,i,s=n(118),o=n(5959),l=n.n(o),c=n(3733);class u extends s.Bs{}i=({model:e})=>{const{message:t,remedyMessage:n,imgWidth:r,padding:a}=e.useState();return l().createElement(c.p,{message:t,remedyMessage:n,imgWidth:r,padding:a})},(a="Component")in(r=u)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},806:(e,t,n)=>{"use strict";n.d(t,{D9:()=>m,Ms:()=>u,Qt:()=>d});var r=n(5959),a=n.n(r),i=n(118),s=n(2007),o=n(1051),l=n(3761);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class u extends i.Bs{constructor(...e){super(...e),c(this,"onClick",()=>{var e;const t=(0,o.gG)(this);var n;const r=null!==(n=null===(e=this.state.frame.fields.find(e=>e.labels))||void 0===e?void 0:e.labels)&&void 0!==n?n:{};if(this.state.labelKey){if(!r[this.state.labelKey])return}else if(1!==Object.keys(r).length)return;var a;const i=null!==(a=this.state.labelKey)&&void 0!==a?a:Object.keys(r)[0],s=(0,o.ee)(this.state.frame,this.state.labelKey);d(t,i,s),this.state.onClick({labelName:i})})}}c(u,"Component",({model:e})=>{var t,n,r,i,l;const c=null!==(l=null===(t=e.state)||void 0===t?void 0:t.labelKey)&&void 0!==l?l:"",u=null===(n=e.state)||void 0===n?void 0:n.frame.fields.filter(e=>"time"!==e.type);var d;const p=null!==(d=null==u||null===(i=u[0])||void 0===i||null===(r=i.labels)||void 0===r?void 0:r[c])&&void 0!==d?d:"";return m((0,o.gG)(e),c,p.replace(/"/g,""))?a().createElement(a().Fragment,null):a().createElement(s.Button,{variant:"primary",size:"sm",fill:"text",onClick:e.onClick,icon:"search-plus"},"Add to filters")});const d=(e,t,n)=>{const r=e.state.filters.filter(e=>e.key===l.iH||e.key!==t);history.pushState(null,""),e.setState({filters:[...r,{key:t,operator:"=",value:n}]})},m=(e,t,n)=>(0,o.gG)(e).state.filters.find(e=>e.key===t&&e.value===n)},892:(e,t,n)=>{"use strict";n.d(t,{z:()=>a});var r=n(1829);function a(){return{refId:"A",query:`{${r.ui}} | histogram_over_time(duration) with(sample=true)`,queryType:"traceql",tableType:"spans",limit:1e3,spss:10,filters:[]}}},1051:(e,t,n)=>{"use strict";n.d(t,{BB:()=>Ke,W6:()=>We,xo:()=>Ye,_g:()=>Te,Is:()=>He,U4:()=>De,_b:()=>Re,u1:()=>Pe,dB:()=>ke,gG:()=>Fe,pl:()=>Ae,Ey:()=>Me,oT:()=>Ee,ee:()=>Ie,F3:()=>Ve,Kf:()=>Le,GK:()=>Ge,H_:()=>Be,Fp:()=>_e,w$:()=>Xe,h7:()=>ze,gi:()=>$e,YX:()=>je,zY:()=>xe,__:()=>Ne,Et:()=>Ue,em:()=>Ce,H:()=>qe});var r=n(7781),a=n(118),i=n(2395),s=n(1829),o=n(271),l=n(6089),c=n(5959),u=n.n(c),d=n(2468),m=n(2007),p=n(2645),f=n(8531),v=n(6338);const g=e=>{const{index:t,type:n,label:r,labelTitle:a,value:i,valueTitle:s,url:o}=e,l=(0,m.useStyles2)(h);return u().createElement("div",{key:t},0===t&&u().createElement("div",{className:l.rowHeader},u().createElement("span",null,a),u().createElement("span",{className:l.valueTitle},s)),u().createElement("div",{className:l.row,key:t,onClick:()=>{(0,v.EE)(v.NO.home,v.ir.home.panel_row_clicked,{type:n,index:t,value:i}),f.locationService.push(o)}},u().createElement("div",{className:"rowLabel"},r),u().createElement("div",{className:l.action},u().createElement("span",{className:l.actionText},i),u().createElement(m.Icon,{className:l.actionIcon,name:"arrow-right",size:"xl"}))))};function h(e){return{rowHeader:(0,l.css)({color:e.colors.text.secondary,display:"flex",justifyContent:"space-between",alignItems:"center",padding:`0 ${e.spacing(2)} ${e.spacing(1)} ${e.spacing(2)}`}),valueTitle:(0,l.css)({margin:"0 45px 0 0"}),row:(0,l.css)({display:"flex",justifyContent:"space-between",alignItems:"center",gap:e.spacing(2),padding:`${e.spacing(.75)} ${e.spacing(2)}`,"&:hover":{backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary,cursor:"pointer",".rowLabel":{textDecoration:"underline"}}}),action:(0,l.css)({display:"flex",alignItems:"center"}),actionText:(0,l.css)({color:"#d5983c",padding:`0 ${e.spacing(1)}`,width:"max-content"}),actionIcon:(0,l.css)({cursor:"pointer",margin:`0 ${e.spacing(.5)} 0 ${e.spacing(1)}`})}}const b=e=>{var t;const{series:n,type:a}=e,i=(0,m.useStyles2)(y),o=e=>{var t;const n=e.fields.find(e=>"time"!==e.name);var r;return null!==(r=null==n||null===(t=n.labels)||void 0===t?void 0:t["resource.service.name"].replace(/"/g,""))&&void 0!==r?r:"Service name not found"},l=e=>{const t={"var-filters":`resource.service.name|=|${o(e)}`,"var-metric":"errors"};return r.urlUtil.renderUrl(s.D5,t)},c=e=>{var t;const n=e.fields.find(e=>"time"!==e.name);var r;return null!==(r=null==n||null===(t=n.values)||void 0===t?void 0:t.reduce((e,t)=>"number"!=typeof e||isNaN(e)?t:e+t,0))&&void 0!==r?r:1};return u().createElement("div",{className:i.container},null===(t=n.sort((e,t)=>c(t)-c(e)).slice(0,10))||void 0===t?void 0:t.map((e,t)=>u().createElement("span",{key:t},u().createElement(g,{type:a,index:t,label:o(e),labelTitle:"Service",value:c(e),valueTitle:"Total errors",url:l(e)}))))};function y(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`})}}var w=n(7975);function S(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function O(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const x=e=>{const{series:t,type:n}=e,a=(0,m.useStyles2)(E),i=t[0].fields.find(e=>"duration"===e.name);if(i&&i.values){var o,l;const e=null==i||null===(o=i.values.map((e,t)=>t))||void 0===o?void 0:o.sort((e,t)=>(null==i?void 0:i.values[t])-(null==i?void 0:i.values[e])),c=t[0].fields.map(t=>O(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){S(e,t,n[t])})}return e}({},t),{values:null==e?void 0:e.map(e=>t.values[e])})),d=(e,t,n)=>{let r="";return(null==e?void 0:e.values[n])&&(r=e.values[n]),(null==t?void 0:t.values[n])&&(r=0===r.length?t.values[n]:`${r}: ${t.values[n]}`),0===r.length?"Trace service & name not found":r},m=(e,t,n,a)=>{if(!(t&&t.values[a]&&n&&n.values[a]))return console.error("SpanId or traceService not found"),s.bw.Explore;const i={traceId:e,spanId:t.values[a],"var-filters":`resource.service.name|=|${n.values[a]}`,"var-metric":"duration"};return r.urlUtil.renderUrl(s.D5,i)},p=(e,t)=>e&&e.values?(0,w.a3)(e.values[t]/1e3):"Duration not found",f=c.find(e=>"traceIdHidden"===e.name),v=c.find(e=>"spanID"===e.name),h=c.find(e=>"traceName"===e.name),b=c.find(e=>"traceService"===e.name),y=c.find(e=>"duration"===e.name);return u().createElement("div",{className:a.container},null==f||null===(l=f.values)||void 0===l?void 0:l.map((e,t)=>u().createElement("span",{key:t},u().createElement(g,{type:n,index:t,label:d(b,h,t),labelTitle:"Trace",value:p(y,t),valueTitle:"Duration",url:m(e,v,b,t)}))))}return null};function E(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`})}}const j=e=>{var t;const{series:n,type:a}=e,i=(0,m.useStyles2)(k),o=e=>{var t;const n=e.fields.find(e=>"time"!==e.name);var r;return null!==(r=null==n||null===(t=n.labels)||void 0===t?void 0:t["resource.service.name"].replace(/"/g,""))&&void 0!==r?r:"Service name not found"},l=e=>{const t={"var-filters":`resource.service.name|=|${o(e)}`,"var-metric":"duration"};return r.urlUtil.renderUrl(s.D5,t)},c=e=>{var t;const n=e.fields.find(e=>"time"!==e.name);var r;return null!==(r=null==n||null===(t=n.values)||void 0===t?void 0:t.reduce((e,t)=>"number"!=typeof e||isNaN(e)?t:e+t,0))&&void 0!==r?r:1};return u().createElement("div",{className:i.container},null===(t=n.sort((e,t)=>c(t)-c(e)).slice(0,10))||void 0===t?void 0:t.map((e,t)=>u().createElement("span",{key:t},u().createElement(g,{type:a,index:t,label:o(e),labelTitle:"Service",value:(0,w.a3)(1e6*c(e)),valueTitle:"p90",url:l(e)}))))};function k(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`})}}const C=e=>{const{series:t,type:n,message:r}=e,a=(0,m.useStyles2)(P);if(r)return u().createElement("div",{className:a.container},u().createElement("div",{className:a.message},u().createElement(m.Icon,{className:a.icon,name:"exclamation-circle",size:"xl"}),r));if(t&&t.length>0)switch(n){case"slowest-traces":return u().createElement(x,{series:t,type:n});case"errored-services":return u().createElement(b,{series:t,type:n});case"slowest-services":return u().createElement(j,{series:t,type:n})}return u().createElement("div",{className:a.container},"No series data")};function P(e){return{container:(0,l.css)({padding:`${e.spacing(2)} 0`}),icon:(0,l.css)({margin:`0 ${e.spacing(.5)} 0 ${e.spacing(1)}`}),message:(0,l.css)({display:"flex",gap:e.spacing(1.5),margin:`${e.spacing(2)} auto`,width:"60%"})}}class _ extends a.Bs{}var N,D,T;function I(e){switch(e){case"slowest-services":return"clock-nine";case"slowest-traces":return"crosshair";default:return"exclamation-triangle"}}function A(e){return{container:(0,l.css)({border:`1px solid ${e.isDark?e.colors.border.medium:e.colors.border.weak}`,borderRadius:e.spacing(.5),marginBottom:e.spacing(4),width:"100%"}),title:(0,l.css)({color:e.isDark?e.colors.text.secondary:e.colors.text.primary,backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary,borderTopLeftRadius:e.spacing(.5),borderTopRightRadius:e.spacing(.5),display:"flex",justifyContent:"center",alignItems:"center",fontSize:"1.3rem",padding:`${e.spacing(1.5)} ${e.spacing(2)}`}),titleText:(0,l.css)({marginLeft:e.spacing(1)})}}T=({model:e})=>{const{series:t,title:n,type:r,message:a}=e.useState(),i=(0,m.useStyles2)(A);return u().createElement("div",{className:i.container},u().createElement("div",{className:i.title},u().createElement(m.Icon,{name:I(r),size:"lg"}),u().createElement("span",{className:i.titleText},n)),u().createElement(C,{series:t,type:r,message:a}))},(D="Component")in(N=_)?Object.defineProperty(N,D,{value:T,enumerable:!0,configurable:!0,writable:!0}):N[D]=T;var $=n(3049),L=n(7197);function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function B(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){V(e,t,n[t])})}return e}function F(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class z extends a.Bs{constructor(e){super(B({$data:new a.dt({datasource:s.Vl,queries:[F(B({refId:"A",queryType:"traceql",tableType:"spans",limit:10},e.query),{exemplars:0})]})},e)),this.addActivationHandler(()=>{const t=a.jh.getData(this);this._subs.add(t.subscribeToState(t=>{var n,i,s,l;if((null===(n=t.data)||void 0===n?void 0:n.state)===r.LoadingState.Done||(null===(i=t.data)||void 0===i?void 0:i.state)===r.LoadingState.Streaming)if((null===(l=t.data)||void 0===l?void 0:l.state)!==r.LoadingState.Done||0!==t.data.series.length&&0!==t.data.series[0].length){if(t.data.series.length>0){var c;if("slowest-traces"!==e.type||e.renderDurationPanel)this.setState({panel:new a.G1({children:[new _({series:t.data.series,title:e.title,type:e.type})]})});else if((null===(c=t.data)||void 0===c?void 0:c.state)===r.LoadingState.Done){var u,d;let n=(0,L.FC)(null!==(d=null===(u=t.data)||void 0===u?void 0:u.series)&&void 0!==d?d:[]);if(null==n?void 0:n.length){const{minDuration:t}=(0,L.jD)(n);var m;this.setState({panel:new a.G1({children:[new z({query:{query:`{nestedSetParent<0 && duration > ${t} ${null!==(m=e.filter)&&void 0!==m?m:""}}`},title:e.title,type:e.type,renderDurationPanel:!0})]})})}}}}else this.setState({panel:new a.G1({children:[new _({message:_e(e.title.toLowerCase()),title:e.title,type:e.type})]})});else(null===(s=t.data)||void 0===s?void 0:s.state)===r.LoadingState.Error?this.setState({panel:new a.G1({children:[new _({message:Pe(t),title:e.title,type:e.type})]})}):this.setState({panel:new a.G1({direction:"column",maxHeight:o.VV,height:o.VV,children:[new p.G({component:()=>R()})]})})}))})}}function M(){return{container:(0,l.css)({minWidth:"350px",width:"-webkit-fill-available"})}}V(z,"Component",({model:e})=>{const{panel:t}=e.useState(),n=(0,m.useStyles2)(M);if(t)return u().createElement("div",{className:n.container},u().createElement(t.Component,{model:t}))});const R=()=>{const e=(0,m.useStyles2)(H);return u().createElement("div",{className:e.container},u().createElement("div",{className:e.title},u().createElement($.A,{count:1,width:200})),u().createElement("div",{className:e.tracesContainer},[...Array(11)].map((t,n)=>u().createElement("div",{className:e.row,key:n},u().createElement("div",{className:e.rowLeft},u().createElement($.A,{count:1})),u().createElement("div",{className:e.rowRight},u().createElement($.A,{count:1}))))))};function H(e){return{container:(0,l.css)({border:`1px solid ${e.isDark?e.colors.border.medium:e.colors.border.weak}`,borderRadius:e.spacing(.5),marginBottom:e.spacing(4),width:"100%"}),title:(0,l.css)({color:e.colors.text.secondary,backgroundColor:e.colors.background.secondary,fontSize:"1.3rem",padding:`${e.spacing(1.5)} ${e.spacing(2)}`,textAlign:"center"}),tracesContainer:(0,l.css)({padding:`13px ${e.spacing(2)}`}),row:(0,l.css)({display:"flex",justifyContent:"space-between"}),rowLeft:(0,l.css)({margin:"7px 0",width:"150px"}),rowRight:(0,l.css)({width:"50px"})}}const q=()=>u().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"73",height:"72",viewBox:"0 0 73 72",fill:"none"},u().createElement("path",{d:"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z",fill:"#24292E",fillOpacity:"0.75"})),G=()=>u().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:"73",height:"72",viewBox:"0 0 73 72",fill:"none"},u().createElement("path",{d:"M65.3 8.09993C65.3 7.49993 64.7 7.19993 64.1 6.89993C52.7 3.89993 40.4 7.79993 32.9 16.7999L29 21.2999L20.9 19.1999C17.6 17.9999 14.3 19.4999 12.8 22.4999L6.49999 33.5999C6.49999 33.5999 6.49999 33.8999 6.19999 33.8999C5.89999 34.7999 6.49999 35.3999 7.39999 35.6999L17.6 37.7999C16.7 40.4999 15.8 43.1999 15.5 45.8999C15.5 46.4999 15.5 46.7999 15.8 47.0999L24.8 55.7999C25.1 56.0999 25.4 56.0999 26 56.0999C28.7 55.7999 31.7 55.1999 34.4 54.2999L36.5 64.1999C36.5 64.7999 37.4 65.3999 38 65.3999C38.3 65.3999 38.6 65.3999 38.6 65.0999L49.7 58.7999C52.4 57.2999 53.6 53.9999 53 50.9999L50.9 42.2999L55.1 38.3999C64.4 31.4999 68.3 19.4999 65.3 8.09993ZM10.1 33.2999L15.2 23.9999C16.1 22.1999 17.9 21.5999 19.7 22.1999L26.6 23.9999L23.6 27.5999C21.8 29.9999 20 32.3999 18.8 35.0999L10.1 33.2999ZM48.5 56.9999L39.2 62.3999L37.4 53.6999C40.1 52.4999 42.5 50.6999 44.9 48.8999L48.8 45.2999L50.6 52.1999C50.6 53.9999 50 56.0999 48.5 56.9999ZM53.3 36.8999L42.8 46.4999C38.3 50.3999 32.6 52.7999 26.6 53.3999L18.8 45.5999C19.7 39.5999 22.1 33.8999 26 29.3999L30.8 23.9999L31.1 23.6999L35.3 18.8999C41.9 11.0999 52.7 7.49993 62.6 9.59993C64.7 19.7999 61.4 30.2999 53.3 36.8999ZM49.7 16.7999C46.4 16.7999 44 19.4999 44 22.4999C44 25.4999 46.7 28.1999 49.7 28.1999C53 28.1999 55.4 25.4999 55.4 22.4999C55.4 19.4999 53 16.7999 49.7 16.7999ZM49.7 25.4999C48.2 25.4999 47 24.2999 47 22.7999C47 21.2999 48.2 20.0999 49.7 20.0999C51.2 20.0999 52.4 21.2999 52.4 22.7999C52.4 24.2999 51.2 25.4999 49.7 25.4999Z",fill:"#CCCCDC",fillOpacity:"0.65"}));var W=n(1159);function U(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function Y(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){U(i,r,a,s,o,"next",e)}function o(e){U(i,r,a,s,o,"throw",e)}s(void 0)})}}function K(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function X(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const Q=e=>(e.delete(s.$V),e.delete(`var-${s.pf}`),e.delete(`var-${s.xc}`),e),Z=()=>{const e=(0,f.usePluginUserStorage)();return{getBookmarks:()=>te(e),removeBookmark:t=>ae(e,t),bookmarkExists:t=>ie(e,t),toggleBookmark:()=>ne(e)}},J=e=>{if(!e||!e.params)return s.D5;const t=new URLSearchParams(e.params),n=Object.fromEntries(t.entries()),a=t.getAll(`var-${s.Ao}`),i=r.urlUtil.renderUrl(s.D5,X(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){K(e,t,n[t])})}return e}({},n),{[`var-${s.Ao}`]:a}));return i},ee=(e,t)=>Y(function*(){try{yield e.setItem(s.Sr,JSON.stringify(t))}catch(e){console.error("Failed to save bookmarks to storage:",e)}})(),te=e=>Y(function*(){try{const t=yield e.getItem(s.Sr);return t?JSON.parse(t):[]}catch(e){return console.error("Failed to get bookmarks from storage:",e),[]}})(),ne=e=>Y(function*(){const t={params:Q(new URLSearchParams(window.location.search)).toString()};return(yield ie(e,t))?(yield ae(e,t),!1):(yield re(e,t),!0)})(),re=(e,t)=>Y(function*(){const n=yield te(e);n.push(t),yield ee(e,n)})(),ae=(e,t)=>Y(function*(){const n=(yield te(e)).filter(e=>!se(t,e));yield ee(e,n)})(),ie=(e,t)=>Y(function*(){return(yield te(e)).some(e=>se(t,e))})(),se=(e,t)=>{const n=Q(new URLSearchParams(e.params)),r=Q(new URLSearchParams(t.params)),a=`var-${s.Ao}`,i=Array.from(n.keys()).filter(e=>e!==a),o=Array.from(r.keys()).filter(e=>e!==a);if(i.length!==o.length)return!1;const l=i.every(e=>r.has(e)&&n.get(e)===r.get(e));if(!l)return!1;const c=n.getAll(a),u=r.getAll(a);return c.length===u.length&&c.every(e=>u.includes(e))};var oe=n(3761);const le=({bookmark:e})=>{let{actionView:t,primarySignal:n,metric:r,filters:a}=(e=>{if(!e||!e.params)return{actionView:"",primarySignal:"",filters:"",metric:""};const t=new URLSearchParams(e.params);var n,r,a;return{actionView:null!==(n=t.get(s.V2))&&void 0!==n?n:"",primarySignal:null!==(r=t.get(s.W5))&&void 0!==r?r:"",filters:t.getAll(`var-${s.Ao}`).join(s.x5),metric:null!==(a=t.get(`var-${s.PU}`))&&void 0!==a?a:""}})(e);const i=(0,m.useStyles2)(ce);return a=((e,t)=>{const n=(e=>{const t=(0,oe.$L)(e);if(!t||!t.filter)return"";const n=t.filter;return n.key&&n.operator&&void 0!==n.value?`${n.key}|${n.operator}|${n.value}`:""})(t);let r=e.split(s.x5);return r=r.filter(e=>e!==n),r.join(s.x5)})(a,n),a=a.replace(/\|=\|/g," = "),a=a.replace(s.$d,"").replace(s.zd,"").replace(s.X0,""),u().createElement("div",{title:a},u().createElement("div",null,u().createElement("b",null,Ke(r))," of ",u().createElement("b",null,n.replace("_"," "))," (",t,")"),u().createElement("div",{className:i.filters},a))};function ce(){return{filters:(0,l.css)({textOverflow:"ellipsis",overflow:"hidden",WebkitLineClamp:2,display:"-webkit-box",WebkitBoxOrient:"vertical"})}}function ue(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function de(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){ue(i,r,a,s,o,"next",e)}function o(e){ue(i,r,a,s,o,"throw",e)}s(void 0)})}}const me=()=>{const e=(0,m.useStyles2)(pe),{getBookmarks:t,removeBookmark:n}=Z(),[r,a]=(0,c.useState)([]),[i,s]=(0,c.useState)(!0),[o,l]=(0,c.useState)(!1);(0,c.useEffect)(()=>{de(function*(){s(!0);try{const e=yield t();a(e)}catch(e){console.error("Error loading bookmarks:",e),a([])}finally{s(!1)}})()},[]);return i?u().createElement("div",null,u().createElement("div",{className:e.header},u().createElement("h4",null,"Or view bookmarks")),u().createElement("div",{className:e.loading},u().createElement(m.LoadingPlaceholder,{text:"Loading bookmarks..."}))):u().createElement("div",null,u().createElement("div",{className:e.header},u().createElement("h4",null,"Or view bookmarks")),0===r.length?u().createElement("p",{className:e.noBookmarks},"Bookmark your favorite queries to view them here."):u().createElement("div",{className:e.bookmarks},r.map((r,i)=>u().createElement("div",{className:e.bookmark,key:i,onClick:()=>(e=>{(0,v.EE)(v.NO.home,v.ir.home.go_to_bookmark_clicked);const t=J(e);f.locationService.push(t)})(r)},u().createElement("div",{className:e.bookmarkItem},u().createElement(le,{bookmark:r})),u().createElement("div",{className:e.remove},u().createElement(m.Button,{variant:"secondary",fill:"text",icon:"trash-alt",disabled:o,onClick:e=>((e,r)=>de(function*(){r.stopPropagation(),l(!0);try{yield n(e);const r=yield t();a(r)}catch(e){console.error("Error removing bookmark:",e)}finally{l(!1)}})())(r,e)}))))))};function pe(e){return{header:(0,l.css)({textAlign:"center",h4:{margin:0}}),bookmarks:(0,l.css)({display:"flex",flexWrap:"wrap",gap:e.spacing(2),margin:`${e.spacing(4)} 0 ${e.spacing(2)} 0`,justifyContent:"center"}),bookmark:(0,l.css)({display:"flex",flexDirection:"column",justifyContent:"space-between",cursor:"pointer",width:"318px",border:`1px solid ${e.colors.border.medium}`,borderRadius:e.shape.radius.default,"&:hover":{backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary}}),bookmarkItem:(0,l.css)({padding:`${e.spacing(1.5)} ${e.spacing(1.5)} 0 ${e.spacing(1.5)}`,overflow:"hidden"}),filters:(0,l.css)({textOverflow:"ellipsis",overflow:"hidden",WebkitLineClamp:2,display:"-webkit-box",WebkitBoxOrient:"vertical"}),remove:(0,l.css)({display:"flex",justifyContent:"flex-end"}),noBookmarks:(0,l.css)({margin:`${e.spacing(4)} 0 ${e.spacing(2)} 0`,textAlign:"center"}),loading:(0,l.css)({display:"flex",justifyContent:"center",margin:`${e.spacing(4)} 0`})}}class fe extends a.Bs{}function ve(e){return{container:(0,l.css)({display:"flex",gap:e.spacing(7),flexDirection:"column",margin:`0 0 ${e.spacing(4)} 0`,justifyContent:"center"}),header:(0,l.css)({display:"flex",alignItems:"center",backgroundColor:e.isDark?e.colors.background.secondary:e.colors.background.primary,borderRadius:e.spacing(.5),flexWrap:"wrap",justifyContent:"center",padding:e.spacing(3),gap:e.spacing(4)}),headerTitleContainer:(0,l.css)({display:"flex",alignItems:"center"}),title:(0,l.css)({margin:`0 0 0 ${e.spacing(2)}`}),headerActions:(0,l.css)({alignItems:"center",justifyContent:"flex-start",display:"flex",gap:e.spacing(2)}),documentationLink:(0,l.css)({textDecoration:"underline","&:hover":{textDecoration:"underline"}}),subHeader:(0,l.css)({textAlign:"center",h4:{margin:`0 0 -${e.spacing(2)} 0`}}),label:(0,l.css)({fontSize:"12px"}),variablesAndControls:(0,l.css)({alignItems:"center",gap:e.spacing(2),display:"flex",justifyContent:"space-between",width:"100%"}),variables:(0,l.css)({display:"flex",gap:e.spacing(2)}),controls:(0,l.css)({display:"flex",gap:e.spacing(1)})}}function ge(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function he(e){return(t=function*(){const t=a.jh.interpolate(e,s.gR),n=yield(0,f.getDataSourceSrv)().get(t);if(!(n instanceof f.DataSourceWithBackend))throw console.error(new Error("getTagKeysProvider: invalid datasource!")),new Error("getTagKeysProvider: invalid datasource!");const r=n;if(r&&r.getTagKeys){const e=yield r.getTagKeys();return Array.isArray(e)?{replace:!0,values:function(e){const t=e.filter(e=>{var t;return null===(t=e.text)||void 0===t?void 0:t.includes(s.$d)}),n=e.filter(e=>{var t;return null===(t=e.text)||void 0===t?void 0:t.includes(s.zd)}),r=e.filter(e=>{var t,n,r,a;return!((null===(t=e.text)||void 0===t?void 0:t.includes(s.$d))||(null===(n=e.text)||void 0===n?void 0:n.includes(s.zd))||(null===(r=e.text)||void 0===r?void 0:r.includes(s.X0))||(null===(a=e.text)||void 0===a?void 0:a.includes(s.ZV))||-1!==s.uK.concat(s.ZM).indexOf(e.text))});return[...t,...n,...r]}(e)}:(console.error(new Error("getTagKeysProvider: invalid tagKeys!")),{values:[]})}return console.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{values:[]}},function(){var e=this,n=arguments;return new Promise(function(r,a){var i=t.apply(e,n);function s(e){ge(i,r,a,s,o,"next",e)}function o(e){ge(i,r,a,s,o,"throw",e)}s(void 0)})})();var t}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(fe,"Component",({model:e})=>{const t=Ee(e),n=(0,W.useNavigate)(),{controls:r}=t.useState(),a=(0,m.useStyles2)(ve),i=(0,m.useTheme2)(),o=Re(t),l=Me(t);return u().createElement("div",{className:a.container},u().createElement("div",{className:a.header},u().createElement("div",{className:a.headerTitleContainer},i.isDark?u().createElement(G,null):u().createElement(q,null),u().createElement("h2",{className:a.title},"Start your traces exploration!")),u().createElement("div",null,u().createElement("p",null,"Drilldown and visualize your trace data without writing a query."),u().createElement("div",{className:a.headerActions},u().createElement(m.Button,{variant:"primary",onClick:()=>{(0,v.EE)(v.NO.home,v.ir.home.explore_traces_clicked),n(s.D5)}},"Let’s start",u().createElement(m.Icon,{name:"arrow-right",size:"lg"})),u().createElement(m.LinkButton,{icon:"external-link-alt",fill:"text",size:"md",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/traces",className:a.documentationLink,onClick:()=>(0,v.EE)(v.NO.home,v.ir.home.read_documentation_clicked)},"Read documentation")))),u().createElement(me,null),u().createElement("div",{className:a.subHeader},u().createElement("h4",null,"Or quick-start into your tracing data")),u().createElement(m.Stack,{gap:2},u().createElement("div",{className:a.variablesAndControls},u().createElement("div",{className:a.variables},o&&u().createElement(m.Stack,{gap:1,alignItems:"center"},u().createElement("div",{className:a.label},"Data source"),u().createElement(o.Component,{model:o})),l&&u().createElement(m.Stack,{gap:1,alignItems:"center"},u().createElement("div",{className:a.label},"Filter"),u().createElement(l.Component,{model:l}))),u().createElement("div",{className:a.controls},null==r?void 0:r.map(e=>u().createElement(e.Component,{key:e.state.key,model:e}))))))});const be=e=>{if(!e)return"";let t=e.value;return null==t||""===t?"":(Ue.test(t)||["kind"].includes(e.key)||"string"!=typeof t||t.startsWith('"')||t.endsWith('"')||(t=`"${t}"`),`${e.key}${e.operator}${t}`)};function ye(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class we extends a.Bs{_onActivate(){const e=a.jh.getTimeRange(this),t=Me(this);t.setState({getTagKeysProvider:he}),Re(this).subscribeToState(e=>{e.value&&localStorage.setItem(s.cd,e.value.toString())}),Me(this).subscribeToState((t,n)=>{if(t.filters!==n.filters){this.buildPanels(e,t.filters),localStorage.setItem(s.$U,JSON.stringify(t.filters));const r=t.filters.filter(e=>!n.filters.find(t=>t.key===e.key));r.length>0&&(0,v.EE)(v.NO.home,v.ir.home.filter_changed,{key:r[0].key})}}),e.subscribeToState((n,r)=>{n.value.from===r.value.from&&n.value.to===r.value.to||this.buildPanels(e,t.state.filters)}),this.buildPanels(e,t.state.filters)}buildPanels(e,t){const n=e.state.value.from.unix(),r=e.state.value.to.unix(),i=`${(0,d.duration)(r-n,"s").asSeconds()}s`,o=function(e){const t=e.filter(e=>e.key&&e.operator&&e.value).map(e=>be(e)).join(s.x5);return t.length?`&& ${t}`:""}(t);this.setState({body:new a.gF({children:[new a.gF({autoRows:"min-content",columnGap:2,rowGap:2,children:[new a.xK({body:new z({query:{query:`{nestedSetParent < 0 && status = error ${o}} | count_over_time() by (resource.service.name)`,step:i},title:"Errored services",type:"errored-services"})}),new a.xK({body:new z({query:{query:`{nestedSetParent < 0 ${o}} | quantile_over_time(duration, 0.9) by (resource.service.name)`,step:i},title:"Slow services",type:"slowest-services"})}),new a.xK({body:new z({query:{query:`{nestedSetParent<0 ${o}} | histogram_over_time(duration)`},title:"Slow traces",type:"slowest-traces",filter:o})})]})]})})}constructor(e){var t,n,r,i,o;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ye(e,t,n[t])})}return e}({$timeRange:null!==(t=e.$timeRange)&&void 0!==t?t:new a.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:(i=e.initialFilters,o=e.initialDS,new a.Pj({variables:[new a.mI({name:s.EY,label:"Data source",value:o,pluginId:"tempo"}),new a.H9({name:s.zM,datasource:s.Vl,layout:"combobox",filters:i,allowCustomValue:!0})]})),controls:null!==(r=e.controls)&&void 0!==r?r:[new a.KE({}),new a.WM({})]},e)),this.addActivationHandler(this._onActivate.bind(this))}}function Se(e){return{container:(0,l.css)({margin:`${e.spacing(4)} auto`,width:"75%","@media (max-width: 900px)":{width:"95%"}})}}ye(we,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,m.useStyles2)(Se);return u().createElement("div",{className:n.container},u().createElement(fe.Component,{model:e}),t&&u().createElement(t.Component,{model:t}))});var Oe=n(6003);function xe(e){return a.jh.getAncestor(e,i.Nr)}function Ee(e){return a.jh.getAncestor(e,we)}function je(e){return a.jh.getAncestor(e,o.jc)}function ke(e){const t=je(e);return null==t?void 0:t.state.exceptionsScene}function Ce(e,t){return new i.Nr({initialDS:e,initialFilters:null!=t?t:[],$timeRange:new a.JZ({from:"now-30m",to:"now"})})}function Pe(e){var t,n,r;return null!==(r=null==e||null===(n=e.data)||void 0===n||null===(t=n.error)||void 0===t?void 0:t.message)&&void 0!==r?r:"There are no Tempo data sources"}function _e(e){return`No data for selected data source and filter. Select another to see ${e}.`}function Ne(e){const t=a.Go.getUrlState(e);return n=t,r.urlUtil.renderUrl(s.D5,n);var n}function De(e){return a.jh.interpolate(e,s.gR)}function Te(e){return e.map(e=>({label:e,value:e}))}function Ie(e,t){var n;const r=null===(n=e.fields.find(e=>"number"===e.type))||void 0===n?void 0:n.labels;if(!r)return"No labels";const a=Object.keys(r).filter(e=>"p"!==e);return 0===a.length?"No labels":r[t||a[0]].replace(/"/g,"")}function Ae(e){const t=a.jh.lookupVariable(s.z,e);if(!(t instanceof a.yP))throw new Error("Group by variable not found");return t}function $e(e){const t=a.jh.lookupVariable(s.gP,e);if(!(t instanceof a.yP))throw new Error("Span list columns variable not found");return t}function Le(e){const t=a.jh.lookupVariable(s.pf,e);if(!(t instanceof a.yP))throw new Error("Latency threshold variable not found");return t}function Ve(e){const t=a.jh.lookupVariable(s.xc,e);if(!(t instanceof a.yP))throw new Error("Partial latency threshold variable not found");return t}function Be(e){const t=a.jh.lookupVariable(s.PU,e);if(!(t instanceof a.yP))throw new Error("Metric variable not found");return t}function Fe(e){const t=a.jh.lookupVariable(s.Ao,e);if(!(t instanceof a.H9))throw new Error("Filters variable not found");return t}function ze(e){const t=a.jh.lookupVariable(s.CE,e);if(!(t instanceof Oe.x))throw new Error("Primary signal variable not found");return t}function Me(e){const t=a.jh.lookupVariable(s.zM,e);if(!(t instanceof a.H9))throw new Error("Home filter variable not found");return t}function Re(e){const t=a.jh.lookupVariable(s.EY,e);if(!(t instanceof a.mI))throw new Error("Datasource variable not found");return t}function He(e){var t;const n=a.jh.getData(e).state.data,r=null==n||null===(t=n.request)||void 0===t?void 0:t.targets[0];return r?r.step:void 0}function qe(e){return"comparison"===e||"traceList"===e}function Ge(e){return Be(e).useState().value}function We(e){var t,n,r;return null!==(r=null==e||null===(n=e.data)||void 0===n||null===(t=n.series[0].fields)||void 0===t?void 0:t.some(e=>e.values.every(e=>void 0===e)))&&void 0!==r&&r}const Ue=/^-?\d+\.?\d*$/,Ye=e=>Ue.test(e)||"string"!=typeof e||e.startsWith('"')||e.endsWith('"')?e:`"${e}"`,Ke=e=>{var t;return(null==e||null===(t=e[0])||void 0===t?void 0:t.toUpperCase())+(null==e?void 0:e.slice(1))||""},Xe=e=>(t,n)=>{e.publishEvent(new s.vR({traceId:t,spanId:n}),!0)}},1738:(e,t,n)=>{var r={"./af":9805,"./af.js":9805,"./ar":4449,"./ar-dz":4468,"./ar-dz.js":4468,"./ar-kw":3480,"./ar-kw.js":3480,"./ar-ly":4197,"./ar-ly.js":4197,"./ar-ma":2180,"./ar-ma.js":2180,"./ar-ps":9343,"./ar-ps.js":9343,"./ar-sa":230,"./ar-sa.js":230,"./ar-tn":2808,"./ar-tn.js":2808,"./ar.js":4449,"./az":5865,"./az.js":5865,"./be":6627,"./be.js":6627,"./bg":901,"./bg.js":901,"./bm":3179,"./bm.js":3179,"./bn":1966,"./bn-bd":969,"./bn-bd.js":969,"./bn.js":1966,"./bo":6317,"./bo.js":6317,"./br":6474,"./br.js":6474,"./bs":5961,"./bs.js":5961,"./ca":7270,"./ca.js":7270,"./cs":1564,"./cs.js":1564,"./cv":3239,"./cv.js":3239,"./cy":2366,"./cy.js":2366,"./da":2453,"./da.js":2453,"./de":6601,"./de-at":5027,"./de-at.js":5027,"./de-ch":8101,"./de-ch.js":8101,"./de.js":6601,"./dv":6080,"./dv.js":6080,"./el":2655,"./el.js":2655,"./en-au":6836,"./en-au.js":6836,"./en-ca":2086,"./en-ca.js":2086,"./en-gb":2103,"./en-gb.js":2103,"./en-ie":5964,"./en-ie.js":5964,"./en-il":4379,"./en-il.js":4379,"./en-in":765,"./en-in.js":765,"./en-nz":1502,"./en-nz.js":1502,"./en-sg":1152,"./en-sg.js":1152,"./eo":50,"./eo.js":50,"./es":3350,"./es-do":9338,"./es-do.js":9338,"./es-mx":1326,"./es-mx.js":1326,"./es-us":9947,"./es-us.js":9947,"./es.js":3350,"./et":8231,"./et.js":8231,"./eu":8512,"./eu.js":8512,"./fa":9083,"./fa.js":9083,"./fi":5059,"./fi.js":5059,"./fil":2607,"./fil.js":2607,"./fo":3369,"./fo.js":3369,"./fr":7390,"./fr-ca":6711,"./fr-ca.js":6711,"./fr-ch":6152,"./fr-ch.js":6152,"./fr.js":7390,"./fy":2419,"./fy.js":2419,"./ga":3002,"./ga.js":3002,"./gd":4914,"./gd.js":4914,"./gl":6557,"./gl.js":6557,"./gom-deva":8944,"./gom-deva.js":8944,"./gom-latn":5387,"./gom-latn.js":5387,"./gu":7462,"./gu.js":7462,"./he":9237,"./he.js":9237,"./hi":9617,"./hi.js":9617,"./hr":6544,"./hr.js":6544,"./hu":341,"./hu.js":341,"./hy-am":1388,"./hy-am.js":1388,"./id":5251,"./id.js":5251,"./is":1146,"./is.js":1146,"./it":7891,"./it-ch":7,"./it-ch.js":7,"./it.js":7891,"./ja":3727,"./ja.js":3727,"./jv":5198,"./jv.js":5198,"./ka":8974,"./ka.js":8974,"./kk":7308,"./kk.js":7308,"./km":7786,"./km.js":7786,"./kn":4807,"./kn.js":4807,"./ko":1584,"./ko.js":1584,"./ku":1906,"./ku-kmr":5305,"./ku-kmr.js":5305,"./ku.js":1906,"./ky":9190,"./ky.js":9190,"./lb":7396,"./lb.js":7396,"./lo":8503,"./lo.js":8503,"./lt":3010,"./lt.js":3010,"./lv":5192,"./lv.js":5192,"./me":1944,"./me.js":1944,"./mi":6492,"./mi.js":6492,"./mk":2934,"./mk.js":2934,"./ml":1463,"./ml.js":1463,"./mn":8377,"./mn.js":8377,"./mr":8733,"./mr.js":8733,"./ms":8030,"./ms-my":9445,"./ms-my.js":9445,"./ms.js":8030,"./mt":5887,"./mt.js":5887,"./my":7228,"./my.js":7228,"./nb":8294,"./nb.js":8294,"./ne":9559,"./ne.js":9559,"./nl":600,"./nl-be":8796,"./nl-be.js":8796,"./nl.js":600,"./nn":9570,"./nn.js":9570,"./oc-lnc":5662,"./oc-lnc.js":5662,"./pa-in":7101,"./pa-in.js":7101,"./pl":6118,"./pl.js":6118,"./pt":9198,"./pt-br":7203,"./pt-br.js":7203,"./pt.js":9198,"./ro":5565,"./ro.js":5565,"./ru":3315,"./ru.js":3315,"./sd":8473,"./sd.js":8473,"./se":1258,"./se.js":1258,"./si":8798,"./si.js":8798,"./sk":6404,"./sk.js":6404,"./sl":7057,"./sl.js":7057,"./sq":5718,"./sq.js":5718,"./sr":5363,"./sr-cyrl":478,"./sr-cyrl.js":478,"./sr.js":5363,"./ss":7260,"./ss.js":7260,"./sv":2231,"./sv.js":2231,"./sw":7104,"./sw.js":7104,"./ta":7493,"./ta.js":7493,"./te":7705,"./te.js":7705,"./tet":4457,"./tet.js":4457,"./tg":2727,"./tg.js":2727,"./th":2206,"./th.js":2206,"./tk":3419,"./tk.js":3419,"./tl-ph":7243,"./tl-ph.js":7243,"./tlh":16,"./tlh.js":16,"./tr":7020,"./tr.js":7020,"./tzl":8026,"./tzl.js":8026,"./tzm":8537,"./tzm-latn":7899,"./tzm-latn.js":7899,"./tzm.js":8537,"./ug-cn":818,"./ug-cn.js":818,"./uk":8478,"./uk.js":8478,"./ur":7893,"./ur.js":7893,"./uz":9133,"./uz-latn":311,"./uz-latn.js":311,"./uz.js":9133,"./vi":2179,"./vi.js":2179,"./x-pseudo":2455,"./x-pseudo.js":2455,"./yo":3310,"./yo.js":3310,"./zh-cn":7244,"./zh-cn.js":7244,"./zh-hk":76,"./zh-hk.js":76,"./zh-mo":2305,"./zh-mo.js":2305,"./zh-tw":8588,"./zh-tw.js":8588};function a(e){var t=i(e);return n(t)}function i(e){if(!n.o(r,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return r[e]}a.keys=function(){return Object.keys(r)},a.resolve=i,e.exports=a,a.id=1738},1793:(e,t,n)=>{"use strict";n.d(t,{L:()=>l});var r=n(118);const a=n.p+"1382cadfeb81ccdaa67d.svg";function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){i(e,t,n[t])})}return e}function o(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class l extends r.Bs{constructor(e){super(o(s({},e),{queries:[]})),i(this,"_onActivate",()=>{this._subs.add(this.subscribeToState(()=>{this.getQueries(),this.getContext()}))}),i(this,"getQueries",()=>{const e=r.jh.getData(this),t=r.jh.findObject(e,c);if(c(t)){const e=t.state.queries.map(e=>o(s({},e),{query:this.state.query}));JSON.stringify(e)!==JSON.stringify(this.state.queries)&&this.setState({queries:e})}}),i(this,"getContext",()=>{const{queries:e,dsUid:t,labelValue:n,type:i="traceMetrics"}=this.state,o=r.jh.getTimeRange(this);if(!o||!e||!t)return;const l={origin:"Explore Traces",type:i,queries:e,timeRange:s({},o.state.value),datasource:{uid:t},url:window.location.href,id:`${JSON.stringify(e)}`,title:`${n}`,logoPath:a};JSON.stringify(l)!==JSON.stringify(this.state.context)&&this.setState({context:l})}),this.addActivationHandler(this._onActivate.bind(this))}}function c(e){return e instanceof r.dt}},2395:(e,t,n)=>{"use strict";n.d(t,{Nr:()=>ie,oL:()=>oe});var r=n(6089),a=n(5959),i=n.n(a),s=n(7781),o=n(118),l=n(8531),c=n(2007),u=n(1829),d=n(1051),m=n(775),p=n(2645),f=n(3049);function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends o.Bs{getVizPanel(){const e=o.d0.traces().setHoverHeader(!0);return this.state.spanId&&e.setOption("focusedSpanId",this.state.spanId),e}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){v(e,t,n[t])})}return e}({$data:new o.dt({datasource:u.Vl,queries:[{refId:"A",query:e.traceId,queryType:"traceql"}]})},e)),this.addActivationHandler(()=>{const e=o.jh.getData(this);this._subs.add(e.subscribeToState(e=>{var t,n;(null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Done?this.setState({panel:this.getVizPanel().build()}):(null===(n=e.data)||void 0===n?void 0:n.state)===s.LoadingState.Loading&&this.setState({panel:new p.G({component:h})})}))})}}v(g,"Component",({model:e})=>{const{panel:t}=e.useState(),n=(0,c.useStyles2)(b);if(t)return i().createElement("div",{className:n.panelContainer},i().createElement(t.Component,{model:t}))});const h=()=>{const e=(0,c.useStyles2)(b);return i().createElement("div",{className:e.container},i().createElement("div",{className:e.header},i().createElement(f.A,{count:1,width:60}),i().createElement(f.A,{count:1,width:60})),i().createElement(f.A,{count:2,width:"80%"}),i().createElement("div",{className:e.map},i().createElement(f.A,{count:1}),i().createElement(f.A,{count:1,height:70})),i().createElement("div",{className:e.span},i().createElement("span",{className:e.service1},i().createElement(f.A,{count:1})),i().createElement("span",{className:e.bar1},i().createElement(f.A,{count:1}))),i().createElement("div",{className:e.span},i().createElement("span",{className:e.service2},i().createElement(f.A,{count:1})),i().createElement("span",{className:e.bar2},i().createElement(f.A,{count:1}))),i().createElement("div",{className:e.span},i().createElement("span",{className:e.service3},i().createElement(f.A,{count:1})),i().createElement("span",{className:e.bar3},i().createElement(f.A,{count:1}))),i().createElement("div",{className:e.span},i().createElement("span",{className:e.service4},i().createElement(f.A,{count:1})),i().createElement("span",{className:e.bar4},i().createElement(f.A,{count:1}))),i().createElement("div",{className:e.span},i().createElement("span",{className:e.service5},i().createElement(f.A,{count:1})),i().createElement("span",{className:e.bar5},i().createElement(f.A,{count:1}))),i().createElement("div",{className:e.span},i().createElement("span",{className:e.service6},i().createElement(f.A,{count:1})),i().createElement("span",{className:e.bar6},i().createElement(f.A,{count:1}))))};function b(e){return{panelContainer:(0,r.css)({display:"flex",height:"100%",'& [data-testid="data-testid panel content"] > div':{overflow:"auto"},"& .show-on-hover":{display:"none"}}),container:(0,r.css)({height:"calc(100% - 32px)",width:"calc(100% - 32px)",position:"absolute",backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,padding:"5px"}),header:(0,r.css)({marginBottom:"20px",display:"flex",justifyContent:"space-between"}),map:(0,r.css)({marginTop:"20px",marginBottom:"20px"}),span:(0,r.css)({display:"flex"}),service1:(0,r.css)({width:"25%"}),bar1:(0,r.css)({marginLeft:"5%",width:"70%"}),service2:(0,r.css)({width:"25%"}),bar2:(0,r.css)({marginLeft:"10%",width:"15%"}),service3:(0,r.css)({width:"20%",marginLeft:"5%"}),bar3:(0,r.css)({marginLeft:"10%",width:"65%"}),service4:(0,r.css)({width:"20%",marginLeft:"5%"}),bar4:(0,r.css)({marginLeft:"15%",width:"60%"}),service5:(0,r.css)({width:"15%",marginLeft:"10%"}),bar5:(0,r.css)({marginLeft:"20%",width:"35%"}),service6:(0,r.css)({width:"15%",marginLeft:"10%"}),bar6:(0,r.css)({marginLeft:"30%",width:"15%"})}}var y=n(6338);function w(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class S extends o.Bs{_onActivate(){this.updateBody();(0,d.zY)(this).subscribeToState((e,t)=>{e.traceId===t.traceId&&e.spanId===t.spanId||(this.updateBody(),(0,y.EE)(y.NO.analyse_traces,y.ir.analyse_traces.open_trace,{traceId:e.traceId,spanId:e.spanId}))})}updateBody(){const e=(0,d.zY)(this);e.state.traceId?this.setState({body:new g({traceId:e.state.traceId,spanId:e.state.spanId})}):this.setState({body:new m.v({message:"No trace selected"})})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){w(e,t,n[t])})}return e}({},e)),this.addActivationHandler(this._onActivate.bind(this))}}w(S,"Component",({model:e})=>{const{body:t}=e.useState();return t&&i().createElement(t.Component,{model:t})});var O=n(2245),x=n(6003),E=n(3761);class j extends o.Bs{_onActivate(){this.runIssueDetectionQuery();const e=(0,d._b)(this);this._subs.add(e.subscribeToState((e,t)=>{e.value!==t.value&&(this.resetIssues(),this.runIssueDetectionQuery())}))}runIssueDetectionQuery(){const e=(0,d._b)(this),t=(0,s.dateTime)(),n=(0,s.dateTime)(t).subtract(1,"minute"),r=new o.JZ({from:n.toISOString(),to:t.toISOString()}),a=new o.dt({maxDataPoints:1,datasource:{uid:String(e.state.value)},$timeRange:r,queries:[{refId:"issueDetectorQuery",query:"{} | rate()",queryType:"traceql",tableType:"spans",limit:1,spss:1,filters:[]}]});this._subs.add(a.subscribeToState(e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Error){var n,r,a;((null===(a=e.data)||void 0===a||null===(r=a.errors)||void 0===r||null===(n=r[0])||void 0===n?void 0:n.message)||"").includes("localblocks processor not found")&&this.setState({hasIssue:!0})}})),a.activate()}resetIssues(){this.setState({hasIssue:!1})}constructor(){super({hasIssue:!1}),this.addActivationHandler(this._onActivate.bind(this))}}const k=({detector:e})=>{const{hasIssue:t}=e.useState();return t?i().createElement(c.Alert,{severity:"warning",title:"TraceQL metrics not configured"},i().createElement("p",null,'We found an error running a TraceQL metrics query: "localblocks processor not found". This typically means the "local-blocks" processor is not configured in Tempo, which is required for Grafana Traces Drilldown to work.',i().createElement(c.LinkButton,{icon:"external-link-alt",fill:"text",size:"sm",target:"_blank",href:"https://grafana.com/docs/tempo/latest/operations/traceql-metrics"},"Read documentation"))):null};var C=n(1793),P=n(4524),_=n(271);function N({serviceName:e,model:t}){const{isLoading:n,component:r}=(0,l.usePluginComponent)("grafana-asserts-app/entity-assertions-widget/v1"),[s,c]=(0,a.useState)();return(0,a.useEffect)(()=>{const e=o.jh.getTimeRange(t);c(e.state.value);const n=e.subscribeToState(e=>{c(e.value)});return()=>{n.unsubscribe()}},[t]),!n&&r&&s?i().createElement(r,{size:"md",source:"Traces Drilldown",query:{start:s.from.valueOf(),end:s.to.valueOf(),entityName:e,entityType:"Service"},returnToPrevious:!0}):null}var D=n(5755),T=n(1893),I=n(4568),A=n(1508),$=n(7186),L=n(9814);n(1546);function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function B(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){V(e,t,n[t])})}return e}function F(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const z={sm:{width:"25%",minWidth:384},md:{width:"50%",minWidth:568},lg:{width:"75%",minWidth:744}};function M({children:e,onClose:t,closeOnMaskClick:n=!0,scrollableContent:i=!0,title:s,subtitle:o,size:l="md",tabs:u}){const[d,m,p]=function(){const[e,t]=(0,a.useState)(void 0),n=(0,a.useCallback)(e=>{t(R(e.clientX))},[]),r=(0,a.useCallback)(e=>{const n=e.touches[0];t(R(n.clientX))},[]),i=(0,a.useCallback)(e=>{document.removeEventListener("mousemove",n),document.removeEventListener("mouseup",i)},[n]),s=(0,a.useCallback)(e=>{document.removeEventListener("touchmove",r),document.removeEventListener("touchend",s)},[r]);function o(e){e.stopPropagation(),e.preventDefault(),document.addEventListener("mousemove",n),document.addEventListener("mouseup",i)}function l(e){e.stopPropagation(),e.preventDefault(),document.addEventListener("touchmove",r),document.addEventListener("touchend",s)}return[e,o,l]}(),f=(0,c.useStyles2)(H),v=(0,c.useStyles2)(q,l),g=(0,c.useStyles2)(c.getDragStyles),h=a.useRef(null),{dialogProps:b,titleProps:y}=(0,D.s)({},h),{overlayProps:w}=(0,I.e)({isDismissable:!1,isOpen:!0,onClose:t},h);(0,a.useEffect)(()=>{if(document.body)return document.body.classList.add("body-drawer-open"),()=>{document.body.classList.remove("body-drawer-open")}},[]);const S=a.createElement("div",{className:f.content},e),O=null!=d?d:z[l].width,x=z[l].minWidth;return a.createElement(A.A,{open:!0,onClose:t,placement:"right",getContainer:"#trace-exploration",className:f.drawerContent,rootClassName:f.drawer,classNames:{wrapper:v},styles:{wrapper:{width:O,minWidth:x}},width:"",motion:{motionAppear:!0,motionName:f.drawerMotion},maskClassName:f.mask,maskClosable:n,maskMotion:{motionAppear:!0,motionName:f.maskMotion}},a.createElement(T.n1,{restoreFocus:!0,contain:!0,autoFocus:!0},a.createElement("div",F(B({"aria-label":"string"==typeof s?$.Tp.components.Drawer.General.title(s):$.Tp.components.Drawer.General.title("no title"),className:f.container},w,b),{ref:h}),a.createElement("div",{className:(0,r.cx)(g.dragHandleVertical,f.resizer),onMouseDown:m,onTouchStart:p}),a.createElement("div",{className:(0,r.cx)(f.header,Boolean(u)&&f.headerWithTabs)},a.createElement("div",{className:f.actions},a.createElement(c.IconButton,{name:"times",variant:"secondary",onClick:t,"data-testid":$.Tp.components.Drawer.General.close,tooltip:(0,L.t)("grafana-ui.drawer.close","Close")})),"string"==typeof s?a.createElement("div",{className:f.titleWrapper},a.createElement(c.Text,B({element:"h3"},y),s),o&&a.createElement("div",{className:f.subtitle,"data-testid":$.Tp.components.Drawer.General.subtitle},o)):s,u&&a.createElement("div",{className:f.tabsWrapper},u)),i?a.createElement(c.ScrollContainer,{showScrollIndicators:!0},S):S)))}function R(e){let t=document.body.offsetWidth-(e-document.body.offsetLeft);return`${Math.min(t/document.body.clientWidth*100,98).toFixed(2)}vw`}const H=e=>{var t,n;return{container:(0,r.css)({display:"flex",flexDirection:"column",height:"100%",flex:"1 1 0",minHeight:"100%",position:"relative"}),drawer:(0,r.css)({top:0,position:"absolute !important",".rc-drawer-content-wrapper":{boxShadow:e.shadows.z3}}),drawerContent:(0,r.css)({backgroundColor:`${e.colors.background.primary} !important`,display:"flex",overflow:"unset !important",flexDirection:"column"}),drawerMotion:(0,r.css)({"&-appear":{transform:"translateX(100%)",transition:"none !important","&-active":{transition:`${e.transitions.create("transform")} !important`,transform:"translateX(0)"}}}),mask:(0,r.css)({backgroundColor:"transparent !important",position:"absolute !important","&:before":{backgroundColor:`${e.components.overlay.background} !important`,bottom:0,content:'""',left:0,position:"absolute",right:0,top:0}}),maskMotion:(0,r.css)({"&-appear":{opacity:0,"&-active":{opacity:1,transition:e.transitions.create("opacity")}}}),header:(0,r.css)({label:"drawer-header",flexGrow:0,padding:e.spacing(2,2,3),borderBottom:`1px solid ${e.colors.border.weak}`}),headerWithTabs:(0,r.css)({borderBottom:"none"}),actions:(0,r.css)({position:"absolute",right:e.spacing(1),top:e.spacing(1)}),titleWrapper:(0,r.css)({label:"drawer-title",overflowWrap:"break-word"}),subtitle:(0,r.css)({label:"drawer-subtitle",color:e.colors.text.secondary,paddingTop:e.spacing(1)}),content:(0,r.css)({padding:e.spacing(null!==(n=null===(t=e.components.drawer)||void 0===t?void 0:t.padding)&&void 0!==n?n:2),height:"100%",flexGrow:1,minHeight:0}),tabsWrapper:(0,r.css)({label:"drawer-tabs",paddingLeft:e.spacing(2),margin:e.spacing(1,-1,-3,-3)}),resizer:(0,r.css)({top:0,left:e.spacing(-1),bottom:0,position:"absolute",zIndex:e.zIndex.modal})}};function q(e,t){return(0,r.css)({label:`drawer-content-wrapper-${t}`,overflow:"unset !important",[e.breakpoints.down("md")]:{width:`calc(100% - ${e.spacing(2)}) !important`,minWidth:"0 !important"}})}const G=({children:e,title:t,isOpen:n,onClose:r,embedded:a=!1,forceNoDrawer:s=!1,investigationButton:o})=>{const l=(0,c.useStyles2)(W);return n?!s&&!a?i().createElement(M,{size:"lg",title:t,onClose:r},e):i().createElement("div",{className:l.container},i().createElement("div",{className:l.drawerHeader},i().createElement(c.Button,{variant:"primary",fill:"text",size:"md",icon:"arrow-left",onClick:r},"Back to all traces"),a&&o),e):null},W=e=>({container:(0,r.css)({height:"100%",width:"100%",background:e.colors.background.primary,padding:e.spacing(2),display:"flex",flexDirection:"column",position:"absolute",top:0,left:0,zIndex:3}),drawerHeader:(0,r.css)({display:"flex",justifyContent:"space-between",alignItems:"center",paddingBottom:e.spacing(2),h4:{margin:0}})});function U(e){const t=e.filter(e=>e.key&&e.operator&&e.value).map(e=>function(e){let t=e.value;!["span.messaging.destination.partition.id","span.network.protocol.version"].includes(e.key)&&(Y(t)||["status","kind","span:status","span:kind","duration","span:duration","trace:duration","event:timeSinceStart"].includes(e.key)||["true","false"].includes(t)||(n=t,"string"==typeof n&&n.length>=2&&Y(n.slice(1,-1))&&(n.startsWith('"')&&n.endsWith('"')||n.startsWith("'")&&n.endsWith("'"))))||"string"==typeof t&&(t=t.replace(/["\\]/g,e=>`\\${e}`),t=`"${t}"`);var n;return`${e.key}${e.operator}${t}`}(e)).join("&&");return t.length?t:"true"}function Y(e){return null!=e&&""!==e&&!isNaN(Number(e.toString().trim()))}function K(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function X(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){K(e,t,n[t])})}return e}function Q(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class Z extends o.H9{constructor(e){var t;super({addFilterButtonText:"Add filter",name:u.Ao,datasource:u.Vl,hide:s.VariableHide.hideLabel,layout:"combobox",filters:(null!==(t=e.initialFilters)&&void 0!==t?t:[]).map(t=>Q(X({},t),{readOnly:e.embedded,origin:e.embedderName})),allowCustomValue:!0,expressionBuilder:U}),K(this,"initialFilters",void 0),K(this,"embedderName",void 0),K(this,"embedded",void 0),this.initialFilters=e.initialFilters,this.embedderName=e.embedderName,this.embedded=e.embedded,this.subscribeToState(e=>{if(e.filters&&this.embedded){let t=!1;const n=e.filters.map(e=>{var n;return(null===(n=this.initialFilters)||void 0===n?void 0:n.find(t=>t.key===e.key&&t.operator===e.operator&&t.value===e.value))&&!e.readOnly&&e.origin!==this.embedderName?(t=!0,Q(X({},e),{readOnly:!0,origin:this.embedderName})):e});t&&this.setState({filters:n})}})}}function J(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function ee(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function te(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ee(e,t,n[t])})}return e}function ne(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const re="2025-09-11T10:10:21.656Z",ae=`${re.split("T")[0]} (b3b303b)`;class ie extends o.Bs{_onActivate(){this.state.topScene||this.setState({topScene:new _.jc({})}),this._subs.add(this.subscribeToEvent(u.vR,e=>{this.setupInvestigationButton(e.payload.traceId),this.setState({traceId:e.payload.traceId,spanId:e.payload.spanId})})),this.state.traceId&&this.setupInvestigationButton(this.state.traceId);o.jh.lookupVariable(u.EY,this).subscribeToState(e=>{e.value&&localStorage.setItem(u.cd,e.value.toString())}),this.state.issueDetector&&(this.state.issueDetector.isActive||this.state.issueDetector.activate())}getUrlState(){return{traceId:this.state.traceId,spanId:this.state.spanId}}updateFromUrl(e){const t={};(e.traceId||e.spanId)&&(t.traceId=e.traceId?e.traceId:void 0,t.spanId=e.spanId?e.spanId:void 0),this.setState(t)}getMetricVariable(){const e=o.jh.lookupVariable(u.PU,this);if(!(e instanceof o.yP))throw new Error("Metric variable not found");var t;e.getValue()||e.changeValueTo(null!==(t=this.state.initialMetric)&&void 0!==t?t:"rate");return e}getMetricFunction(){return this.getMetricVariable().getValue()}closeDrawer(){this.setState({traceId:void 0,spanId:void 0})}setupInvestigationButton(e){const t=(0,d.zY)(this),n=(0,d.U4)(t),r=new o.dt({datasource:{uid:n},queries:[{refId:"A",query:e,queryType:"traceql"}]}),a=new C.L({query:e,type:"trace",dsUid:n,$data:r});a.activate(),this.setState({addToInvestigationButton:a}),this._subs.add(a.subscribeToState(()=>{this.updateInvestigationLink()})),r.activate(),this._subs.add(r.subscribeToState(e=>{var t,n,r;if((null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Done&&(null===(r=e.data)||void 0===r||null===(n=r.series)||void 0===n?void 0:n.length)>0){var i,o;const t=null===(o=e.data.series[0])||void 0===o||null===(i=o.fields)||void 0===i?void 0:i.find(e=>"serviceName"===e.name);t&&t.values[0]&&a.setState(ne(te({},a.state),{labelValue:`${t.values[0]}`}))}})),a.setState(ne(te({},a.state),{labelValue:e}))}updateInvestigationLink(){return(e=function*(){const{addToInvestigationButton:e}=this.state;if(!e)return;const t=yield(0,P.Fh)(e);t&&this.setState({investigationLink:t})},function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){J(i,r,a,s,o,"next",e)}function o(e){J(i,r,a,s,o,"throw",e)}s(void 0)})}).call(this);var e}constructor(e){var t,n,r;super(te({$timeRange:null!==(t=e.$timeRange)&&void 0!==t?t:new o.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:ue(e),controls:null!==(r=e.controls)&&void 0!==r?r:[new o.KE({}),new o.WM({})],body:new se({}),drawerScene:new S({}),issueDetector:new j},e)),ee(this,"_urlSync",new o.So(this,{keys:["traceId","spanId"]})),ee(this,"onChangeMetricFunction",e=>{const t=this.getMetricVariable();e&&t.getValue()!==e&&t.changeValueTo(e,void 0,!0)}),this.addActivationHandler(this._onActivate.bind(this))}}ee(ie,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,c.useStyles2)(de);return i().createElement("div",{className:n.bodyContainer}," ",t&&i().createElement(t.Component,{model:t})," ")});class se extends o.Bs{}ee(se,"Component",({model:e})=>{const t=(0,d.zY)(e),{controls:n,topScene:r,drawerScene:a,traceId:s,issueDetector:o,investigationLink:l,addToInvestigationButton:u,embedded:m}=t.useState(),{hasIssue:p}=(null==o?void 0:o.useState())||{hasIssue:!1},f=(0,c.useStyles2)(de);return i().createElement("div",{className:f.container,id:"trace-exploration"},p&&o&&i().createElement(k,{detector:o}),m?i().createElement(le,{model:e}):i().createElement(ce,{controls:n,model:e}),i().createElement("div",{className:f.body},r&&i().createElement(r.Component,{model:r})),i().createElement(G,{isOpen:!!a&&!!s,onClose:()=>t.closeDrawer(),title:`View trace ${s}`,embedded:m,forceNoDrawer:m,investigationButton:u&&l&&i().createElement(c.Button,{variant:"secondary",size:"sm",icon:"plus-square",onClick:e=>{(null==l?void 0:l.onClick)&&l.onClick(e),(0,y.EE)(y.NO.analyse_traces,y.ir.analyse_traces.add_to_investigation_trace_view_clicked),setTimeout(()=>t.closeDrawer(),100)}},P.R_)},a&&i().createElement(a.Component,{model:a})))});const oe=e=>{const[t,n]=i().useState(),r=(0,d.zY)(e),s=(0,d.gG)(r),o=e=>{var t;const n=e.find(e=>"resource.service.name"===e.key);return"="===(null==n?void 0:n.operator)||"=~"===(null==n?void 0:n.operator)?null==n||null===(t=n.value)||void 0===t?void 0:t.replace(/"/g,""):void 0};return(0,a.useEffect)(()=>{n(o(s.state.filters));const e=s.subscribeToState(e=>{n(o(e.filters))});return()=>{e.unsubscribe()}},[s]),t},le=({model:e})=>{var t;const n=(0,l.useReturnToPrevious)(),r=(0,c.useStyles2)(de,!0),s=(0,d.zY)(e),{returnToPreviousSource:u}=s.useState(),m=(0,d.gG)(s),p=(0,d.h7)(s),f=s.state.controls.find(e=>e instanceof o.KE),v=null===(t=s.state.$timeRange)||void 0===t?void 0:t.useState(),g=m.useState(),h=s.getMetricVariable().useState(),[b,w]=i().useState(()=>(0,d.__)(s));return null==p||p.changeValueTo(E.Xn[1].value),(0,a.useEffect)(()=>{w((0,d.__)(s))},[v,g,h,s]),i().createElement("div",{className:r.headerContainer},i().createElement(c.Stack,{gap:1,alignItems:"center",wrap:"wrap",justifyContent:"space-between"},i().createElement(p.Component,{model:p}),m&&i().createElement("div",null,i().createElement(m.Component,{model:m})),i().createElement(c.Stack,{gap:1,alignItems:"center"},i().createElement(c.LinkButton,{href:b,variant:"secondary",icon:"arrow-right",onClick:()=>{n(u||"previous"),(0,y.EE)(y.NO.common,y.ir.common.go_to_full_app_clicked)}},"Traces Drilldown"),f&&i().createElement(f.Component,{model:f}))))},ce=({controls:e,model:t})=>{const n=(0,c.useStyles2)(de),[r,a]=i().useState(!1),s=oe(t),m=(0,d.zY)(t),p=o.jh.lookupVariable(u.EY,m),f=(0,d.gG)(m),v=(0,d.h7)(m);function g(){const e=(0,c.useStyles2)(de);return i().createElement("div",{className:e.menuHeader},i().createElement("h5",null,"Grafana Traces Drilldown v","1.1.4"),i().createElement("div",{className:e.menuHeaderSubtitle},"Last update: ",ae))}const h=i().createElement(c.Menu,{header:i().createElement(g,null)},i().createElement("div",{className:n.menu},l.config.feedbackLinksEnabled&&i().createElement(c.Menu.Item,{label:"Give feedback",ariaLabel:"Give feedback",icon:"comment-alt-message",url:"https://grafana.qualtrics.com/jfe/form/SV_9LUZ21zl3x4vUcS",target:"_blank",onClick:()=>(0,y.EE)(y.NO.common,y.ir.common.global_docs_link_clicked)}),i().createElement(c.Menu.Item,{label:"Documentation",ariaLabel:"Documentation",icon:"external-link-alt",url:"https://grafana.com/docs/grafana/next/explore/simplified-exploration/traces/",target:"_blank",onClick:()=>(0,y.EE)(y.NO.common,y.ir.common.feedback_link_clicked)})));return i().createElement("div",{className:n.headerContainer},i().createElement(c.Stack,{gap:1,justifyContent:"space-between",wrap:"wrap"},i().createElement(c.Stack,{gap:1,alignItems:"center",wrap:"wrap"},p&&i().createElement(c.Stack,{gap:0,alignItems:"center"},i().createElement("div",{className:n.datasourceLabel},"Data source"),i().createElement(p.Component,{model:p}))),i().createElement("div",{className:n.controls},i().createElement(N,{serviceName:s||"",model:t}),i().createElement(c.Dropdown,{overlay:h,onVisibleChange:()=>a(!r)},i().createElement(c.Button,{variant:"secondary",icon:"info-circle"},"Need help",i().createElement(c.Icon,{className:n.helpIcon,name:r?"angle-up":"angle-down",size:"lg"}))),e.map(e=>i().createElement(e.Component,{key:e.state.key,model:e})))),i().createElement(c.Stack,{gap:1,alignItems:"center",wrap:"wrap"},i().createElement(c.Stack,{gap:0,alignItems:"center"},i().createElement("div",{className:n.datasourceLabel},"Filters"),v&&i().createElement(v.Component,{model:v})),f&&i().createElement("div",null,i().createElement(f.Component,{model:f}))))};function ue(e){return new o.Pj({variables:[new o.mI({name:u.EY,label:"Data source",value:e.initialDS,pluginId:"tempo",isReadOnly:e.embedded}),new x.x({name:u.CE,isReadOnly:e.embedded}),new Z({initialFilters:e.initialFilters,embedderName:e.embedderName,embedded:e.embedded}),new o.yP({name:u.PU,hide:O.zL.hideVariable}),new o.yP({name:u.z,defaultToAll:!1,value:e.initialGroupBy}),new o.yP({name:u.gP,defaultToAll:!1}),new o.yP({name:u.pf,defaultToAll:!1,hide:O.zL.hideVariable}),new o.yP({name:u.xc,defaultToAll:!1,hide:O.zL.hideVariable})]})}function de(e,t){return{bodyContainer:(0,r.css)({label:"bodyContainer",flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),container:(0,r.css)({label:"container",flexGrow:1,display:"flex",gap:e.spacing(1),minHeight:"100%",flexDirection:"column",padding:`0 ${e.spacing(2)} ${e.spacing(2)} ${e.spacing(2)}`,overflow:"auto",maxHeight:"100%",position:"relative"}),drawerHeader:(0,r.css)({display:"flex",justifyContent:"space-between",alignItems:"center",borderBottom:`1px solid ${e.colors.border.weak}`,paddingBottom:e.spacing(2),marginBottom:e.spacing(2),h3:{margin:0}}),drawerHeaderButtons:(0,r.css)({display:"flex",justifyContent:"flex-end",gap:e.spacing(1.5)}),body:(0,r.css)({label:"body",flexGrow:1,display:"flex",flexDirection:"column",gap:e.spacing(1)}),headerContainer:(0,r.css)({label:"headerContainer",backgroundColor:t?e.colors.background.primary:e.colors.background.canvas,display:"flex",flexDirection:"column",position:"sticky",top:0,zIndex:3,padding:`${e.spacing(1.5)} 0`,gap:e.spacing(1)}),datasourceLabel:(0,r.css)({label:"datasourceLabel",fontSize:"12px",padding:`0 ${e.spacing(1)}`,height:"32px",display:"flex",alignItems:"center",justifyContent:"flex-start",fontWeight:e.typography.fontWeightMedium,position:"relative",right:-1,width:"90px"}),controls:(0,r.css)({label:"controls",display:"flex",gap:e.spacing(1),zIndex:3,flexWrap:"wrap"}),menu:(0,r.css)({label:"menu","svg, span":{color:e.colors.text.link}}),menuHeader:r.css`
      padding: ${e.spacing(.5,1)};
      white-space: nowrap;
    `,menuHeaderSubtitle:r.css`
      color: ${e.colors.text.secondary};
      font-size: ${e.typography.bodySmall.fontSize};
    `,tooltip:(0,r.css)({label:"tooltip",fontSize:"14px",lineHeight:"22px",width:"180px",textAlign:"center"}),helpIcon:(0,r.css)({label:"helpIcon",marginLeft:e.spacing(1)}),filters:(0,r.css)({label:"filters",marginTop:e.spacing(1),display:"flex",gap:e.spacing(1)})}}},2645:(e,t,n)=>{"use strict";n.d(t,{G:()=>p});var r,a,i,s=n(6089),o=n(118),l=n(2007),c=n(5959),u=n.n(c),d=n(3049),m=n(9504);class p extends o.Bs{}i=({model:e})=>{const t=(0,l.useTheme2)(),n=(0,l.useStyles2)(v),{component:r}=e.useState();return u().createElement("div",{className:n.container,"data-testid":m.b.loadingState},u().createElement(d.z,{baseColor:t.colors.emphasize(t.colors.background.secondary),highlightColor:t.colors.emphasize(t.colors.background.secondary,.1),borderRadius:t.shape.radius.default},r()))},(a="Component")in(r=p)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i;const f=(0,s.keyframes)({"0%":{opacity:0},"100%":{opacity:1}});function v(){return{container:(0,s.css)({label:"loading-state-scene",animationName:f,animationDelay:"100ms",animationTimingFunction:"ease-in",animationDuration:"100ms",animationFillMode:"backwards"})}}},2860:(e,t,n)=>{"use strict";n.d(t,{M:()=>o});var r=n(5959),a=n.n(r),i=n(2007),s=n(6089);const o=({isStreaming:e,iconSize:t=14})=>{const n=(0,i.useStyles2)(l,t);return e?a().createElement(i.Tooltip,{content:"Streaming"},a().createElement(i.Icon,{name:"circle-mono",size:"md",className:n.streamingIndicator})):null},l=(e,t)=>({streamingIndicator:(0,s.css)({width:`${t}px`,height:`${t}px`,backgroundColor:e.colors.success.text,fill:e.colors.success.text,borderRadius:"50%",display:"inline-block"})})},3247:(e,t,n)=>{"use strict";n.d(t,{G:()=>i,h:()=>s});var r=n(1269),a=n(7781);const i=e=>[{topic:a.DataTopic.Annotations,operator:()=>t=>t.pipe((0,r.map)(t=>t.map(t=>{if("exemplar"===t.name){const n=t.fields.find(e=>"traceId"===e.name);n&&(n.config.links=[{title:"View trace",url:"#${__value.raw}",onClick:t=>{var n,r,a;t.e.stopPropagation();const i=null===(a=t.e.target)||void 0===a||null===(r=a.parentElement)||void 0===r||null===(n=r.parentElement)||void 0===n?void 0:n.href;if(!i||-1===i.indexOf("#"))return;const s=i.split("#")[1];s&&""!==s&&(null==e||e(s))}}])}return t})))}],s=()=>[{topic:a.DataTopic.Annotations,operator:()=>e=>e.pipe((0,r.map)(e=>e.filter(e=>"exemplar"!==e.name)))}]},3733:(e,t,n)=>{"use strict";n.d(t,{p:()=>g});var r=n(5959),a=n.n(r),i=n(2007),s=n(6089),o=n(1792);const l=n.p+"944c737f589d02ecf603.svg",c=n.p+"e79edcfbe2068fae2364.svg";var u=n(3241);const d=(e=50)=>{const[t,n]=(0,r.useState)({x:null,y:null});return(0,r.useEffect)(()=>{const t=(0,u.throttle)(e=>{n({x:e.clientX,y:e.clientY})},e);return window.addEventListener("mousemove",t),()=>{window.removeEventListener("mousemove",t)}},[e]),t},m=({width:e="auto",height:t,show404:n=!1})=>{const r=(0,i.useTheme2)(),{x:s,y:u}=d(),m=(0,i.useStyles2)(p,s,u,n);return a().createElement(o.A,{src:r.isDark?l:c,className:m.svg,height:t,width:e})};m.displayName="GrotNotFound";const p=(e,t,n,r)=>{const{innerWidth:a,innerHeight:i}=window,o=n&&n/i,l=t&&t/a,c=null!==o?f(o,-20,5):0,u=null!==l?f(l,-5,5):0;return{svg:(0,s.css)({"#grot-404-arm, #grot-404-magnifier":{transform:`rotate(${c}deg) translateX(${u}%)`,transformOrigin:"center",transition:"transform 50ms linear"},"#grot-404-text":{display:r?"block":"none"}})}},f=(e,t,n)=>e*(n-t)+t;var v=n(9504);const g=({message:e,remedyMessage:t,imgWidth:n,padding:r})=>{const s=(0,i.useStyles2)(h,r);return a().createElement("div",{className:s.container,"data-testid":v.b.emptyState},a().createElement(i.Stack,{direction:"column",alignItems:"center",gap:3},a().createElement(m,{width:null!=n?n:300}),"string"==typeof e&&a().createElement(i.Text,{textAlignment:"center",variant:"h5"},e),"string"!=typeof e&&e,t&&a().createElement("div",{className:s.remedy},a().createElement(i.Stack,{gap:.5,alignItems:"center"},a().createElement(i.Icon,{name:"info-circle"}),a().createElement(i.Text,{textAlignment:"center",variant:"body"},t)))))};function h(e,t){return{container:(0,s.css)({width:"100%",display:"flex",justifyContent:"space-evenly",flexDirection:"column",padding:t||0}),remedy:(0,s.css)({marginBottom:e.spacing(4)})}}g.displayName="EmptyState"},3761:(e,t,n)=>{"use strict";n.d(t,{$L:()=>i,Xn:()=>a,iH:()=>r});const r="span.db.system.name",a=[{label:"Root spans",value:"nestedSetParent<0",filter:{key:"nestedSetParent",operator:"<",value:"0"},description:"Focus your analysis on the root span of each trace"},{label:"All spans",value:"true",filter:{key:"",operator:"",value:!0},description:"View and analyse raw span data. This option may result in long query times."},{label:"Server spans",value:"kind=server",filter:{key:"kind",operator:"=",value:"server"},description:"Explore server-specific segments of traces"},{label:"Consumer spans",value:"kind=consumer",filter:{key:"kind",operator:"=",value:"consumer"},description:"Analyze interactions initiated by consumer services"},{label:"Database calls",value:`${r}!=""`,filter:{key:r,operator:"!=",value:'""'},description:"Evaluate the performance issues in database interactions"}],i=e=>a.find(t=>t.value===e)},4524:(e,t,n)=>{"use strict";n.d(t,{Fh:()=>x,GD:()=>w,R_:()=>g});var r=n(7781),a=n(118),i=n(5959),s=n.n(i),o=n(1793),l=n(8531),c=n(6338),u=n(1051),d=n(1269);function m(e,t,n,r,a,i,s){try{var o=e[i](s),l=o.value}catch(e){return void n(e)}o.done?t(l):Promise.resolve(l).then(r,a)}function p(e){return function(){var t=this,n=arguments;return new Promise(function(r,a){var i=e.apply(t,n);function s(e){m(i,r,a,s,o,"next",e)}function o(e){m(i,r,a,s,o,"throw",e)}s(void 0)})}}function f(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function v(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const g="Add to investigation",h="grafana-exploretraces-app/investigation/v1",b="investigations_divider",y="Investigations";class w extends a.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){super(e),this.addActivationHandler(()=>{const e=[{text:"Navigation",type:"group"},{text:"Explore",iconClassName:"compass",href:S(this),onClick:()=>O()}];this.setState({body:new a.Lw({items:e})});const t=(0,u.zY)(this),n=(0,u.U4)(t),r=new o.L({query:this.state.query,dsUid:n});r.activate(),this.setState({addToInvestigationButton:r}),this._subs.add(null==r?void 0:r.subscribeToState(()=>{var e;e=this,p(function*(){const t=e.state.addToInvestigationButton;if(t){var n;const l=yield x(t);var r;const u=null!==(r=null===(n=e.state.body)||void 0===n?void 0:n.state.items)&&void 0!==r?r:[],d=u.find(e=>e.text===g);var a,i,s,o;l&&(d?d&&(null===(a=e.state.body)||void 0===a||a.setItems(u.filter(e=>!1===[b,y,g].includes(e.text)))):(null===(i=e.state.body)||void 0===i||i.addItem({text:b,type:"divider"}),null===(s=e.state.body)||void 0===s||s.addItem({text:y,type:"group"}),null===(o=e.state.body)||void 0===o||o.addItem({text:g,iconClassName:"plus-square",onClick:e=>{l.onClick&&l.onClick(e),(0,c.EE)(c.NO.analyse_traces,c.ir.analyse_traces.add_to_investigation_clicked)}})))}})()})),r.setState(v(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){f(e,t,n[t])})}return e}({},r.state),{labelValue:this.state.labelValue}))})}}f(w,"Component",({model:e})=>{const{body:t}=e.useState();return t?s().createElement(t.Component,{model:t}):s().createElement(s().Fragment,null)});const S=e=>{const t=(0,u.zY)(e),n=(0,u.U4)(t),i=a.jh.getTimeRange(e).state.value,s=(0,u.Is)(e),o=JSON.stringify({"traces-explore":{range:(0,r.toURLRange)(i.raw),queries:[{refId:"A",datasource:n,query:e.state.query,step:s}]}});var c;const d=null!==(c=l.config.appSubUrl)&&void 0!==c?c:"";return r.urlUtil.renderUrl(`${d}/explore`,{panes:o,schemaVersion:1})},O=()=>{(0,c.EE)(c.NO.analyse_traces,c.ir.analyse_traces.open_in_explore_clicked)},x=e=>p(function*(){const t=e.state.context;if(void 0!==l.getPluginLinkExtensions){return(0,l.getPluginLinkExtensions)({extensionPointId:h,context:t}).extensions[0]}if(void 0!==l.getObservablePluginLinks){return(yield(0,d.firstValueFrom)((0,l.getObservablePluginLinks)({extensionPointId:h,context:t})))[0]}})()},4917:(e,t,n)=>{"use strict";n.d(t,{F:()=>a,p:()=>r});const r=e=>{var t;const n=e.fields.find(e=>"Baseline"===e.name),r=e.fields.find(e=>"Selection"===e.name);let a=0,i=0;for(let e=0;e<((null==n||null===(t=n.values)||void 0===t?void 0:t.length)||0);e++){const t=((null==r?void 0:r.values[e])||0)-((null==n?void 0:n.values[e])||0);Math.abs(t)>Math.abs(a||0)&&(a=t,i=e)}return{maxDifference:a,maxDifferenceIndex:i}},a=e=>{if("duration"!==e)return{query:"status = error",type:"auto"}}},6003:(e,t,n)=>{"use strict";n.d(t,{x:()=>g});var r=n(5959),a=n.n(r),i=n(118),s=n(3761),o=n(2007),l=n(6089),c=n(5225),u=n(6338);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function m(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const p=e=>{const t=(0,o.useStyles2)(h);return a().createElement(c.c.Menu,m(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){d(e,t,n[t])})}return e}({},e),{className:t.customMenu}))};function f({selectProps:e}){const t=e.menuIsOpen?"angle-up":"angle-down";return a().createElement(o.Icon,{name:t,size:"md"})}const v=()=>{const e=(0,o.useStyles2)(h);return a().createElement("div",{className:e.heading},a().createElement(o.Text,{weight:"bold",variant:"bodySmall",color:"secondary"},"Primary signal"))};class g extends i.yP{}d(g,"Component",({model:e})=>{const t=(0,o.useStyles2)(h),{value:n,isReadOnly:i}=e.useState();(0,r.useEffect)(()=>{n||e.changeValueTo(i?s.Xn[1].value:s.Xn[0].value)});const l=s.Xn.slice(0,2),c=s.Xn.find(e=>e.value===n);c&&!l.some(e=>e.filter.key===c.filter.key)&&l.push(c);const d=s.Xn.filter(e=>!l.some(t=>t.value===e.value)),m=t=>{(0,u.EE)(u.NO.analyse_traces,u.ir.analyse_traces.primary_signal_changed,{primary_signal:t}),e.changeValueTo(t,void 0,!0)};return i?a().createElement(a().Fragment,null):a().createElement(a().Fragment,null,a().createElement(o.RadioButtonGroup,{options:l,value:n,onChange:m,disabled:i,className:t.buttonGroup}),a().createElement(o.Select,{options:[{label:"Primary signal",options:d}],value:"",placeholder:"",isSearchable:!1,isClearable:!1,width:4,onChange:e=>m(e.value),className:t.select,components:{IndicatorSeparator:()=>null,SingleValue:()=>null,Menu:p,DropdownIndicator:f,GroupHeading:v}}))});const h=e=>({select:l.css`
    [class$='input-suffix'] {
      position: absolute;
      z-index: 2;
    }

    :focus-within {
      outline: none;
      box-shadow: none;
    }

    > div {
      padding: 0;
    }

    input {
      opacity: 0 !important;
    }

    border-radius: 0 2px 2px 0;
    border-left: none;
  `,buttonGroup:l.css`
    border-radius: 2px 0 0 2px;
  `,customMenu:l.css`
    width: 230px;

    [class$='grafana-select-option-grafana-select-option-focused'] {
      background: transparent;

      ::before {
        display: none;
      }
    }
  `,heading:(0,l.css)({padding:e.spacing(1,1,.75,.75),borderLeft:"2px solid transparent",borderBottom:`1px solid ${e.colors.border.weak}`})})},6338:(e,t,n)=>{"use strict";n.d(t,{EE:()=>i,NO:()=>s,ir:()=>o});var r=n(8531),a=n(2533);const i=(e,t,n)=>{(0,r.reportInteraction)(((e,t)=>`${a.id.replace(/-/g,"_")}_${e}_${t}`)(e,t),n)},s={analyse_traces:"analyse_traces",home:"home",common:"common"},o={[s.analyse_traces]:{action_view_changed:"action_view_changed",breakdown_group_by_changed:"breakdown_group_by_changed",breakdown_add_to_filters_clicked:"breakdown_add_to_filters_clicked",comparison_add_to_filters_clicked:"comparison_add_to_filters_clicked",select_attribute_in_comparison_clicked:"select_attribute_in_comparison_clicked",layout_type_changed:"layout_type_changed",start_investigation:"start_investigation",stop_investigation:"stop_investigation",open_trace:"open_trace",open_in_explore_clicked:"open_in_explore_clicked",add_to_investigation_clicked:"add_to_investigation_clicked",add_to_investigation_trace_view_clicked:"add_to_investigation_trace_view_clicked",span_list_columns_changed:"span_list_columns_changed",toggle_bookmark_clicked:"toggle_bookmark_clicked",primary_signal_changed:"primary_signal_changed",exception_message_clicked:"exception_message_clicked"},[s.home]:{homepage_initialized:"homepage_initialized",panel_row_clicked:"panel_row_clicked",explore_traces_clicked:"explore_traces_clicked",read_documentation_clicked:"read_documentation_clicked",filter_changed:"filter_changed",go_to_bookmark_clicked:"go_to_bookmark_clicked"},[s.common]:{metric_changed:"metric_changed",new_filter_added_manually:"new_filter_added_manually",app_initialized:"app_initialized",global_docs_link_clicked:"global_docs_link_clicked",metric_docs_link_clicked:"metric_docs_link_clicked",feedback_link_clicked:"feedback_link_clicked",go_to_full_app_clicked:"go_to_full_app_clicked"}}},6374:(e,t,n)=>{"use strict";n.d(t,{$:()=>l});var r=n(118),a=n(7975);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){i(e,t,n[t])})}return e}function o(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class l extends r.dt{_onActivateStep(){const e=(0,a.oM)(this,this.state.maxDataPoints);this.setState({queries:this.state.queries.map(t=>o(s({},t),{step:e}))});r.jh.getTimeRange(this).subscribeToState((e,t)=>{if(e.value.from!==t.value.from||e.value.to!==t.value.to){const e=(0,a.oM)(this,this.state.maxDataPoints);this.setState({queries:this.state.queries.map(t=>o(s({},t),{step:e}))})}})}constructor(e){super(e),this.addActivationHandler(this._onActivateStep.bind(this))}}},6997:(e,t,n)=>{"use strict";n.d(t,{z:()=>i});var r=n(118),a=n(2007);const i=(e,t)=>{const n="errors"===e||!1,i=r.d0.timeseries().setOption("legend",{showLegend:!1}).setCustomFieldConfig("drawStyle",a.DrawStyle.Bars).setCustomFieldConfig("stacking",{mode:a.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",75).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("axisLabel","Rate").setOverrides(e=>{e.matchFieldsWithNameByRegex(".*").overrideColor({mode:"fixed",fixedColor:n?"semi-dark-red":"green"})}).setOption("tooltip",{mode:a.TooltipDisplayMode.Multi});return void 0!==t&&i.setCustomFieldConfig("axisWidth",t),i}},7197:(e,t,n)=>{"use strict";n.d(t,{Mu:()=>A,jD:()=>L,FC:()=>$});var r=n(5959),a=n.n(r),i=n(118),s=n(7781),o=n(1829),l=n(775),c=n(2645),u=n(8327),d=n(6997),m=n(9840),p=n(6374),f=n(6089),v=n(2007),g=n(2860),h=n(1051),b=n(8855),y=n(9938),w=n(892),S=n(3241),O=n(6338);function x(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class E extends i.Bs{constructor({selection:e}){super({selection:e}),x(this,"startInvestigation",()=>{const e=(0,h.YX)(this);e.setState({selection:this.state.selection}),(0,h.H)(e.state.actionView)||e.setActionView("comparison"),(0,O.EE)(O.NO.analyse_traces,O.ir.analyse_traces.start_investigation,{selection:this.state.selection,metric:(0,h.GK)(this)})})}}function j(e){return{wrapper:(0,f.css)({display:"flex",gap:"16px",alignItems:"center"}),placeholder:(0,f.css)({color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize,display:"flex",gap:e.spacing.x0_5})}}x(E,"Component",({model:e})=>{const{selection:t}=(0,h.YX)(e).useState(),n=(0,v.useStyles2)(j),r="auto"===(null==t?void 0:t.type),i=r?"Slowest traces are selected, navigate to the Comparison or Slow Traces tab for more details.":void 0;return a().createElement("div",{className:n.wrapper},a().createElement(v.Button,{variant:"secondary",size:"sm",fill:"solid",disabled:r,icon:"bolt",onClick:e.startInvestigation,tooltip:i},r?"Slowest traces selected":"Select slowest traces"))});var k=n(3247),C=n(8531);function P({serviceName:e,model:t}){const{isLoading:n,component:r}=(0,C.usePluginComponent)("grafana-asserts-app/insights-timeline-widget/v1"),s=(0,v.useStyles2)(_),o=i.jh.getTimeRange(t).useState(),l=(0,h.H_)(t).state.value;let c=[];"errors"===l?c=["critical","warning"]:"rate"===l&&(c=["info"]);let u=[];return"duration"===l&&(u=["latency"]),!n&&r&&o&&e?a().createElement(r,{serviceName:e,start:o.from.valueOf(),end:o.to.valueOf(),filterBySeverity:c,filterBySummaryKeywords:u,label:a().createElement("div",{className:s.label},"Insights")}):null}function _(e){return{label:(0,f.css)({fontSize:"12px",color:e.colors.text.secondary,marginLeft:"35px",marginTop:"-3px"})}}var N=n(2395);function D(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function T(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){D(e,t,n[t])})}return e}function I(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class A extends i.Bs{isDuration(){return"duration"===(0,h.H_)(this).state.value}_onActivate(){const e=(0,h.H_)(this).state.value;this.setState({$data:new i.Es({$data:new p.$({maxDataPoints:this.isDuration()?24:64,datasource:o.Vl,queries:[this.isDuration()?(0,w.z)():(0,m.l)({metric:e,sample:!0})]}),transformations:this.isDuration()?[...(0,k.h)()]:[...(0,k.G)((0,h.w$)(this))]}),panel:this.getVizPanel()})}getVizPanel(){const e=(0,h.H_)(this).state.value;var t;return this.isDuration()?(0,b.QA)(this,null!==(t=this.state.yBuckets)&&void 0!==t?t:[]):this.getRateOrErrorVizPanel(e)}getRateOrErrorVizPanel(e){const t=(0,d.z)(e,70).setHoverHeader(!0).setDisplayMode("transparent");return"rate"===e?t.setCustomFieldConfig("axisLabel","span/s"):"errors"===e&&t.setCustomFieldConfig("axisLabel","error/s").setColor({fixedColor:"semi-dark-red",mode:"fixed"}),new i.G1({direction:"row",children:[new i.vA({body:t.build()})]})}buildSelectionAnnotation(e){var t,n,r,a;if(!(0,h.H)(e.actionView))return;const i=null===(n=e.selection)||void 0===n||null===(t=n.raw)||void 0===t?void 0:t.x,o=null===(a=e.selection)||void 0===a||null===(r=a.raw)||void 0===r?void 0:r.y,l=(0,s.arrayToDataFrame)([{time:(null==i?void 0:i.from)||0,xMin:(null==i?void 0:i.from)||0,xMax:(null==i?void 0:i.to)||0,timeEnd:(null==i?void 0:i.to)||0,yMin:null==o?void 0:o.from,yMax:null==o?void 0:o.to,isRegion:!0,fillOpacity:.15,lineWidth:1,lineStyle:"solid",color:y._E,text:"Comparison selection"}]);return l.name="xymark",[l]}constructor(e){super(T({yBuckets:[],actions:[],isStreaming:!1},e)),this.addActivationHandler(()=>{this._onActivate();const e=i.jh.getData(this),t=(0,h.YX)(this),n=i.jh.getTimeRange(this);this._subs.add(e.subscribeToState(r=>{var a,d,m;if(this.setState({isStreaming:(null===(a=r.data)||void 0===a?void 0:a.state)===s.LoadingState.Streaming}),(null===(d=r.data)||void 0===d?void 0:d.state)===s.LoadingState.Done)if(0===r.data.series.length||0===r.data.series[0].length||(0,h.W6)(r))this.setState({panel:new i.G1({children:[new i.vA({body:new l.v({message:o.PL,imgWidth:150})})]})});else{let a=[];if(this.isDuration()){var p,f;if(a=$((null===(p=e.state.data)||void 0===p?void 0:p.series)||[]),t.state.selection&&(null===(f=r.data)||void 0===f?void 0:f.state)===s.LoadingState.Done){var v,g;const n=this.buildSelectionAnnotation(t.state);n&&!(null===(g=e.state.data)||void 0===g||null===(v=g.annotations)||void 0===v?void 0:v.length)&&e.setState({data:I(T({},e.state.data),{annotations:n})})}if(null==a?void 0:a.length){var y;const{minDuration:e,minBucket:r}=L(a),i={type:"auto"};(0,h.Kf)(this).changeValueTo(e),(0,h.F3)(this).changeValueTo((0,b.xx)(r-1,a,.3)),i.duration={from:e,to:""},i.raw={x:{from:1e3*n.state.value.from.unix(),to:1e3*n.state.value.to.unix()},y:{from:r-.5,to:a.length-.5}},this.setState({actions:[new E({selection:i})]}),(null===(y=t.state.selection)||void 0===y?void 0:y.duration)&&"auto"!==t.state.selection.type||t.setState({selection:i})}}this.setState({yBuckets:a,panel:this.getVizPanel()})}else(null===(m=r.data)||void 0===m?void 0:m.state)===s.LoadingState.Loading&&this.setState({panel:new i.G1({direction:"column",children:[new c.G({component:()=>(0,u.NO)(1)})]})})})),this._subs.add(t.subscribeToState((t,n)=>{var r;if((null===(r=e.state.data)||void 0===r?void 0:r.state)===s.LoadingState.Done&&(!(0,S.isEqual)(t.selection,n.selection)||t.actionView!==n.actionView)&&this.isDuration()){const n=this.buildSelectionAnnotation(t);e.setState({data:I(T({},e.state.data),{annotations:n})})}}))})}}D(A,"Component",({model:e})=>{const{panel:t,actions:n,isStreaming:r}=e.useState(),{value:i}=(0,h.H_)(e).useState(),s=(0,v.useStyles2)(V),o=(0,N.oL)(e);if(!t)return;const l="duration"===i?"Click and drag to compare selection with baseline.":"";return a().createElement("div",{className:s.container},a().createElement("div",{className:s.headerContainer},a().createElement("div",{className:s.titleContainer},a().createElement("div",{className:s.titleRadioWrapper},a().createElement(v.RadioButtonList,{name:`metric-${i}`,options:[{title:"",value:"selected"}],value:"selected"}),a().createElement("span",null,(()=>{switch(i){case"errors":return"Errors rate";case"rate":return"Span rate";case"duration":return"Histogram by duration";default:return""}})())),l&&a().createElement("div",{className:s.subtitle},l)),a().createElement("div",{className:s.actions},r&&a().createElement(g.M,{isStreaming:!0,iconSize:10}),null==n?void 0:n.map(e=>a().createElement(e.Component,{model:e,key:e.state.key})))),a().createElement(t.Component,{model:t}),a().createElement(P,{serviceName:o||"",model:e}))});const $=e=>e.map(e=>parseFloat(e.fields[1].name)).sort((e,t)=>e-t),L=e=>{const t=Math.floor(e.length/4);let n=e.length-t-1;return n<0&&(n=0),{minDuration:(0,b.xx)(n-1,e),minBucket:n}};function V(e){return{container:(0,f.css)({width:"100%",display:"flex",flexDirection:"column",border:`1px solid ${e.colors.border.weak}`,borderRadius:"2px",background:e.colors.background.primary,".show-on-hover":{display:"none"},"section, section:hover":{borderColor:"transparent"},"& .u-select":{border:"1px solid #ffffff75"}}),headerContainer:(0,f.css)({width:"100%",display:"flex",flexDirection:"row",padding:"8px",gap:"8px",justifyContent:"space-between",alignItems:"flex-start",fontWeight:e.typography.fontWeightBold}),titleContainer:(0,f.css)({display:"flex",flexDirection:"column",gap:"4px"}),titleRadioWrapper:(0,f.css)({display:"flex",alignItems:"center"}),actions:(0,f.css)({display:"flex",gap:"8px",alignItems:"center"}),subtitle:(0,f.css)({display:"flex",color:e.colors.text.secondary,fontSize:"12px",fontWeight:400,"& svg":{margin:"0 2px"}})}}},7975:(e,t,n)=>{"use strict";n.d(t,{KS:()=>m,a3:()=>d,oM:()=>p});var r=n(3241),a=n(118),i=n(2689);const s=1e3,o=1e6,l=6e7,c=36e8,u=(Math.log10(s),[{unit:"d",microseconds:864e8,ofPrevious:24},{unit:"h",microseconds:c,ofPrevious:60},{unit:"m",microseconds:l,ofPrevious:60},{unit:"s",microseconds:o,ofPrevious:1e3},{unit:"ms",microseconds:s,ofPrevious:1e3},{unit:"μs",microseconds:1,ofPrevious:1e3}]),d=e=>{const[t,n]=(0,r.dropWhile)(u,({microseconds:t},n)=>n<u.length-1&&t>e);if(1e3===t.ofPrevious)return`${(0,r.round)(e/t.microseconds,2)}${t.unit}`;let a=Math.floor(e/t.microseconds),i=e/n.microseconds%t.ofPrevious;const s=Math.round(i);s===t.ofPrevious?(a+=1,i=0):i=s;const o=`${a}${t.unit}`;if(0===i)return o;return`${o} ${`${i}${n.unit}`}`},m=(e,t=50)=>Math.floor(e/t)||1,p=(e,t)=>{const n=a.jh.getTimeRange(e),r=n.state.value.from.unix(),s=n.state.value.to.unix(),o=(0,i.duration)(s-r,"s");return`${m(o.asSeconds(),t)}s`}},8327:(e,t,n)=>{"use strict";n.d(t,{hE:()=>_,NO:()=>D});var r,a,i,s=n(5959),o=n.n(s),l=n(7781),c=n(118),u=n(775),d=n(6089),m=n(2007),p=n(3049),f=n(2645),v=n(9504);class g extends c.Bs{}i=({model:e})=>{const{message:t}=e.useState();return o().createElement(m.Alert,{title:"Query error",severity:"error","data-testid":v.b.errorState},t)},(a="Component")in(r=g)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i;var h=n(3241);const b=e=>{const t=(0,m.useStyles2)(y),{searchQuery:n,onSearchQueryChange:r}=e;return o().createElement(m.Field,{className:t.searchField},o().createElement(m.Input,{placeholder:"Search",prefix:o().createElement(m.Icon,{name:"search"}),value:n,onChange:r,id:"searchFieldInput"}))};function y(e){return{searchField:(0,d.css)({marginBottom:e.spacing(1)})}}var w=n(1051),S=n(1829);function O(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){O(e,t,n[t])})}return e}function E(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function j(e){return E(x({},e),{fields:e.fields.map(e=>E(x({},e),{values:e.values}))})}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function C(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){k(e,t,n[t])})}return e}function P(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class _ extends c.Bs{renderFilteredData(e){e.series&&e.series.length>0?this.performRepeat(e):this.state.body.setState({children:[new c.vA({body:new u.v({message:"No data for search term",padding:"32px"})})]})}groupSeriesBy(e,t){const n=e.series.reduce((e,n)=>{var r,a;const i=null===(a=n.fields.find(e=>e.type===l.FieldType.number))||void 0===a||null===(r=a.labels)||void 0===r?void 0:r[t];return i?(e[i]||(e[i]=[]),e[i].push(n),e):e},{}),r=[];for(const e in n){const t=n[e].sort((e,t)=>{var n;return(null===(n=e.name)||void 0===n?void 0:n.localeCompare(t.name))||0}),a=j(t[0]);t.slice(1,t.length).forEach(e=>a.fields.push(e.fields[1])),r.push((0,l.sortDataFrame)(a,0))}return r}performRepeat(e){const t=[];let n=e.series;this.state.groupBy&&(n=this.groupSeriesBy(e,(0,w.pl)(this).getValueText()));for(let r=0;r<n.length;r++){if(0===n[r].fields.filter(e=>e.type===l.FieldType.number).reduce((e,t)=>e+t.values.reduce((e,t)=>e+(t||0),0)||0,0))continue;const a=this.state.getLayoutChild(e,n[r],r);t.push(a)}this.state.body.setState({children:t})}constructor(e){super(e),k(this,"onSearchQueryChange",e=>{this.setState({searchQuery:e.currentTarget.value})}),k(this,"onSearchQueryChangeDebounced",(0,h.debounce)(e=>{var t;const n=c.jh.getData(this),r=P(C({},n.state.data),{series:null===(t=n.state.data)||void 0===t?void 0:t.series.filter(I(e))});this.renderFilteredData(r)},250)),this.addActivationHandler(()=>{const e=c.jh.getData(this);this._subs.add(e.subscribeToState(e=>{var t,n,r,a,i;if((null===(t=e.data)||void 0===t?void 0:t.state)===l.LoadingState.Done||(null===(n=e.data)||void 0===n?void 0:n.state)===l.LoadingState.Streaming){if(0===e.data.series.length&&(null===(a=e.data)||void 0===a?void 0:a.state)!==l.LoadingState.Streaming)this.state.body.setState({children:[new c.vA({body:new u.v({message:S.PL,remedyMessage:S.a5,padding:"32px"})})]});else if((null===(i=e.data)||void 0===i?void 0:i.state)===l.LoadingState.Done){var s;const t=P(C({},e.data),{series:null===(s=e.data)||void 0===s?void 0:s.series.filter(I(this.state.searchQuery))});this.renderFilteredData(t),this.publishEvent(new S.sv({series:e.data.series}),!0)}}else if((null===(r=e.data)||void 0===r?void 0:r.state)===l.LoadingState.Error){var o,d,m;this.state.body.setState({children:[new c.gF({children:[new g({message:null!==(m=null===(d=e.data.errors)||void 0===d||null===(o=d[0])||void 0===o?void 0:o.message)&&void 0!==m?m:"An error occurred in the query"})]})]})}else this.state.body.setState({children:[new c.gF({children:[new f.G({component:()=>D(8)})]})]})})),this.subscribeToState((e,t)=>{var n;e.searchQuery!==t.searchQuery&&this.onSearchQueryChangeDebounced(null!==(n=e.searchQuery)&&void 0!==n?n:"")}),e.state.data&&this.performRepeat(e.state.data)})}}function N(){return{container:(0,d.css)({display:"flex",flexDirection:"column",flexGrow:1})}}k(_,"Component",({model:e})=>{const{body:t,searchQuery:n}=e.useState(),r=(0,m.useStyles2)(N);return o().createElement("div",{className:r.container},o().createElement(b,{searchQuery:null!=n?n:"",onSearchQueryChange:e.onSearchQueryChange}),o().createElement(t.Component,{model:t}))});const D=e=>{const t=(0,m.useStyles2)(T);return o().createElement("div",{className:t.container},[...Array(e)].map((e,n)=>o().createElement("div",{className:t.itemContainer,key:n},o().createElement("div",{className:t.header},o().createElement("div",{className:t.title},o().createElement(p.A,{count:1})),o().createElement("div",{className:t.action},o().createElement(p.A,{count:1}))),o().createElement("div",{className:t.yAxis},[...Array(2)].map((e,n)=>o().createElement("div",{className:t.yAxisItem,key:n},o().createElement(p.A,{count:1})))),o().createElement("div",{className:t.xAxis},[...Array(2)].map((e,n)=>o().createElement("div",{className:t.xAxisItem,key:n},o().createElement(p.A,{count:1})))))))};function T(e){return{container:(0,d.css)({display:"grid",gridTemplateColumns:S.MV,gridAutoRows:"200px",rowGap:e.spacing(1),columnGap:e.spacing(1)}),itemContainer:(0,d.css)({backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.background.secondary}`,padding:"5px"}),header:(0,d.css)({display:"flex",justifyContent:"space-between"}),title:(0,d.css)({width:"100px"}),action:(0,d.css)({width:"60px"}),yAxis:(0,d.css)({display:"flex",flexDirection:"column",justifyContent:"space-around",marginTop:"35px"}),yAxisItem:(0,d.css)({width:"60px",height:"55px"}),xAxis:(0,d.css)({display:"flex",justifyContent:"space-evenly"}),xAxisItem:(0,d.css)({width:"55px"})}}const I=e=>t=>{const n=null==e?void 0:e.trim();if(!n)return!0;const r=new RegExp(n,"i");return t.fields.some(e=>!!e.labels&&Object.values(e.labels).find(e=>r.test(e)))}},8855:(e,t,n)=>{"use strict";n.d(t,{QA:()=>s,dX:()=>o,xx:()=>l});var r=n(1051),a=n(6338),i=n(118);function s(e,t){const n=(0,r.YX)(e),s=o().setHoverHeader(!0).setOption("selectionMode","xy").build();return s.setState({extendPanelContext:(e,i)=>{i.onSelectRange=e=>{var i,s,o,c;if(0===e.length)return void n.setState({selection:void 0});const u=e[0],d={type:"manual",raw:u};if(d.timeRange={from:Math.round(((null===(i=u.x)||void 0===i?void 0:i.from)||0)/1e3),to:Math.round(((null===(s=u.x)||void 0===s?void 0:s.to)||0)/1e3)},d.timeRange.from===d.timeRange.to)return;const m=l(((null===(o=e[0].y)||void 0===o?void 0:o.from)||0)-1,t),p=l((null===(c=e[0].y)||void 0===c?void 0:c.to)||0,t);d.duration={from:m,to:p},n.onUserUpdateSelection(d),(0,r.H)(n.state.actionView)||n.setActionView("comparison"),(0,a.EE)(a.NO.analyse_traces,a.ir.analyse_traces.start_investigation,{selection:d,metric:"duration"})}}}),new i.G1({direction:"row",children:[new i.vA({body:s})]})}const o=()=>i.d0.heatmap().setOption("legend",{show:!1}).setOption("yAxis",{unit:"s",axisLabel:"duration"}).setOption("color",{scheme:"Blues",steps:16}).setOption("rowsFrame",{value:"Spans"});function l(e,t,n){if(!t)return"";if(e<0)return"0";const r=t[Math.floor(e)]*(n||1);return!r||isNaN(r)?"":r>=1?`${r.toFixed(0)}s`:`${(1e3*r).toFixed(0)}ms`}},9504:(e,t,n)=>{"use strict";n.d(t,{b:()=>r});const r={emptyState:"data-testid empty-state",errorState:"data-testid error-state",loadingState:"data-testid loading-state"}},9840:(e,t,n)=>{"use strict";n.d(t,{l:()=>i,n:()=>a});var r=n(1829);function a({metric:e,groupByKey:t,extraFilters:n,sample:a=!1}){let i=`${r.ui}`;"errors"===e&&(i+=" && status=error"),n&&(i+=` && ${n}`),t&&t!==r.y2&&(i+=` && ${t} != nil`);let s="rate()";switch(e){case"errors":s="rate()";break;case"duration":s="quantile_over_time(duration, 0.9)"}let o=[];t&&t!==r.y2&&o.push(t);return`{${i}} | ${s} ${o.length?`by(${o.join(", ")})`:""}${a?" with(sample=true)":""}`}function i(e){return{refId:"A",query:a(e),queryType:"traceql",tableType:"spans",limit:100,spss:10,filters:[]}}},9938:(e,t,n)=>{"use strict";n.d(t,{bT:()=>S,_E:()=>O,nF:()=>x,x:()=>k});var r=n(118),a=n(8327),i=n(2007),s=n(1625),o=n(6089),l=n(5959),c=n.n(l),u=n(1051),d=n(806),m=n(4917);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function f(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){p(e,t,n[t])})}return e}class v extends r.Bs{_onActivate(){const{frame:e}=this.state;this.setState(f({},(0,m.p)(e))),this._subs.add(this.subscribeToState((e,t)=>{if(e.frame!==t.frame){const{frame:t}=e;this.setState(f({},(0,m.p)(t)))}}))}getAttribute(){return this.state.frame.name}getValue(){const e=this.state.frame.fields.find(e=>"Value"===e.name);return null==e?void 0:e.values[this.state.maxDifferenceIndex||0]}onAddToFilters(){const e=(0,u.gG)(this),t=this.getAttribute();t&&(0,d.Qt)(e,t,this.getValue())}constructor(e){super(f({},e)),this.addActivationHandler(()=>this._onActivate())}}function g(e){return{container:(0,o.css)({display:"flex",flexDirection:"column",flexGrow:1,height:"100%"}),differenceContainer:(0,o.css)({display:"flex",flexDirection:"column",flexGrow:1,border:`1px solid ${e.colors.secondary.border}`,background:e.colors.background.primary,padding:"8px",marginBottom:e.spacing(2),fontSize:"12px",height:"116px"}),differenceValue:(0,o.css)({fontSize:"36px",fontWeight:"bold",textAlign:"center"}),value:(0,o.css)({textAlign:"center",color:e.colors.secondary.text,textWrap:"nowrap",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}),title:(0,o.css)({fontWeight:500})}}p(v,"Component",({model:e})=>{const{maxDifference:t,maxDifferenceIndex:n,panel:r}=e.useState(),a=(0,i.useStyles2)(g),s=e.getValue();var o;const l=null!==(o=e.state.frame.name)&&void 0!==o?o:"",m=(0,d.D9)((0,u.gG)(e),l,s.replace(/"/g,""));return c().createElement("div",{className:a.container},c().createElement(r.Component,{model:r}),c().createElement("div",{className:a.differenceContainer},void 0!==t&&void 0!==n&&c().createElement(c().Fragment,null,c().createElement(i.Stack,{gap:1,justifyContent:"space-between",alignItems:"center"},c().createElement("div",{className:a.title},"Highest difference"),!m&&c().createElement(i.Button,{size:"sm",variant:"primary",icon:"search-plus",fill:"text",onClick:()=>e.onAddToFilters()},"Add to filters")),c().createElement("div",{className:a.differenceValue},(100*Math.abs(t)).toFixed(0===t?0:2),"%"),c().createElement("div",{className:a.value},s))))});var h=n(1829);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){b(e,t,n[t])})}return e}function w(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const S="#5794F299",O="#FF9930";function x(e,t){return new a.hE({body:new r.gF({templateColumns:h.MV,autoRows:"320px",children:[]}),getLayoutChild:j({},E,e,t)})}const E=e=>e.name||"No name available";function j(e,t,n,a){return(i,s)=>{const o=s.name?e[s.name]:void 0,l=new r.Zv({data:w(y({},i),{series:[y({},s)]})});if(o){const e=o.state.body;return e.setState({frame:s}),e.state.panel.setState({$data:l}),o}const c=k(a).setTitle(t(s)).setData(l),u=n(s);u&&c.setHeaderActions(u);const d=new r.xK({body:new v({frame:s,panel:c.build()})});return s.name&&(e[s.name]=d),d}}function k(e){return r.d0.barchart().setOption("legend",{showLegend:!1}).setOption("tooltip",{mode:s.$N.Multi}).setMax(1).setOverrides(t=>{t.matchFieldsWithName("Value").overrideCustomFieldConfig("axisPlacement",i.AxisPlacement.Hidden),t.matchFieldsWithName("Baseline").overrideColor({mode:"fixed",fixedColor:"duration"===e?S:"semi-dark-green"}).overrideUnit("percentunit"),t.matchFieldsWithName("Selection").overrideColor({mode:"fixed",fixedColor:"duration"===e?O:"semi-dark-red"}).overrideUnit("percentunit")})}}}]);
//# sourceMappingURL=211.js.map?_cache=521089bb432d9ae7374f