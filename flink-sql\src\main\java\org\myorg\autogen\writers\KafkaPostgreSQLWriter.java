package org.myorg.autogen.writers;

import org.apache.flink.table.api.TableEnvironment;
import org.myorg.autogen.configs.WriterConfig;
import org.myorg.autogen.configs.SchemaConfig;

/**
 * Kafka writer implementation for PostgreSQL-compatible format with upsert capability
 * Uses upsert-kafka connector for deduplication
 */
public class KafkaPostgreSQLWriter implements Writer {

    @Override
    public void createSinkTable(TableEnvironment tableEnv, WriterConfig config, SchemaConfig outputSchema, String tableName) {
        StringBuilder ddl = new StringBuilder();
        
        ddl.append("CREATE TABLE ").append(tableName).append(" (\n");
        
        // Generate schema from output schema configuration
        if (outputSchema != null && outputSchema.getColumns() != null) {
            generateSchemaFromConfig(ddl, outputSchema, config.getMode());
        } else {
            // Default schema if none provided
            generateDefaultSchema(ddl, config.getMode());
        }
        
        ddl.append(") WITH (\n");
        
        // Use upsert-kafka connector for PostgreSQL compatibility
        if ("upsert".equalsIgnoreCase(config.getMode())) {
            ddl.append("  'connector' = 'upsert-kafka',\n");
        } else {
            ddl.append("  'connector' = 'kafka',\n");
        }
        
        // Topic configuration
        String topic = config.getTopic() != null ? config.getTopic() : config.getTable();
        ddl.append("  'topic' = '").append(topic).append("',\n");
        
        // Kafka properties
        ddl.append("  'properties.bootstrap.servers' = 'redpanda:9092',\n");
        
        // Key format for upsert mode
        if ("upsert".equalsIgnoreCase(config.getMode())) {
            ddl.append("  'key.format' = 'json',\n");
        }
        
        // PostgreSQL-compatible JSON format
        ddl.append("  'value.format' = 'json',\n");
        ddl.append("  'value.json.timestamp-format.standard' = 'ISO-8601',\n");
        ddl.append("  'value.json.map-null-key.mode' = 'LITERAL',\n");
        ddl.append("  'value.json.map-null-key.literal' = 'null'\n");
        
        ddl.append(")");
        
        System.out.println("Creating PostgreSQL-compatible Kafka sink table with DDL:");
        System.out.println(ddl.toString());
        
        tableEnv.executeSql(ddl.toString());
    }

    @Override
    public String getWriterType() {
        return "KafkaPostgreSQLWriter";
    }

    private void generateSchemaFromConfig(StringBuilder ddl, SchemaConfig schema, String mode) {
        boolean first = true;
        
        // Add regular columns
        for (SchemaConfig.ColumnDefinition column : schema.getColumns()) {
            if (!first) {
                ddl.append(",\n");
            }
            ddl.append("  `").append(column.getName()).append("` ").append(column.getType());
            first = false;
        }
        
        // Add primary key if specified and upsert mode
        if ("upsert".equalsIgnoreCase(mode)) {
            if (schema.getPrimaryKey() != null && !schema.getPrimaryKey().isEmpty()) {
                ddl.append(",\n  PRIMARY KEY (");
                boolean firstKey = true;
                for (String keyColumn : schema.getPrimaryKey()) {
                    if (!firstKey) {
                        ddl.append(", ");
                    }
                    ddl.append("`").append(keyColumn).append("`");
                    firstKey = false;
                }
                ddl.append(") NOT ENFORCED");
            }
        }
        
        ddl.append("\n");
    }

    private void generateDefaultSchema(StringBuilder ddl, String mode) {
        ddl.append("  `id` BIGINT,\n");
        ddl.append("  `name` STRING,\n");
        ddl.append("  `age` INT,\n");
        ddl.append("  `updated_at` TIMESTAMP(3)");
        
        // Primary key for upsert mode
        if ("upsert".equalsIgnoreCase(mode)) {
            ddl.append(",\n  PRIMARY KEY (`id`) NOT ENFORCED");
        }
        
        ddl.append("\n");
    }
}
