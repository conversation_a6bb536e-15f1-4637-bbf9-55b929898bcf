"""
Station Status data model
"""

import uuid
from dataclasses import dataclass
from typing import Optional
import json


@dataclass
class StationStatus:
    """
    Data model for station status events
    """
    id: str
    status: str
    
    def __init__(self, id: Optional[str] = None, status: str = "off"):
        """
        Initialize StationStatus
        
        Args:
            id: Station UUID string. If None, generates a random UUID
            status: Station status ("on" or "off")
        """
        self.id = id if id is not None else str(uuid.uuid4())
        self.status = status
    
    def to_json(self) -> str:
        """Convert to JSON string for Kafka publishing"""
        return json.dumps({
            "id": self.id,
            "status": self.status
        })
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            "id": self.id,
            "status": self.status
        }
    
    @classmethod
    def from_dict(cls, data: dict) -> 'StationStatus':
        """Create StationStatus from dictionary"""
        return cls(
            id=data.get("id"),
            status=data.get("status", "off")
        )
