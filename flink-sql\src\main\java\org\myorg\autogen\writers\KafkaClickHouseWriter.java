package org.myorg.autogen.writers;

import org.apache.flink.table.api.TableEnvironment;
import org.myorg.autogen.configs.WriterConfig;
import org.myorg.autogen.configs.SchemaConfig;

/**
 * Kafka writer implementation for ClickHouse-compatible format
 * Based on the ClickHouse Kafka engine pattern from clickhouse_bootstrap.sql
 */
public class KafkaClickHouseWriter implements Writer {

    @Override
    public void createSinkTable(TableEnvironment tableEnv, WriterConfig config, SchemaConfig outputSchema, String tableName) {
        StringBuilder ddl = new StringBuilder();

        ddl.append("CREATE TABLE ").append(tableName).append(" (\n");

        // Generate topic-specific schema based on the output topic
        String topic = config.getTopic() != null ? config.getTopic() : config.getTable();
        generateSchemaForTopic(ddl, config, topic);

        ddl.append(") WITH (\n");

        // Use upsert-kafka connector exactly like OrderDataStreamJob
        if ("upsert".equalsIgnoreCase(config.getMode())) {
            ddl.append("  'connector' = 'upsert-kafka',\n");
        } else {
            ddl.append("  'connector' = 'kafka',\n");
        }

        // Topic configuration
        ddl.append("  'topic' = '").append(topic).append("',\n");

        // Kafka properties - exactly like OrderDataStreamJob
        ddl.append("  'properties.bootstrap.servers' = 'redpanda:9092',\n");

        // Key format for upsert mode (like OrderDataStreamJob)
        if ("upsert".equalsIgnoreCase(config.getMode())) {
            ddl.append("  'key.format' = 'json',\n");
        }

        // Value format exactly like OrderDataStreamJob
        ddl.append("  'value.format' = 'json'\n");
        
        ddl.append(")");
        
        System.out.println("Creating ClickHouse-compatible Kafka sink table with DDL:");
        System.out.println(ddl.toString());
        
        tableEnv.executeSql(ddl.toString());
    }

    @Override
    public String getWriterType() {
        return "KafkaClickHouseWriter";
    }

    private void generateSchemaForTopic(StringBuilder ddl, WriterConfig config, String topic) {
        System.out.println("Generating sink schema for topic: " + topic);

        if (topic != null && topic.contains("station")) {
            // Station sink schema - matches station output columns
            System.out.println("Using station sink schema");
            ddl.append("  `station_id` STRING,\n");
            ddl.append("  `current_status` STRING");

            // Add primary key for upsert mode
            if ("upsert".equalsIgnoreCase(config.getMode())) {
                ddl.append(",\n  PRIMARY KEY (`station_id`) NOT ENFORCED");
            }
        } else {
            // Order sink schema - matches order output columns
            System.out.println("Using order sink schema");
            ddl.append("  `order_id` STRING,\n");
            ddl.append("  `station_id` STRING,\n");
            ddl.append("  `total_amount` DOUBLE,\n");
            ddl.append("  `order_status` STRING");

            // Add primary key for upsert mode
            if ("upsert".equalsIgnoreCase(config.getMode())) {
                ddl.append(",\n  PRIMARY KEY (`order_id`) NOT ENFORCED");
            }
        }

        ddl.append("\n");
    }

    private void generateDefaultSchema(StringBuilder ddl) {
        // Default schema based on ClickHouse bootstrap example
        ddl.append("  `order_id` STRING,\n");
        ddl.append("  `station_id` STRING,\n");
        ddl.append("  `total_amount` DOUBLE,\n");
        ddl.append("  `order_status` STRING\n");
    }
}
