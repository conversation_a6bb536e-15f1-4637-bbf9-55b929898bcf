"""
Configuration utilities
"""

import os
import random
from typing import List, Optional
import logging

logger = logging.getLogger(__name__)


class Config:
    """
    Configuration manager for fake data generator
    """

    # Database configuration
    DATABASE_HOST = os.getenv("DATABASE_HOST", "localhost")
    DATABASE_PORT = os.getenv("DATABASE_PORT", "5432")
    DATABASE_NAME = os.getenv("DATABASE_NAME", "service-rental-demo")
    DATABASE_USERNAME = os.getenv("DATABASE_USERNAME", "admin")
    DATABASE_PASSWORD = os.getenv("DATABASE_PASSWORD", "admin")

    # Kafka configuration
    KAFKA_HOST = os.getenv("KAFKA_HOST", "localhost")
    KAFKA_PORT = os.getenv("KAFKA_PORT", "19092")
    KAFKA_TOPIC = os.getenv("KAFKA_TOPIC", "cf.iot.station.status")
    ORDER_KAFKA_TOPIC = os.getenv("ORDER_KAFKA_TOPIC", "cf.iot.order.events")

    # Generation configuration
    DELAY = int(os.getenv("DELAY", "300"))  # milliseconds
    MODE = os.getenv("MODE", "RANDOM_GEN")  # RANDOM_GEN, ONE_GEN, ORDER_GEN

    # Generation modes
    RANDOM_GEN = "RANDOM_GEN"
    ONE_GEN = "ONE_GEN"
    ORDER_GEN = "ORDER_GEN"

    # Order configuration
    ORDER_STATUSES = ["pending", "completed", "failed", "cancelled"]
    ORDER_CURRENCIES = ["USD", "EUR", "GBP", "CAD"]
    ORDER_AMOUNT_RANGE = (5.0, 500.0)  # Min and max order amounts

    @classmethod
    def get_random_order_status(cls) -> str:
        """Get random order status"""
        return random.choice(cls.ORDER_STATUSES)

    @classmethod
    def get_random_currency(cls) -> str:
        """Get random currency"""
        return random.choice(cls.ORDER_CURRENCIES)

    @classmethod
    def get_random_order_amount(cls) -> float:
        """Get random order amount"""
        return round(random.uniform(*cls.ORDER_AMOUNT_RANGE), 2)

    @classmethod
    def get_station_status_probability(cls) -> bool:
        """
        Get station status with 25% probability of being True (on)
        Mimics the original `25PercentageIsTrue` function
        """
        return not (random.random() < 0.5 and random.random() < 0.5)

    @classmethod
    def setup_logging(cls, level: str = "INFO"):
        """
        Setup logging configuration

        Args:
            level: Logging level (DEBUG, INFO, WARNING, ERROR)
        """
        logging.basicConfig(
            level=getattr(logging, level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        logger.info(f"Logging configured at {level} level")

    @classmethod
    def validate_config(cls) -> bool:
        """
        Validate configuration settings

        Returns:
            True if configuration is valid, False otherwise
        """
        try:
            # Validate delay is positive
            if cls.DELAY <= 0:
                logger.error("DELAY must be positive")
                return False

            # Validate mode
            valid_modes = [cls.RANDOM_GEN, cls.ONE_GEN, cls.ORDER_GEN]
            if cls.MODE not in valid_modes:
                logger.error(f"MODE must be one of: {valid_modes}")
                return False

            # Validate order amount range
            if cls.ORDER_AMOUNT_RANGE[0] >= cls.ORDER_AMOUNT_RANGE[1]:
                logger.error("ORDER_AMOUNT_RANGE min must be less than max")
                return False

            logger.info("Configuration validation passed")
            return True
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

    @classmethod
    def print_config(cls):
        """Print current configuration"""
        logger.info("=== Configuration ===")
        logger.info(
            f"Database: {cls.DATABASE_HOST}:{cls.DATABASE_PORT}/{cls.DATABASE_NAME}")
        logger.info(f"Kafka: {cls.KAFKA_HOST}:{cls.KAFKA_PORT}")
        logger.info(f"Station Topic: {cls.KAFKA_TOPIC}")
        logger.info(f"Order Topic: {cls.ORDER_KAFKA_TOPIC}")
        logger.info(f"Mode: {cls.MODE}")
        logger.info(f"Delay: {cls.DELAY}ms")
        logger.info("====================")
