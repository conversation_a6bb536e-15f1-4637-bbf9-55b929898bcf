{"version": 3, "file": "535.js?_cache=e2a01fd0b11de0d12956", "mappings": "wNAIA,MAAMA,GAAuBC,EAAAA,EAAAA,MAAK,IAAM,+DAE3BC,EAAY,IAErB,kBAACC,EAAAA,OAAMA,KACL,kBAACC,EAAAA,MAAKA,CAACC,KAAMC,EAAAA,GAAOC,QAASC,QAAS,kBAACR,EAAAA,QAEvC,kBAACI,EAAAA,MAAKA,CAACC,KAAM,IAAKG,QAAS,kBAACC,EAAAA,SAAQA,CAACC,SAAAA,EAAQC,GAAIL,EAAAA,GAAOC,a,cCL9D,MAAMK,EAAqBC,IAAAA,cAAyC,MAEpE,MAAMC,UAAYD,IAAAA,cAChBE,MAAAA,GACE,OACE,kBAACH,EAAmBI,SAAQ,CAACC,MAAOC,KAAKC,OACvC,kBAACC,EAAAA,WAAUA,CAACC,OAAQC,EAAAA,eAAeC,QACjC,kBAACrB,EAASA,OAIlB,EAGF,S", "sources": ["webpack://grafana-exploretraces-app/./components/Routes/Routes.tsx", "webpack://grafana-exploretraces-app/./components/App/App.tsx"], "sourcesContent": ["import React, { lazy } from 'react';\nimport { Navigate, Route, Routes } from 'react-router-dom';\nimport { ROUTES } from 'utils/shared';\n\nconst TraceExplorationPage = lazy(() => import('../../pages/Explore/TraceExplorationPage'));\n\nexport const AppRoutes = () => {\n  return (\n    <Routes>\n      <Route path={ROUTES.Explore} element={<TraceExplorationPage />} />\n      {/* <Route path={ROUTES.Home} element={<HomePage />} /> */}\n      <Route path={'/'} element={<Navigate replace to={ROUTES.Explore} />} />\n    </Routes>\n  );\n};\n", "import React from 'react';\nimport { AppRootProps, PageLayoutType } from '@grafana/data';\nimport { AppRoutes } from '../Routes';\nimport { PluginPage } from '@grafana/runtime';\n\n// This is used to be able to retrieve the root plugin props anywhere inside the app.\nconst PluginPropsContext = React.createContext<AppRootProps | null>(null);\n\nclass App extends React.PureComponent<AppRootProps> {\n  render() {\n    return (\n      <PluginPropsContext.Provider value={this.props}>\n        <PluginPage layout={PageLayoutType.Custom}>\n          <AppRoutes />\n        </PluginPage>\n      </PluginPropsContext.Provider>\n    );\n  }\n}\n\nexport default App;\n"], "names": ["TraceExplorationPage", "lazy", "AppRoutes", "Routes", "Route", "path", "ROUTES", "Explore", "element", "Navigate", "replace", "to", "PluginPropsContext", "React", "App", "render", "Provider", "value", "this", "props", "PluginPage", "layout", "PageLayoutType", "Custom"], "sourceRoot": ""}