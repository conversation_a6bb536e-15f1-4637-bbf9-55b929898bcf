package org.myorg.autogen.transformers;

import org.myorg.autogen.configs.TransformerConfig;

/**
 * Base interface for all data transformers
 */
public interface Transformer {
    
    /**
     * Apply transformation to the SQL query
     * 
     * @param inputQuery The input SQL query
     * @param config Transformer configuration
     * @return Transformed SQL query
     */
    String transform(String inputQuery, TransformerConfig config);
    
    /**
     * Get the transformer type that this implementation handles
     * 
     * @return Transformer type string
     */
    String getTransformerType();
}
