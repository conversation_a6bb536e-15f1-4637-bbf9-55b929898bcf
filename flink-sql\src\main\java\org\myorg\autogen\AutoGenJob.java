package org.myorg.autogen;

/**
 * Main entry point for the Flink Auto-Generator Job
 */
public class AutoGenJob {
    
    public static void main(String[] args) {
        if (args.length != 1) {
            System.err.println("Usage: AutoGenJob <yaml-config-file>");
            System.err.println("Example: AutoGenJob /path/to/job.yaml");
            System.exit(1);
        }
        
        String yamlFilePath = args[0];
        
        try {
            System.out.println("Starting Flink Auto-Generator Job");
            System.out.println("Configuration file: " + yamlFilePath);
            
            JobGenerator jobGenerator = new JobGenerator();
            jobGenerator.generateAndExecuteJobs(yamlFilePath);
            
            System.out.println("All jobs have been submitted successfully!");
            
        } catch (Exception e) {
            System.err.println("Failed to execute jobs: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}
