{"version": 3, "file": "2.js?_cache=e8dfc983374527051249", "mappings": "iKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,iDACxB,iBAAkB,yBAClB,yBAA0B,mDAE3B,yBAA0B,CACzB,sBAAuB,gDACvB,mBAAoB,qDAErB,gBAAiB,CAChBC,QAAS,yFACTC,SAAU,oCACVC,MAAO,oBAER,wBAAyB,CACxB,wBAAyB,mBACzB,sBAAuB,sBACvB,sBAAuB,sBAExB,iBAAkB,CACjB,iBAAkB,qBAClB,cAAe,wBACf,uBAAwB,sBAEzB,iBAAkB,CACjB,eAAgB,mBAChB,aAAc,uBAEf,oCAAqC,CACpC,eAAgB,cAChB,iBAAkB,wCAEnBC,SAAU,CACT,gCAAiC,0CAElC,YAAa,CACZD,MAAO,CACNA,MAAO,UAGT,2BAA4B,CAC3BE,QAAS,YAEV,qBAAsB,CACrB,uBAAwB,mCACxB,sCAAuC,+DAExC,yBAA0B,CACzB,2DAA4D,2IAC5D,kBAAmB,8CAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,aAEnB,oBAAqB,CACpB,uBAAwB,uBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,kCAEpC,wBAAyB,CACxB,wBAAyB,oBACzB,mBAAoB,qBAErB,yBAA0B,CACzB,2BAA4B,sBAC5B,aAAc,CACb,2BAA4B,8BAE7B,qBAAsB,6BACtB,sBAAuB,sBACvB,eAAgB,CACf,2BAA4B,4BAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,eAGb,8CAA+C,CAC9C,mBAAoB,UACpBC,QAAS,wHACT,gDAAiD,wDAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,oCACzB,uBAAwB,mCACxB,gCAAiC,yCACjC,oDAAqD,4FACrD,0BAA2B,+BAC3B,uBAAwB,oCACxB,mBAAoB,mCACpB,mDAAoD,2DACpD,uBAAwB,mCACxB,kDAAmD,mGACnD,iCAAkC,oDAClC,oCAAqC,sDAIxC,6BAA8B,CAC7B,+BAAgC,4BAChC,6BAA8B,2BAE/B,oBAAqB,CACpB,2BAA4B,2BAE7B,8BAA+B,CAC9B,kBAAmB,6BAEpB,2BAA4B,CAC3BC,MAAO,aAER,yBAA0B,CACzB,mBAAoB,yBAErB,4BAA6B,CAC5B,6CAA8C,0FAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,YAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,UAGR,uBAAwB,CACvB,0BAA2B,qBAE5B,wBAAyB,CACxB,2BAA4B,6B", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/fr-FR/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Modifier le filtre ayant pour clé {{keyLabel}}\",\n\t\t\t\"managed-filter\": \"Filtre géré {{origin}}\",\n\t\t\t\"remove-filter-with-key\": \"Supprimer le filtre ayant pour clé {{keyLabel}}\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Supprimer la valeur du filtre – {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"Utiliser une valeur personnalisée : {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Si vous êtes arrivé ici via un lien, il se peut qu’il y ait un bug dans l’application.\",\n\t\t\tsubTitle: \"L’URL ne correspond à aucune page\",\n\t\t\ttitle: \"Page introuvable\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"Réduire la scène\",\n\t\t\t\"expand-button-label\": \"Développer la scène\",\n\t\t\t\"remove-button-label\": \"Supprimer la scène\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Détails de l’objet\",\n\t\t\t\"scene-graph\": \"Graphique de la scène\",\n\t\t\t\"title-scene-debugger\": \"Débogueur de scène\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Réduire la ligne\",\n\t\t\t\"expand-row\": \"Développer la ligne\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Comparaison\",\n\t\t\t\"button-tooltip\": \"Activer la comparaison d’intervalles\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Widget de redimensionnement du panneau\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Titre\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Explorer\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Chargement du panneau du plugin…\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"Le plugin de panneau ne contient aucun composant de panneau\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Le rendu d’un trop grand nombre de séries dans un seul panneau peut nuire aux performances et rendre les données plus difficiles à lire.\",\n\t\t\t\"warning-message\": \"Affichage limité à {{seriesLimit}} séries\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Supprimer\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Annuler la requête\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Modifier l’opérateur du filtre\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Ajouter un filtre\",\n\t\t\t\"title-add-filter\": \"Ajouter un filtre\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Supprimer le filtre\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Sélectionner une étiquette\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Sélectionner une étiquette\",\n\t\t\t\"title-remove-filter\": \"Supprimer le filtre\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Sélectionner une valeur\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"par défaut\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"effacer\",\n\t\t\ttooltip: \"Appliqué par défaut dans ce tableau de bord. En cas de modification, il s’applique aussi aux autres tableaux de bord.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Restaurer le groupage défini par ce tableau de bord.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Valeurs séparées par des virgules\",\n\t\t\t\t\t\"double-quoted-values\": \"Valeurs entre guillemets doubles\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Formater la date de différentes façons\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Formater les variables à valeurs multiples avec la syntaxe glob : exemple {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"Échappement HTML des valeurs\",\n\t\t\t\t\t\"json-stringify-value\": \"Valeur au format JSON (stringify)\",\n\t\t\t\t\t\"keep-value-as-is\": \"Conserver la valeur telle quelle\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Plusieurs valeurs sont formatées ainsi : variable=valeur\",\n\t\t\t\t\t\"single-quoted-values\": \"Valeurs entre guillemets simples\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Utile pour l’échappement des valeurs dans les URL en tenant compte des caractères de syntaxe URI\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Utile pour l’échappement des valeurs dans les URL\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Les valeurs sont séparées par le caractère « | »\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Sélecteur de regroupement\",\n\t\t\t\"placeholder-group-by-label\": \"Regrouper par étiquette\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Sélectionner une valeur\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Chargement des options...\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Appliquer\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"Aucune option trouvée\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Une erreur est survenue lors de la récupération des étiquettes. Cliquez pour réessayer\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Bonjour\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Texte\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Saisir une valeur\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Sélectionner une valeur\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}