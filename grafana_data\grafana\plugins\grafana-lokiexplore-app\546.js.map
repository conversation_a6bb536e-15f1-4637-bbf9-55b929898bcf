{"version": 3, "file": "546.js?_cache=8e0beb2f22d2cbf6b122", "mappings": "wOAmBA,MAAMA,EAAc,CAClB,CAACC,EAAAA,sBAAsBC,OAAQC,EAAAA,GAAcD,MAC7C,CAACD,EAAAA,sBAAsBG,UAAWD,EAAAA,GAAcC,SAChD,CAACH,EAAAA,sBAAsBI,YAAaF,EAAAA,GAAcG,WAClD,CAACL,EAAAA,sBAAsBM,eAAgBJ,EAAAA,GAAcK,eAGxC,SAASC,GAA0B,cAChDC,EAAa,KACbC,EAAI,aACJC,EAAY,uBACZC,EAAsB,gBACtBC,EAAe,GACfC,IAEA,MAAMC,GAAsBC,EAAAA,EAAAA,uBAEtBC,GAAOC,EAAAA,EAAAA,SAAQ,KACnB,MAAMC,EAAYN,EAAgB,GAElC,IACGM,IAEDA,aAAAA,EAAAA,EAAWC,YAAapB,EAAAA,sBAAsBC,MAE9C,OAAO,KAGT,MAAMoB,GAAaC,EAAAA,EAAAA,IAAaH,EAAUI,OAE1C,IAAIC,EAAS,IAAIC,gBAwBjB,OAtBIhB,IACFe,GAASE,EAAAA,EAAAA,IAAgBC,EAAAA,GAAcC,aAAcnB,EAAee,IAGlEd,IACFc,GAASE,EAAAA,EAAAA,IAAgBC,EAAAA,GAAcE,cAAenB,EAAMc,IAG1DV,IACFU,GAASE,EAAAA,EAAAA,IAAgBC,EAAAA,GAAcG,YAAahB,EAAIU,IAG1DX,EAAgBkB,QAASC,IACvBR,GAASS,EAAAA,EAAAA,IACPN,EAAAA,GAAcO,OACd,GAAGF,EAAeG,QAAQpC,EAAYiC,EAAeZ,cAAagB,EAAAA,EAAAA,KAChEC,EAAAA,EAAAA,IAAqBL,EAAeT,YACjCa,EAAAA,EAAAA,KAAoBE,EAAAA,EAAAA,IAAmBN,EAAeT,UAC3DC,MAIGe,EAAAA,EAAAA,IAAa,YAAYpB,EAAUgB,QAAQd,SAAmBG,IACpE,CAACf,EAAeC,EAAMI,EAAID,IAE7B,OAAKI,EAIDN,EACKA,EAAa,CAAEM,SAItB,kBAACuB,EAAAA,WAAUA,CACTC,QAAQ,YACRxB,KAAMA,EACNyB,QAAS,IAAM3B,EAAoBH,GAA0B,aAC9D,0BAZM,IAgBX,C", "sources": ["webpack://grafana-lokiexplore-app/./Components/OpenInLogsDrilldownButton/OpenInLogsDrilldownButton.tsx"], "sourcesContent": ["import React, { useMemo } from 'react';\n\nimport { AbstractLabelOperator } from '@grafana/data';\nimport { useReturnToPrevious } from '@grafana/runtime';\nimport { LinkButton } from '@grafana/ui';\n\nimport { OpenInLogsDrilldownButtonProps } from './types';\nimport {\n  appendUrlParameter,\n  createAppUrl,\n  escapeURLDelimiters,\n  replaceEscapeChars,\n  replaceSlash,\n  setUrlParameter,\n  stringifyAdHocValues,\n  UrlParameters,\n} from 'services/extensions/links';\nimport { LabelFilterOp } from 'services/filterTypes';\n\nconst operatorMap = {\n  [AbstractLabelOperator.Equal]: LabelFilterOp.Equal,\n  [AbstractLabelOperator.NotEqual]: LabelFilterOp.NotEqual,\n  [AbstractLabelOperator.EqualRegEx]: LabelFilterOp.RegexEqual,\n  [AbstractLabelOperator.NotEqualRegEx]: LabelFilterOp.RegexNotEqual,\n};\n\nexport default function OpenInLogsDrilldownButton({\n  datasourceUid,\n  from,\n  renderButton,\n  returnToPreviousSource,\n  streamSelectors,\n  to,\n}: OpenInLogsDrilldownButtonProps) {\n  const setReturnToPrevious = useReturnToPrevious();\n\n  const href = useMemo(() => {\n    const mainLabel = streamSelectors[0];\n\n    if (\n      !mainLabel ||\n      // we can't open in explore logs if main label matcher is something different from equal\n      mainLabel?.operator !== AbstractLabelOperator.Equal\n    ) {\n      return null;\n    }\n\n    const labelValue = replaceSlash(mainLabel.value);\n\n    let params = new URLSearchParams();\n\n    if (datasourceUid) {\n      params = setUrlParameter(UrlParameters.DatasourceId, datasourceUid, params);\n    }\n\n    if (from) {\n      params = setUrlParameter(UrlParameters.TimeRangeFrom, from, params);\n    }\n\n    if (to) {\n      params = setUrlParameter(UrlParameters.TimeRangeTo, to, params);\n    }\n\n    streamSelectors.forEach((streamSelector) => {\n      params = appendUrlParameter(\n        UrlParameters.Labels,\n        `${streamSelector.name}|${operatorMap[streamSelector.operator]}|${escapeURLDelimiters(\n          stringifyAdHocValues(streamSelector.value)\n        )},${escapeURLDelimiters(replaceEscapeChars(streamSelector.value))}`,\n        params\n      );\n    });\n\n    return createAppUrl(`/explore/${mainLabel.name}/${labelValue}/logs`, params);\n  }, [datasourceUid, from, to, streamSelectors]);\n\n  if (!href) {\n    return null;\n  }\n\n  if (renderButton) {\n    return renderButton({ href });\n  }\n\n  return (\n    <LinkButton\n      variant=\"secondary\"\n      href={href}\n      onClick={() => setReturnToPrevious(returnToPreviousSource || 'previous')}\n    >\n      Open in Logs Drilldown\n    </LinkButton>\n  );\n}\n"], "names": ["operatorMap", "AbstractLabelOperator", "Equal", "LabelFilterOp", "NotEqual", "EqualRegEx", "RegexEqual", "NotEqualRegEx", "RegexNotEqual", "OpenInLogsDrilldownButton", "datasourceUid", "from", "renderButton", "returnToPreviousSource", "streamSelectors", "to", "setReturnToPrevious", "useReturnToPrevious", "href", "useMemo", "mainLabel", "operator", "labelValue", "replaceSlash", "value", "params", "URLSearchParams", "setUrlParameter", "UrlParameters", "DatasourceId", "TimeRangeFrom", "TimeRangeTo", "for<PERSON>ach", "streamSelector", "appendUrlParameter", "Labels", "name", "escapeURLDelimiters", "stringifyAdHocValues", "replaceEscapeChars", "createAppUrl", "LinkButton", "variant", "onClick"], "sourceRoot": ""}