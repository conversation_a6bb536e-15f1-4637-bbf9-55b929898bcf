package org.myorg.autogen.utils;

import org.myorg.autogen.readers.Reader;
import org.myorg.autogen.readers.KafkaReader;
import org.myorg.autogen.writers.Writer;
import org.myorg.autogen.writers.KafkaClickHouseWriter;
import org.myorg.autogen.writers.KafkaPostgreSQLWriter;
import org.myorg.autogen.writers.KafkaAvroWriter;
import org.myorg.autogen.transformers.Transformer;
import org.myorg.autogen.transformers.WithColumnTransformer;
import org.myorg.autogen.transformers.ProjectionTransformer;

import java.util.HashMap;
import java.util.Map;

/**
 * Factory class for creating reader, writer, and transformer instances
 */
public class ComponentFactory {
    
    private static final Map<String, Reader> readers = new HashMap<>();
    private static final Map<String, Writer> writers = new HashMap<>();
    private static final Map<String, Transformer> transformers = new HashMap<>();
    
    static {
        // Register readers
        registerReader(new KafkaReader());

        // Register writers
        registerWriter(new KafkaClickHouseWriter());
        registerWriter(new KafkaPostgreSQLWriter());
        registerWriter(new KafkaAvroWriter());

        // Register transformers
        registerTransformer(new WithColumnTransformer());
        registerTransformer(new ProjectionTransformer());
    }
    
    private static void registerReader(Reader reader) {
        readers.put(reader.getReaderType(), reader);
    }
    
    private static void registerWriter(Writer writer) {
        writers.put(writer.getWriterType(), writer);
    }
    
    private static void registerTransformer(Transformer transformer) {
        transformers.put(transformer.getTransformerType(), transformer);
    }
    
    public static Reader getReader(String type) {
        Reader reader = readers.get(type);
        if (reader == null) {
            throw new IllegalArgumentException("Unknown reader type: " + type);
        }
        return reader;
    }
    
    public static Writer getWriter(String type) {
        Writer writer = writers.get(type);
        if (writer == null) {
            throw new IllegalArgumentException("Unknown writer type: " + type);
        }
        return writer;
    }
    
    public static Transformer getTransformer(String type) {
        Transformer transformer = transformers.get(type);
        if (transformer == null) {
            throw new IllegalArgumentException("Unknown transformer type: " + type);
        }
        return transformer;
    }
}
