# Use Python 3.11 slim image as base
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the application code
COPY . .

# Make main.py executable
RUN chmod +x main.py

# Set environment variables with defaults
ENV DATABASE_HOST=postgres
ENV DATABASE_PORT=5432
ENV DATABASE_NAME=service-rental-demo
ENV DATABASE_USERNAME=admin
ENV DATABASE_PASSWORD=admin
ENV KAFKA_HOST=redpanda
ENV KAFKA_PORT=9092
ENV KAFKA_TOPIC=cf.iot.station.status
ENV ORDER_KAFKA_TOPIC=cf.iot.order.events
ENV MODE=RANDOM_GEN
ENV DELAY=300

# Default command
CMD ["python", "main.py", "--mode", "RANDOM_GEN", "--iterations", "1000", "--delay", "300"]
