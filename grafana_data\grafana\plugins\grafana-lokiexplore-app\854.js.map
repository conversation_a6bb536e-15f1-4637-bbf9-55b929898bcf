{"version": 3, "file": "854.js?_cache=9da793b3efc18875808d", "mappings": "2LAAA,IAAIA,E,2GAEJ,MAAMC,EAA4C,oBAAhBC,YAA8B,IAAIA,YAAY,QAAS,CAAEC,WAAW,EAAMC,OAAO,IAAU,CAAEC,OAAQ,KAAQ,MAAMC,MAAM,+BAEhI,oBAAhBJ,aAA+BD,EAAkBI,SAE5D,IAAIE,EAA0B,KAE9B,SAASC,IAIL,OAHgC,OAA5BD,GAA2E,IAAvCA,EAAwBE,aAC5DF,EAA0B,IAAIG,WAAWV,EAAKW,OAAOC,SAElDL,CACX,CAEA,SAASM,EAAmBC,EAAKC,GAE7B,OADAD,KAAc,EACPb,EAAkBI,OAAOG,IAAuBQ,SAASF,EAAKA,EAAMC,GAC/E,CAEA,MAAME,EAAO,IAAIC,MAAM,KAAKC,UAAKC,GAEjCH,EAAKI,UAAKD,EAAW,MAAM,GAAM,GAEjC,IAAIE,EAAYL,EAAKM,OAErB,SAASC,EAAcC,GACfH,IAAcL,EAAKM,QAAQN,EAAKI,KAAKJ,EAAKM,OAAS,GACvD,MAAMG,EAAMJ,EAIZ,OAHAA,EAAYL,EAAKS,GAEjBT,EAAKS,GAAOD,EACLC,CACX,CAEA,SAASC,EAAUD,GAAO,OAAOT,EAAKS,EAAM,CAQ5C,SAASE,EAAWF,GAChB,MAAMG,EAAMF,EAAUD,GAEtB,OATJ,SAAoBA,GACZA,EAAM,MACVT,EAAKS,GAAOJ,EACZA,EAAYI,EAChB,CAIII,CAAWJ,GACJG,CACX,CAEA,IAAIE,EAAkB,EAEtB,MAAMC,EAA4C,oBAAhBC,YAA8B,IAAIA,YAAY,SAAW,CAAEC,OAAQ,KAAQ,MAAM5B,MAAM,+BAEnH6B,EAAwD,mBAAjCH,EAAkBI,WACzC,SAAUC,EAAKC,GACjB,OAAON,EAAkBI,WAAWC,EAAKC,EAC7C,EACM,SAAUD,EAAKC,GACjB,MAAMC,EAAMP,EAAkBE,OAAOG,GAErC,OADAC,EAAKE,IAAID,GACF,CACHE,KAAMJ,EAAId,OACVmB,QAASH,EAAIhB,OAErB,EAEA,SAASoB,EAAkBN,EAAKO,EAAQC,GAEpC,QAAgBzB,IAAZyB,EAAuB,CACvB,MAAMN,EAAMP,EAAkBE,OAAOG,GAC/BvB,EAAM8B,EAAOL,EAAIhB,OAAQ,KAAO,EAGtC,OAFAf,IAAuBQ,SAASF,EAAKA,EAAMyB,EAAIhB,QAAQiB,IAAID,GAC3DR,EAAkBQ,EAAIhB,OACfT,CACX,CAEA,IAAIC,EAAMsB,EAAId,OACVT,EAAM8B,EAAO7B,EAAK,KAAO,EAE7B,MAAM+B,EAAMtC,IAEZ,IAAIuC,EAAS,EAEb,KAAOA,EAAShC,EAAKgC,IAAU,CAC3B,MAAMC,EAAOX,EAAIY,WAAWF,GAC5B,GAAIC,EAAO,IAAM,MACjBF,EAAIhC,EAAMiC,GAAUC,CACxB,CAEA,GAAID,IAAWhC,EAAK,CACD,IAAXgC,IACAV,EAAMA,EAAIa,MAAMH,IAEpBjC,EAAM+B,EAAQ/B,EAAKC,EAAKA,EAAMgC,EAAsB,EAAbV,EAAId,OAAY,KAAO,EAC9D,MAAMe,EAAO9B,IAAuBQ,SAASF,EAAMiC,EAAQjC,EAAMC,GAGjEgC,GAFYZ,EAAaE,EAAKC,GAEhBI,QACd5B,EAAM+B,EAAQ/B,EAAKC,EAAKgC,EAAQ,KAAO,CAC3C,CAGA,OADAhB,EAAkBgB,EACXjC,CACX,CAEA,SAASqC,EAAWC,GAChB,OAAOA,OACX,CAEA,IAAIC,EAAwB,KAE5B,SAASC,IAIL,OAH8B,OAA1BD,IAA4E,IAA1CA,EAAsBzC,OAAO2C,eAAgEnC,IAA1CiC,EAAsBzC,OAAO2C,UAA0BF,EAAsBzC,SAAWZ,EAAKW,OAAOC,UACzLyC,EAAwB,IAAIG,SAASxD,EAAKW,OAAOC,SAE9CyC,CACX,CAEA,SAASI,EAAYC,GAEjB,MAAMC,SAAcD,EACpB,GAAY,UAARC,GAA4B,WAARA,GAA4B,MAAPD,EACzC,MAAQ,GAAGA,IAEf,GAAY,UAARC,EACA,MAAO,IAAID,KAEf,GAAY,UAARC,EAAkB,CAClB,MAAMC,EAAcF,EAAIE,YACxB,OAAmB,MAAfA,EACO,SAEA,UAAUA,IAEzB,CACA,GAAY,YAARD,EAAoB,CACpB,MAAME,EAAOH,EAAIG,KACjB,MAAmB,iBAARA,GAAoBA,EAAKtC,OAAS,EAClC,YAAYsC,KAEZ,UAEf,CAEA,GAAI3C,MAAM4C,QAAQJ,GAAM,CACpB,MAAMnC,EAASmC,EAAInC,OACnB,IAAIwC,EAAQ,IACRxC,EAAS,IACTwC,GAASN,EAAYC,EAAI,KAE7B,IAAI,IAAIM,EAAI,EAAGA,EAAIzC,EAAQyC,IACvBD,GAAS,KAAON,EAAYC,EAAIM,IAGpC,OADAD,GAAS,IACFA,CACX,CAEA,MAAME,EAAiB,sBAAsBC,KAAKC,SAASC,KAAKV,IAChE,IAAIW,EACJ,KAAIJ,EAAe1C,OAAS,GAIxB,OAAO4C,SAASC,KAAKV,GAEzB,GALIW,EAAYJ,EAAe,GAKd,UAAbI,EAIA,IACI,MAAO,UAAYC,KAAKC,UAAUb,GAAO,GAC7C,CAAE,MAAOc,GACL,MAAO,QACX,CAGJ,OAAId,aAAepD,MACR,GAAGoD,EAAIG,SAASH,EAAIe,YAAYf,EAAIgB,QAGxCL,CACX,CAaO,SAASM,EAAYC,GACxB,IACI,MAAMC,EAAS7E,EAAK8E,iCAAiC,IACrD9E,EAAK2E,YAAYE,EAAQ1B,EAAWyB,GAAU,EAAIpD,EAAcoD,IAChE,IAAIG,EAAKzB,IAAqB0B,SAASH,EAAS,GAAO,GAEvD,GADSvB,IAAqB0B,SAASH,EAAS,GAAO,GAEnD,MAAMjD,EAAWmD,EAEzB,CAAE,QACE/E,EAAK8E,gCAAgC,GACzC,CACJ,CAUO,SAASG,IACZjF,EAAKiF,aACT,CAEA,SAASC,EAAYC,EAAGC,GACpB,IACI,OAAOD,EAAEE,MAAMC,KAAMF,EACzB,CAAE,MAAOG,GACLvF,EAAKwF,qBAAqBhE,EAAc+D,GAC5C,CACJ,CAEA,MAAME,EAAmE,oBAAzBC,qBAC1C,CAAEC,SAAU,OAAUC,WAAY,QAClC,IAAIF,qBAAqB5E,GAAOd,EAAK6F,+BAA+B/E,IAAQ,EAAG,IAI9E,MAAMgF,EAET,aAAOC,CAAOjF,GACVA,KAAc,EACd,MAAMW,EAAMuE,OAAOC,OAAOH,EAAoBI,WAG9C,OAFAzE,EAAI0E,UAAYrF,EAChB2E,EAAgCE,SAASlE,EAAKA,EAAI0E,UAAW1E,GACtDA,CACX,CAEA,kBAAA2E,GACI,MAAMtF,EAAMwE,KAAKa,UAGjB,OAFAb,KAAKa,UAAY,EACjBV,EAAgCG,WAAWN,MACpCxE,CACX,CAEA,IAAAuF,GACI,MAAMvF,EAAMwE,KAAKc,qBACjBpG,EAAK6F,+BAA+B/E,EAAK,EAC7C,CAIA,WAAAwF,CAAYC,GACR,IACI,MAAM1B,EAAS7E,EAAK8E,iCAAiC,IACrD9E,EAAKwG,wBAAwB3B,EAAQrD,EAAc+E,IACnD,IAAIxB,EAAKzB,IAAqB0B,SAASH,EAAS,GAAO,GACnD4B,EAAKnD,IAAqB0B,SAASH,EAAS,GAAO,GAEvD,GADSvB,IAAqB0B,SAASH,EAAS,GAAO,GAEnD,MAAMjD,EAAW6E,GAIrB,OAFAnB,KAAKa,UAAYpB,IAAO,EACxBU,EAAgCE,SAASL,KAAMA,KAAKa,UAAWb,MACxDA,IACX,CAAE,QACEtF,EAAK8E,gCAAgC,GACzC,CACJ,CAMA,kBAAO4B,CAAYC,GACf,IACI,MAAM9B,EAAS7E,EAAK8E,iCAAiC,IACrD9E,EAAK4G,gCAAgC/B,EAAQ1B,EAAWwD,GAAQ,EAAInF,EAAcmF,IAClF,IAAI5B,EAAKzB,IAAqB0B,SAASH,EAAS,GAAO,GACnD4B,EAAKnD,IAAqB0B,SAASH,EAAS,GAAO,GAEvD,GADSvB,IAAqB0B,SAASH,EAAS,GAAO,GAEnD,MAAMjD,EAAW6E,GAErB,OAAOX,EAAoBC,OAAOhB,EACtC,CAAE,QACE/E,EAAK8E,gCAAgC,GACzC,CACJ,CAOA,oBAAO+B,CAAcF,GACjB,IACI,MAAM9B,EAAS7E,EAAK8E,iCAAiC,IACrD9E,EAAK8G,kCAAkCjC,EAAQ1B,EAAWwD,GAAQ,EAAInF,EAAcmF,IACpF,IAAI5B,EAAKzB,IAAqB0B,SAASH,EAAS,GAAO,GACnD4B,EAAKnD,IAAqB0B,SAASH,EAAS,GAAO,GAEvD,GADSvB,IAAqB0B,SAASH,EAAS,GAAO,GAEnD,MAAMjD,EAAW6E,GAErB,OAAOX,EAAoBC,OAAOhB,EACtC,CAAE,QACE/E,EAAK8E,gCAAgC,GACzC,CACJ,CAMA,kBAAAiC,CAAmBC,GACf,IACI,MAAMnC,EAAS7E,EAAK8E,iCAAiC,IACrD9E,EAAKiH,uCAAuCpC,EAAQS,KAAKa,UAAW3E,EAAcwF,IAClF,IAAIjC,EAAKzB,IAAqB0B,SAASH,EAAS,GAAO,GACnD4B,EAAKnD,IAAqB0B,SAASH,EAAS,GAAO,GAEvD,GADSvB,IAAqB0B,SAASH,EAAS,GAAO,GAEnD,MAAMjD,EAAW6E,GAErB,OAAO7E,EAAWmD,EACtB,CAAE,QACE/E,EAAK8E,gCAAgC,GACzC,CACJ,EAkCJ,SAASoC,IACL,MAAMC,EAAU,CAChBA,IAAc,CAAC,GAgRf,OA/QAA,EAAQC,IAAIC,qBAAuB,SAASC,EAAMC,GAE9C,OAAO/F,EADK,IAAIlB,MAAMO,EAAmByG,EAAMC,IAEnD,EACAJ,EAAQC,IAAII,qBAAuB,SAASF,GAExC,MADwC,iBAArB3F,EAAU2F,EAEjC,EACAH,EAAQC,IAAIK,2BAA6B,SAASH,GAC9C1F,EAAW0F,EACf,EACAH,EAAQC,IAAIM,sBAAwB,SAASJ,EAAMC,GAC/C,MAAM9F,EAAME,EAAU4F,GAChB1F,EAAsB,iBAAV,EAAqBJ,OAAML,EAC7C,IAAIuG,EAAOxE,EAAWtB,GAAO,EAAIc,EAAkBd,EAAK7B,EAAK4H,kBAAmB5H,EAAK6H,oBACjFC,EAAO/F,EACXuB,IAAqByE,SAAST,EAAO,EAAOQ,GAAM,GAClDxE,IAAqByE,SAAST,EAAO,EAAOK,GAAM,EACtD,EACAR,EAAQC,IAAIY,4BAA8B,SAASV,GAE/C,OAAO9F,EADKG,EAAU2F,GAE1B,EACAH,EAAQC,IAAIa,sBAAwB,SAASX,EAAMC,GAE/C,OAAO/F,EADKX,EAAmByG,EAAMC,GAEzC,EACAJ,EAAQC,IAAIc,qBAAuB,SAASZ,GACxC,MAAM5D,EAAM/B,EAAU2F,GAEtB,MAD4B,iBAAV,GAA8B,OAAR5D,CAE5C,EACAyD,EAAQC,IAAIe,wBAA0B,SAASb,GAE3C,YADgClG,IAApBO,EAAU2F,EAE1B,EACAH,EAAQC,IAAIgB,cAAgB,SAASd,EAAMC,GAEvC,OADY5F,EAAU2F,KAAS3F,EAAU4F,EAE7C,EACAJ,EAAQC,IAAIiB,sBAAwB,SAASf,EAAMC,GAC/C,MAAM9F,EAAME,EAAU4F,GAChB1F,EAAsB,iBAAV,EAAqBJ,OAAML,EAC7CkC,IAAqBgF,WAAWhB,EAAO,EAAOnE,EAAWtB,GAAO,EAAIA,GAAK,GACzEyB,IAAqByE,SAAST,EAAO,GAAQnE,EAAWtB,IAAM,EAClE,EACAsF,EAAQC,IAAImB,qBAAuB,SAASjB,GAExC,MADwC,iBAArB3F,EAAU2F,EAEjC,EACAH,EAAQC,IAAIoB,2BAA6B,SAASlB,GAE9C,OAAO9F,EADKiH,OAAOC,QAAQ,GAAIpB,GAEnC,EACAH,EAAQC,IAAIuB,oBAAsB,SAASrB,EAAMC,GAE7C,OADY5F,EAAU2F,KAAU3F,EAAU4F,EAE9C,EACAJ,EAAQC,IAAIwB,uBAAyB,SAAStB,GAC1C,MAAMuB,EAAIlH,EAAU2F,GAEpB,MAD0B,kBAAR,EAAqBuB,EAAI,EAAI,EAAK,CAExD,EACA1B,EAAQC,IAAI0B,2BAA6B,WAErC,OAAOtH,EADK,IAAIlB,MAEpB,EACA6G,EAAQC,IAAI2B,6BAA+B,SAASzB,EAAMC,GACtD,MACMI,EAAOhF,EADDhB,EAAU4F,GAAM7C,MACQ1E,EAAK4H,kBAAmB5H,EAAK6H,oBAC3DC,EAAO/F,EACbuB,IAAqByE,SAAST,EAAO,EAAOQ,GAAM,GAClDxE,IAAqByE,SAAST,EAAO,EAAOK,GAAM,EACtD,EACAR,EAAQC,IAAI4B,6BAA+B,SAAS1B,EAAMC,GACtD,IAAI0B,EACAC,EACJ,IACID,EAAc3B,EACd4B,EAAc3B,EACd4B,QAAQC,MAAMvI,EAAmByG,EAAMC,GAC3C,CAAE,QACEvH,EAAKqJ,gBAAgBJ,EAAaC,EAAa,EACnD,CACJ,EACA/B,EAAQC,IAAIkC,4BAA8B,SAAShC,EAAMC,GACrDgC,YAAYC,KAAK3I,EAAmByG,EAAMC,GAC9C,EACAJ,EAAQC,IAAIqC,2BAA6B,SAASnC,EAAMC,GACpD,IAAI0B,EACAC,EACJ,IACID,EAAc3B,EACd4B,EAAc3B,CAElB,CAAE,QACEvH,EAAKqJ,gBAAgBJ,EAAaC,EAAa,EACnD,CACJ,EACA/B,EAAQC,IAAIsC,2BAA6B,SAASpC,EAAMC,EAAMoC,EAAMC,EAAMC,EAAMC,EAAMC,EAAMC,GACxF,IAAIf,EACAC,EACJ,IACID,EAAc3B,EACd4B,EAAc3B,CAElB,CAAE,QACEvH,EAAKqJ,gBAAgBJ,EAAaC,EAAa,EACnD,CACJ,EACA/B,EAAQC,IAAI6C,+BAAiC,WAAa,OAAO/E,EAAY,SAAUoC,EAAMC,EAAMoC,EAAMC,GACrG,IAAIX,EACAC,EACAgB,EACAC,EACJ,IACIlB,EAAc3B,EACd4B,EAAc3B,EACd2C,EAAcP,EACdQ,EAAcP,EACdL,YAAYa,QAAQvJ,EAAmByG,EAAMC,GAAO1G,EAAmB8I,EAAMC,GACjF,CAAE,QACE5J,EAAKqJ,gBAAgBJ,EAAaC,EAAa,GAC/ClJ,EAAKqJ,gBAAgBa,EAAaC,EAAa,EACnD,CACJ,EAAGE,UAAW,EACdlD,EAAQC,IAAIkD,uBAAyB,SAAShD,GAE1C,MADwC,mBAArB3F,EAAU2F,EAEjC,EACAH,EAAQC,IAAImD,0BAA4B,SAASjD,EAAMC,GAEnD,OADY5F,EAAU2F,IAAS3F,EAAU4F,EAE7C,EACAJ,EAAQC,IAAIoD,qBAAuB,SAASlD,GAExC,OADa3F,EAAU2F,EAE3B,EACAH,EAAQC,IAAIqD,8BAAgC,SAASnD,EAAMC,GACvD,MACMI,EAAOhF,EADD+H,OAAO/I,EAAU4F,IACOvH,EAAK4H,kBAAmB5H,EAAK6H,oBAC3DC,EAAO/F,EACbuB,IAAqByE,SAAST,EAAO,EAAOQ,GAAM,GAClDxE,IAAqByE,SAAST,EAAO,EAAOK,GAAM,EACtD,EACAR,EAAQC,IAAIuD,sBAAwB,SAASrD,GAEzC,OAAO9F,EADK8F,EAEhB,EACAH,EAAQC,IAAIwD,qCAAuC,SAAStD,EAAMC,GAE9D,OAAO/F,EADKG,EAAU2F,GAAM3F,EAAU4F,IAE1C,EACAJ,EAAQC,IAAIyD,2BAA6B,SAASvD,EAAMC,EAAMoC,GAC1DhI,EAAU2F,GAAM1F,EAAW2F,IAAS3F,EAAW+H,EACnD,EACAxC,EAAQC,IAAI0D,4BAA8B,WAAa,OAAO5F,EAAY,SAAUoC,EAAMC,GAEtF,OAAO/F,EADKG,EAAU2F,GAAMlD,KAAKzC,EAAU4F,IAE/C,EAAG8C,UAAW,EACdlD,EAAQC,IAAI2D,2BAA6B,SAASzD,EAAMC,GAEpD,OAAO/F,EADKG,EAAU2F,GAAMC,IAAS,GAEzC,EACAJ,EAAQC,IAAI4D,8BAAgC,SAAS1D,GAEjD,OADY3F,EAAU2F,GAAM/F,MAEhC,EACA4F,EAAQC,IAAI6D,2BAA6B,WAErC,OAAOzJ,EADK,IAAIN,MAEpB,EACAiG,EAAQC,IAAI8D,4BAA8B,SAAS5D,GAE/C,OAAO9F,EADKG,EAAU2F,GAAM6D,KAEhC,EACAhE,EAAQC,IAAIgE,4BAA8B,WAAa,OAAOlG,EAAY,SAAUoC,GAEhF,OAAO9F,EADKG,EAAU2F,GAAM6D,OAEhC,EAAGd,UAAW,EACdlD,EAAQC,IAAIiE,4BAA8B,SAAS/D,GAE/C,OADY3F,EAAU2F,GAAMgE,IAEhC,EACAnE,EAAQC,IAAImE,6BAA+B,SAASjE,GAEhD,OAAO9F,EADKG,EAAU2F,GAAMkE,MAEhC,EACArE,EAAQC,IAAIqE,gCAAkC,WAE1C,OAAOjK,EADKkK,OAAOC,SAEvB,EACAxE,EAAQC,IAAIwE,2BAA6B,WAAa,OAAO1G,EAAY,SAAUoC,EAAMC,GAErF,OAAO/F,EADKqK,QAAQC,IAAInK,EAAU2F,GAAO3F,EAAU4F,IAEvD,EAAG8C,UAAW,EACdlD,EAAQC,IAAI2E,2BAA6B,WAErC,OAAOvK,EADK,IAAIwE,OAEpB,EACAmB,EAAQC,IAAI4E,2BAA6B,SAAS1E,EAAMC,EAAMoC,GAC1DhI,EAAU2F,GAAMC,IAAS,GAAK3F,EAAW+H,EAC7C,EACAxC,EAAQC,IAAI6E,+BAAiC,SAAS3E,GAElD,OADYpG,MAAM4C,QAAQnC,EAAU2F,GAExC,EACAH,EAAQC,IAAI8E,8CAAgD,SAAS5E,GACjE,IAAI6E,EACJ,IACIA,EAASxK,EAAU2F,aAAiB8E,WACxC,CAAE,MAAO5H,GACL2H,GAAS,CACb,CAEA,OADYA,CAEhB,EACAhF,EAAQC,IAAIiF,qCAAuC,SAAS/E,GAExD,OADYgF,OAAOC,cAAc5K,EAAU2F,GAE/C,EACAH,EAAQC,IAAIoF,+BAAiC,SAASlF,GAElD,OAAO9F,EADKwE,OAAOyG,QAAQ9K,EAAU2F,IAEzC,EACAH,EAAQC,IAAIsF,8BAAgC,SAASpF,GAEjD,OAAO9F,EADKG,EAAU2F,GAAM1G,OAEhC,EACAuG,EAAQC,IAAIuF,2BAA6B,SAASrF,GAE9C,OAAO9F,EADK,IAAId,WAAWiB,EAAU2F,IAEzC,EACAH,EAAQC,IAAIwF,2BAA6B,SAAStF,EAAMC,EAAMoC,GAC1DhI,EAAU2F,GAAM9E,IAAIb,EAAU4F,GAAOoC,IAAS,EAClD,EACAxC,EAAQC,IAAIyF,8BAAgC,SAASvF,GAEjD,OADY3F,EAAU2F,GAAM/F,MAEhC,EACA4F,EAAQC,IAAI0F,6CAA+C,SAASxF,GAChE,IAAI6E,EACJ,IACIA,EAASxK,EAAU2F,aAAiB5G,UACxC,CAAE,MAAO8D,GACL2H,GAAS,CACb,CAEA,OADYA,CAEhB,EACAhF,EAAQC,IAAI2F,6BAA+B,SAASzF,EAAMC,GACtD,MAAMsB,EAAIlH,EAAU4F,GACd1F,EAAoB,iBAAR,EAAmBgH,OAAIzH,EACzCkC,IAAqB0J,YAAY1F,EAAO,EAAOnE,EAAWtB,GAAO4G,OAAO,GAAK5G,GAAK,GAClFyB,IAAqByE,SAAST,EAAO,GAAQnE,EAAWtB,IAAM,EAClE,EACAsF,EAAQC,IAAI6F,wBAA0B,SAAS3F,EAAMC,GACjD,MACMI,EAAOhF,EADDc,EAAY9B,EAAU4F,IACEvH,EAAK4H,kBAAmB5H,EAAK6H,oBAC3DC,EAAO/F,EACbuB,IAAqByE,SAAST,EAAO,EAAOQ,GAAM,GAClDxE,IAAqByE,SAAST,EAAO,EAAOK,GAAM,EACtD,EACAR,EAAQC,IAAI8F,iBAAmB,SAAS5F,EAAMC,GAC1C,MAAM,IAAIjH,MAAMO,EAAmByG,EAAMC,GAC7C,EACAJ,EAAQC,IAAI+F,kBAAoB,WAE5B,OAAO3L,EADKxB,EAAKW,OAErB,EAEOwG,CACX,CAMA,SAASiG,EAAoBC,EAAUC,GAQnC,OAPAtN,EAAOqN,EAASE,QAChBC,EAAWC,uBAAyBH,EACpCjK,EAAwB,KACxB9C,EAA0B,KAG1BP,EAAK0N,mBACE1N,CACX,CAEA,SAAS2N,EAASL,GACd,QAAalM,IAATpB,EAAoB,OAAOA,OAGT,IAAXsN,GAA0BtH,OAAO4H,eAAeN,KAAYtH,OAAOE,YAC5EoH,UAAUA,GAEZnE,QAAQ0E,KAAK,8EAEb,MAAM1G,EAAUD,IAIVoG,aAAkBQ,YAAYC,SAChCT,EAAS,IAAIQ,YAAYC,OAAOT,IAKpC,OAAOF,EAFU,IAAIU,YAAYE,SAASV,EAAQnG,GAEbmG,EACzC,CAEAW,eAAeT,EAAWU,GACtB,QAAa9M,IAATpB,EAAoB,OAAOA,OAGD,IAAnBkO,GAAkClI,OAAO4H,eAAeM,KAAoBlI,OAAOE,YAC5FgI,kBAAkBA,GAEpB/E,QAAQ0E,KAAK,kGAEiB,IAAnBK,IACPA,EAAiB,IAAIC,IAAI,cAE7B,MAAMhH,EAAUD,KAEc,iBAAnBgH,GAAmD,mBAAZE,SAA0BF,aAA0BE,SAA4B,mBAARD,KAAsBD,aAA0BC,OACtKD,EAAiBG,MAAMH,IAK3B,MAAM,SAAEb,EAAQ,OAAEC,SA7WtBW,eAA0BX,EAAQnG,GAC9B,GAAwB,mBAAbmH,UAA2BhB,aAAkBgB,SAAU,CAC9D,GAAgD,mBAArCR,YAAYS,qBACnB,IACI,aAAaT,YAAYS,qBAAqBjB,EAAQnG,EAE1D,CAAE,MAAO5B,GACL,GAA0C,oBAAtC+H,EAAOkB,QAAQ1C,IAAI,gBAInB,MAAMvG,EAHN4D,QAAQ0E,KAAK,oMAAqMtI,EAK1N,CAGJ,MAAMkJ,QAAcnB,EAAOoB,cAC3B,aAAaZ,YAAYa,YAAYF,EAAOtH,EAEhD,CAAO,CACH,MAAMkG,QAAiBS,YAAYa,YAAYrB,EAAQnG,GAEvD,OAAIkG,aAAoBS,YAAYE,SACzB,CAAEX,WAAUC,UAGZD,CAEf,CACJ,CAgVuCuB,OAAiBV,EAAgB/G,GAEpE,OAAOiG,EAAoBC,EAAUC,EACzC,CAGA,S", "sources": ["webpack://grafana-lokiexplore-app/../node_modules/@bsull/augurs/changepoint.js"], "sourcesContent": ["let wasm;\n\nconst cachedTextDecoder = (typeof TextDecoder !== 'undefined' ? new TextDecoder('utf-8', { ignoreBOM: true, fatal: true }) : { decode: () => { throw Error('TextDecoder not available') } } );\n\nif (typeof TextDecoder !== 'undefined') { cachedTextDecoder.decode(); };\n\nlet cachedUint8ArrayMemory0 = null;\n\nfunction getUint8ArrayMemory0() {\n    if (cachedUint8ArrayMemory0 === null || cachedUint8ArrayMemory0.byteLength === 0) {\n        cachedUint8ArrayMemory0 = new Uint8Array(wasm.memory.buffer);\n    }\n    return cachedUint8ArrayMemory0;\n}\n\nfunction getStringFromWasm0(ptr, len) {\n    ptr = ptr >>> 0;\n    return cachedTextDecoder.decode(getUint8ArrayMemory0().subarray(ptr, ptr + len));\n}\n\nconst heap = new Array(128).fill(undefined);\n\nheap.push(undefined, null, true, false);\n\nlet heap_next = heap.length;\n\nfunction addHeapObject(obj) {\n    if (heap_next === heap.length) heap.push(heap.length + 1);\n    const idx = heap_next;\n    heap_next = heap[idx];\n\n    heap[idx] = obj;\n    return idx;\n}\n\nfunction getObject(idx) { return heap[idx]; }\n\nfunction dropObject(idx) {\n    if (idx < 132) return;\n    heap[idx] = heap_next;\n    heap_next = idx;\n}\n\nfunction takeObject(idx) {\n    const ret = getObject(idx);\n    dropObject(idx);\n    return ret;\n}\n\nlet WASM_VECTOR_LEN = 0;\n\nconst cachedTextEncoder = (typeof TextEncoder !== 'undefined' ? new TextEncoder('utf-8') : { encode: () => { throw Error('TextEncoder not available') } } );\n\nconst encodeString = (typeof cachedTextEncoder.encodeInto === 'function'\n    ? function (arg, view) {\n    return cachedTextEncoder.encodeInto(arg, view);\n}\n    : function (arg, view) {\n    const buf = cachedTextEncoder.encode(arg);\n    view.set(buf);\n    return {\n        read: arg.length,\n        written: buf.length\n    };\n});\n\nfunction passStringToWasm0(arg, malloc, realloc) {\n\n    if (realloc === undefined) {\n        const buf = cachedTextEncoder.encode(arg);\n        const ptr = malloc(buf.length, 1) >>> 0;\n        getUint8ArrayMemory0().subarray(ptr, ptr + buf.length).set(buf);\n        WASM_VECTOR_LEN = buf.length;\n        return ptr;\n    }\n\n    let len = arg.length;\n    let ptr = malloc(len, 1) >>> 0;\n\n    const mem = getUint8ArrayMemory0();\n\n    let offset = 0;\n\n    for (; offset < len; offset++) {\n        const code = arg.charCodeAt(offset);\n        if (code > 0x7F) break;\n        mem[ptr + offset] = code;\n    }\n\n    if (offset !== len) {\n        if (offset !== 0) {\n            arg = arg.slice(offset);\n        }\n        ptr = realloc(ptr, len, len = offset + arg.length * 3, 1) >>> 0;\n        const view = getUint8ArrayMemory0().subarray(ptr + offset, ptr + len);\n        const ret = encodeString(arg, view);\n\n        offset += ret.written;\n        ptr = realloc(ptr, len, offset, 1) >>> 0;\n    }\n\n    WASM_VECTOR_LEN = offset;\n    return ptr;\n}\n\nfunction isLikeNone(x) {\n    return x === undefined || x === null;\n}\n\nlet cachedDataViewMemory0 = null;\n\nfunction getDataViewMemory0() {\n    if (cachedDataViewMemory0 === null || cachedDataViewMemory0.buffer.detached === true || (cachedDataViewMemory0.buffer.detached === undefined && cachedDataViewMemory0.buffer !== wasm.memory.buffer)) {\n        cachedDataViewMemory0 = new DataView(wasm.memory.buffer);\n    }\n    return cachedDataViewMemory0;\n}\n\nfunction debugString(val) {\n    // primitive types\n    const type = typeof val;\n    if (type == 'number' || type == 'boolean' || val == null) {\n        return  `${val}`;\n    }\n    if (type == 'string') {\n        return `\"${val}\"`;\n    }\n    if (type == 'symbol') {\n        const description = val.description;\n        if (description == null) {\n            return 'Symbol';\n        } else {\n            return `Symbol(${description})`;\n        }\n    }\n    if (type == 'function') {\n        const name = val.name;\n        if (typeof name == 'string' && name.length > 0) {\n            return `Function(${name})`;\n        } else {\n            return 'Function';\n        }\n    }\n    // objects\n    if (Array.isArray(val)) {\n        const length = val.length;\n        let debug = '[';\n        if (length > 0) {\n            debug += debugString(val[0]);\n        }\n        for(let i = 1; i < length; i++) {\n            debug += ', ' + debugString(val[i]);\n        }\n        debug += ']';\n        return debug;\n    }\n    // Test for built-in\n    const builtInMatches = /\\[object ([^\\]]+)\\]/.exec(toString.call(val));\n    let className;\n    if (builtInMatches.length > 1) {\n        className = builtInMatches[1];\n    } else {\n        // Failed to match the standard '[object ClassName]'\n        return toString.call(val);\n    }\n    if (className == 'Object') {\n        // we're a user defined class or Object\n        // JSON.stringify avoids problems with cycles, and is generally much\n        // easier than looping through ownProperties of `val`.\n        try {\n            return 'Object(' + JSON.stringify(val) + ')';\n        } catch (_) {\n            return 'Object';\n        }\n    }\n    // errors\n    if (val instanceof Error) {\n        return `${val.name}: ${val.message}\\n${val.stack}`;\n    }\n    // TODO we could test for more things here, like `Set`s and `Map`s.\n    return className;\n}\n/**\n* Initialize logging.\n*\n* You can use this to emit logs from augurs to the browser console.\n* The default is to log everything to the console, but you can\n* change the log level and whether logs are emitted to the console\n* or to the browser's performance timeline.\n*\n* IMPORTANT: this function should only be called once. It will throw\n* an exception if called more than once.\n* @param {LogConfig | undefined} [config]\n*/\nexport function initLogging(config) {\n    try {\n        const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n        wasm.initLogging(retptr, isLikeNone(config) ? 0 : addHeapObject(config));\n        var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n        var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n        if (r1) {\n            throw takeObject(r0);\n        }\n    } finally {\n        wasm.__wbindgen_add_to_stack_pointer(16);\n    }\n}\n\n/**\n* Initialize the logger and panic hook.\n*\n* This will be called automatically when the module is imported.\n* It sets the default tracing subscriber to `tracing-wasm`, and\n* sets WASM panics to print to the console with a helpful error\n* message.\n*/\nexport function custom_init() {\n    wasm.custom_init();\n}\n\nfunction handleError(f, args) {\n    try {\n        return f.apply(this, args);\n    } catch (e) {\n        wasm.__wbindgen_exn_store(addHeapObject(e));\n    }\n}\n\nconst ChangepointDetectorFinalization = (typeof FinalizationRegistry === 'undefined')\n    ? { register: () => {}, unregister: () => {} }\n    : new FinalizationRegistry(ptr => wasm.__wbg_changepointdetector_free(ptr >>> 0, 1));\n/**\n* A changepoint detector.\n*/\nexport class ChangepointDetector {\n\n    static __wrap(ptr) {\n        ptr = ptr >>> 0;\n        const obj = Object.create(ChangepointDetector.prototype);\n        obj.__wbg_ptr = ptr;\n        ChangepointDetectorFinalization.register(obj, obj.__wbg_ptr, obj);\n        return obj;\n    }\n\n    __destroy_into_raw() {\n        const ptr = this.__wbg_ptr;\n        this.__wbg_ptr = 0;\n        ChangepointDetectorFinalization.unregister(this);\n        return ptr;\n    }\n\n    free() {\n        const ptr = this.__destroy_into_raw();\n        wasm.__wbg_changepointdetector_free(ptr, 0);\n    }\n    /**\n    * @param {ChangepointDetectorType} detectorType\n    */\n    constructor(detectorType) {\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            wasm.changepointdetector_new(retptr, addHeapObject(detectorType));\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);\n            if (r2) {\n                throw takeObject(r1);\n            }\n            this.__wbg_ptr = r0 >>> 0;\n            ChangepointDetectorFinalization.register(this, this.__wbg_ptr, this);\n            return this;\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n    /**\n    * Create a new Bayesian Online changepoint detector with a Normal Gamma prior.\n    * @param {NormalGammaDetectorOptions | undefined} [opts]\n    * @returns {ChangepointDetector}\n    */\n    static normalGamma(opts) {\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            wasm.changepointdetector_normalGamma(retptr, isLikeNone(opts) ? 0 : addHeapObject(opts));\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);\n            if (r2) {\n                throw takeObject(r1);\n            }\n            return ChangepointDetector.__wrap(r0);\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n    /**\n    * Create a new Autoregressive Gaussian Process changepoint detector\n    * with the default kernel and parameters.\n    * @param {DefaultArgpcpDetectorOptions | undefined} [opts]\n    * @returns {ChangepointDetector}\n    */\n    static defaultArgpcp(opts) {\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            wasm.changepointdetector_defaultArgpcp(retptr, isLikeNone(opts) ? 0 : addHeapObject(opts));\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);\n            if (r2) {\n                throw takeObject(r1);\n            }\n            return ChangepointDetector.__wrap(r0);\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n    /**\n    * Detect changepoints in the given time series.\n    * @param {number[] | Float64Array} y\n    * @returns {Changepoints}\n    */\n    detectChangepoints(y) {\n        try {\n            const retptr = wasm.__wbindgen_add_to_stack_pointer(-16);\n            wasm.changepointdetector_detectChangepoints(retptr, this.__wbg_ptr, addHeapObject(y));\n            var r0 = getDataViewMemory0().getInt32(retptr + 4 * 0, true);\n            var r1 = getDataViewMemory0().getInt32(retptr + 4 * 1, true);\n            var r2 = getDataViewMemory0().getInt32(retptr + 4 * 2, true);\n            if (r2) {\n                throw takeObject(r1);\n            }\n            return takeObject(r0);\n        } finally {\n            wasm.__wbindgen_add_to_stack_pointer(16);\n        }\n    }\n}\n\nasync function __wbg_load(module, imports) {\n    if (typeof Response === 'function' && module instanceof Response) {\n        if (typeof WebAssembly.instantiateStreaming === 'function') {\n            try {\n                return await WebAssembly.instantiateStreaming(module, imports);\n\n            } catch (e) {\n                if (module.headers.get('Content-Type') != 'application/wasm') {\n                    console.warn(\"`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\\n\", e);\n\n                } else {\n                    throw e;\n                }\n            }\n        }\n\n        const bytes = await module.arrayBuffer();\n        return await WebAssembly.instantiate(bytes, imports);\n\n    } else {\n        const instance = await WebAssembly.instantiate(module, imports);\n\n        if (instance instanceof WebAssembly.Instance) {\n            return { instance, module };\n\n        } else {\n            return instance;\n        }\n    }\n}\n\nfunction __wbg_get_imports() {\n    const imports = {};\n    imports.wbg = {};\n    imports.wbg.__wbindgen_error_new = function(arg0, arg1) {\n        const ret = new Error(getStringFromWasm0(arg0, arg1));\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbindgen_is_string = function(arg0) {\n        const ret = typeof(getObject(arg0)) === 'string';\n        return ret;\n    };\n    imports.wbg.__wbindgen_object_drop_ref = function(arg0) {\n        takeObject(arg0);\n    };\n    imports.wbg.__wbindgen_string_get = function(arg0, arg1) {\n        const obj = getObject(arg1);\n        const ret = typeof(obj) === 'string' ? obj : undefined;\n        var ptr1 = isLikeNone(ret) ? 0 : passStringToWasm0(ret, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);\n        var len1 = WASM_VECTOR_LEN;\n        getDataViewMemory0().setInt32(arg0 + 4 * 1, len1, true);\n        getDataViewMemory0().setInt32(arg0 + 4 * 0, ptr1, true);\n    };\n    imports.wbg.__wbindgen_object_clone_ref = function(arg0) {\n        const ret = getObject(arg0);\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbindgen_string_new = function(arg0, arg1) {\n        const ret = getStringFromWasm0(arg0, arg1);\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbindgen_is_object = function(arg0) {\n        const val = getObject(arg0);\n        const ret = typeof(val) === 'object' && val !== null;\n        return ret;\n    };\n    imports.wbg.__wbindgen_is_undefined = function(arg0) {\n        const ret = getObject(arg0) === undefined;\n        return ret;\n    };\n    imports.wbg.__wbindgen_in = function(arg0, arg1) {\n        const ret = getObject(arg0) in getObject(arg1);\n        return ret;\n    };\n    imports.wbg.__wbindgen_number_get = function(arg0, arg1) {\n        const obj = getObject(arg1);\n        const ret = typeof(obj) === 'number' ? obj : undefined;\n        getDataViewMemory0().setFloat64(arg0 + 8 * 1, isLikeNone(ret) ? 0 : ret, true);\n        getDataViewMemory0().setInt32(arg0 + 4 * 0, !isLikeNone(ret), true);\n    };\n    imports.wbg.__wbindgen_is_bigint = function(arg0) {\n        const ret = typeof(getObject(arg0)) === 'bigint';\n        return ret;\n    };\n    imports.wbg.__wbindgen_bigint_from_u64 = function(arg0) {\n        const ret = BigInt.asUintN(64, arg0);\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbindgen_jsval_eq = function(arg0, arg1) {\n        const ret = getObject(arg0) === getObject(arg1);\n        return ret;\n    };\n    imports.wbg.__wbindgen_boolean_get = function(arg0) {\n        const v = getObject(arg0);\n        const ret = typeof(v) === 'boolean' ? (v ? 1 : 0) : 2;\n        return ret;\n    };\n    imports.wbg.__wbg_new_abda76e883ba8a5f = function() {\n        const ret = new Error();\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_stack_658279fe44541cf6 = function(arg0, arg1) {\n        const ret = getObject(arg1).stack;\n        const ptr1 = passStringToWasm0(ret, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);\n        const len1 = WASM_VECTOR_LEN;\n        getDataViewMemory0().setInt32(arg0 + 4 * 1, len1, true);\n        getDataViewMemory0().setInt32(arg0 + 4 * 0, ptr1, true);\n    };\n    imports.wbg.__wbg_error_f851667af71bcfc6 = function(arg0, arg1) {\n        let deferred0_0;\n        let deferred0_1;\n        try {\n            deferred0_0 = arg0;\n            deferred0_1 = arg1;\n            console.error(getStringFromWasm0(arg0, arg1));\n        } finally {\n            wasm.__wbindgen_free(deferred0_0, deferred0_1, 1);\n        }\n    };\n    imports.wbg.__wbg_mark_f0616123624944ec = function(arg0, arg1) {\n        performance.mark(getStringFromWasm0(arg0, arg1));\n    };\n    imports.wbg.__wbg_log_914e3639af348b4e = function(arg0, arg1) {\n        let deferred0_0;\n        let deferred0_1;\n        try {\n            deferred0_0 = arg0;\n            deferred0_1 = arg1;\n            console.log(getStringFromWasm0(arg0, arg1));\n        } finally {\n            wasm.__wbindgen_free(deferred0_0, deferred0_1, 1);\n        }\n    };\n    imports.wbg.__wbg_log_12b4ba535cbd9499 = function(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7) {\n        let deferred0_0;\n        let deferred0_1;\n        try {\n            deferred0_0 = arg0;\n            deferred0_1 = arg1;\n            console.log(getStringFromWasm0(arg0, arg1), getStringFromWasm0(arg2, arg3), getStringFromWasm0(arg4, arg5), getStringFromWasm0(arg6, arg7));\n        } finally {\n            wasm.__wbindgen_free(deferred0_0, deferred0_1, 1);\n        }\n    };\n    imports.wbg.__wbg_measure_a990198e921c09fd = function() { return handleError(function (arg0, arg1, arg2, arg3) {\n        let deferred0_0;\n        let deferred0_1;\n        let deferred1_0;\n        let deferred1_1;\n        try {\n            deferred0_0 = arg0;\n            deferred0_1 = arg1;\n            deferred1_0 = arg2;\n            deferred1_1 = arg3;\n            performance.measure(getStringFromWasm0(arg0, arg1), getStringFromWasm0(arg2, arg3));\n        } finally {\n            wasm.__wbindgen_free(deferred0_0, deferred0_1, 1);\n            wasm.__wbindgen_free(deferred1_0, deferred1_1, 1);\n        }\n    }, arguments) };\n    imports.wbg.__wbindgen_is_function = function(arg0) {\n        const ret = typeof(getObject(arg0)) === 'function';\n        return ret;\n    };\n    imports.wbg.__wbindgen_jsval_loose_eq = function(arg0, arg1) {\n        const ret = getObject(arg0) == getObject(arg1);\n        return ret;\n    };\n    imports.wbg.__wbindgen_as_number = function(arg0) {\n        const ret = +getObject(arg0);\n        return ret;\n    };\n    imports.wbg.__wbg_String_b9412f8799faab3e = function(arg0, arg1) {\n        const ret = String(getObject(arg1));\n        const ptr1 = passStringToWasm0(ret, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);\n        const len1 = WASM_VECTOR_LEN;\n        getDataViewMemory0().setInt32(arg0 + 4 * 1, len1, true);\n        getDataViewMemory0().setInt32(arg0 + 4 * 0, ptr1, true);\n    };\n    imports.wbg.__wbindgen_number_new = function(arg0) {\n        const ret = arg0;\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_getwithrefkey_edc2c8960f0f1191 = function(arg0, arg1) {\n        const ret = getObject(arg0)[getObject(arg1)];\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_set_f975102236d3c502 = function(arg0, arg1, arg2) {\n        getObject(arg0)[takeObject(arg1)] = takeObject(arg2);\n    };\n    imports.wbg.__wbg_call_1084a111329e68ce = function() { return handleError(function (arg0, arg1) {\n        const ret = getObject(arg0).call(getObject(arg1));\n        return addHeapObject(ret);\n    }, arguments) };\n    imports.wbg.__wbg_get_3baa728f9d58d3f6 = function(arg0, arg1) {\n        const ret = getObject(arg0)[arg1 >>> 0];\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_length_ae22078168b726f5 = function(arg0) {\n        const ret = getObject(arg0).length;\n        return ret;\n    };\n    imports.wbg.__wbg_new_a220cf903aa02ca2 = function() {\n        const ret = new Array();\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_next_de3e9db4440638b2 = function(arg0) {\n        const ret = getObject(arg0).next;\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_next_f9cb570345655b9a = function() { return handleError(function (arg0) {\n        const ret = getObject(arg0).next();\n        return addHeapObject(ret);\n    }, arguments) };\n    imports.wbg.__wbg_done_bfda7aa8f252b39f = function(arg0) {\n        const ret = getObject(arg0).done;\n        return ret;\n    };\n    imports.wbg.__wbg_value_6d39332ab4788d86 = function(arg0) {\n        const ret = getObject(arg0).value;\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_iterator_888179a48810a9fe = function() {\n        const ret = Symbol.iterator;\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_get_224d16597dbbfd96 = function() { return handleError(function (arg0, arg1) {\n        const ret = Reflect.get(getObject(arg0), getObject(arg1));\n        return addHeapObject(ret);\n    }, arguments) };\n    imports.wbg.__wbg_new_525245e2b9901204 = function() {\n        const ret = new Object();\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_set_673dda6c73d19609 = function(arg0, arg1, arg2) {\n        getObject(arg0)[arg1 >>> 0] = takeObject(arg2);\n    };\n    imports.wbg.__wbg_isArray_8364a5371e9737d8 = function(arg0) {\n        const ret = Array.isArray(getObject(arg0));\n        return ret;\n    };\n    imports.wbg.__wbg_instanceof_ArrayBuffer_61dfc3198373c902 = function(arg0) {\n        let result;\n        try {\n            result = getObject(arg0) instanceof ArrayBuffer;\n        } catch (_) {\n            result = false;\n        }\n        const ret = result;\n        return ret;\n    };\n    imports.wbg.__wbg_isSafeInteger_7f1ed56200d90674 = function(arg0) {\n        const ret = Number.isSafeInteger(getObject(arg0));\n        return ret;\n    };\n    imports.wbg.__wbg_entries_7a0e06255456ebcd = function(arg0) {\n        const ret = Object.entries(getObject(arg0));\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_buffer_b7b08af79b0b0974 = function(arg0) {\n        const ret = getObject(arg0).buffer;\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_new_ea1883e1e5e86686 = function(arg0) {\n        const ret = new Uint8Array(getObject(arg0));\n        return addHeapObject(ret);\n    };\n    imports.wbg.__wbg_set_d1e79e2388520f18 = function(arg0, arg1, arg2) {\n        getObject(arg0).set(getObject(arg1), arg2 >>> 0);\n    };\n    imports.wbg.__wbg_length_8339fcf5d8ecd12e = function(arg0) {\n        const ret = getObject(arg0).length;\n        return ret;\n    };\n    imports.wbg.__wbg_instanceof_Uint8Array_247a91427532499e = function(arg0) {\n        let result;\n        try {\n            result = getObject(arg0) instanceof Uint8Array;\n        } catch (_) {\n            result = false;\n        }\n        const ret = result;\n        return ret;\n    };\n    imports.wbg.__wbindgen_bigint_get_as_i64 = function(arg0, arg1) {\n        const v = getObject(arg1);\n        const ret = typeof(v) === 'bigint' ? v : undefined;\n        getDataViewMemory0().setBigInt64(arg0 + 8 * 1, isLikeNone(ret) ? BigInt(0) : ret, true);\n        getDataViewMemory0().setInt32(arg0 + 4 * 0, !isLikeNone(ret), true);\n    };\n    imports.wbg.__wbindgen_debug_string = function(arg0, arg1) {\n        const ret = debugString(getObject(arg1));\n        const ptr1 = passStringToWasm0(ret, wasm.__wbindgen_malloc, wasm.__wbindgen_realloc);\n        const len1 = WASM_VECTOR_LEN;\n        getDataViewMemory0().setInt32(arg0 + 4 * 1, len1, true);\n        getDataViewMemory0().setInt32(arg0 + 4 * 0, ptr1, true);\n    };\n    imports.wbg.__wbindgen_throw = function(arg0, arg1) {\n        throw new Error(getStringFromWasm0(arg0, arg1));\n    };\n    imports.wbg.__wbindgen_memory = function() {\n        const ret = wasm.memory;\n        return addHeapObject(ret);\n    };\n\n    return imports;\n}\n\nfunction __wbg_init_memory(imports, memory) {\n\n}\n\nfunction __wbg_finalize_init(instance, module) {\n    wasm = instance.exports;\n    __wbg_init.__wbindgen_wasm_module = module;\n    cachedDataViewMemory0 = null;\n    cachedUint8ArrayMemory0 = null;\n\n\n    wasm.__wbindgen_start();\n    return wasm;\n}\n\nfunction initSync(module) {\n    if (wasm !== undefined) return wasm;\n\n\n    if (typeof module !== 'undefined' && Object.getPrototypeOf(module) === Object.prototype)\n    ({module} = module)\n    else\n    console.warn('using deprecated parameters for `initSync()`; pass a single object instead')\n\n    const imports = __wbg_get_imports();\n\n    __wbg_init_memory(imports);\n\n    if (!(module instanceof WebAssembly.Module)) {\n        module = new WebAssembly.Module(module);\n    }\n\n    const instance = new WebAssembly.Instance(module, imports);\n\n    return __wbg_finalize_init(instance, module);\n}\n\nasync function __wbg_init(module_or_path) {\n    if (wasm !== undefined) return wasm;\n\n\n    if (typeof module_or_path !== 'undefined' && Object.getPrototypeOf(module_or_path) === Object.prototype)\n    ({module_or_path} = module_or_path)\n    else\n    console.warn('using deprecated parameters for the initialization function; pass a single object instead')\n\n    if (typeof module_or_path === 'undefined') {\n        module_or_path = new URL('changepoint_bg.wasm', import.meta.url);\n    }\n    const imports = __wbg_get_imports();\n\n    if (typeof module_or_path === 'string' || (typeof Request === 'function' && module_or_path instanceof Request) || (typeof URL === 'function' && module_or_path instanceof URL)) {\n        module_or_path = fetch(module_or_path);\n    }\n\n    __wbg_init_memory(imports);\n\n    const { instance, module } = await __wbg_load(await module_or_path, imports);\n\n    return __wbg_finalize_init(instance, module);\n}\n\nexport { initSync };\nexport default __wbg_init;\n"], "names": ["wasm", "cachedTextDecoder", "TextDecoder", "ignoreBOM", "fatal", "decode", "Error", "cachedUint8ArrayMemory0", "getUint8ArrayMemory0", "byteLength", "Uint8Array", "memory", "buffer", "getStringFromWasm0", "ptr", "len", "subarray", "heap", "Array", "fill", "undefined", "push", "heap_next", "length", "addHeapObject", "obj", "idx", "getObject", "takeObject", "ret", "dropObject", "WASM_VECTOR_LEN", "cachedTextEncoder", "TextEncoder", "encode", "encodeString", "encodeInto", "arg", "view", "buf", "set", "read", "written", "passStringToWasm0", "malloc", "realloc", "mem", "offset", "code", "charCodeAt", "slice", "isLikeNone", "x", "cachedDataViewMemory0", "getDataViewMemory0", "detached", "DataView", "debugString", "val", "type", "description", "name", "isArray", "debug", "i", "builtInMatches", "exec", "toString", "call", "className", "JSON", "stringify", "_", "message", "stack", "initLogging", "config", "retptr", "__wbindgen_add_to_stack_pointer", "r0", "getInt32", "custom_init", "handleError", "f", "args", "apply", "this", "e", "__wbindgen_exn_store", "ChangepointDetectorFinalization", "FinalizationRegistry", "register", "unregister", "__wbg_changepointdetector_free", "ChangepointDetector", "__wrap", "Object", "create", "prototype", "__wbg_ptr", "__destroy_into_raw", "free", "constructor", "detectorType", "changepointdetector_new", "r1", "normalGamma", "opts", "changepointdetector_normalGamma", "defaultArgpcp", "changepointdetector_defaultArgpcp", "detectChangepoints", "y", "changepointdetector_detectChangepoints", "__wbg_get_imports", "imports", "wbg", "__wbindgen_error_new", "arg0", "arg1", "__wbindgen_is_string", "__wbindgen_object_drop_ref", "__wbindgen_string_get", "ptr1", "__wbindgen_malloc", "__wbindgen_realloc", "len1", "setInt32", "__wbindgen_object_clone_ref", "__wbindgen_string_new", "__wbindgen_is_object", "__wbindgen_is_undefined", "__wbindgen_in", "__wbindgen_number_get", "setFloat64", "__wbindgen_is_bigint", "__wbindgen_bigint_from_u64", "BigInt", "asUintN", "__wbindgen_jsval_eq", "__wbindgen_boolean_get", "v", "__wbg_new_abda76e883ba8a5f", "__wbg_stack_658279fe44541cf6", "__wbg_error_f851667af71bcfc6", "deferred0_0", "deferred0_1", "console", "error", "__wbindgen_free", "__wbg_mark_f0616123624944ec", "performance", "mark", "__wbg_log_914e3639af348b4e", "__wbg_log_12b4ba535cbd9499", "arg2", "arg3", "arg4", "arg5", "arg6", "arg7", "__wbg_measure_a990198e921c09fd", "deferred1_0", "deferred1_1", "measure", "arguments", "__wbindgen_is_function", "__wbindgen_jsval_loose_eq", "__wbindgen_as_number", "__wbg_String_b9412f8799faab3e", "String", "__wbindgen_number_new", "__wbg_getwithrefkey_edc2c8960f0f1191", "__wbg_set_f975102236d3c502", "__wbg_call_1084a111329e68ce", "__wbg_get_3baa728f9d58d3f6", "__wbg_length_ae22078168b726f5", "__wbg_new_a220cf903aa02ca2", "__wbg_next_de3e9db4440638b2", "next", "__wbg_next_f9cb570345655b9a", "__wbg_done_bfda7aa8f252b39f", "done", "__wbg_value_6d39332ab4788d86", "value", "__wbg_iterator_888179a48810a9fe", "Symbol", "iterator", "__wbg_get_224d16597dbbfd96", "Reflect", "get", "__wbg_new_525245e2b9901204", "__wbg_set_673dda6c73d19609", "__wbg_isArray_8364a5371e9737d8", "__wbg_instanceof_ArrayBuffer_61dfc3198373c902", "result", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "__wbg_isSafeInteger_7f1ed56200d90674", "Number", "isSafeInteger", "__wbg_entries_7a0e06255456ebcd", "entries", "__wbg_buffer_b7b08af79b0b0974", "__wbg_new_ea1883e1e5e86686", "__wbg_set_d1e79e2388520f18", "__wbg_length_8339fcf5d8ecd12e", "__wbg_instanceof_Uint8Array_247a91427532499e", "__wbindgen_bigint_get_as_i64", "setBigInt64", "__wbindgen_debug_string", "__wbindgen_throw", "__wbindgen_memory", "__wbg_finalize_init", "instance", "module", "exports", "__wbg_init", "__wbindgen_wasm_module", "__wbindgen_start", "initSync", "getPrototypeOf", "warn", "WebAssembly", "<PERSON><PERSON><PERSON>", "Instance", "async", "module_or_path", "URL", "Request", "fetch", "Response", "instantiateStreaming", "headers", "bytes", "arrayBuffer", "instantiate", "__wbg_load"], "sourceRoot": ""}