# Flink SQL Demo - YAML-Driven Stream Processing

A production-ready Apache Flink streaming data processing system that eliminates Java coding through intelligent YAML-driven job generation.

## 🏗️ **System Architecture**

This demo implements a complete streaming data pipeline using:

- **Apache Flink** - Stream processing engine with SQL interface
- **RedPanda** - High-performance Kafka-compatible streaming platform
- **ClickHouse** - Real-time analytical database
- **Grafana** - Monitoring and visualization
- **Docker Compose** - Container orchestration

## 🚀 **Key Features**

### **Zero-Code Streaming Jobs**
- **YAML-Only Configuration** - Define complete streaming jobs without writing Java code
- **Intelligent Auto-Generation** - Automatic Flink SQL generation from YAML specifications
- **Component-Based Architecture** - Modular readers, transformers, and writers
- **Auto-Schema Detection** - Smart schema generation based on topic naming patterns

### **Production-Ready Pipeline**
- **Order Processing** - Real-time order event processing with field transformations
- **Station Monitoring** - Live station status tracking with aggregation
- **ClickHouse Integration** - Direct streaming to analytical database with upsert support
- **Multi-Job Deployment** - Deploy multiple streaming jobs from single configuration

### **Enterprise Features**
- **Fault Tolerance** - Built-in checkpointing and recovery mechanisms
- **Error Handling** - Robust JSON parsing with graceful error recovery
- **Monitoring Stack** - Integrated Flink UI and Grafana dashboards
- **Scalable Architecture** - Horizontal scaling support for high-throughput scenarios

## 📋 **Quick Start**

### **Prerequisites**
- Docker & Docker Compose
- Java 11+ (for building)
- Maven 3.6+ (for building)

### **1. Start the Infrastructure**
```bash
# Start all services
docker-compose up -d

# Verify services are running
docker-compose ps
```

### **2. Build the Autogen System**
```bash
cd flink-sql
mvn clean package -DskipTests
```

### **3. Deploy Streaming Jobs**
```bash
# Copy JAR and configuration to Flink
docker cp flink-job/streaming-jobs.yaml jobmanager:/tmp/streaming-jobs.yaml
docker cp flink-sql/target/autogen-job.jar jobmanager:/tmp/autogen-job.jar

# Submit jobs to Flink cluster
docker exec -it jobmanager /opt/flink/bin/flink run -d /tmp/autogen-job.jar /tmp/streaming-jobs.yaml
```

### **4. Monitor and Verify**
- **Flink UI**: http://localhost:8081 - Job monitoring and metrics
- **Grafana**: http://localhost:3000 - Data visualization dashboards
- **ClickHouse**: Query data directly via SQL client

## 📊 **Data Flow**

```
Data Generators → RedPanda (Kafka) → Flink Autogen → ClickHouse
                                          ↓
                                    Grafana Dashboards
```

### **Active Processing Jobs**

#### **Order Processing Pipeline**
- **Source**: `cf.iot.order.events` topic
- **Transformations**:
  - `id` → `order_id` (primary key)
  - `amount` → `total_amount` (currency conversion ready)
  - `status` → `order_status` (standardized status)
- **Sink**: `cf.data_platform.processed.orders` → ClickHouse
- **Schema**: `order_id`, `station_id`, `total_amount`, `order_status`

#### **Station Status Pipeline**
- **Source**: `cf.iot.station.status` topic
- **Transformations**:
  - `id` → `station_id` (primary key)
  - `status` → `current_status` (normalized status)
- **Sink**: `cf.data_platform.station.status` → ClickHouse
- **Schema**: `station_id`, `current_status` → aggregated to `offline_count`

## 🔧 **Configuration**

### **YAML Job Configuration**
Jobs are defined in `flink-job/streaming-jobs.yaml`:

```yaml
jobs:
  - name: order_processing_autogen
    readers:
      type: KafkaReader
      topic: cf.iot.order.events
      format:
        type: json
    transformers:
       - type: WithColumnTransformer
         columns:
           - colname: order_id
             expr: id
           - colname: total_amount
             expr: amount
           - colname: order_status
             expr: status
       - type: ProjectionTransformer
         columns:
           - colname: order_id
           - colname: station_id
           - colname: total_amount
           - colname: order_status
    writers:
      type: KafkaClickHouseWriter
      topic: cf.data_platform.processed.orders
      mode: upsert
      format:
        type: clickhouse
```

### **Available Components**

#### **Readers**
- `KafkaReader` - Kafka topic consumption with intelligent schema detection

#### **Transformers**
- `WithColumnTransformer` - Column creation/renaming with SQL expressions
- `ProjectionTransformer` - Column selection and filtering

#### **Writers**
- `KafkaClickHouseWriter` - ClickHouse-compatible output with upsert support

## 📈 **Monitoring & Verification**

### **Real-time Metrics**
- **Flink UI**: Job status, throughput, latency, checkpoints
- **Grafana**: Custom dashboards for business metrics
- **ClickHouse**: Direct SQL queries for data verification

### **Data Verification Commands**
```sql
-- Check order processing (should show thousands of records)
SELECT COUNT(*) FROM cf.processed_orders_merge_tree;
SELECT * FROM cf.processed_orders_merge_tree ORDER BY ingestion_time DESC LIMIT 5;

-- Check station status (should show real-time updates)
SELECT COUNT(*) FROM cf.station_status_merge_tree WHERE event_date > now() - INTERVAL 1 MINUTE;
SELECT station_id, offline_count, event_date FROM cf.station_status_merge_tree ORDER BY event_date DESC LIMIT 5;
```

## 🛠️ **Development**

### **Adding New Jobs**
1. **Define YAML Configuration** - Add job specification to `streaming-jobs.yaml`
2. **Rebuild Autogen JAR** - `mvn clean package -DskipTests`
3. **Deploy Updated Configuration** - Copy JAR and YAML to Flink cluster
4. **Monitor Deployment** - Verify via Flink UI

### **Extending Components**
- **Custom Readers** - Implement `Reader` interface for new data sources
- **Custom Transformers** - Implement `Transformer` interface for complex logic
- **Custom Writers** - Implement `Writer` interface for new sinks
- **Registration** - Add components to `ComponentFactory`

## 🔍 **Troubleshooting**

### **Common Issues & Solutions**
- **Job Failures** → Check Flink UI logs and exception details
- **Schema Mismatches** → Verify transformer output matches writer expectations
- **No Data Flow** → Confirm Kafka topics exist and ClickHouse tables are created
- **Performance Issues** → Monitor resource usage and adjust parallelism

### **Useful Commands**
```bash
# Job Management
docker exec -it jobmanager /opt/flink/bin/flink list
docker exec -it jobmanager /opt/flink/bin/flink cancel <job-id>

# Data Verification
docker exec -it clickhouse clickhouse-client --query "SELECT COUNT(*) FROM cf.processed_orders_merge_tree"
docker exec -it clickhouse clickhouse-client --query "SHOW TABLES FROM cf"

# System Monitoring
docker-compose logs -f jobmanager
docker-compose logs -f clickhouse
```

## 📚 **Documentation**

- **[TECHNICAL_GUIDE.md](TECHNICAL_GUIDE.md)** - Detailed architecture and implementation
- **[YAML_JOB_GUIDE.md](YAML_JOB_GUIDE.md)** - Complete YAML configuration reference

## 🎯 **Production Use Cases**

This system enables:
- **Real-time Analytics** - Live dashboards with sub-second latency
- **Event Stream Processing** - Order processing, IoT data, user behavior
- **Data Pipeline Automation** - ETL/ELT with zero custom code
- **Microservices Integration** - Event-driven architecture patterns
- **Operational Intelligence** - System monitoring and alerting

## 🚀 **Production Deployment**

For production environments:
1. **Resource Scaling** - Configure Flink parallelism and container resources
2. **Security** - Enable Kafka authentication and ClickHouse access control
3. **Monitoring** - Integrate with enterprise monitoring (Prometheus, DataDog)
4. **Backup/Recovery** - Configure persistent storage and disaster recovery
5. **CI/CD Integration** - Automate build, test, and deployment pipelines

---

**Transform your streaming data architecture with YAML-driven simplicity and enterprise-grade reliability!** 🎉